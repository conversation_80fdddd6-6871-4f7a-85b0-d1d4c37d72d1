﻿
.float-container {
    /*border: 1px solid #000000;*/
    /*padding: 20px;*/
}

.float-child {
    width: 50%;
    float: left;
    /*padding: 20px;*/
    /*border: 1px solid #000000;*/
}

table.dataTable.row-border tbody th, table.dataTable.row-border tbody td, table.dataTable.display tbody th, table.dataTable.display tbody td {
    border-top: 1px solid #3f3c3c;
    border-left: 1px solid #3f3c3c;
    border-right: 1px solid #3f3c3c;
}

table.dataTable.display tbody tr:first-child td {
    width: 120px;
}

table.dataTable tbody td {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: normal;
}

.subTable table {
    border-collapse: collapse;
    height: 100%;
    width: 100%;
}

.subTable {
    padding: 0px !important;
    font-weight: bold;
    border-collapse: collapse;
    height: 100%;
    width: 100%;
}

#playlistindex {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

[class*="col-"] {
    float: left;
}

.bm-photo {
    cursor: pointer;
    width: calc(100% - 15px);
    height: calc(100% - 2px);
}

.bm-img {
    font-size: 120px;
}


/*.ui-new-dialog {
            z-index: 10000 !important;
        }*/

/* enable absolute positioning */
.inner-addon {
    position: relative;
}
    /* style icon */
    .inner-addon .fa-search {
        position: absolute;
        padding: 10px;
        pointer-events: none;
    }
/* align icon */
.left-addon .glyphicon {
    left: 0px;
}
/* add padding  */
.left-addon input {
    padding-left: 30px;
}

.ul-rpt-menu li {
    width: 100%;
}

button.ui-datepicker-current {
    /*display: none;*/
}

.rpt-name-link {
    font-weight: bold;
}

.panel-title > a {
    display: block;
    position: relative;
}

    .panel-title > a:after {
        content: "\f078"; /* fa-chevron-down */
        font-family: 'FontAwesome';
        position: absolute;
        right: 0;
    }

    .panel-title > a[aria-expanded="true"]:after {
        content: "\f077"; /* fa-chevron-up */
    }

.modal-lg {
    left: 0;
    width: 100% !important;
}

.modal-header .close {
    margin-top: -20px;
}

.report-section-h2 {
    margin-top: 14px;
}

.rpt-tree {
    background-color: #2c3e50;
    color: #fff;
}

    .rpt-tree ul {
        padding-left: 10px !important;
        /*margin-left: 0px !important;*/
    }

        .rpt-tree ul li {
            /*border-bottom: 1px solid #1F262E;*/
            -webkit-box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.1);
            -moz-box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.1);
            box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.1);
        }

.rpt-tree-panel {
    /*width: 50%;*/
    height: 310px;
    max-height: 320px;
    overflow-y: scroll;
}

.rpt-panel-body {
    padding: 5px;
}

.rpt-panel-heading {
    padding: 5px 7px;
}

.ui-state-hover, .ui-state-focus {
    background: none !important;
    color: #3189c3 !important;
}
/*.ui-state-active, .ui-widget-content .ui-state-active { background: #000 url("images/ui-bg_gloss-wave_50_009E0F_500x100.png") 50% 50% repeat-x !important; }*/
.ui-state-active, .ui-widget-content .ui-state-active {
    background-color: #000 !important;
}


.wizard > .content {
    min-height: 27em !important;
}

    .wizard > .content > .body {
        padding: 0 2% !important;
    }

.wizard .content .body#divReportSteps-p-1 {
    overflow-y: auto;
}

.nav_pageNum {
    width: 50px !important;
    padding-left: 7px !important;
}

#cbxUsers.form-control {
    height: 46px !important;
}

.form-control[disabled] {
    background-color: #eeeeee !important;
}

@media (max-width:575px) {
    .hidden-xs {
        display: none !important;
    }
}

@media (min-width:576px) and (max-width:767px) {
    .hidden-xs, .hidden-sm {
        display: none !important;
    }
}

@media (min-width:768px) and (max-width:991px) {
    .hidden-md {
        display: none !important;
    }
}

@media (min-width:992px) and (max-width:1199px) {
    .hidden-lg {
        display: none !important;
    }
}

@media (min-width:1200px) {
    .hidden-xl {
        display: none !important;
    }
}

@media screen and (max-width: 1366px) {
    .eval-row {
        height: 52px;
        padding: 3px 5px;
    }

    .radio-inline + .radio-inline,
    .checkbox-inline + .checkbox-inline {
        margin-left: 0px;
    }
}

#divsavedreport {
    height: -moz-calc(100% - (50px + 30px));
    height: -webkit-calc(100% - (50px + 30px));
    height: calc(100% - (50px + 30px));
    display: block;
}

.table-bordered {
    box-sizing: border-box !important;
    -moz-box-sizing: border-box !important;
    -webkit-box-sizing: border-box !important;
}

/*#btnSearchSaveCalls {
            display: none !important;
        }*/

.modal-open {
    overflow: auto !important;
}

.modal-header {
    background-color: #2184be;
    color: #fff;
}

.nav-tabs.nav-justified > li {
    border: 1px solid #fff;
}

    .nav-tabs.nav-justified > li > a {
        border: none !important;
        border-radius: 0;
        padding: 0;
        text-transform: uppercase;
    }

        .nav-tabs.nav-justified > li > a:hover {
            border: none;
            border-radius: 0;
            background-color: inherit !important;
        }

        .nav-tabs.nav-justified > li > a > .vessel-rpt-text {
            border: none;
            border-top: 4px solid #FFF !important;
            display: block;
            background-color: #217dbb;
            color: #fff;
            padding: 20px 0;
        }

    .nav-tabs.nav-justified > li.active > a > .vessel-rpt-text {
        border-top: 4px solid #217dbb !important;
        background-color: #FFF;
        color: #000;
    }

    .nav-tabs.nav-justified > li > a > .map {
        border: none;
        height: 90px;
        margin-bottom: 15px;
        display: block;
    }

    .nav-tabs.nav-justified > li.active > a {
        border-right: 1px solid #fff;
        background-color: #fff;
    }

.title {
    margin-left: 0px;
}

.fa-image, .fa-youtube-play {
    font-size: 70px;
}

.bm-img {
    font-size: 120px;
}

.searchable-container {
    margin-top: 40px;
}

.table-condensed {
    margin-bottom: 0px !important;
}

    .table-condensed th,
    .table-condensed td,
    .table-condensed tbody > tr > td {
        padding: 3px 5px !important;
    }

.info-block {
    border-right: 5px solid #E6E6E6;
    margin-bottom: 15px;
}

    .info-block .square-box {
        width: 80px;
        min-height: 80px;
        margin-right: 15px;
        text-align: center !important;
        background-color: #676767;
        padding: 5px 0;
    }

    .info-block:hover .info-block.block-info {
        border-color: #20819e;
    }

    .info-block.block-info .square-box, .info-block.block-info .bm-box {
        background-color: #5bc0de;
        color: #fff;
    }

    .info-block .bm-box {
        width: 140px;
        min-height: 140px;
        margin-right: 15px;
        text-align: center !important;
        background-color: #676767;
        padding: 5px 0;
    }


label.text-bold {
    font-weight: bold;
    margin-bottom: 3px;
}

.survey-title-details {
    text-align: center;
}

    .survey-title-details .survey-hire {
        margin-top: 0;
        margin-bottom: 0;
        text-transform: uppercase;
    }

.survey-comp-name {
    margin-top: 0;
    color: #3989c6;
}

.text-heading {
    color: #3989c6;
}

.bm-photo {
    cursor: pointer;
    width: 125px;
    height: 125px;
}

.text-bold {
    font-weight: bold;
}

.picker {
    border-radius: 5px;
    //width: 20px;
    height: 20px;
    cursor: pointer;
    -webkit-transition: all linear .2s;
    -moz-transition: all linear .2s;
    -ms-transition: all linear .2s;
    -o-transition: all linear .2s;
    transition: all linear .2s;
    border: thin solid #eee;
}

    .picker:hover {
        transform: scale(1.1);
    }

#tblEvents, #tblEvents th, #tblEvents td {
    border: 1px solid black;
    border-collapse: collapse;
}

.dataTables_scrollBody {
    /*overflow-x: hidden !important;*/
    overflow-y: auto !important;
}

/*video::-webkit-media-controls,
        video::-webkit-media-controls-panel,
        video::-webkit-media-controls-play-button,
        video::-webkit-media-controls-start-play-button {
            display: none !important;
            -webkit-appearance: none;
        }*/


.twitter {
    font: normal normal 10px Arial;
    text-align: center;
    color: #998578;
    text-transform: uppercase;
    letter-spacing: 3px;
}

.twitter {
    color: #000000;
    text-decoration: none;
    display: block;
    padding: 14px;
    -webkit-transition: all .25s ease;
    -moz-transition: all .25s ease;
    -ms-transition: all .25s ease;
    -o-transition: all .25s ease;
    transition: all .25s ease;
}

    .twitter:hover {
        color: #FF7D6D;
        text-decoration: none;
    }

/*span {
            font-style: italic;
            display: block;
        }

        img {
            max-width: 100%;
        }*/

/* Floating Social Media Bar Style Starts Here */

.fl-fl {
    background: #292b2e;
    text-transform: uppercase;
    letter-spacing: 3px;
    padding: 4px;
    width: 200px;
    position: fixed;
    right: -160px;
    z-index: 1000;
    font: normal normal 10px Arial;
    -webkit-transition: all .25s ease;
    -moz-transition: all .25s ease;
    -ms-transition: all .25s ease;
    -o-transition: all .25s ease;
    transition: all .25s ease;
}

    /*.fa {
            font-size: 20px;
            color: #fff;
            padding: 10px 0;
            width: 40px;
            margin-left: 8px;
        }*/

    .fl-fl:hover {
        right: 0;
    }

    .fl-fl a {
        color: #fff !important;
        text-decoration: none;
        text-align: center;
        line-height: 43px !important;
        /*vertical-align: top!important;*/
    }

.float-fb {
    top: 160px;
}

.float-tw {
    top: 215px;
}

.float-gp {
    top: 270px;
}

.float-rs {
    top: 325px;
}

.float-ig {
    top: 380px;
}

.float-pn {
    top: 435px;
}



.callTypeSprite {
    background: url(../assets/images/callTypes.png) center center no-repeat;
    width: 16px;
    height: 16px;
    margin: auto auto;
    display: block;
}

.callType2 {
    height: 14px;
    background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/2.png) center center no-repeat;
}

.callType4 {
    height: 10px;
    background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/4.png) center center no-repeat;
}

.callType5 {
    height: 13px;
    background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/5.png) center center no-repeat;
}

.callType9 {
    background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/9.png) center center no-repeat;
}

.callType10 {
    background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/10.png) center center no-repeat;
}

.mtrSignType1_revview {
    height: 14px;
    background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/1_revview.png) center center no-repeat;
}

.groupTypeSprite {
    background: url(../assets/images/groupType_white.png) center center no-repeat;
    width: 14px;
    margin: auto auto;
    display: block;
}

li.groupType:before {
    background: url("../assets/images/groupType_white.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    content: "";
    display: inline-block;
    margin-bottom: 0;
    /*margin-left: -35px;*/
    margin-top: 3px;
    min-height: 14px;
    position: absolute;
    vertical-align: middle;
    width: 14px;
}

table.dataTable thead th {
    vertical-align: middle;
    text-align: center;
    white-space: nowrap;
    min-width: fit-content;
}

.deleteIcon {
    background: url("../../assets/icons/common/iconDelete.png") center center no-repeat transparent;
    display: block;
    height: 16px;
    margin-top: 2px;
    width: 16px;
}

#divuseraddeditem .vis-foreground {
    background-color: #DDDDDD;
    font-weight: bold;
}

#divuseraddeditem .vis-center > .vis-content {
    overflow-x: hidden;
    overflow-y: hidden;
}

#divuseraddeditem .vis-left .vis-content {
    background: none;
}

.vis-item.vis-range {
    border-radius: 8px !important;
}

.vis-item {
    border-color: #97B0F8 !important;
    background-color: #A5D8A2 !important;
}

.vis-center > .vis-content {
    overflow-x: hidden;
    overflow-y: scroll;
    height: 100%;
}

.vis-left > .vis-content {
    height: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
}

#visualization_addfiles > .vis-timeline {
    max-height: 300px !important;
}

.vis-current-time {
    z-index: 1000 !important;
}

.dataTables_info:hover {
    background-color: #2b2b2b;
    color: #fff;
}

#divExportprogressBar div {
    /*color: #fff;*/
    background-color: #2980b9;
}

#divAdditionalButtons {
    height: auto !important;
}

.tooltip.right {
    margin-left: -7px;
}
/*.tooltip.left {
            margin-left: -40px;
        }*/

.mg-top-0-16em {
    margin-top: 0.16em;
}

/**
            Playlist Fullscreen Metadata Changes
        */

@media all and (display-mode: fullscreen) {
    .playlist-timeline-video-player {
        height: 100%;
        width: 66%;
        float: left;
    }

    .playlist-timeline-video-player-metadata {
        display: block;
    }

    .show-on-fullscreen-only {
        display: block;
    }

    .hide-on-fullscreen {
        display: none;
    }
}

@media not all and (display-mode: fullscreen) {
    .playlist-timeline-video-player {
        width: 100%;
        height: 100%;
    }

    .playlist-timeline-video-player-metadata {
        display: none;
    }

    .show-on-fullscreen-only {
        display: none;
    }

    .hide-on-fullscreen {
        display: block;
    }
}

div.scrollmenu {
    background-color: #2980b9;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    animation-play-state: paused;
}

    div.scrollmenu a {
        display: inline-block;
        color: white;
        text-align: center;
        padding: 14px;
        text-decoration: none;
    }

        div.scrollmenu a:hover {
            background-color: #777;
        }

.arrow {
    position: absolute;
    z-index: 2
}

#leftArr {
    left: 0;
}

#rightArr {
    right: 0;
}

.nav-tabs > .close {
    margin: -2px 0 0 10px;
    font-size: 18px;
}
