﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.SurveyEntities
{
    [Serializable]
    public class Question
    {

        #region Properties

        public long Id { get; set; }
        public long RevSyncQuestionId { get; set; }
        public int SurveyId { get; set; }
        public int RevSyncSurveyId { get; set; }
        public int SectionId { get; set; }
        public int RevSyncSectionId { get; set; }
        public string Statement { get; set; }
        public int Ordering { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
        public short TypeId { get; set; }
        public ControlType Type
        {
            get { return (ControlType)TypeId; }
            set { TypeId = (byte)value; }
        }
        public float Score { get; set; }

        #endregion

        #region Associations

        /// <summary>
        /// 
        /// </summary>
        public List<Option> Options { get; set; }

        #endregion

        public object Clone()
        {
            return ObjectUtil.DeepClone(this);
        }

    }
}
