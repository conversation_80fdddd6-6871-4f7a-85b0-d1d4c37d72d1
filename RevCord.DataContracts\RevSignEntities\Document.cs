﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RevSignEntities
{
    public class Document
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string FileName { get; set; }
        public bool IsUploaded { get; set; }
        public string Notes { get; set; }
        public int CompleteInDays { get; set; }
        public bool EnableReminder { get; set; }
        public int ReminderAfterDays { get; set; }
        public SigningTypeId SigningTypeId { get; set; }

        public DocumentStatus DocumentStatus { get; set; }
        public int OwnerId { get; set; }
        public string OwnerName { get; set; }
        public string OwnerEmail { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public DateTime CompletionDate { get; set; }
        public bool IsDeleted { get; set; }


        public string PdfLink { get; set; }
        public string SignedFileLink { get; set; }
        public string SignedFileName { get; set; }
        public List<DocumentSigner> DocumentSigners { get; set; }

        public string EventDate { get; set; }
        public int IwbDocumentId { get; set; }
    }

    public class DocumentInfo
    {
        public int DocumentId { get; set; }
        public string Title { get; set; }
        public string FileName { get; set; }
        public string PdfLink { get; set; }
        public string Notes { get; set; }
        public int CompleteInDays { get; set; }
        public string DocumentStatus { get; set; }
        public int OwnerId { get; set; }
        public string OwnerName { get; set; }
        public string OwnerEmail { get; set; }
        public DateTime DocumentCreatedDate { get; set; }
        public string SignStatus { get; set; }
        public bool HasSigned { get; set; }
        public int RsDocumentSignerId { get; set; }
        public long SignerId { get; set; }
        public string SignerName { get; set; }
        public string SignerEmail { get; set; }
        public string Reason { get; set; }
        public string SignedFileName { get; set; }
        public string SignedFileLink { get; set; }

        public string MetaData { get; set; }
        public string SignedOn { get; set; }

        public string ViewPdfLink { get; set; }
        public int TenantId { get; set; }
        public bool IsViewOnly { get; set; }
    }
    public class DocumentResult
    {
        public int ResultNo { get; set; }
        public string ResultMessage { get; set; }
        public DocumentInfo DocumentInfo { get; set; }
    }
}
