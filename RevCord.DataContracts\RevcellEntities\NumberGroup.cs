﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RevcellEntities
{
    [JsonObject("data")]
    public class NumberGroup
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("phone_number_count")]
        public int PhoneNumberCount { get; set; }

        [Json<PERSON>roperty("sticky_sender")]
        public bool StickySender { get; set; }
    }
}
