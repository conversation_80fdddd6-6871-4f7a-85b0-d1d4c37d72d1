﻿/// <reference path="../jQuery/jqueryintellisense.js" />
var Displaying = { 'en': 'Displaying', 'es': 'Mostrando' };
var To = { 'en': 'to', 'es': 'a' };
var Of = { 'en': 'of', 'es': 'de' };
var Files = { 'en': 'files', 'es': 'archivos' };
var Page = { 'en': 'Page', 'es': 'Página' };
var NoItems = { 'en': 'No Items', 'es': 'No hay Archivos' };
var ProcessingWait = { 'en': 'Processing, please wait ..', 'es': 'Procesando, favor espere ...' };

var gridColModel;
var plOption = [];
var imgColPL = 3;
var addPlDuration = 1000;
var progressDuration = 3000;
var zipDownloadDur = 5000;
var startPlayback = false;
var editCommentsLength = 100;
var pListId = 0;
var zipFileName = '';
var noOfMaxCallsToPlay = 100;
var noOfPlayingCalls = 0;
var emptyGrpExt = -999;
var lastSearchCriteria = null;
var isLoginToQB = false;
var encryptionPassword = "poi098";
var bUserSearchLoaded = false;
var userSearchIndex = 0;
//Added by ifuturz
var iconCss, iconCss1;
var tooltip, ht, isUserItemIcon;
var lastCallId = '';
var mdPanel = null;
var notesDialog = null;

var messageTemplate = `
        <div id="divPlMediaMeta" class ="container-fluid">
            <div class ="row-fluid">
                    <div class ="span3">
                    <div class ="pl-accordian">
                        <h3><a href="#">Interview Details</a></h3>
                        <div>
                            <div class ="meta_container case-info">
                                <table class ="table meta-info-table">
                                    <tbody>
                                        <tr>
                                            <td class ="meta-label text-left">Event #: </td>
                                            <td class ="meta-info-value text-left"><span class ="pl-case-no"></span></td>
                                        </tr>
                                        <tr>
                                            <td class ="meta-label text-left">Location: </td>
                                            <td class ="meta-info-value text-left"><span class ="pl-case-location"></span></td>
                                        </tr>
                                        <tr>
                                            <td class ="meta-label text-left">Date: </td>
                                            <td class ="meta-info-value text-left"><span class ="pl-case-date"></span></td>
                                        </tr>
                                        <tr>
                                            <td class ="meta-label text-left">User: </td>
                                            <td class ="meta-info-value text-left"><span class ="pl-case-Interviewer"></span></td>
                                        </tr>
                                        <tr>
                                            <td class ="meta-label text-left">Interviewee: </td>
                                            <td class ="meta-info-value text-left"><span class ="pl-case-Interviewee"></span></td>
                                        </tr>
                                        <tr>
                                            <td class ="meta-label text-left">Notes: </td>
                                            <td class ="meta-info-value text-left"><span class ="pl-case-Notes"></span></td>
                                        </tr>
                                        <tr class ="hide">
                                            <td class ="meta-label text-left">DialogId: </td>
                                            <td class ="meta-info-value text-left"><span class ="pl-case-qb-dialogid"></span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class ="span3">
                    <div id="pl-event-bm" class ="pl-accordian">
                        <h3><a href="#">Bookmarks</a></h3>
                        <div>
                            <div id="divPlBookmarkDetails" style="overflow: auto; height: auto;">
                                <div style="width: 100%; overflow: auto; float: left; font-size: small;" class ="meta_container">
                                    <table id="tblPlBookmarkContent" class ='table meta-info-table table-details' border='0'>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class ="span6">
                    <div id="pl-event-chat" class ="pl-accordian">
                        <h3><a href="#">Chat</a></h3>
                        <div>
                            <div class ="">
                                <div id="pl-messages-list" class ="content list-group">

                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>`;

//End by ifuturz
//var screenWidth = 550;
//var screenHeight = 400;

var checkeventBuffering = true; // KM IFUTURZ - TO RESTRICT CALLING eventBuffering WHILE ITS ALREADY IN ANOTHER CALL
var currentSelectedPlaylistId = 0; // KM IFUTURZ - FOR STORING CURRENT PLAYLIST ID TO PASS PLAYLIST ID FOR INSERTING SOCIAL FILES
var bEventShare = false;
$(document).ready(function () {
    try {
        
        bEventShare = true;
        adjustMainPlayer();
        resizeViewEventWindow();
        tenantId = $('#hdnTenantId').val();
        setTimeout(function () {
            $(window).trigger('resize');
        }, 500);

        // 1. Check if link is expired.
        if ($('#hdnIsExpired').val() == 'true') {
            blankGrid();
            $('#tblCalls').flexAddData({ rows: [], page: 1, total: 0 });
            showInformationDialog('Link has expired.');

            return;
        }

        // 2. Confirm Access Code
        buildAndDisplayDialog();

        //window.setTimeout(function () {
        //    loadCallSearchResults(populateGetCriteria($('#hdnEventId').val()), 'GetCallsByEvent');
        //}, 1000);

        setTimeout(function () {
            $('.ui-new-dialog-titlebar-close').tooltip();
        }, 3000);


        $('#hdnActiveTab').val('Search');
        saveTracksByPLId();
        //ADDED BY KM IFUTURZ - START
        if (!boolIEBrowser) {
            SetBuffering(false);
            SetFilePlayerTitlesLocalization();
            showhideCurrentDuration(0);
        }
        //ADDED BY KM IFUTURZ - STOP

        //lastSearchCriteria = isECEnabled == true ? populateSearchCriteriaEC(1) : populateSearchCriteria(1);

        fullScreenEventHandlers();

        $('#videofileplayer').bind('contextmenu', function () { return false; });
        $('#videofileplayer1').bind('contextmenu', function () { return false; });
        $('#idvideolist').bind('contextmenu', function () { return false; });
    } catch (e) {
        //JL(frontendLogger).info(e.message);
        console.error(e.message);
    }
});

$(function () {
    $('#lnkExpandClose').on('click', function () {
        //window.setTimeout(resizeViewEventWindow, 500);

        setTimeout(function () {
            resizeViewEventWindow();

        }, 20);

        //resizeCallsTable();
    });
});

$(window).resize(function () {

    setTimeout(function () {
        resizeViewEventWindow();
    }, 20);

});

//#region Media Functions
function getRecorderVod(recid) {
    if (isECEnabled) {
        for (var i = 0; i < jsonRecorders.length; i++) {
            if (jsonRecorders[i]['Id'] == recid) {
                //JL(frontendLogger).info('found=' + jsonRecorders[i].Id + '-' + jsonRecorders[i].Name + '-' + jsonRecorders[i].IP + '-' + jsonRecorders[i].VOD);
                return jsonRecorders[i].VOD;
            }
        }
    }
    else {
        return vodServerURL;
    }
}
function getRecorderIP(recid) {
    if (isECEnabled) {
        for (var i = 0; i < jsonRecorders.length; i++) {
            if (jsonRecorders[i]['Id'] == recid) {
                //JL(frontendLogger).info('found=' + jsonRecorders[i].Id + '-' + jsonRecorders[i].Name + '-' + jsonRecorders[i].IP + '-' + jsonRecorders[i].VOD);
                return jsonRecorders[i].IP;
            }
        }
    }
    else {
        return screenURL;
    }
}
function getRecorderIsPrimary(recid) {
    if (isECEnabled) {
        for (var i = 0; i < jsonRecorders.length; i++) {
            if (jsonRecorders[i]['Id'] == recid) {
                //JL(frontendLogger).info('found=' + jsonRecorders[i].Id + '-' + jsonRecorders[i].Name + '-' + jsonRecorders[i].IP + '-' + jsonRecorders[i].VOD);
                return jsonRecorders[i].IsPrimary;
            }
        }
    }
    else {
        return true;
    }
}
function createMediaURI(recid, mediaType, sTime, fName, durInSec, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var vod = getRecorderVod(recid);
    if (vod) {
        //JL(frontendLogger).info(vod + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName);
        return vod + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&tenantId=" + tenantId;
    }

    ////return vodServerURL + "main=0&sub=0" + "&media=" + mediaType + "&ver=1&user=&attr=0&date=" + pdate + "&file=" + fName + "&index=" + ptime;
    //return vodServerURL + "main=0&sub=0" + "&media=" + mediaType + "&ver=1&user=&attr=0&date=" + pdate + "&file=" + fName + "&index=" + ptime + "&time=" + ptime + "&duration=" + durInSec;
    //JL(frontendLogger).info(vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName);
    //return vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName;
}
function createMessageURI_Silverlight(fName, mediaType, sTime, msgBody, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    return clientRootURL + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&text=" + msgBody + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
}
function createMessageURI(mediaType, sTime, fName, msgBody, chNum, chName, durInSec) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    ///Commented by ifuturz for message text
    //return clientRootURL + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&text=" + msgBody + "&tel=" + chNum + "&chName=" + chName;
    ///End by ifuturz for message text
    ///Added by ifuturz for message text
    return clientRootURL + "&media=" + mediaType + "&file=" + fName + ".uai" + "&date=" + pdate + "&time=" + ptime + "&text=" + msgBody + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&duration=" + durInSec;
    ///End by ifuturz for message text
}
function createVideoURI(fName, mediaType, sTime, durInSec, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    return videoURL + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
}
function createInquireURI(fName, mediaType, sTime, durInSec, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    //   
    //JL(frontendLogger).info("KM Inquire URL");
    // JL(frontendLogger).info(inquireURL + pdate + "/" + fName);
    if (tenantId > 0) {
        return inquireURL + tenantId + "/" + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
    } else {
        return inquireURL + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
    }
    
    //  return inquireURL + pdate + "/" + fName;
}
//function createScreenURI(mediaType, sTime, fName, durInSec, screenName, chNum, chName) {
//    var pdate = sTime.substring(0, 8);
//    var ptime = sTime.substring(8, 14);
//    var screen = screenURL + pdate + "/" + screenName;
//    //JL(frontendLogger).info(vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName + "&screen=" + encodeURIComponent(screen));
//    return vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName + "&screen=" + encodeURIComponent(screen);
//}
function createScreenURI(recid, mediaType, sTime, fName, durInSec, screenName, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    if (isECEnabled) {
        var recip = getRecorderIP(recid);
        var screen = "http://" + recip + "/videofiles/" + pdate + "/" + screenName;
        //JL(frontendLogger).info("http://" + recip + ":4510/VOD/VSWebVODAgent.dll?" + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName + "&screen=" + encodeURIComponent(screen));
        return "http://" + recip + ":4510/VOD/VSWebVODAgent.dll?" + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&screen=" + encodeURIComponent(screen);
    }
    else {
        var screen = screenURL + pdate + "/" + screenName;
        //JL(frontendLogger).info(vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName + "&screen=" + encodeURIComponent(screen));
        return vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&screen=" + encodeURIComponent(screen);
    }
}

function createScreenURIWithoutAudio(fName, mediaType, sTime, durInSec, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    return screenURL + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName;
}

function createScreenURIWithoutDSF(recid, mediaType, startTime, durInSec, screenName, chNum, chName) {
    var screenURI;
    var pdate = startTime.substring(0, 8);
    var ptime = startTime.substring(8, 14);
    if (isECEnabled) {
        var recip = getRecorderIP(recid);
        screenURI = "http://" + recip + "/videofiles/" + pdate + "/" + screenName + '?media=' + mediaType + "&file=" + fName + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSec;
    }
    else {
        screenURI = screenURL + pdate + "/" + screenName + '?media=' + mediaType + "&file=" + fName + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSec + "&tel=" + chNum + "&chName=" + chName;
    }
    return screenURI;
}

function createQuadScreenURI(recid, mediaType, sTime, durInSec, screenNames, fName) {

    var screenFileNames = screenNames.split(',');
    var quadScreenURI = [];
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var durInSecInt = parseInt(durInSec, 10);
    $.each(screenFileNames, function (index, value) {
        //JL(frontendLogger).info(index +'-'+ value);
        if (isECEnabled) {
            var recip = getRecorderIP(recid);
            var screenURI = "http://" + recip + "/videofiles/" + pdate + "/" + value + '?media=' + 3 + "&file=" + fName + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSecInt;
            //JL(frontendLogger).info(screenURI);
            quadScreenURI.push(screenURI);
        }
        else {
            var screenURI = screenURL + pdate + "/" + value + '?media=' + 3 + "&file=" + fName + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSecInt;
            //JL(frontendLogger).info(screenURI);
            quadScreenURI.push(screenURI);
        }
    });
    return quadScreenURI;
}

function createQuadScreenURI_Silverlight(recid, mediaType, sTime, durInSec, screenNames, fName) {

    var screenFileNames = screenNames.split(',');
    var quadScreenURI = [];
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var durInSecInt = parseInt(durInSec, 10);
    $.each(screenFileNames, function (index, value) {
        //JL(frontendLogger).info(index +'-'+ value);
        if (isECEnabled) {
            var recip = getRecorderIP(recid);
            var screenURI = "http://" + recip + "/videofiles/" + pdate + "/" + value + '?media=' + 3 + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSecInt;
            //JL(frontendLogger).info(screenURI);
            quadScreenURI.push(screenURI);
        }
        else {
            var screenURI = screenURL + pdate + "/" + value + '?media=' + 3 + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSecInt;
            //JL(frontendLogger).info(screenURI);
            quadScreenURI.push(screenURI);
        }
    });
    return quadScreenURI;
}
function createDocumentURI_Silverlight(mediaType, sTime, fName, durInSec, pages) {

    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var userId = $('#header-login-name').attr('uid');
    //var plId = $("#ulPlaylists > li.active").attr("id");

    var element = $("#cbxLoadPlayList").find('option:selected');
    var plid = element.val();
    var durInSecInt = parseInt(durInSec, 10);
    //JL(frontendLogger).info(docURL + fName + "?media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&pages=" + pages + "&userAdded=1");
    if (isMTEnable == undefined || isMTEnable == false)
        return docURL + userId + '/' + plid + '/' + fName + "?media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSecInt + "&pages=" + pages + "&userAdded=1";
    else
        return docURL + 'Tenant_' + tenantId + '/' + userId + '/' + plid + '/' + fName + "?media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSecInt + "&pages=" + pages + "&userAdded=1";
}
function createDocumentURI(mediaType, sTime, fName, durInSec, pages) {

    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var userId = $('#header-login-name').attr('uid');
    //var plId = $("#ulPlaylists > li.active").attr("id");

    var element = $("#cbxLoadPlayList").find('option:selected');
    var plid = element.val();
    var durInSecInt = parseInt(durInSec, 10);
    //JL(frontendLogger).info(docURL + fName + "?media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&pages=" + pages + "&userAdded=1");
    if (isMTEnable == undefined || isMTEnable == false)
        return docURL + userId + '/' + plid + '/' + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSecInt + "&pages=" + pages + "&userAdded=1";
    else
        return docURL + 'Tenant_' + tenantId + '/' + userId + '/' + plid + '/' + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSecInt + "&pages=" + pages + "&userAdded=1";
}

function convertTimeToSeconds(hms) {
    //var hms = '02:04:33';
    var a = hms.split(':');
    var seconds = (+a[0]) * 60 * 60 + (+a[1]) * 60 + (+a[2]);
    //JL(frontendLogger).info(seconds);
    return seconds;
}
function SecondsTohhmmss(totalSeconds) {
    var hours = Math.floor(totalSeconds / 3600);
    var minutes = Math.floor((totalSeconds - (hours * 3600)) / 60);
    var seconds = totalSeconds - (hours * 3600) - (minutes * 60);

    // round seconds
    seconds = Math.round(seconds * 100) / 100

    var result = (hours < 10 ? "0" + hours : hours);
    result += ":" + (minutes < 10 ? "0" + minutes : minutes);
    result += ":" + (seconds < 10 ? "0" + seconds : seconds);
    return result;
}
//#endregion Media Functions

function addtoplayer(currentRow) {
    var $tr = currentRow;//$(currentRow).closest('tr');
    //$tr.toggleClass('trSelected');

    var recid = $tr.attr('recid');
    var callId = $tr.attr('id');
    var flName = $tr.attr('fName');
    var startTime = $tr.attr('stTime');
    //var durInSec = $tr.attr('cDuration'); //  ($tr.attr('cDuration'));
    var durInMilliSec = $tr.attr('cdDuration');
    var durInSec = durInMilliSec / 1000;
    var cType = $tr.attr('ctype');
    var chNum = $tr.attr('chNum');
    var chName = $tr.attr('chName');
    //var screenName = $tr.attr('sName');
    var screenName = $tr.attr('sName');


    var gps = $tr.attr('interview_GPS');
    var interview_DateTime = $tr.attr('interview_DateTime');
    var interview_Interviewer = $tr.attr('interview_Interviewer');
    var interview_Interviewee = $tr.attr('interview_Interviewee');
    var interview_InterviewId = $tr.attr('interview_InterviewId');
    var interview_Notes = $tr.attr('interview_Notes');

    var interview_details = interview_InterviewId + ";" + gps + ";" + interview_DateTime + ";" + interview_Interviewer + ";" + interview_Interviewee + ";" + interview_Notes;

    //JL(frontendLogger).info(durInSec);
    //JL(frontendLogger).info(callId + ' - ' + flName + ' - ' + startTime + ' - ' + durInSec);
    //JL(frontendLogger).info('  callId: ' + callId +'  flName: ' + flName +'  startTime: ' + startTime +'  durInSec: ' + durInSec);
    //var fileExt = flName.split('.')[1];
    var recid = $tr.attr('recid');
    var bm = $tr.find("input[id=hdnCallBookmarks]").val();
    //Inquiremarkerupdate(recid , "2" , "1449068524" , "STORED PROCEDURE UPDATE TEST From program" , "STORED PROCEDURE UPDATE TEST From program");
    var vod = '';

    //var checkbox = $tr.find("input[id='chkRow']:checkbox");
    var checkbox = $tr.find("input:checkbox.tblrowChk");
    //JL(frontendLogger).info($tr.find('td:eq(1)').text() + ' - ' + noOfPlayingCalls);
    //    if (noOfPlayingCalls == 50) {
    //        //JL(frontendLogger).info('50 added to player');
    //        return;
    //    }

    //   

    var isPictureEvent = $tr.attr('isPictureEvent');

    if (cType == "1" || cType == "13") {


        // var fileExt = flName.sub(flName.lastIndexOf('.') + 1);// flName.split('.')[1];
        var fileExt = flName.split('.')[1];

        if (checkbox.is(':checked')) {
            //if (getRecorderIsPrimary(recid))
            //    $tr.children('td[abbr="colSNo"]').find("div").append("<a id='btnEvaluateCall' href='javascript:void(0);' style='padding-left:3px;text-align:right;'><img src='../assets/icons/eval/QA1_20X17.png' height='16' alt='' /></a>");
            switch (fileExt.toLowerCase()) {
                case "dsf":
                    vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                    // vod = createMediaURI(recid, 0, startTime, flName, 10, chNum, chName);
                    if (boolIEBrowser)
                        slAddMedia1_Silverlight(callId, vod, bm);
                    else
                        slAddMedia1(callId, vod, bm);
                    //slAddMedia(callId, vod);
                    //noOfPlayingCalls++;
                    if (startPlayback)
                        window.setTimeout(function () { slPlayMedia(callId); startPlayback = false; }, addPlDuration);
                    //slPlayMedia(callId);
                    break;
                default:
                    //   case "mp3":
                    vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);
                    if (boolIEBrowser)
                        slAddMedia1_Silverlight(callId, vod, bm);
                    else
                        slAddMedia1(callId, vod, bm);
                    //noOfPlayingCalls++;
                    if (startPlayback)
                        window.setTimeout(function () { slPlayMedia(callId); startPlayback = false; }, addPlDuration);
                    //slPlayMedia(callId);
                    break;
            }
            //$tr.find('td:last').append("<a id='btnEvaluateCall' href='javascript:void(0);' class='btnEvaluateRow' style=''>Evaluate</a>");
            //$tr.find('td.evalCol div').append("<a id='btnEvaluateCall' href='javascript:void(0);' style='padding-left:3px;text-align:right;'><img src='../assets/icons/eval/QA1_20X17.png' height='16' alt='' /></a>");
        }
        else {
            $tr.children('td[abbr="colSNo"]').find('a[id=btnEvaluateCall]').remove();
            if (boolIEBrowser)
                slDeleteMedia_Silverlight(callId);
            else {
                slDeleteMedia(callId);

                DefaultSegmentPosition_ondeletemedia();
            }
            //noOfPlayingCalls--;
            if ($tr.find('td:nth-child(1)').hasClass('playCol')) {
                $tr.find('td:nth-child(1)').removeClass('playCol');
            }
            //$tr.find('td[abbr="colSNo"').find('a[id=btnEvaluateCall]').remove();
        }
    }
    if (cType == "2") {
        if (checkbox.is(':checked')) {
            swapVideoPlayerContents('Search');
            $("#InterviewBookmarkDetails").hide();
            //if (boolIEBrowser) {
            //    var x1 = flName.split('.');
            //    flName = x1[0] + ".wmv";
            //}


            var vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName); // 
            JL(frontendLogger).info("Video Url: ", vid);
            if (boolIEBrowser)
                slAddMedia_Silverlight(callId, vid);
            else
                slAddMedia(callId, vid);

            if (boolIEBrowser) {
                $("#videoContainerSV").width(350).height(232);
                //$('#objLVPlayer').width(320).height(240);
                $('#objLVPlayer').attr({ 'width': 320, 'height': 240 });
            }

            if (startPlayback)
                window.setTimeout(function () { slPlayMedia(callId); startPlayback = false; }, addPlDuration);
        }
        else {
            if (boolIEBrowser)
                slDeleteMedia_Silverlight(callId);
            else {
                slDeleteMedia(callId);
                DefaultSegmentPosition_ondeletemedia();
            }
            noOfPlayingCalls--;


            if ($tr.find('td:nth-child(1)').hasClass('playCol')) {
                $tr.find('td:nth-child(1)').removeClass('playCol');
            }
        }
    }
    if (cType == "6") {
        if (checkbox.is(':checked')) {
            //if (getRecorderIsPrimary(recid))
            //    $tr.children('td[abbr="colSNo"]').find("div").append("<a id='btnEvaluateCall' href='javascript:void(0);' style='padding-left:3px;text-align:right;'><img src='../assets/icons/eval/QA1_20X17.png' height='16' alt='' /></a>");
            $("#InterviewBookmarkDetails").hide();
            swapVideoPlayerContents('Search');

            var audioUri = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
            var screensUri = createQuadScreenURI(recid, 3, startTime, durInSec, screenName, flName);
            if (boolIEBrowser)
                screensUri = createQuadScreenURI_Silverlight(recid, 3, startTime, durInSec, screenName, flName);

            if (flName)
                audioUri += "&screen=withaudio";
            else
                audioUri += "&screen=withoutaudio";

            //if (flName)
            //    slAddQuadMedia(callId, audioUri, screensUri);
            //else
            //    slAddQuadScreen(callId, screensUri);
            if (boolIEBrowser)
                slAddQuadMedia(callId, audioUri, screensUri);
            else
                AddQuadMedia(callId, audioUri, screensUri);

            if (startPlayback)
                window.setTimeout(function () { slPlayMedia(callId); startPlayback = false; }, addPlDuration);
        }
        else {
            $tr.children('td[abbr="colSNo"]').find('a[id=btnEvaluateCall]').remove();
            if (boolIEBrowser)
                slDeleteMedia_Silverlight(callId);
            else {
                slDeleteMedia(callId);
                DefaultSegmentPosition_ondeletemedia();
                //noOfPlayingCalls--;
            }

            if ($tr.find('td:nth-child(1)').hasClass('playCol')) {
                $tr.find('td:nth-child(1)').removeClass('playCol');
            }
        }
    }
    if (cType == "7") {  // this is called for inquire events (mp3, m4a, mov, mp4 files)..
        if (checkbox.is(':checked')) {
            $("#divBookmarks").empty();
            //if (getRecorderIsPrimary(recid))
            //    $tr.children('td[abbr="colSNo"]').find("div").append("<a id='btnEvaluateCall' href='javascript:void(0);' style='padding-left:3px;text-align:right;'><img src='../assets/icons/eval/QA1_20X17.png' height='16' alt='' /></a>");

            if (isPictureEvent == "true") {
                let vid = createInquireURI(flName, 3, startTime, 1, chNum, chName, recid);
                slAddMedia(callId, vid, "true");
            }
            else {

                //$('#btnSearchSaveCalls').attr('src', '../assets/images/player/export_active.png');
                // InquireInterviewDetails(currentRow);
                $("#InterviewBookmarkDetails").show();
                swapVideoPlayerContents('Search');
                //searchVideoPlayerVisible('block');
                var vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName); // 
                var bookmark_inquire = "<inquire>" + bm + "</inquire>";
                //slAddInquireMedia(callId, vid, bookmark_inquire, interview_details);
                //     
                if (boolIEBrowser) {
                    //arivu
                    getInqFileVideoBookmarks(callId);

                    //slAddMedia_Silverlight(callId, vid);
                    slAddInquireMedia(callId, vid, bookmark_inquire, interview_details, null);
                }

                else {
                    slAddMedia(callId, vid);
                    // getEventDetail(callId);
                }
                //getInqFileVideoBookmarks(callId);
                getCallidForBookmarkEdit(callId);
                getCallIdForBookmarkAdd(callId);
                getCallIdforNotes(callId);
                //  slAddInquireMedia(callId, vid, '<inquire><Root><Record><Id>7</Id><CallIndex>2</CallIndex><Pos>1</Pos><Text>False information;markernotes;aadhirai</Text></Record><Record><Id>9</Id><CallIndex>2</CallIndex><Pos>15</Pos><Text>Point of interest;markernotes;12342</Text></Record><Record><Id>10</Id><CallIndex>2</CallIndex><Pos>20</Pos><Text>Confession</Text></Record><Record><Id>11</Id><CallIndex>2</CallIndex><Pos>24</Pos><Text>Marker 5;markernotes;adhithi</Text></Record><Record><Id>12</Id><CallIndex>2</CallIndex><Pos>28</Pos><Text>Marker 6</Text></Record><Record><Id>13</Id><CallIndex>2</CallIndex><Pos>32</Pos><Text>Marker 7</Text></Record><Record><Id>14</Id><CallIndex>2</CallIndex><Pos>37</Pos><Text>Marker 8</Text></Record><Record><Id>15</Id><CallIndex>2</CallIndex><Pos>40</Pos><Text>Marker 9</Text></Record><Record><Id>8</Id><CallIndex>2</CallIndex><Pos>5</Pos><Text>Cross check information</Text></Record></Root></inquire>', '1446822922721;10.3685239,78.8117354;Nov 06, 2015 - 20:45 PM;Marker test;John Goodman');

                if (startPlayback)
                    window.setTimeout(function () { slPlayMedia(callId); startPlayback = false; }, addPlDuration);
            }

        }
        else {
            $tr.children('td[abbr="colSNo"]').find('a[id=btnEvaluateCall]').remove();
            if (boolIEBrowser)
                slDeleteMedia_Silverlight(callId);
            else {
                slDeleteMedia(callId);
                DefaultSegmentPosition_ondeletemedia();
            }

            noOfPlayingCalls--;


            //noOfPlayingCalls--;
            if ($tr.find('td:nth-child(1)').hasClass('playCol')) {
                $tr.find('td:nth-child(1)').removeClass('playCol');
            }
        }
    }
    if (cType == "11") {  // this is called for inquire events (mp3, m4a, mov, mp4 files)..
        if (checkbox.is(':checked')) {
            if (getRecorderIsPrimary(recid))
                $tr.children('td[abbr="colSNo"]').find("div").append("<a id='btnEvaluateCall' href='javascript:void(0);' style='padding-left:3px;text-align:right;'><img src='../assets/icons/eval/QA1_20X17.png' height='16' alt='' /></a>");
            //$('#btnSearchSaveCalls').attr('src', '../assets/images/player/export_active.png');
            // InquireInterviewDetails(currentRow);
            $("#InterviewBookmarkDetails").show();
            swapVideoPlayerContents('Search');
            //searchVideoPlayerVisible('block');
            var vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName); // 
            var bookmark_inquire = "<inquire>" + bm + "</inquire>";
            //slAddInquireMedia(callId, vid, bookmark_inquire, interview_details);
            //     
            if (boolIEBrowser) {
                //arivu
                getInqFileVideoBookmarks(callId);

                //slAddMedia_Silverlight(callId, vid);
                slAddInquireMedia(callId, vid, bookmark_inquire, interview_details, null);
            }
            else {
                slAddMedia(callId, vid);
                //getMDEventDetail(callId);
            }
            getInqFileVideoBookmarks(callId);
            getCallidForBookmarkEdit(callId);
            getCallIdForBookmarkAdd(callId);
            getCallIdforNotes(callId);
            //  slAddInquireMedia(callId, vid, '<inquire><Root><Record><Id>7</Id><CallIndex>2</CallIndex><Pos>1</Pos><Text>False information;markernotes;aadhirai</Text></Record><Record><Id>9</Id><CallIndex>2</CallIndex><Pos>15</Pos><Text>Point of interest;markernotes;12342</Text></Record><Record><Id>10</Id><CallIndex>2</CallIndex><Pos>20</Pos><Text>Confession</Text></Record><Record><Id>11</Id><CallIndex>2</CallIndex><Pos>24</Pos><Text>Marker 5;markernotes;adhithi</Text></Record><Record><Id>12</Id><CallIndex>2</CallIndex><Pos>28</Pos><Text>Marker 6</Text></Record><Record><Id>13</Id><CallIndex>2</CallIndex><Pos>32</Pos><Text>Marker 7</Text></Record><Record><Id>14</Id><CallIndex>2</CallIndex><Pos>37</Pos><Text>Marker 8</Text></Record><Record><Id>15</Id><CallIndex>2</CallIndex><Pos>40</Pos><Text>Marker 9</Text></Record><Record><Id>8</Id><CallIndex>2</CallIndex><Pos>5</Pos><Text>Cross check information</Text></Record></Root></inquire>', '1446822922721;10.3685239,78.8117354;Nov 06, 2015 - 20:45 PM;Marker test;John Goodman');

            if (startPlayback)
                window.setTimeout(function () { slPlayMedia(callId); startPlayback = false; }, addPlDuration);

        }
        else {
            $tr.children('td[abbr="colSNo"]').find('a[id=btnEvaluateCall]').remove();
            if (boolIEBrowser)
                slDeleteMedia_Silverlight(callId);
            else {
                slDeleteMedia(callId);
                DefaultSegmentPosition_ondeletemedia();
            }

            noOfPlayingCalls--;


            //noOfPlayingCalls--;
            if ($tr.find('td:nth-child(1)').hasClass('playCol')) {
                $tr.find('td:nth-child(1)').removeClass('playCol');
            }
        }
    }
}
var callid_for_inquire = 0;
function DisplayInterviewDetails(callId) {
    callid_for_inquire = callId;
    var gps = "";
    var interview_DateTime = "";
    var interview_Interviewer = "";
    var interview_Interviewee = "";
    var interview_InterviewId = "";
    var interview_Notes = "";
    var interview_details = "";
    $('#tblCalls tbody tr').each(function () {

        if (callId == $(this).attr('id')) {
            gps = $(this).attr('interview_GPS');
            interview_DateTime = $(this).attr('interview_DateTime');
            interview_Interviewer = $(this).attr('interview_Interviewer');
            interview_Interviewee = $(this).attr('interview_Interviewee');
            interview_InterviewId = $(this).attr('interview_InterviewId');
            interview_Notes = $(this).attr('interview_Notes');
            if ($.trim($(this).attr('interview_Notes')) == '') {
                interview_Notes = '';
            }
            interview_details = interview_InterviewId + ";" + gps + ";" + interview_DateTime + ";" + interview_Interviewer + ";" + interview_Interviewee + ";" + interview_Notes;
            if ($(this).attr('ErrorInMuxProcess') == 1)
                showErrorDialog('Error in Audio conference Mux Process. Only video event is available.');
        }

    });
    inquireInterviewDetails(interview_details);
}

function Inquiremarkerupdate(recid, markerid, callid, markertext, markernotes) {

    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: {
            method: 'Inquiremarkerupdate'
                , callid: callid
                , markerid: markerid
                , markertext: markertext
                , markernotes: markernotes
                , recid: recid
        },
    });

}

function showMetadata(eventId) {
    mdPanel = $.jsPanel({
        id: 'pl-item-md',
        headerTitle: 'Metadata',
        //size: { width: 400, height: 200 },
        //contentSize: { width: 820, height: 200 },
        contentSize: { width: function () { return $(window).width() - 100 }, height: 200 },//contentSize: { width: 750, height: 200 },
        contentOverflow: { vertical: 'scroll' },
        position: 'left-top 50 340',//position: "bottom right",//position: { my: "left-top", at: "left-top", offsetX: 50, offsetY: 50 },
        theme: 'success',
        //panelstatus: 'minimized',
        status: 'minimized',
        closeOnEscape: 'true',
        //content: $("#divPlMediaMeta").html(),
        content: messageTemplate,
        callback: function (panel) {
            getPlaylistEventDetail(eventId);
            $('div', this.content).eq(2).find('i').css({
                cursor: 'pointer'
            }).click(function () { panel.close(); });
            $("div.pl-accordian").accordion({
                autoHeight: true,
                collapsible: true,
                //heightStyle: 'auto',//'fill',//'panel',//'content',
                heightStyle: 'content',//'content', //'auto',//'fill',//'panel',// /*ADDED BY KM IFUTURZ*/
                activate: function (event, ui) {
                    JL(frontendLogger).info('activate');
                    event.preventDefault();
                },
                beforeActivate: function (event, ui) {
                    if (ui.newHeader[0]) {
                        var currHeader = ui.newHeader;
                        var currContent = currHeader.next('.ui-accordion-content');
                    } else {
                        var currHeader = ui.oldHeader;
                        var currContent = currHeader.next('.ui-accordion-content');
                    }
                    var isPanelSelected = currHeader.attr('aria-selected') == 'true';
                    currHeader.toggleClass('ui-corner-all', isPanelSelected).toggleClass('accordion-header-active ui-state-active ui-corner-top', !isPanelSelected).attr('aria-selected', ((!isPanelSelected).toString()));
                    currHeader.children('.ui-icon').toggleClass('ui-icon-triangle-1-e', isPanelSelected).toggleClass('ui-icon-triangle-1-s', !isPanelSelected);
                    currContent.toggleClass('accordion-content-active', !isPanelSelected)
                    if (isPanelSelected) { currContent.slideUp(); } else { currContent.slideDown(); }

                    return false;
                }
            });

        }
    });
}

function hideMetadata() {
    if (mdPanel)
        mdPanel.close();
}

var acdOptions = { autoHeight: false, collapsible: true, active: false };

$(function () {
    $('.vis-item-content span.call-type-pl').live('click', function (e) {
        var eventId = $.trim($(e.target).data('evt-id'));
        showMetadata(eventId);

    });
    $('img.pl-item-lv-inq').live('click', function (e) {
        e.preventDefault();
        var eventId = $.trim($(e.target).closest('tr').attr('callid'));
        var callType = $(e.target).closest('tr').attr('ctype');
        if (callType == '7' || callType == '8') {
            //getPlaylistEventDetail(eventId);
            showMetadata(eventId);
        }
    });
    $('.close-pl-metadata').click(function (e) {
            hideMetadata();
    });

    $("div.accordian").accordion({
        //fillSpace: true,
        //autoHeight: true,
        autoHeight: false,
        collapsible: true,
        heightStyle: 'content',//'content', //'auto',//'fill',//'panel',//
        activate: function (event, ui) {
            JL(frontendLogger).info('activate');
            event.preventDefault();
        },
        beforeActivate: function (event, ui) {
            if (ui.newHeader[0]) {
                var currHeader = ui.newHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            } else {
                var currHeader = ui.oldHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            }
            var isPanelSelected = currHeader.attr('aria-selected') == 'true';
            currHeader.toggleClass('ui-corner-all', isPanelSelected).toggleClass('accordion-header-active ui-state-active ui-corner-top', !isPanelSelected).attr('aria-selected', ((!isPanelSelected).toString()));
            currHeader.children('.ui-icon').toggleClass('ui-icon-triangle-1-e', isPanelSelected).toggleClass('ui-icon-triangle-1-s', !isPanelSelected);
            currContent.toggleClass('accordion-content-active', !isPanelSelected)
            if (isPanelSelected) { currContent.slideUp(); } else { currContent.slideDown(); }

            return false;
        }
    });


    $("div.pl-accordian").accordion({
        autoHeight: true,
        collapsible: true,
        //heightStyle: 'auto',//'fill',//'panel',//'content',
        heightStyle: 'content',//'content', //'auto',//'fill',//'panel',// /*ADDED BY KM IFUTURZ*/
        activate: function (event, ui) {
            JL(frontendLogger).info('activate');
            event.preventDefault();
        },
        beforeActivate: function (event, ui) {
            if (ui.newHeader[0]) {
                var currHeader = ui.newHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            } else {
                var currHeader = ui.oldHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            }
            var isPanelSelected = currHeader.attr('aria-selected') == 'true';
            currHeader.toggleClass('ui-corner-all', isPanelSelected).toggleClass('accordion-header-active ui-state-active ui-corner-top', !isPanelSelected).attr('aria-selected', ((!isPanelSelected).toString()));
            currHeader.children('.ui-icon').toggleClass('ui-icon-triangle-1-e', isPanelSelected).toggleClass('ui-icon-triangle-1-s', !isPanelSelected);
            currContent.toggleClass('accordion-content-active', !isPanelSelected)
            if (isPanelSelected) { currContent.slideUp(); } else { currContent.slideDown(); }

            return false;
        }
    });

    makeGroupsTree();
    
    $('#tblCalls tbody tr input.tblrowChk').live("change", function () {
        //var $tr = $(this).parent().parent('tr:first');//$tr.toggleClass('selected');
        //checkUncheckAll();
        //addtoplayer($(this).parent().parent().parent());
        var $tr = $(this).closest('tr');
        if ($(this).is(":checked")) {
            $tr.addClass('trSelected');
            $('.share-event-link').removeClass('hide');
            //ShowBookmarksInPlayer($tr);
        }
        else {
            $tr.removeClass('trSelected');
            if (!boolIEBrowser)
                ClearBookmarksInPlayer(); // ADDED BY KM IFUTURZ - FOR CLEAR BOOKMARKS IN SEARCH > FILE PLAYER
        }

        //ShowBookmarksInPlayer($tr);
        addtoplayer($tr);
        DeSelectHeaderSearch(false);
        var selectedRows = $('#tblCalls tbody tr input.tblrowChk:checkbox:checked').length;
        JL(frontendLogger).info(selectedRows);
        if (selectedRows == 0) {
            $('.share-event-link').addClass('hide');
        }

    });

    $('#tblCalls tbody tr input#chkRetainCall').live("change", function () {
        updateRetaintion($(this).parent().parent().parent());
    });

    $('#tblCalls tbody tr').live("dblclick", function () {

        startPlayback = true;

        if (!boolIEBrowser && !$(this).closest('tr').find("input:checkbox.tblrowChk").is(':checked'))
            doubleclick = true;
        //$('input#chkRow', this).trigger('click');
        $('input:checkbox.tblrowChk', this).trigger('click');
        ////addtoplayer($(this));
    });

    $('#btnSaveCalls').live('click', function () {
        saveTracks();
    });

    $("#lnkAddPlaylist").click(function () {
        $('#divPlaylist').attr('plID', 0).dialog("open");
        $("#divPlaylist").parent().appendTo($("form:first"));
        return false;
    });

    $("#lnkVideoClose").click(function () {
        reOrderzIndex('searchNonVideoView');
    });

    $("#btnSearchCall").click(function () {
        //slReset();
        $('input#chkAllRows').attr('checked', false);
        DeSelectHeaderSearch(true);

        if (validateCriteria()) {
            bUserSearchLoaded = false;
            userSearchIndex = 0;
            $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
            var method = 'GetCallsByEvent';
            var qry = populateSearchCriteria(1);
            if (isECEnabled) {
                method = 'GetCallsByEvent';
                qry = populateSearchCriteriaEC(1);
            }
            lastSearchCriteria = qry;
            $("#tblCalls").flexOptions({ url: '../Handlers/VoiceRecHandlers/GuestHandler.ashx?method=' + method, query: qry, newp: 1 }).flexReload();
            $.unblockUI();
            if (getSearchType() != 'Default') {
                var searchType = getSearchType();
                if (searchType === 'Advance')
                    populateUserSearchesDDL(langKeys[currentLanguage]['tUserSearchAdvance']);
                else if (searchType === 'Global')
                    populateUserSearchesDDL(langKeys[currentLanguage]['tUserSearchGlobal']);
            }
        }
        //ieAddPlaceHolders();
        return false;
    });

    $("#ddlUserSearches").live('change', function () {
        var selectedIndex = $("#ddlUserSearches").prop('selectedIndex');
        var length = $('#ddlUserSearches > option').length;
        var actualIndex = length - selectedIndex - 1;
        //JL(frontendLogger).info("selectedIndex = " + selectedIndex + " lenght = " + length + " actualIndex = " + actualIndex);
        if (selectedIndex > 0) {
            bUserSearchLoaded = true;
            userSearchIndex = actualIndex;
            $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
            var method = 'PreviousSearch';
            $("#tblCalls").flexOptions({ url: '../Handlers/VoiceRecHandlers/VRHandler.ashx?method=' + method, query: actualIndex }).flexReload();
            $.unblockUI();
        }
        // To clear all the criteria fields, trigger the click event of lnkClearSelectedCalls.
        $("#lnkClearSelectedCalls").trigger('click');
    });

    $("#lnkClearSelectedCalls").click(function () {
        //slReset();
        var callId = '';
        //$('#tblCalls tbody tr .tblrowChk:checked').each(function () 
        //$('#tblCalls tbody tr input.tblrowChk:checkbox:checked').each(function () {
        $('#tblCalls tbody tr .tblrowChk:checked').each(function () {
            callId = $(this).closest('tr').attr('id');
            $(this).attr('checked', false);
            $(this).closest("tr").toggleClass("trSelected", this.checked);

            $(this).closest('tr').find('td[abbr="colSNo"]').find('a[id=btnEvaluateCall]').remove(); // $(this).parent().parent().parent().find('td[abbr="colSNo"]').find('a[id=btnEvaluateCall]').remove();
            $(this).closest('td').removeClass('playCol');
            if (boolIEBrowser) {
                slDeleteMedia_Silverlight(callId);
            }
            else
                slDeleteMedia(callId);
            DeSelectHeaderSearch(false);
        });
        $('input#chkAllRows').attr('checked', false); //$('#chkAllRows').prop('checked', false);
        $('#divCallSearch input[type="text"]').val('');
        $("input[id='txtDateStart'], input[id='txtDateEnd']").datepicker("option", "maxDate", null);
        $("input[id='txtDateStart'], input[id='txtDateEnd']").datepicker("option", "minDate", null);
        $('#tree7').find('input[type=checkbox]:checked').removeAttr('checked');
        $("#ddlUserSearches").prop('selectedIndex', '0');
    });

    $("#btnExportCalls").click(function () {
        $("#divExportData").dialog("open");
        $("#divExportData").parent().appendTo($("form:first"));
        return false;
    });

    $("#cbxPlayList").change(function () {
        var element = $(this).find('option:selected');
        var existingItems = parseInt($("#ulPlaylists > li[id='" + $(this).val() + "']").attr('noofitems'), 10);
        if ($(this).val() != 0) {
            var plds = getSelectedCallsForPl();
            var newCount = existingItems + plds.length;
            if (newCount <= 100) {
                if ($(this).val() != 0 && plds.length && plds !== 'undefined') {
                    addItemsInPlaylist($(this).val(), plds);
                }
                else {
                    showErrorDialog(langKeys[currentLanguage]['emsgSelectOneRow']);
                    $('#cbxPlayList').find('option:eq(0)').prop('selected', true);
                    return false;
                }
            }
            else {
                showErrorDialog(langKeys[currentLanguage]['emsgPlLimit']);
                //$("#cbxPlayList option:contains(Add to Playlist)").prop('selected', 'selected');
                $('#cbxPlayList').find('option:eq(0)').prop('selected', true);
            }
        }
    });

    $("#cbxLoadPlayList").change(function () {

        checkeventBuffering = false; // KM IFUTURZ - FOR RESOLVING BLINKKING IN TIMELINE PLAYER WHILE CHANGING PLAYLIST
        currentSelectedPlaylistId = $(this).val(); // KM IFUTURZ - STORING CURRENT PLAYLIST ID

        if (!boolIEBrowser)
            deleteallmedia();
        var element = $(this).find('option:selected');
        if ($(this).val() != 0) {
            var plid = $(this).val();
            var planchor = $("#ulPlaylists > li > span[id='" + plid + "']").parent();
            $('#hdnActiveTab').val('Timeline');
            $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
            getplaylistDetail(plid);
            $.unblockUI();
        }
    });

    var singleClickDelay = 300, clicks = 0, timer = null;

    $("#ulPlaylists > li > a").live("click", function (e) {
        $("#ulPlaylists > li.active").removeClass("active");

        var plid = $(this).parent().attr('id'); // $(this).attr('id');

        checkeventBuffering = false; // KM IFUTURZ - FOR RESOLVING BLINKKING IN TIMELINE PLAYER WHILE CHANGING PLAYLIST
        currentSelectedPlaylistId = plid; // KM IFUTURZ - STORING CURRENT PLAYLIST ID

        var curObj = $(this).closest('span'); //this;
        //JL(frontendLogger).info(this);
        clicks++;  //count clicks
        try {
            if (clicks === 1) {
                timer = window.setTimeout(function () {
                    ////alert("Single Click");  //perform single-click action
                    uncheckAllSearchRows();
                    $("#ulPlaylists > li[id=" + plid + "]").addClass("active");
                    $('#hdnActiveTab').val('Timeline');
                    //$.blockUI({ message: '<h1> Processing...</h1>' });
                    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });

                    $('#divTimeline').css('display', 'none');
                    // KM on 20160708

                    $('#divtimelineviewdesign').show();

                    getplaylistDetail(plid);
                    //$('#cbxLoadPlayList', select).remove();
                    $('#cbxLoadPlayList').children().remove();
                    $('#cbxPlayList option').clone().appendTo('#cbxLoadPlayList');
                    $('#cbxLoadPlayList option[value="0"]').text(langKeys[currentLanguage]['cbxLoadPl1']);
                    $("select[id='cbxLoadPlayList'] > option[value='" + plid + "']").attr("selected", "selected");
                    //$("select[id='cbxLoadPlayList'] > option[value=0]").text(langKeys[currentLanguage]['cbxLoadPl1']);
                    clicks = 0;             //after action performed, reset counter
                }, singleClickDelay);
            } else {
                clearTimeout(timer);    //prevent single-click action
                $('#divPlaylist').attr('plID', plid).dialog("open");
                $('#txtPlaylistName').val($(this).text());
                $('#txtPlComments').val($(this).parent().attr('comments'));
                $("#divPlaylist").parent().appendTo($("form:first"));

                clicks = 0;             //after action performed, reset counter
            }
        } catch (e) {
            //alert(e.message);
        }
        ////Added by ifuturz on 20161215
        lPos = 0;
        hPos = 1;
        if (!boolIEBrowser) {
            DefaultSegmentPosition();
            displaylink();

        }

        ////End by ifuturz on 20161215

    }).live("dblclick", function (e) {
        e.preventDefault();  //cancel system double-click event
    });

    $("#ulPlaylists li span").live("click", function (e) {
        ////alert('del' + $(this).attr('id'));
        var plid = $(this).parent().attr('id'); // $(this).attr('id');
        showConfirmationDialog(langKeys[currentLanguage]['cmsgPlDel'], plid, deletePlaylist);
    });

    $('#lnkDeleteAllPL').live('click', function () {
        //deletePlaylistItem(1, 'DeletePlaylistDetails');
        if ($('#tblPlaylist > tbody > tr').length) {
            var playlistId = $("#ulPlaylists > li.active").attr("id");
            showConfirmationDialog(langKeys[currentLanguage]['cmsgPldDelAll'], playlistId, removePlaylistItems);
        }
    });

    $("#tblPlaylist tbody tr .delItem").live('click', function () {
        var rowId = $(this).parent('tr').attr('id');
        var callId = $(this).parent('tr').attr('callid');
        if ($(this).parent('tr').attr('isuseritem') == 'true')
            showConfirmationDialog(langKeys[currentLanguage]['cmsgPldDel'], callId, removePlaylistUserItem);
        else
            showConfirmationDialog(langKeys[currentLanguage]['cmsgPldDel'], rowId, removePlaylistItem);
    });

    $('#tblPlaylist tbody tr').live("dblclick", function () {
        if ($(this).attr('ctype') == '1' || $(this).attr('ctype') == '2' || $(this).attr('ctype') == '6' || $(this).attr('ctype') == '7' || $(this).attr('ctype') == '9' || $(this).attr('ctype') == '10')
            if (boolIEBrowser)
                slPlayMedia_Silverlight($(this).attr('callId'));
            else
                slPlayMedia($(this).attr('callId'));
    });

    var selectedPlObj;
    $('td.editPL').live("click", function () {
        //JL(frontendLogger).info($(this).next().find('div').html());
        if ($('#editboxPlComm').length == 0) {
            selectedPlObj = $(this);
            var currentValue = $(this).text();
            var newValue = currentValue;

            $(this).html("<input id='editboxPlComm' type='text' value='" + currentValue + "' size='" + editCommentsLength + "' />");
            $('#editboxPlComm').focus();
        }
    });
    $('td.editPL').live("keydown", function (event) {
        if (event.which == 13) {
            event.preventDefault(); //event.stopPropragation();
            var callId = $(this).parent().attr('callid');
            var txtval = $('#editboxPlComm').val();
            if (txtval != '' && callId.length) {
                if (isECEnabled) {
                    var recCallIds = [];
                    recCallIds.push({ 'RecId': $(this).parent().attr('recid'), 'CallId': $(this).parent().attr('callid') });
                    updateCallCustomFields(recCallIds, txtval, 'colCallComments');
                }
                else
                    updatePlRowContents("'" + callId + "'", txtval);

                var newValue = $('#editboxPlComm').val();
                selectedPlObj.html('');
                selectedPlObj.text(newValue);
            }
        }
    });
    $('#editboxPlComm').live('blur', function () {
        var newValue = $('#editboxPlComm').val();
        selectedPlObj.html('');
        selectedPlObj.text(newValue);
    });

    $('a[id="btnEvaluateCall"]').live('click', function () {
        ////var $curRow = $(this).closest('tr'),$newRow = $curRow.clone(true);
        //var $curRow = $(this).parent().parent().parent(), $callId = $curRow.attr('id');
        //var $userNum = $curRow.find("input[id=hdnAppUserId]").val();
        //var $recId = $curRow.attr('recid');
        //filterSurveyDropdown($recId);
        //initializeSurvey();
        //$('#divSelectSurvey').attr('cID', $callId).attr('uID', $userNum).attr('recId', $recId).dialog("open");
    });

    $("#lnkSlideDown").live('click', function () {
        if ($("#pl-item-md").length)
            $('.jsPanel-btn-close', '#pl-item-md').trigger('click');

        if (!boolIEBrowser) {
            nState = FilePlayerState.Stop;
            LoopingFalse();
        }

        $('#hdnActiveTab').val('Search');
        //changeLayout('searchMode');
        displaylink();
        adjustWindow();
        //logging
        var msgData = loginUserName;
        var params = '';
        logActivity(loginUserId, 53, msgData, params);
    });

    $('#txtPlSearch').keyup(function () {
        searchPlListviewTable($(this).val());
    });

    $("#btnMapPlaylist").live('click', function () {
        //$('#divTimeline').hide();
        makePlMapDialog();
        var plId = $("#ulPlaylists > li.active").attr("id");
        loadPlMapCtrl(plId);
        //mapPlaylist();
    });

    $("#divPlaylist").keydown(function (event) {
        if (event.keyCode == 13) {
            $(this).parent().find("button:eq(0)").trigger("click");
            return false;
        }
    });

    $("#ulUploadedFiles > li > img").live("click", function (e) {
        deleteFile($("#ulPlaylists > li.active").attr("id"), $('#header-login-name').attr('uid'), $(this).attr('dname'), $(this).attr('fname'));
    });

    $("#ulPlaylistAddFiles > li > img").live("click", function (e) {
        deleteFile_addfiles($("#ulPlaylists > li.active").attr("id"), $('#header-login-name').attr('uid'), $(this).attr('dname'), $(this).attr('fname'));
    });


    $("#tblCalls tbody tr td span.callType6").live("click", function (e) {
        if ($(this).closest('tr').find('td:first-child').hasClass('playCol')) {
            reOrderzIndex('searchVideoView');
            slPlayMedia($(this).closest('tr').attr('id'));
        }
    });

    $("#lnkTranscribe").live('click', function () {
        if (isSTTLicensed == '' || isSTTLicensed == false || isSTTLicensed == 'false' || isSTTLicensed == 'False') {
            showErrorDialog('Your STT license has expired. Please contact support.');
            return;
        }
        var $tr = $(this).closest('tr');

        var callId = $tr.attr('id');
        var fileName = $tr.attr('fName');
        var startTime = $tr.attr('stTime');
        var durInSec = $tr.attr('cduration');

        showTranscriptionConfirmation(callId, fileName, startTime, durInSec);
    });
    $(".read-full-transcript").live("click", function (e) {
        showTranscriptionDialog(langKeys[currentLanguage]['msgDlgTitleTranscript'], $(this).siblings(".full-transcript").text(), $(this).closest('tr').attr('id'), $(this).attr("transcriptid"), $(this).attr("transcriptconfidence"));
    });

    $("#lnkTimeline").live('click', function () {
        ///Added by ifuturz on 20161229
        if ($('#hdnActiveTab').val() != 'Timeline') {
            $('#hdnActiveTab').val('Timeline');
            displaylink();
        }
        //logging
        var msgData = loginUserName;
        var params = '{Id:' + $('#cbxLoadPlayList :selected').val() + ',Name:' + $('#cbxLoadPlayList').find(":selected").text() + '}';
        logActivity(loginUserId, 52, msgData, params);
    });

    $('#btnAddManualFile').live('click', function () {
        showExportDialog();
    });

    $(".bm-details").live("click", function (e) {
        //JL(frontendLogger).info($(this).closest('tr').attr('id'));
        var $tr = $(this).closest('tr');
        showBookmarkDialog($tr);
    });

    //ADDED BY KM - IFUTURZ FOR BOOKMARK DIALOG IN LISTVIEW - START
    $(".bm-details-Listview").live("click", function (e) {
        var $tr = $(this).closest('tr');
        showBookmarkDialogListView($tr);
    });
    //ADDED BY KM - IFUTURZ FOR BOOKMARK DIALOG IN LISTVIEW - END

    $(".read-full-comments").live("click", function (e) {
        var $tr = $(this).closest('tr');
        showCommentsDialog($tr);
    });

    $("#lnkVitalMedications").live("click", function (e) {
        debugger
        var medNames = $(this).attr('med-names');
        var medImages = $(this).attr('med-images');
        var date = $(this).attr('date');
        var medImgPath = inquireURL + date + '/';
        var arrMedNames = $(this).attr('med-names').split(',');
        var arrMedImages = $(this).attr('med-images').split(',');

        var medlist = '<ul class="drug-list"><li class="ui-menu-item">' + arrMedNames.join('</li><li>') + '</li></ul>';

        var $html = '<div style="margin-top: 10px;">' +
                                '<div class="med-items"><p>' + medlist + '</p></div>' +
                                '<div id="divMedImgNames" class="med-images"></div>' +
                            '</div>';
        showInfoDialog('List of Medications', $html);

        var $div = $('#divMedImgNames');
        $.each(arrMedImages, function (i, val) {
            if (val != "")
                $('<img />').attr({ 'src': medImgPath + val, 'alt': val, 'title': val, 'width': 150, 'height': 100, 'class': 'app-Img' }).appendTo($div);
        });
    });

    $("#lnkVitalDrugs").live("click", function (e) {
        //showInfoDialog('List of Drugs', 'Load all Drug images...');
        var drugsImgPath = rxUploadsRootPath + 'Drugs/';
        var names = $(this).attr('drug-names');
        var images = $(this).attr('drug-images');
        var arrNames = $(this).attr('drug-names').split(',');
        var arrImages = $(this).attr('drug-images').split(',');

        lst = '';
        if (names.length) {
            lst = '<ul class="drug-list"><li class="ui-menu-item">' + arrNames.join('</li><li>') + '</li></ul>';
        }
        var $html = '<div style="margin-top: 10px;">' +
                                '<div class="med-items">' + lst + '</div>' +
                                '<div id="divDrugImgNames" class="drug-images"></div>' +
                            '</div>';
        showInfoDialog('Drug Details', $html);
        if (images.length) {
            var $div = $('#divDrugImgNames');
            $.each(arrImages, function (i, v) {
                $('<img />').attr({ 'src': drugsImgPath + v, 'alt': v, 'title': v, 'width': 150, 'height': 100, 'class': 'app-Img' }).appendTo($div);
            });
        }
    });

    $("#lnkVitalEEG").live("click", function (e) {
        //var eegImgPath = rxUploadsRootPath + 'EEG/';

        var names = $(this).attr('eeg-names');
        var images = $(this).attr('eeg-images');
        var arrNames = $(this).attr('eeg-names').split(',');
        var arrImages = $(this).attr('eeg-images').split(',');
        var date = $(this).attr('date');

        var eegImgPath = inquireURL + date + '/';

        lst = '';
        if (names.length) {
            lst = '<ul class="eeg-list"><li class="ui-menu-item">' + arrNames.join('</li><li>') + '</li></ul>';
        }
        var $html = '<div style="margin-top: 10px;">' +
                                '<div class="med-items">' + lst + '</div>' +
                                '<div id="divEEGImgNames" class="eeg-images"></div>' +
                            '</div>';
        showInfoDialog('EEG', $html);
        if (images.length) {
            var $div = $('#divEEGImgNames');
            $.each(arrImages, function (i, v) {
                $('<img />').attr({ 'src': eegImgPath + v, 'alt': v, 'title': v, 'width': 150, 'height': 100, 'class': 'app-Img' }).appendTo($div);
            });
        }
    });

});

function adjustWindow() {
    $(".bDiv").css('height', $('#divCallSearchResults').parent().height() - 60);
    $(".nDiv").css('height', $(".bDiv").height());
    $('.footer.row').css('height', $(window).height() - $('#divTimeline').height() + $('#divPlSearch').height() - 100);
}

function checkUncheckAll() {
    var totalCheckboxes = $("#tblCalls input:checkbox.tblrowChk").size();
    var checkedCheckboxes = $("#tblCalls input.tblrowChk:checkbox:checked").size();
    if (totalCheckboxes == checkedCheckboxes) {
        $("#tblCalls input[id='chkAllRows']:checkbox").attr('checked', true);
    }
    else {
        $("#tblCalls input[id='chkAllRows']:checkbox").attr('checked', false);
    }
}

function makeGroupsTree() {
    $('#tree7').checkboxTree({
        collapseImage: '../assets/icons/common/downArrow.gif',
        expandImage: '../assets/icons/common/rightArrow_white.gif',
        blankarrow: "../assets/icons/common/blank.png",
        onCheck: {
            ancestors: 'checkIfFull',
            descendants: 'check'
        },
        onUncheck: {
            ancestors: 'uncheck'
        }
    });
    $('#tree7').checkboxTree('collapse', $("li").find("[catgroup='Screens']"));
}

//#region User Searches
function populateUserSearchesDDL(searchType) {
    var ddl = document.getElementById('ddlUserSearches');
    ddl.insertBefore(new Option(searchType + " - " + formateDate(new Date()), "Option" + ddl.options.length + 1), ddl.options[1]);
}

function formateDate(today) {
    var dd = today.getDate();
    var mm = today.getMonth() + 1;
    var hh = today.getHours();
    var min = today.getMinutes();
    var sec = today.getSeconds();
    var yyyy = today.getFullYear();

    if (dd < 10)
        dd = '0' + dd;
    if (mm < 10)
        mm = '0' + mm;
    if (hh < 10)
        hh = '0' + hh;
    if (min < 10)
        min = '0' + min;
    if (sec < 10)
        sec = '0' + sec;
    return yyyy + '-' + mm + '-' + dd + " " + hh + ":" + min + ":" + sec;
}
//#endregion User Searches

//#region View Type

function changeLayout(mode) {
    //JL(frontendLogger).info(mode);
    switch (mode) {
        case "searchMode": //
            $('.left.col').css('display', 'block');
            $('.right.col').css('left', '220px');
            $('#divSearch').css('top', '80px');
            $('#divTopNav').css('display', 'block');

            $('.body.row').css('top', '165px');
            $('#divSearch').css('height', '85px');
            $('#divPlSearch').css('display', 'none');
            $('#divCallSearch').css('display', 'block');
            $('#divCallSearchResults').css('display', 'block');
            $("#divMainPlayerContainer").css('height', '100%');
            //searchVideoPlayerVisible('block');
            break;
        case "playlistMode": //
            $('.left.col').css('display', 'none');
            $('.right.col').css('left', '0px');
            $('#divSearch').css('top', '0px');
            $('#divTopNav').css('display', 'none');

            //$('.footer.row').css('height', '89%');
            $('.footer.row').css('height', $(window).height() - $('#divTimeline').height() + $('#divPlSearch').height() - 100);

            $('.body.row').css('top', '45px');
            $('#divSearch').css('height', '45px');
            $('#divPlSearch').css('display', 'block'); //PL Search Bar
            $('#divCallSearch').css('display', 'none'); //Call Search Params
            $('#divCallSearchResults').css('display', 'none'); //Calls Search Results
            //searchVideoPlayerVisible('none');
            break;
    }
}
var items_playlist_count = 0;
// Add Milliseconds to StartTime..

function getEndDate(start, ms) {
    d = new Date(start.replace(/-/g, "/"));

    end_date = new Date(d.setMilliseconds(ms));
    return vis.moment(end_date).format('YYYY-MM-DD HH:mm:ss');
}

// sort array function..

function sortByDate(a, b) {
    if (a.startTime < b.startTime)
        return -1;
    if (a.startTime > b.startTime)
        return 1;
    return 0;
}
var bIsZeroItem = true; //arivu
var bIsAddUserFileDialog = false;//arivu
var userAddedFile = "";
var bIsHasFile = false;
var bIsTLLoadedInAUF = false;
var bIsDestroyTLInAUF = false;
var oldplaylistid = 0;
var bNotSavedAUF = false;
//var pl, plA;
function displaylink() {
    try {

        if (boolIEBrowser) {

            var hfVal = $('#hdnActiveTab').val();
            var playlistId = $("#ulPlaylists > li.active").attr("id")
            var userId = $('#header-login-name').attr('uid');

            if (hfVal == "Search") {
                swapVideoPlayerContents('Search');
                DeSelectHeaderSearch();

                $('.body.row').css('z-index', '2'); // swap z-index

                $("#ulPlaylists > li").removeClass('active');

                $('#divTimeline').css('display', 'none');
                $('#divListview').css('display', 'none');

                changeLayout('searchMode');

                slReset_Silverlight();
                slDisableTimeline_Silverlight();
            }

            if (hfVal == "Timeline") {
                //swapVideoPlayerContents('Listview');
                $('#divTimeline').css('display', 'block'); //PL Timeline View
                $('#divListview').css('display', 'none'); //PL Listview

                slReset_Silverlight();
                //slEnableTimeline();

                //slCtl.Content.JS2SL.SetPlaylistParameters(helperServiceIp, userId, playlistId, true);
                slCtl.Content.JS2SL.SetPlaylistParameters(helperServiceIp, userId, playlistId, true, nDiffMinutes);

                $('.body.row').css('z-index', '1'); // swap z-index

                var msgURI = '';
                $("#tblPlaylist > tbody > tr").each(function () {
                    msgURI = '';

                    var recid = $(this).attr('recid');
                    var callId = $(this).attr('callId');
                    var flName = $(this).attr('fName');
                    var startTime = $(this).attr('stTime');
                    var fileExt = flName.substr(flName.lastIndexOf('.') + 1); //flName.split('.')[1];
                    var durInSec = $(this).attr('cDuration'); //convertTimeToSeconds($(this).attr('cDuration'));
                    var cType = $(this).attr('ctype');
                    var chNum = $(this).attr('chNum');
                    var chName = $(this).attr('chName');
                    var screenName = $(this).attr('sName').split(',')[0];;
                    var pages = $(this).attr('pages');
                    var isUserItem = $(this).attr('isuseritem');
                    switch (cType) {
                        case "1": //Audio
                            var vod = '';
                            var vid = '';
                            //JL(frontendLogger).info(callId + '-' + vod);
                            switch (fileExt.toLowerCase()) {
                                case "dsf":
                                    vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                    if (vod)
                                        slAddMedia_Silverlight(callId, vod);
                                    break;
                                case "mp3":
                                    if (isUserItem)
                                        vod = createDocumentURI_Silverlight(5, startTime, flName, durInSec);
                                    else
                                        vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);
                                    slAddMedia_Silverlight(callId, vod);
                                    break;
                                case "wav":
                                    vod = createDocumentURI(1, startTime, flName, durInSec, pages);
                                    slAddMedia_Silverlight(callId, vod);
                                    break;
                            }
                            break;
                        case "2": //Video
                            if (isUserItem == 'true')
                                vid = createDocumentURI_Silverlight(3, startTime, flName, durInSec, 0);
                            else
                                vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                            //JL(frontendLogger).info(callId + vid);
                            slAddMedia_Silverlight(callId, vid);
                            break;
                        case "3": //SMS

                            var mbody = $(this).find('td:nth-child(3) img').attr('msgBody');
                            if (mbody == undefined)
                                mbody = $(this).find('td:nth-child(4) img').attr('msgBody');
                            msgURI = createMessageURI_Silverlight(flName, 6, startTime, mbody, chNum, chName);
                            //JL(frontendLogger).info('msgURI-SMS:-' + callId + msgURI);
                            slAddMedia_Silverlight(callId, msgURI);
                            break;
                        case "4": //Social
                            var mbody = $(this).find('td:nth-child(3) img').attr('msgBody');
                            if (mbody == undefined)
                                mbody = $(this).find('td:nth-child(4) img').attr('msgBody');
                            msgURI = createMessageURI_Silverlight(flName, 8, startTime, mbody, chNum, chName);
                            //JL(frontendLogger).info(callId + msgURI);
                            slAddMedia_Silverlight(callId, msgURI);
                            break;
                        case "5": //Email
                            var mbody = $(this).find('td:nth-child(3) img').attr('msgBody');
                            if (mbody == undefined)
                                mbody = $(this).find('td:nth-child(4) img').attr('msgBody');
                            msgURI = createMessageURI_Silverlight(flName, 7, startTime, mbody, chNum, chName);
                            //JL(frontendLogger).info(callId + msgURI);
                            slAddMedia_Silverlight(callId, msgURI);
                            break;
                        case "6": //Screens
                            if (flName)
                                vid = createScreenURI(recid, 0, startTime, flName, durInSec, screenName, chNum, chName);
                            else
                                vid = createScreenURIWithoutDSF(recid, 3, startTime, durInSec, screenName, chNum, chName);
                            //JL(frontendLogger).info(vid);
                            slAddMedia_Silverlight(callId, vid);
                            break;
                        case "7": //Inquire

                            var gps = $(this).attr('interview_GPS');
                            var interview_DateTime = $(this).attr('interview_DateTime');
                            var interview_Interviewer = $(this).attr('interview_Interviewer');
                            var interview_Interviewee = $(this).attr('interview_Interviewee');
                            var interview_InterviewId = $(this).attr('interview_InterviewId');
                            var interview_Notes = $(this).attr('interview_Notes');

                            var interview_details = interview_InterviewId + ";" + gps + ";" + interview_DateTime + ";" + interview_Interviewer + ";" + interview_Interviewee + ";" + interview_Notes;
                            var bm = $(this).find("input[id=hdnCallBookmarks]").val();
                            var bookmark_inquire = "<inquire>" + bm + "</inquire>";

                            vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName);
                            // vid = createInquireURI(recid, 0, startTime, flName, durInSec, screenName, chNum, chName);
                            //JL(frontendLogger).info(vid);

                            //  slAddMedia(callId, vid);
                            var fileExt = flName.split('.');
                            var ftype = "";
                            if (fileExt[1] == "mp3" || fileExt[1] == "m4a" || fileExt[1] == "wav") {
                                ftype = "audio";
                            }
                            else
                                ftype = "video";
                            slAddInquireMedia(callId, vid, bookmark_inquire, interview_details, ftype);
                            break;
                        case "9": //User Added Image
                            vod = createDocumentURI_Silverlight(9, startTime, flName, durInSec, pages);
                            //JL(frontendLogger).info(vod);
                            slAddMedia_Silverlight(callId, vod);
                            break;
                        case "10": //User Added Document
                            //$("#divMainPlayerContainer").css('height', $('#divMainPlayerContainer').height() - $('#divTimeline').height() + 45);
                            vod = createDocumentURI_Silverlight(10, startTime, flName, durInSec, pages);
                            //JL(frontendLogger).info(vod);
                            slAddMedia_Silverlight(callId, vod);
                            break;
                        default:
                            $.unblockUI();
                            showErrorDialog("Invalid Call Type: " + cType);
                            break;
                    }

                });
                if (slHaveVideo() == false) {
                    $('#divTimeline').slideUp(5000);
                    $('#divLvVideoPlayer').slideUp(5000);
                    $('#divListViewTable').stop().animate({ top: 0 }, 5000);

                    //ADDED BY KM - IFUTURZ TO RESOLVE TIMELINE VIEW TIMELINE CHART GOES BEYOND VIDEO PANEL IN IE SILVERLIGHT FALLBACK. - START.
                    $('#silverlightControlHost').css({ 'top': '6%' });
                    $('#slvrObject').css({ 'height': '94%' });
                    //ADDED BY KM - IFUTURZ TO RESOLVE TIMELINE VIEW TIMELINE CHART GOES BEYOND VIDEO PANEL IN IE SILVERLIGHT FALLBACK. - END.
                }
                else {
                    $('#divTimeline').slideDown(3000);
                    $('#divLvVideoPlayer').slideDown(3000);
                    $('#divListViewTable').stop().animate({ top: 282 }, 3000);

                    //ADDED BY KM - IFUTURZ TO RESOLVE TIMELINE VIEW TIMELINE CHART GOES BEYOND VIDEO PANEL IN IE SILVERLIGHT FALLBACK. - START.
                    $('#silverlightControlHost').css({ 'top': '50%' });
                    $('#slvrObject').css({ 'height': '50%' });
                    //ADDED BY KM - IFUTURZ TO RESOLVE TIMELINE VIEW TIMELINE CHART GOES BEYOND VIDEO PANEL IN IE SILVERLIGHT FALLBACK. - END.
                }
                $('#txtPlSearch').val('').hide();
            }

            if (hfVal == "Listview") {
                swapVideoPlayerContents('Listview');
                //disableQuadView();
                $('#divLvVideoPlayer').show();

                $('#divTimeline').css('display', 'none');
                $('#divListview').css('display', 'block');

                //$("lnkListview").removeClass("inActiveplLV").addClass("activeplLV");

                slReset_Silverlight();
                //slDisableTimeline();
                slCtl.Content.JS2SL.SetPlaylistParameters(helperServiceIp, userId, playlistId, false, nDiffMinutes);
                $('.body.row').css('z-index', '2'); // swap z-index
                var recid = '';
                var cType = '';
                var callId = '';
                var flName = '';
                var startTime = '';
                var fileExt = '';
                var durInSec = '';
                var durInMilliSec = '';
                var cType = '';
                var chNum = '';
                var chName = '';
                var screenName = '';
                var bookmark = '';
                var vod = '';
                var vid = '';
                $("#tblPlaylist > tbody > tr").each(function () {

                    recid = $(this).attr('recid');
                    cType = $(this).attr('ctype');
                    callId = $(this).attr('callId');
                    flName = $(this).attr('fName');
                    startTime = $(this).attr('stTime');
                    fileExt = flName.substr(flName.lastIndexOf('.') + 1); // flName.split('.')[1];
                    durInSec = $(this).attr('cDuration'); // convertTimeToSeconds($(this).attr('cDuration'));
                    // durInMilliSec = $(this).attr('cdDuration');
                    //  durInSec = durInMilliSec / 1000;
                    chNum = $(this).attr('chNum');
                    chName = $(this).attr('chName');
                    screenName = $(this).attr('sName').split(',')[0];
                    bookmark = $(this).find('input[id=hdnBookmarkXml]').val();
                    var pages = $(this).attr('pages');
                    var isUserItem = $(this).attr('isuseritem');
                    //JL(frontendLogger).info(fileExt);
                    if (cType == "1" || cType == "13") {
                        //JL(frontendLogger).info(callId + '-' + vod);
                        switch (fileExt.toLowerCase()) {
                            case "dsf":
                                vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                if (vod)
                                    slAddMedia_Silverlight(callId, vod);
                                break;
                            case "mp3":
                                if (isUserItem)
                                    vod = createDocumentURI(5, startTime, flName, durInSec);
                                else
                                    vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);
                                slAddMedia_Silverlight(callId, vod);
                                break;
                            case "wav":
                                vod = createDocumentURI(1, startTime, flName, durInSec, pages);
                                slAddMedia_Silverlight(callId, vod);
                                break;
                        }
                    }
                    else if (cType == "2") {
                        //vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                        if (isUserItem == 'true')
                            vid = createDocumentURI(3, startTime, flName, durInSec, 0);
                        else
                            vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                        slAddMedia_Silverlight(callId, vid);
                    }
                    else if (cType == "6") {
                        if (flName)
                            vid = createScreenURI(recid, 0, startTime, flName, durInSec, screenName, chNum, chName);
                        else
                            vid = createScreenURIWithoutDSF(recid, 3, startTime, durInSec, screenName, chNum, chName);
                        slAddMedia_Silverlight(callId, vid);
                    }
                    else if (cType == "7") {
                        var fileExt = flName.split('.');
                        var ftype = "";
                        vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName);
                        var bookmark_inquire = "<inquire></inquire>";
                        if (fileExt[1] == "mp3" || fileExt[1] == "m4a" || fileExt[1] == "wav") ftype = "audio";
                        else ftype = "video";
                        slAddInquireMedia(callId, vid, bookmark_inquire, null, ftype);
                    }
                    else if (cType == "9") { //User Added Image
                        vod = createDocumentURI(9, startTime, flName, durInSec, pages);
                        slAddMedia_Silverlight(callId, vod);
                    }
                    else if (cType == "10") { //User Added Document
                        vod = createDocumentURI(10, startTime, flName, durInSec, pages);
                        slAddMedia_Silverlight(callId, vod);
                    }
                });

                $('#tblPlaylist > tbody > tr').show(); // $('#tblPlaylist > tbody > tr').attr("style", "display: inline;");
                $('#txtPlSearch').val('').show();

            }
        } else {
            if (!boolIEBrowser) ClearBookmarksInPlayer(); // ADDED BY KM IFUTURZ

            SetFilePlayerTitlesLocalization(); // ADDED BY KM IFUTURZ

            var hfVal = $('#hdnActiveTab').val();
            var playlistId = $("#ulPlaylists > li.active").attr("id")
            var userId = $('#header-login-name').attr('uid');

            if (hfVal == "Search") {
                $('input[id=chkAllCalls]').attr('checked', false); // ADDED BY KM IFUTURZ TO UNCHECK CHECKBOX FOR ALL CALLS AFTER NEVIGATE

                // KM for HTML5
                Reset();
                currentloadedscreen = 'search';
                deleteallmedia();
                containsvideo = false;
                Reset();
                //vplayer = document.getElementById("videofileplayer");

                $('#idvideolist_listview').html('');
                swapVideoPlayerContents('Search');
                SetBuffering(false);
                current_screencallid = "";
                screenvideo_obj = null;
                is_multiblobplay = false;

                $('#videofileplayer1').css({ 'width': '0px', 'height': '0px' });
                $('.body.row').css('z-index', '2'); // swap z-index
                $("#ulPlaylists > li").removeClass('active');
                $('#divTimeline').css('display', 'none');
                $('#divListview').css('display', 'none');

                changeLayout('searchMode');
                $('#divtimelineviewdesign').hide();
                if (!bIsZeroItem) {
                    try {

                        if (pl != null) {
                            if (pl.timeline) {
                                pl.timeline.destroy();
                                pl = null
                            }
                        }

                        if (plAuserAdded.timeline) {
                            plAuserAdded.timeline.destroy();
                            plAuserAdded = null
                        }
                    } catch (e) { }
                }

                bIsAddUserFileDialog = false;
                // $('#hdnActiveForm').val('');
                slReset();
                slDisableTimeline();
                ///Added by ifuturz on 20161223
                pbw = ProgressEx.width();
                slUpperSlider.css('left', pbw);
                ///End by ifuturz on 20161223
            }

            if (hfVal == "Timeline") {
                sequencer.SetPosition(0); // ADDED BY KM IFUTURZ TO RESOLVE PLAYBACK AND POSITION AFTER SWITCHING FROM LISTVIEW TO TIMELINE VIEW.
                $("#divuseraddeditem").html(""); // ADDED BY KM IFUTURZ TO RESOLVE UAI TIMELINE VIEW NEW PLAYLIST ISSUE [SOCIAL ITEMS UI].
                removeContainersElementsTL(); // ADDED BY KM IFUTURZ TO REMOVE ALL VIDEO CONTAINERS CONTAINS 
                $('#divtimelineviewdesign').val('');
				
				reOrderzIndex("searchNonVideoView");
				
                if (bIsTLLoadedInAUF) {

                    try {
                        if (plA != null) { // Added by ifuturz pageSize === "undefined"
                            if (plA.timeline) {
                                plA.timeline.destroy();
                                plA = null
                            }
                        }
                        if (pl != null) {
                            if (pl.timeline) {
                                //this code will destroy timline, later we can recreate a new timeline
                                pl.timeline.destroy();
                                pl = null
                            }
                        }

                        if (plAuserAdded != null) {
                            if (plAuserAdded.timeline) {
                                plAuserAdded.timeline.destroy();
                                plAuserAdded = null
                            }
                        }
                    } catch (e) {

                    }

                    ///Added by ifututrz to solve on switch between playlist from dropdown
                    sequencer.setLoaded(false);
                    ///End by ifututrz to solve on switch between playlist from dropdown
                    bIsDestroyTLInAUF = false;
                }
                else if (!bIsZeroItem) {
                    if (currentloadedscreen == 'Timeline') {

                        try {
                            if (pl != null) { // Added by ifuturz
                                if (pl.timeline) {
                                    //this code will destroy timline, later we can recreate a new timeline
                                    pl.timeline.destroy();
                                    pl = null;
                                }
                            }
                            if (plA != null) { // Added by ifuturz
                                if (plA.timeline) {
                                    plA.timeline.destroy();
                                    plA = null;
                                }
                            }
                            if (plAuserAdded != null) {
                                if (plAuserAdded.timeline) {
                                    plAuserAdded.timeline.destroy();
                                    plAuserAdded = null
                                }
                            }
                        } catch (e) {

                        }

                    }
                    bIsDestroyTLInAUF = false;
                }
                else {
                    if (bIsDestroyTLInAUF) {
                        try {
                            if (plA != null) { // Added by ifuturz
                                if (plA.timeline) {
                                    plA.timeline.destroy();
                                    plA = null;
                                }
                            }
                        } catch (e) {

                        }

                        bIsDestroyTLInAUF = false;
                    }
                }

                totalTimelineDuration = 0;

                //swapVideoPlayerContents('Listview');
                $('#divTimeline').css('display', 'block'); //PL Timeline View
                $('#divListview').css('display', 'none'); //PL Listview

                currentloadedscreen = 'Timeline';
                //vplayer = document.getElementById("videofileplayer");
                deleteallmedia();
                SetBuffering(false);
                Reset();
                slReset();
                $('#videofileplayer1').css({ 'width': '0px', 'height': '0px' });

                current_screencallid = "";
                screenvideo_obj = null;
                is_multiblobplay = false;
                
                $('.body.row').css('z-index', '1'); // swap z-index

                var items = []; // timeline
                var groups = []; // timeline
                var temp = [];  // used to sort array based on starttime
                var sortedPlaylist = []; // store sorted array.
                var msgURI = '';
                var items_playlist_count = 0;
                var firstFile = true;
                ///Added by ifuturz for userAdded item
                var items1 = []; // timeline
                var groups1 = []; // timeline

                $("#tblPlaylist > tbody > tr").each(function () {
                    bIsZeroItem = false;
                    msgURI = '';

                    var recid1 = $(this).attr('recid');
                    var callId1 = $(this).attr('callId');
                    var flName1 = $(this).attr('fName');
                    var startTime1 = $(this).attr('stTime');
                    var fileExt1 = flName1.substr(flName1.lastIndexOf('.') + 1); //flName.split('.')[1];
                    var durInSec1 = $(this).attr('cDuration'); //convertTimeToSeconds($(this).attr('cDuration'));
                    // var durInMilliSec = $(this).attr('cdDuration');
                    //var durInSec = durInMilliSec / 1000;
                    var cType1 = $(this).attr('ctype');
                    var chNum1 = $(this).attr('chNum');
                    var chName1 = $(this).attr('chName');
                    //var screenName1 = $(this).attr('sName').split(',')[0]; /// Commeted by ifuturz
                    var screenName1 = $(this).attr('sName').split(',')[0]; ///Added by ifututrz on 20161230 to get videos from string after split
                    var pages1 = $(this).attr('pages');
                    var isUserItem1 = $(this).attr('isuseritem');
                    isUserItemIcon = $(this).attr('isuseritem');
                    var msgBody1 = $(this).attr('msgBody');  //// Added by ifuturz for Message,Email and Social
                    if (firstFile) {
                        if ($('#txtDateAddUserFile').val().length == 0) // ADDED BY KM IFUTURZ TO RESOLVE EDITED DATE TIME CHNAGE ON FILE SELECT IN TIMELINE UAI VIEW
                            sendStartTime(startTime1);
                    }
                    firstFile = false;
                    if (flName1 == "") {
                        flName1 = screenName1;
                    }
                    if (isUserItem1 && flName1 == "") {
                        flName1 = "User Added Item";
                        fileExt1 = "uai";
                    }
                    ////End by ifuturz on 20161230 for Video file on timeline
                    temp.push({
                        'recid': recid1,
                        'CallId': callId1,
                        'flName': flName1,
                        'startTime': startTime1,
                        'fileExt': fileExt1,
                        'durInSec': durInSec1,
                        'cType': cType1,
                        'chNum': chNum1,
                        'chName': chName1,
                        'screenName': screenName1,
                        'pages': pages1,
                        'isUserItem': isUserItem1,
                        'msgBody': msgBody1          //// Added by ifuturz for Message,Email and Social
                    });
                    items_playlist_count++;
                });
                items_playlist_count = 0;

                sortedPlaylist = temp.sort(sortByDate);
                if (sortedPlaylist.length == 0) {
                    bIsZeroItem = true;
                    //var starttime = "";
                    //sendStartTime(null);
                }
                var bIsCurrentUserAddedFile = bIsHasFile;
                if (bIsAddUserFileDialog) {
                    if (bIsCurrentUserAddedFile) {

                        var dt = new Date();
                        var callId = "";
                        var flName = userAddedFile;
                        var extInfo = flName.split('.');
                        var startTime = vis.moment(dt).format('YYYY-MM-DD HH:mm:ss');//dt.getFullYear() + "0" + dt.getMonth() + "0" + dt.getDay() + dt.getHours() + dt.getMinutes() + dt.getSeconds();
                        var fileExt = extInfo[extInfo.length - 1].toLowerCase();
                        var durInSec = 60;

                        var datea = startTime;//startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                        var datea_Stoptime = getEndDate(datea, durInSec * 1000);

                        iconCss1 = setTimelineIcon(cType, fileExt);
                        // items.push({ x: items_playlist_count, id: items_playlist_count, content: '<img class="item_image" src="https://cdn2.iconfinder.com/data/icons/circle-icons-1/64/speaker-16.png"> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName });//Commented by Arivu
                        items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName, callId: callId, flName: flName });
                        ////Commented by ifuturz
                        //groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='https://cdn3.iconfinder.com/data/icons/70-basic-icons/100/close-32.png' /></a>" });
                        ////Commented by ifuturz
                        //Added by ifuturz
                        groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                        //Added by ifututrz
                        bNotSavedAUF = true;
                        items_playlist_count++;

                        if (bIsZeroItem) { // check this condition. what is zeroitem here.

                            plA = new PlayerList();
                            plA.seteventua(false);
                            plA.loadItems(items, groups, true);
                            plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth
                            bIsTLLoadedInAUF = true;
                            bIsAddUserFileDialog = true;
                            bNotSavedAUF = true;
                        }
                    }

                    for (i = 0; i < sortedPlaylist.length; i++) {

                        msgURI = '';

                        var recid = sortedPlaylist[i].recid;
                        var callId = sortedPlaylist[i].CallId;
                        var flName = sortedPlaylist[i].flName;
                        var startTime = sortedPlaylist[i].startTime;
                        var fileExt = sortedPlaylist[i].fileExt;
                        var durInSec = sortedPlaylist[i].durInSec;
                        var cType = sortedPlaylist[i].cType;
                        var chNum = sortedPlaylist[i].chNum;
                        var chName = sortedPlaylist[i].chName;
                        var screenName = sortedPlaylist[i].screenName;
                        var pages = sortedPlaylist[i].pages;
                        var isUserItem = sortedPlaylist[i].isUserItem;
                        isUserItemIcon = sortedPlaylist[i].isUserItem;
                        var msgBody = sortedPlaylist[i].msgBody;        //// Added by ifuturz for Message,Email and Social
                        var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                        var datea_Stoptime = getEndDate(datea, durInSec * 1000);

                        ////Added by ifuturz on 20161228
                        if (isUserItem && flName == "User Added Item") {
                            flName = "User Added Item";
                            fileExt = "uai";
                        }
                        tooltip = "";
                        tooltip = '<div>';
                        tooltip += 'Channel : ' + flName + '\n';
                        tooltip += 'Date : ' + datea + '\n';
                        tooltip += 'Time : ' + datea.split(' ')[1] + '\n';
                        tooltip += 'Duration : ' + timeFormate(durInSec) + '\n';
                        tooltip += '</div>';
                        ht = $($.parseHTML(tooltip));

                        iconCss1 = setTimelineIcon(cType, fileExt);
                        items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-evt-id="' + callId + '" class="' + iconCss1 + '" ></span> ' + '<span style="margin-left: 3px;">' + flName + ' | ' + datea + '<span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: ht.html(), callId: callId, flName: flName });
                        groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                        items_playlist_count++;
                    }
                }
                else {
                    for (i = 0; i < sortedPlaylist.length; i++) {
                        msgURI = '';
                        //debugger;
                        var recid = sortedPlaylist[i].recid;
                        var callId = sortedPlaylist[i].CallId;
                        var flName = sortedPlaylist[i].flName;
                        var startTime = sortedPlaylist[i].startTime;
                        var fileExt = sortedPlaylist[i].fileExt;
                        var durInSec = sortedPlaylist[i].durInSec;
                        var cType = sortedPlaylist[i].cType;
                        var chNum = sortedPlaylist[i].chNum;
                        var chName = sortedPlaylist[i].chName;
                        var screenName = sortedPlaylist[i].screenName;
                        var pages = sortedPlaylist[i].pages;
                        var isUserItem = sortedPlaylist[i].isUserItem;
                        isUserItemIcon = sortedPlaylist[i].isUserItem;
                        var msgBody = sortedPlaylist[i].msgBody;        //// Added by ifuturz for Message,Email and Social
                        var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                        var datea_Stoptime = getEndDate(datea, durInSec * 1000);

                        // this image url might nort available that is why this  issue occurs. you can use come images from the assets directory..

                        // items.push({ x: items_playlist_count, id: items_playlist_count, content: '<img class="item_image" src="https://cdn2.iconfinder.com/data/icons/circle-icons-1/64/speaker-16.png"> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName });//Commented by Arivu
                        ////Added by ifuturz on 20161228
                        if (isUserItem && flName == "User Added Item") {
                            flName = "User Added Item";
                            fileExt = "uai";
                        }
                        tooltip = "";
                        tooltip = '<div>';
                        tooltip += 'Channel : ' + flName + '\n';
                        tooltip += 'Date : ' + datea + '\n';
                        tooltip += 'Time : ' + datea.split(' ')[1] + '\n';
                        tooltip += 'Duration : ' + timeFormate(durInSec) + '\n';
                        tooltip += '</div>';
                        ht = $($.parseHTML(tooltip));

                        iconCss1 = setTimelineIcon(cType, fileExt);
                        items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-evt-id="' + callId + '" class="' + iconCss1 + '" ></span> ' + '<span style="margin-left: 3px;">' + flName + ' | ' + datea + '<span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: ht.html(), callId: callId, flName: flName });
                        ////Commented by ifuturz
                        //groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='http://cdn3.iconfinder.com/data/icons/70-basic-icons/100/close-32.png' /></a>" });
                        ////Commented by ifuturz

                        //Added by ifuturz
                        groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                        //End by ifututrz
                        /////Commented by ifuturz
                        //items_playlist_count++;
                        /////End by ifuturz

                        switch (cType) {
                            case "1": //Audio
                            case "13": //Teams
                                var vod = '';
                                var vid = '';
                                //JL(frontendLogger).info(callId + '-' + vod);
                                switch (fileExt.toLowerCase()) {
                                    case "dsf":
                                        vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                        if (vod)
                                            slAddMedia(callId, vod);
                                        break;
                                    case "mp3":
                                        //ADDED BY KM IFUTURZ TO RESOLVE ISSUE WHILE UPLOAD .dsf.mp3 FILE - START
                                        if (isUserItem)
                                            vod = createDocumentURI(5, startTime, flName, durInSec);
                                        else
                                            vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);

                                        iconCss = "callTypeSprite callType1";
                                        slAddMedia(callId, vod);
                                        break;
                                        //ADDED BY KM IFUTURZ TO RESOLVE ISSUE WHILE UPLOAD .dsf.mp3 FILE - STOP
                                    case "wav":
                                        vod = createDocumentURI(1, startTime, flName, durInSec, pages);
                                        slAddMedia(callId, vod);
                                        break;
                                }
                                break;
                            case "2": //Video
                                if (isUserItem == true || isUserItem == 'true') {
                                    vid = createDocumentURI(3, startTime, flName, durInSec, 0);
                                }
                                else {
                                    vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                                }
                                //JL(frontendLogger).info(callId + vid);
                                slAddMedia(callId, vid);
                                containsvideo = true;
                                break;
                            case "3": //SMS
                                ///Added by ifututrz for SMS
                                msgURI = createMessageURI(6, startTime, flName, msgBody, chNum, chName, durInSec);
                                ///End by ifututrz for SMS
                                //JL(frontendLogger).info('msgURI-SMS:-' + callId + msgURI);
                                slAddSocialMedia(callId, msgURI);
                                items1.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + '<span><span style="margin-left: 3px;" id=' + datea + '>' + datea + '</span>  <span style="margin-left: 130px;">' + msgBody + '</span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: '', callId: callId, flName: msgBody });
                                groups1.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                                break;
                            case "4": //Social
                                //msgURI = createMessageURI(8, startTime, $(this).find('td:nth-child(3) img').attr('msgBody'), chNum, chName);
                                msgURI = createMessageURI(8, startTime, flName, msgBody, chNum, chName, durInSec);
                                //JL(frontendLogger).info(callId + msgURI);
                                slAddSocialMedia(callId, msgURI);
                                items1.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + '<span><span style="margin-left: 3px;" id=' + datea + '>' + datea + '</span>  <span style="margin-left: 130px;">' + msgBody + '</span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: '', callId: callId, flName: msgBody });
                                groups1.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                                break;
                            case "5": //Email
                                //msgURI = createMessageURI(7, startTime, $(this).find('td:nth-child(3) img').attr('msgBody'), chNum, chName);
                                msgURI = createMessageURI(7, startTime, flName, msgBody, chNum, chName, durInSec);
                                //JL(frontendLogger).info(callId + msgURI);
                                slAddSocialMedia(callId, msgURI);
                                items1.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + '<span><span style="margin-left: 3px;" id=' + datea + '>' + datea + '</span>  <span style="margin-left: 130px;">' + msgBody + '</span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: '', callId: callId, flName: msgBody });
                                groups1.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                                break;
                            case "6": //Screens
                                var audioUri = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                var screensUri = createQuadScreenURI(recid, 3, startTime, durInSec, screenName, flName);
                                if (boolIEBrowser)
                                    screensUri = createQuadScreenURI_Silverlight(recid, 3, startTime, durInSec, screenName, flName);

                                var fnameSplited = flName.split('.');
                                var ext = fnameSplited[1];

                                if (ext == 'dsf' || ext == 'DSF')
                                    audioUri += "&screen=withaudio";
                                else
                                    audioUri += "&screen=withoutaudio";

                                if (ext == 'dsf' || ext == 'DSF')
                                    AddQuadMedia(callId, audioUri, screensUri);
                                else
                                    slAddMedia(callId, screensUri[0]);

                                containsvideo = true;
                                break;
                            case "7": //Inquire
                                var gps = $(this).attr('interview_GPS');
                                var interview_DateTime = $(this).attr('interview_DateTime');
                                var interview_Interviewer = $(this).attr('interview_Interviewer');
                                var interview_Interviewee = $(this).attr('interview_Interviewee');
                                var interview_InterviewId = $(this).attr('interview_InterviewId');
                                var interview_Notes = $(this).attr('interview_Notes');

                                var interview_details = interview_InterviewId + ";" + gps + ";" + interview_DateTime + ";" + interview_Interviewer + ";" + interview_Interviewee + ";" + interview_Notes;
                                var bm = $(this).find("input[id=hdnCallBookmarks]").val();
                                var bookmark_inquire = "<inquire>" + bm + "</inquire>";

                                vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName);
                                // vid = createInquireURI(recid, 0, startTime, flName, durInSec, screenName, chNum, chName);
                                //JL(frontendLogger).info(vid);

                                slAddMedia(callId, vid);
                                var fileExt = flName.split('.');
                                var ftype = "";
                                if (fileExt[1] == "mp3" || fileExt[1] == "m4a" || fileExt[1] == "wav") {
                                    ftype = "audio";
                                }
                                else
                                    ftype = "video";
                                // slAddInquireMedia(callId, vid, bookmark_inquire, interview_details);
                                containsvideo = true;
                                break;
                            case "9": //User Added Image
                                // durInSec = 60;
                                vod = createDocumentURI(9, startTime, flName, durInSec, pages);
                                JL(frontendLogger).info('createDocumentURI for Image  :: ' + vod);
                                //JL(frontendLogger).info(vod);
                                slAddMedia(callId, vod); // this is interface for imeage.
                                containsvideo = true;
                                break;
                            case "10": //User Added Document
                                //$("#divMainPlayerContainer").css('height', $('#divMainPlayerContainer').height() - $('#divTimeline').height() + 45);
                                // durInSec = 60;
                                vod = createDocumentURI(10, startTime, flName, durInSec, pages);
                                JL(frontendLogger).info('createDocumentURI for User Added Document  :: ' + vod);
                                //JL(frontendLogger).info(vod);
                                slAddMedia(callId, vod); // this is for document..
                                containsvideo = true;
                                break;
                            default:
                                $.unblockUI();
                                showErrorDialog("Invalid Call Type: " + cType);
                                break;
                        }
                        ///Added by ifuturz
                        items_playlist_count++;
                        ///End by ifuturz
                    }

                }

                if (!slHaveVideo()) $('#divTimeline').slideUp(5000);
                else $('#divTimeline').slideDown(3000);

                $('#txtPlSearch').val('').hide();
                $('#divtimelineviewdesign').show();

                if (sortedPlaylist.length > 0)//arivu
                {
                    //; //ifuturz
                    //PlayerList for TL 
                    if (bIsAddUserFileDialog) {
                        plA = new PlayerList();
                        plA.seteventua(false);
                        plA.loadItems(items, groups, true);
                        plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth
                        //bIsAddUserFileDialog = false;
                        bIsCurrentUserAddedFile = true;
                        //bIsTLLoadedInAUF = true;
                    }
                    else {
                        JL(frontendLogger).info('search.js-->pl = new PlayerList();');
                        pl = new PlayerList();
                        //pl.loadItems(items, groups, false);

                        // Added by IFuturz to catch the error
                        try {
                            pl.loadItems(items, groups, false); // ERROR_PL_LOAD_ITEMS
                            ////alert("pl.loadItems-->try");
                        } catch (e) {
                            var error = e.message;
                            var err = e.toString();
                            ////alert("pl.loadItems-->catch-->error-->" + error);
                        }
                        // Added by IFuturz to catch the error

                        // pl.init(items, groups);
                        pl.init(items, groups, 'visualization');

                        plA = new PlayerList();
                        plA.seteventua(false);
                        plA.loadItems(items, groups, false);
                        plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth

                        ///Added by ifuturz for user added item
                        plAuserAdded = new PlayerList();

                        if (items1.length > 0) {
                            plAuserAdded.loadItemsUserAdded(items1, groups1, false);
                            plAuserAdded.initUserAdded(items1, groups1, 'divuseraddeditem');
                        }
                        ///End by ifuturz for user added item

                        // plA.syncScroll();
                        bIsTLLoadedInAUF = true;
                        bIsCurrentUserAddedFile = false;

                        // pl.syncScroll();
                        //pl.start();  // when loading the timeline page.. i just call stop method.. this will start and pause.. so, either i can remove this call. or
                        // you can 
                        //pl.start();
                        // pl.play();
                        //pl.pause();
                    }
                }
                $('.remove_item').on('click', function () {
                    try {
                        for (i = 0; i < items.length; i++) {
                            if ($(this).data('id') == items[i].id) {

                                var callid = items[i].callId;
                                if (!bNotSavedAUF)
                                    removePlaylistUserItem(callid);
                                $("#tblPlaylist tbody tr[callid='" + callid + "']").remove(); // ADDED BY KM IFUTURZ TO REMOVE ROW TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                                //var fileName = items[i].title;
                                var fileName = items[i].flName;
                                var lastItem = false;
                                if (items.length == 1) {
                                    lastItem = true;

                                }
                                bIsTLLoadedInAUF = false;
                                bIsZeroItem = false;
                                deleteAddUserFile(playlistId, userId, null, fileName, lastItem, bIsCurrentUserAddedFile);
                                //getplaylistDetail(playlistId);
                            }
                        }
                        plA.removeItem([$(this).data('id')]);
                        getFileDetails(null);
                        displaylink(); // ADDED BY KM IFUTURZ TO RELOAD TIMELINE VIEW AND CREATE NEW OBJECTS OF sequencer.js ARRAYS TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                    } catch (e) {
                        //alert(e.message);
                    }
                });

                ////Added by ifuturz for UserAdded Item on click and mouse hover
                $('#divuseraddeditem .vis-group').on('click', function () {
                    $('#divuseraddeditem .vis-group').removeClass('mouseClickClass');
                    $(this).addClass('mouseClickClass');
                });

                $('#divuseraddeditem .vis-group').hover(function () {
                    if (!$(this).hasClass('mouseClickClass')) {
                        $(this).addClass('mouseHoverClass');
                    }
                }, function () {
                    if ($(this).hasClass('mouseHoverClass')) {
                        $(this).removeClass('mouseHoverClass');
                    }
                });
                ////End by ifuturz for UserAdded Item on click and mouse hover

                // bIsCurrentUserAddedFile = false;
                ///Added by ifuturz on 20161223
                pbw = ProgressEx.width();
                //slUpperSlider.css('left', pbw); // COMMENTED BY KM IFUTURZ FOR RESOLVE SCRUBBER ISSUE
                //slLowerSlider.css('left', 0); // COMMENTED BY KM IFUTURZ FOR RESOLVE SCRUBBER ISSUE
                slUpperSlider.css('left', pbw - 1); // ADDEDE BY KM IFUTURZ TO RESOLVE SCRUBBER ISSUE
                slLowerSlider.css('left', '5px'); // ADDEDE BY KM IFUTURZ TO RESOLVE SCRUBBER ISSUE

                // $('#divLvVideoPlayer').css('display', 'none'); //Commented by arivu on 20183001
                // JL(frontendLogger).info('divLvVideoPlayer-->display->none');
                ///End by ifuturz on 20161223

                totalDurationTimeline(); // ADDED BY KM IFUTURZ FOR TOTAL DURATION TIMELINE
                $('#hdnspnTotalDuration').val('0'); // ADDED BY KM IFUTURZ FOR RESOLVE TOOLTIP BUG

                //ADDED BY KM IFUTURZ TO RESOLVE TIMELINE ZOOM IN ZOOM OUT TIMELINE TIME ISSUE - START
                try {
                    $('#visualization > .vis-timeline > .vis-bottom').css('left', $('#visualization > .vis-timeline > .vis-vertical')[0].style.left);
                } catch (ex) {
                    //alert(ex.message);
                }
                //ADDED BY KM IFUTURZ TO RESOLVE TIMELINE ZOOM IN ZOOM OUT TIMELINE TIME ISSUE - END
            }

            if (hfVal == "Listview") {
                // KM for HTML5

                $('#divLvVideoPlayer').show();

                try {
                    if (!bIsZeroItem) {
                        if (pl.timeline) {
                            //this code will destroy timline, later we can recreate a new timeline
                            pl.timeline.destroy();
                            pl = null
                        }

                        if (plA.timeline) {
                            plA.timeline.destroy();
                            plA = null
                        }
                    }
                } catch (e) {

                }

                //KM IFUTURZ - START
                try {
                    if (bIsTLLoadedInAUF) {
                        if (plA != null) { // Added by ifuturz
                            if (plA.timeline) {
                                plA.timeline.destroy();
                                plA = null
                            }
                        }
                        if (pl != null) {
                            if (pl.timeline) {
                                //this code will destroy timline, later we can recreate a new timeline
                                pl.timeline.destroy();
                                pl = null
                            }
                        }

                        if (plAuserAdded != null) {
                            if (plAuserAdded.timeline) {
                                plAuserAdded.timeline.destroy();
                                plAuserAdded = null
                            }
                        }

                        ///Added by ifututrz to solve on switch between playlist from dropdown
                        sequencer.setLoaded(false);
                        ///End by ifututrz to solve on switch between playlist from dropdown
                        bIsDestroyTLInAUF = false;
                    }
                    else if (!bIsZeroItem) {
                        if (pl != null) { // Added by ifuturz
                            if (pl.timeline) {
                                //this code will destroy timline, later we can recreate a new timeline
                                pl.timeline.destroy();
                                pl = null;
                            }
                        }
                        if (plA != null) { // Added by ifuturz
                            if (plA.timeline) {
                                plA.timeline.destroy();
                                plA = null;
                            }
                        }
                        if (plAuserAdded != null) {
                            if (plAuserAdded.timeline) {
                                plAuserAdded.timeline.destroy();
                                plAuserAdded = null
                            }
                        }
                        bIsDestroyTLInAUF = false;
                    }
                    else {
                        if (bIsDestroyTLInAUF) {
                            if (plA != null) { // Added by ifuturz
                                if (plA.timeline) {
                                    plA.timeline.destroy();
                                    plA = null;
                                }
                            }
                            bIsDestroyTLInAUF = false;
                        }
                    }

                    var items = []; // timeline
                    var groups = []; // timeline
                    var temp = [];  // used to sort array based on starttime
                    var sortedPlaylist = []; // store sorted array.
                    var msgURI = '';
                    var items_playlist_count = 0;
                    var firstFile = true;
                    ///Added by ifuturz for userAdded item
                    var items1 = []; // timeline
                    var groups1 = []; // timeline

                    $("#tblPlaylist > tbody > tr").each(function () {
                        bIsZeroItem = false;
                        msgURI = '';

                        var recid1 = $(this).attr('recid');
                        var callId1 = $(this).attr('callId');
                        var flName1 = $(this).attr('fName');
                        var startTime1 = $(this).attr('stTime');
                        var fileExt1 = flName1.substr(flName1.lastIndexOf('.') + 1); //flName.split('.')[1];
                        var durInSec1 = $(this).attr('cDuration'); //convertTimeToSeconds($(this).attr('cDuration'));
                        // var durInMilliSec = $(this).attr('cdDuration');
                        //var durInSec = durInMilliSec / 1000;
                        var cType1 = $(this).attr('ctype');
                        var chNum1 = $(this).attr('chNum');
                        var chName1 = $(this).attr('chName');
                        //var screenName1 = $(this).attr('sName').split(',')[0]; /// Commeted by ifuturz
                        var screenName1 = $(this).attr('sName').split(',')[0]; ///Added by ifututrz on 20161230 to get videos from string after split
                        var pages1 = $(this).attr('pages');
                        var isUserItem1 = $(this).attr('isuseritem');
                        var msgBody1 = $(this).attr('msgBody');  //// Added by ifuturz for Message,Email and Social
                        if (firstFile) {
                            sendStartTime(startTime1);
                        }
                        firstFile = false;
                        ////Added by ifuturz on 20161230 for Video file on timeline
                        if (!screenName1 == '') {
                            flName1 = screenName1;
                        }
                        if (isUserItem1 && flName1 == "") {
                            flName1 = "User Added Item";
                            fileExt1 = "uai";
                        }
                        ////End by ifuturz on 20161230 for Video file on timeline
                        temp.push({
                            'recid': recid1,
                            'CallId': callId1,
                            'flName': flName1,
                            'startTime': startTime1,
                            'fileExt': fileExt1,
                            'durInSec': durInSec1,
                            'cType': cType1,
                            'chNum': chNum1,
                            'chName': chName1,
                            'screenName': screenName1,
                            'pages': pages1,
                            'isUserItem': isUserItem1,
                            'msgBody': msgBody1          //// Added by ifuturz for Message,Email and Social
                        });
                        items_playlist_count++;
                    });
                    items_playlist_count = 0;

                    sortedPlaylist = temp.sort(sortByDate);
                    if (sortedPlaylist.length == 0) {
                        bIsZeroItem = true;
                        //var starttime = "";
                        //sendStartTime(null);
                    }
                    var bIsCurrentUserAddedFile = bIsHasFile;
                    if (bIsAddUserFileDialog) {
                        if (bIsCurrentUserAddedFile) {

                            var dt = new Date();
                            var callId = "";
                            var flName = userAddedFile;
                            var extInfo = flName.split('.');
                            var startTime = vis.moment(dt).format('YYYY-MM-DD HH:mm:ss');//dt.getFullYear() + "0" + dt.getMonth() + "0" + dt.getDay() + dt.getHours() + dt.getMinutes() + dt.getSeconds();
                            var fileExt = extInfo[1];
                            var durInSec = 60;

                            var datea = startTime;//startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                            var datea_Stoptime = getEndDate(datea, durInSec * 1000);

                            iconCss1 = setTimelineIcon(cType, fileExt);
                            items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName, callId: callId });
                            groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                            bNotSavedAUF = true;
                            items_playlist_count++;

                            if (bIsZeroItem) {
                                plA = new PlayerList();
                                plA.loadItems(items, groups, true);
                                plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth
                                bIsTLLoadedInAUF = true;
                                bIsAddUserFileDialog = true;
                                bNotSavedAUF = true;
                            }
                        }

                        for (i = 0; i < sortedPlaylist.length; i++) {

                            msgURI = '';

                            var recid = sortedPlaylist[i].recid;
                            var callId = sortedPlaylist[i].CallId;
                            var flName = sortedPlaylist[i].flName;
                            var startTime = sortedPlaylist[i].startTime;
                            var fileExt = sortedPlaylist[i].fileExt;
                            var durInSec = sortedPlaylist[i].durInSec;
                            var cType = sortedPlaylist[i].cType;
                            var chNum = sortedPlaylist[i].chNum;
                            var chName = sortedPlaylist[i].chName;
                            var screenName = sortedPlaylist[i].screenName;
                            var pages = sortedPlaylist[i].pages;
                            var isUserItem = sortedPlaylist[i].isUserItem;
                            var msgBody = sortedPlaylist[i].msgBody;        //// Added by ifuturz for Message,Email and Social
                            var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                            var datea_Stoptime = getEndDate(datea, durInSec * 1000);

                            ////Added by ifuturz on 20161228
                            if (isUserItem && flName == "User Added Item") {
                                flName = "User Added Item";
                                fileExt = "uai";
                            }
                            tooltip = "";
                            tooltip = '<div>';
                            tooltip += 'Channel : ' + flName + '\n';
                            tooltip += 'Date : ' + datea + '\n';
                            tooltip += 'Time : ' + datea.split(' ')[1] + '\n';
                            tooltip += 'Duration : ' + timeFormate(durInSec) + '\n';
                            tooltip += '</div>';
                            ht = $($.parseHTML(tooltip));

                            iconCss1 = setTimelineIcon(cType, fileExt);
                            items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-evt-id="' + callId + '" class="' + iconCss1 + '" ></span> ' + '<span style="margin-left: 3px;">' + flName + ' | ' + datea + '<span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: ht.html(), callId: callId, flName: flName });
                            groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                            items_playlist_count++;
                        }
                    }
                    else {
                        for (i = 0; i < sortedPlaylist.length; i++) {
                            msgURI = '';

                            var recid = sortedPlaylist[i].recid;
                            var callId = sortedPlaylist[i].CallId;
                            var flName = sortedPlaylist[i].flName;
                            var startTime = sortedPlaylist[i].startTime;
                            var fileExt = sortedPlaylist[i].fileExt;
                            var durInSec = sortedPlaylist[i].durInSec;
                            var cType = sortedPlaylist[i].cType;
                            var chNum = sortedPlaylist[i].chNum;
                            var chName = sortedPlaylist[i].chName;
                            var screenName = sortedPlaylist[i].screenName;
                            var pages = sortedPlaylist[i].pages;
                            var isUserItem = sortedPlaylist[i].isUserItem;
                            var msgBody = sortedPlaylist[i].msgBody;        //// Added by ifuturz for Message,Email and Social
                            var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                            var datea_Stoptime = getEndDate(datea, durInSec * 1000);
                            if (isUserItem && flName == "User Added Item") {
                                flName = "User Added Item";
                                fileExt = "uai";
                            }
                            tooltip = "";
                            tooltip = '<div>';
                            tooltip += 'Channel : ' + flName + '\n';
                            tooltip += 'Date : ' + datea + '\n';
                            tooltip += 'Time : ' + datea.split(' ')[1] + '\n';
                            tooltip += 'Duration : ' + timeFormate(durInSec) + '\n';
                            tooltip += '</div>';
                            ht = $($.parseHTML(tooltip));

                            iconCss1 = setTimelineIcon(cType, fileExt);
                            items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-evt-id="' + callId + '" class="' + iconCss1 + '" ></span> ' + '<span style="margin-left: 3px;">' + flName + ' | ' + datea + '<span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: ht.html(), callId: callId, flName: flName });
                            groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                            items_playlist_count++;
                        }
                    }

                    if (sortedPlaylist.length > 0)//arivu
                    {
                        //PlayerList for TL 
                        if (bIsAddUserFileDialog) {
                            plA = new PlayerList();
                            plA.loadItems(items, groups, true);
                            plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth
                            //bIsAddUserFileDialog = false;
                            bIsCurrentUserAddedFile = true;
                            //bIsTLLoadedInAUF = true;
                        }
                        else {
                            //pl = new PlayerList();
                            //pl.loadItems(items, groups, false);
                            //// pl.init(items, groups);
                            //pl.init(items, groups, 'visualization');


                            plA = new PlayerList();
                            plA.loadItems(items, groups, false);
                            plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth

                            // plA.syncScroll();
                            bIsTLLoadedInAUF = true;
                            bIsCurrentUserAddedFile = false;

                        }
                    }
                    $('.remove_item').on('click', function () {

                        try {
                            
                            for (i = 0; i < items.length; i++) {
                                if ($(this).data('id') == items[i].id) {

                                    var callid = items[i].callId;
                                    if (!bNotSavedAUF)
                                        removePlaylistUserItem(callid);
                                    $("#tblPlaylist tbody tr[callid='" + callid + "']").remove(); // ADDED BY KM IFUTURZ TO REMOVE ROW TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                                    //var fileName = items[i].title;
                                    var fileName = items[i].flName;
                                    var lastItem = false;
                                    if (items.length == 1) {
                                        lastItem = true;

                                    }
                                    bIsTLLoadedInAUF = false;
                                    bIsZeroItem = false;
                                    deleteAddUserFile(playlistId, userId, null, fileName, lastItem, bIsCurrentUserAddedFile);
                                    //getplaylistDetail(playlistId);
                                }
                            }
                            plA.removeItem([$(this).data('id')]);
                            //  pl.removeItem([$(this).data('id')]);
                            //Added by ifutrz to delete on User Added item list delete
                            // plAuserAdded.removeItem([$(this).data('id')]);
                            //End by ifutrz to delete on User Added item list delete
                            getFileDetails(null);
                            if (items.length == 1) {
                                //bIsZeroItem = true;
                            }

                            displaylink(); // ADDED BY KM IFUTURZ TO RELOAD TIMELINE VIEW AND CREATE NEW OBJECTS OF sequencer.js ARRAYS TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                        } catch (e) {
                            //alert(e.message);
                        }
                    });
                    // bIsCurrentUserAddedFile = false;
                } catch (e) {
                    //alert(e.message);
                }
                //KM IFUTURZ - STOP

                $('#divtimelineviewdesign').hide();

                $('#idvideolist_listview').html('');
                current_screencallid = "";
                screenvideo_obj = null;
                deleteallmedia();
                Reset();
                SetBuffering(false);
                currentloadedscreen = 'ListView';
                islistview = true;
                playbackItems = [];
                nActiveItem = 0;
                nActiveIndex = -1;
                nOldActiveItem = 0;
                nState = FilePlayerState.None;
                containsvideo = false;
                is_multiblobplay = false;
                $('#divTimeline').css('display', 'none');
                $('#divListview').css('display', 'block');
                $('#videofileplayer1').css({ 'width': '0px', 'height': '0px' });

                //$("lnkListview").removeClass("inActiveplLV").addClass("activeplLV");
                $('#hdnActiveForm').val('');
                slReset();
                //slDisableTimeline();
                //slCtl.Content.JS2SL.SetPlaylistParameters(helperServiceIp, userId, playlistId, false, nDiffMinutes);
                SetPlaylistParameters(helperServiceIp, userId, playlistId, false, nDiffMinutes);
                $('.body.row').css('z-index', '2'); // swap z-index
                var recid = '';
                var cType = '';
                var callId = '';
                var flName = '';
                var startTime = '';
                var fileExt = '';
                var durInSec = '';
                var durInMilliSec = '';
                var cType = '';
                var chNum = '';
                var chName = '';
                var screenName = '';
                var bookmark = '';
                var vod = '';
                var vid = '';
                $("#tblPlaylist > tbody > tr").each(function () {
                    bIsZeroItem = false;
                    recid = $(this).attr('recid');
                    cType = $(this).attr('ctype');
                    callId = $(this).attr('callId');
                    flName = $(this).attr('fName');
                    startTime = $(this).attr('stTime');
                    fileExt = flName.substr(flName.lastIndexOf('.') + 1); // flName.split('.')[1];
                    durInSec = $(this).attr('cDuration'); // convertTimeToSeconds($(this).attr('cDuration'));
                    // durInMilliSec = $(this).attr('cdDuration');
                    //  durInSec = durInMilliSec / 1000;
                    chNum = $(this).attr('chNum');
                    chName = $(this).attr('chName');
                    //screenName = $(this).attr('sName').split(',')[0];
                    screenName = $(this).attr('sName');
                    bookmark = $(this).find('input[id=hdnBookmarkXml]').val();
                    var pages = $(this).attr('pages');
                    var isUserItem = $(this).attr('isuseritem');
                    //isUserItemIcon = sortedPlaylist[i].isUserItem;
                    var sdate = new Date(getStartTimeDate(startTime));
                    var edate = new Date((new Date(getStartTimeDate(startTime)).getTime()) + (durInSec * 1000));
                    //JL(frontendLogger).info(fileExt);
                    if (cType == "1" || cType == "13") {
                        //JL(frontendLogger).info(callId + '-' + vod);
                        switch (fileExt.toLowerCase()) {
                            case "dsf":
                                vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                if (vod) {
                                    slAddMedia1(callId, vod, bookmark);
                                }
                                break;
                            case "mp3":
                                if (isUserItem)
                                    vod = createDocumentURI(5, startTime, flName, durInSec);
                                else
                                    vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);
                                slAddMedia(callId, vod);
                                break;
                            case "wav":
                                vod = createDocumentURI(1, startTime, flName, durInSec, pages);
                                slAddMedia(callId, vod);
                                break;
                        }
                    } else if (cType == "2") {
                        //vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                        if (isUserItem == 'true')
                            vid = createDocumentURI(3, startTime, flName, durInSec, 0);
                        else
                            vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                        slAddMedia(callId, vid);
                        containsvideo = true;
                    } else if (cType == "6") {
                        var audioUri = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                        var screensUri = createQuadScreenURI(recid, 3, startTime, durInSec, screenName, flName);

                        //slAddMedia(callId, screensUri[0]);

                        if (flName)
                            audioUri += "&screen=withaudio";
                        else
                            audioUri += "&screen=withoutaudio";

                        AddQuadMedia(callId, audioUri, screensUri);
                        containsvideo = true;
                    } else if (cType == "7") {
                        var fileExt = flName.split('.');
                        var ftype = "";
                        vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName);
                        var bookmark_inquire = "<inquire></inquire>";

                        slAddMedia(callId, vid);
                        if (fileExt[1] == "mp3" || fileExt[1] == "m4a" || fileExt[1] == "wav") ftype = "audio";
                        else ftype = "video";

                        containsvideo = true;
                    } else if (cType == "9") { //User Added Image
                        vod = createDocumentURI(9, startTime, flName, durInSec, pages);
                        slAddMediaListView(callId, vod, sdate, edate);
                        containsvideo = true;
                    } else if (cType == "10") { //User Added Document
                        vod = createDocumentURI(10, startTime, flName, durInSec, pages);
                        slAddMediaListView(callId, vod, sdate, edate);
                        containsvideo = true;
                    }
                });

                $('#tblPlaylist > tbody > tr').show(); // $('#tblPlaylist > tbody > tr').attr("style", "display: inline;");
                $('#txtPlSearch').val('').show();
                //logging
                var msgData = loginUserName;
                var params = '{Id:' + $('#cbxLoadPlayList :selected').val() + ',Name:' + $('#cbxLoadPlayList').find(":selected").text() + '}';
                logActivity(loginUserId, 51, msgData, params);
                try {
                    if (!containsvideo) {
                        $('#divLvVideoPlayer').hide();
                        $('#divListViewTable').css({ 'top': '0px' });
                    } else {
                        swapVideoPlayerContents('Listview');

                        $('#divFileVideoPlayer').css({ 'width': '0px', 'height': '0px' });

                        $('#divFileVideoPlayer').css({ 'width': '0px', 'height': '0px' });
                        $('#videofileplayer').css({ 'width': '0px', 'height': '0px' });
                    }
                } catch (e) {
                    //alert('DisplayLink ' + e.message);
                }
            }
        }
    } catch (e) {
        //alert('DisplayLink ' + e.message);
    }
}

function searchVideoPlayerVisible(display) {
    $("#videoContainerSV").css('display', display);
}
//#endregion View Type


//#region z-index's
var searchareapanel = "";
function swapVideoPlayerContents(mode) {
    //JL(frontendLogger).info('call swapVideoPlayerContents(' + mode + ')');
    if (boolIEBrowser) {
        switch (mode) {
            case "Search":
                if ($('#divSearchVideoPlayer').html() == '') {
                    $('#divSearchVideoPlayer').html($('#divLvVideoPlayer').html());
                    $('#divLvVideoPlayer').html('');

                    adjustQuadPlayer();
                }
                break;
            case "Listview":
                if ($('#divSearchVideoPlayer').html() != '') {
                    $('#divLvVideoPlayer').html($('#divSearchVideoPlayer').html());
                    $('#divSearchVideoPlayer').html('');

                    //$("#videoContainerSV").width(350).height(232);  //width:350px,height:232px
                    //$('#objLVPlayer').width(320).height(240);
                    $('#objLVPlayer').attr({ 'width': 320, 'height': 240 });
                    //$('#divSearchVideoPane #videoContainerSV object').attr({ 'width': '320', 'height': '240' });
                }
                break;
        }
    } else {
        switch (mode) {
            case "Search":
                if ($('#divSearchVideoPlayer').html() == '') {
                    $('#divSearchVideoPlayer').html(searchareapanel);
                    $('#divLvVideoPlayer').html('');
                    var obj = document.getElementById('videofileplayer');
                    obj.style.left = '0px';
                    adjustQuadPlayer();
                }
                break;
            case "Listview":
                if ($('#divSearchVideoPlayer').html() != '') {
                    $('#divLvVideoPlayer').html($('#divSearchVideoPlayer').html());
                    searchareapanel = $('#divSearchVideoPlayer').html();
                    $('#divSearchVideoPlayer').html('');
                    $('#objLVPlayer').attr({ 'width': 320, 'height': 240 });

                    $('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });

                    $('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });
                    $('#videofileplayer').css({ 'width': '320px', 'height': '240px' });

                    var obj = document.getElementById('videofileplayer');
                    obj.style.left = '600px';
                }
                break;
        }
    }
}

function reOrderzIndex(mode) {
    //JL(frontendLogger).info(mode);
    switch (mode) {
        case "SLDialogOpen": //on Search View Video Open
            $("#silverlightControlHost").css('z-index', '99');
            break;
        case "SLDialogClose": //.css({'z-index' : '100'}) //style.zIndex
            if ($("#divSearchVideoPane").css('z-index') == '4')
                $("#silverlightControlHost").css('z-index', '3');
            else
                $("#silverlightControlHost").css('z-index', '1');
            break;
        case "searchVideoView": //on Search View Video Open
			if(boolIEBrowser){
				$("#divSearchVideoPane").css('z-index', '4');
				$("#silverlightControlHost").css('z-index', '3');
				searchVideoPlayerVisible('block');// $("#videoContainerSV").css('display', 'block');
				break;
			}else{
				$("#divSearchVideoPane").css('z-index', '4');
				searchVideoPlayerVisible('block');// $("#videoContainerSV").css('display', 'block');
				break;
			}
        case "searchNonVideoView":
			if(boolIEBrowser){
				$("#divSearchVideoPane").css('z-index', '-1');
				$("#silverlightControlHost").css('z-index', '1');
				searchVideoPlayerVisible('none'); //$("#videoContainerSV").css('display', 'none');
				break;
			}else{
				$("#divSearchVideoPane").css('z-index', '-1');
				searchVideoPlayerVisible('none'); //$("#videoContainerSV").css('display', 'none');
				break;
			}

            //case "searchNormalView":      
            //    $("#divSearchVideoPane").css('z-index', '-1');    
            //    $("#silverlightControlHost").css('z-index', '1');    
            //    break;    
    }
}

var bVideoPanelLoaded = false;
var bDemoVideo = false;
function html5player_adjust() {
    bVideoPanelLoaded = true;
    var height = $(window).height() - ($(window).height() * 0.3);
    var videoWidth = $(window).width() - ($(window).width() * 0.5);
    var videoHeight = $(window).height() - ($(window).height() * 0.5);
    // $('#divCloseVideo').removeClass('hide');
}

function html5player_adjust_listview() {

    $('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });

    $('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });
    $('#videofileplayer').css({ 'width': '320px', 'height': '240px' });

    var obj = document.getElementById('videofileplayer');
    obj.style.left = '600px';
}

function html5player_adjust_timelineview() {

    //KM divLvVideoPlayer_timeline

    if ($('#divSearchVideoPlayer').html() != '') {
        $('#divLvVideoPlayer_timeline').html($('#divSearchVideoPlayer').html());
        $('#divSearchVideoPlayer').html('');
        $('#objLVPlayer').attr({ 'width': 320, 'height': 240 });
        //$('#divSearchVideoPane #videoContainerSV object').attr({ 'width': '320', 'height': '240' });
    }

    $('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });

    $('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });
    $('#videofileplayer').css({ 'width': '320px', 'height': '240px' });
}


function html5player_adjust_Quad() {
    bVideoPanelLoaded = true;
    var height = $(window).height() - ($(window).height() * 0.3);
    var videoWidth = $(window).width() - ($(window).width() * 0.5);
    var videoHeight = $(window).height() - ($(window).height() * 0.5);
    // $('#divCloseVideo').removeClass('hide');
    $('#divVideoContainer').css({ 'height': height });
    $('#divQuadPlayer').css({ 'height': height });
}

function adjustSearchViewPlayerSize() {
    
    $('#divSearchVideoPane').css({ 'width': $('#divCallRecords').width(), 'height': $('#divCallRecords').height() });
    $('#divSearchVideoPane #videoContainerSV ').css({ 'width': $('#divCallRecords').width() - 20, 'height': $('#divCallRecords').height() - 20 });

    //  $('#divSearchVideoPane #videoContainerSV #divSearchVideoPlayer').css({ 'width': $('#divCallRecords').width(), 'height': $('#divCallRecords').height() - 20 });
    $('#divSearchVideoPane #videoContainerSV #divSearchVideoPlayerBookmark').css({ 'width': $('#divCallRecords').width() - 100, 'height': $('#divCallRecords').height() - 20 });
}

function adjustQuadPlayerSize(callId, itemType) {
    //JL(frontendLogger).info(callId + "-" + itemType + "-" + $('#hdnActiveTab').val());

    var callType = 0;
    $('#tblCalls tbody tr').each(function () {
        if (callId == $(this).attr('id'))
            callType = $(this).attr('ctype');
    })
    if (callType == 6 || callType == 7)
        $('#divCloseVideo').removeClass('hide');
    else
        $('#divCloseVideo').addClass('hide');

    //if ($('#hdnActiveTab').val() == 'Search')
    // if (itemType == 3 && (callType == 6 || callType == 7) && $('#hdnActiveTab').val() == 'Search') {
    if (itemType == 3 && $('#hdnActiveTab').val() == 'Search' && callType == 7) {

        $('#divCloseVideo').removeClass('hide');
        $('#divSearchVideoPane').css({ 'width': $('#callList').width(), 'height': $('#callList').height() + 35 });
        $('#divSearchVideoPane #videoContainerSV').css({ 'width': $('#callList').width(), 'height': $('#callList').height() - 30 });
        var requiredWidth = $('#videoContainerSV').width() - $('#videoContainerSV').width() * 60 / 100;
        $('#divSearchVideoPane #videoContainerSV #divSearchVideoPlayer').css({ 'width': $('#callList').width() - requiredWidth, 'height': $('#callList').height() - 30 });
        $('#divSearchVideoPane #videoContainerSV object').attr({ 'width': $('#videoContainerSV').width() - requiredWidth, 'height': $('#videoContainerSV').height() - 20 }); //arivu Should remember km
        // $('#player-container').css({ 'height': $('#videoContainerSV').height() - 20 });
        // $('#divSearchVideoPane #videoContainerSV #divSearchVideoPlayer').css({ 'width': $('#player-container').width(), 'height': $('#player-container').height() - 20 });
        // $('#divSearchVideoPane #videoContainerSV object').attr({ 'width': $('#player-container').width(), 'height': $('#player-container').height() }); //arivu Should remember VPS
    }
    else if (itemType == 3 && $('#hdnActiveTab').val() == 'Search' && callType == 6) {
        $('#divCloseVideo').removeClass('hide');
        $('#divSearchVideoPane').css({ 'width': $('#callList').width(), 'height': $('#callList').height() + 35 });
        $('#divSearchVideoPane #videoContainerSV').css({ 'width': $('#callList').width(), 'height': $('#callList').height() - 20 });
        $('#divSearchVideoPane #videoContainerSV #divSearchVideoPlayer').css({ 'width': $('#callList').width() - 20, 'height': $('#callList').height() - 20 });
        $('#divSearchVideoPane #videoContainerSV object').attr({ 'width': $('#videoContainerSV').width() - 20, 'height': $('#videoContainerSV').height() }); //arivu Should remember km
        $('#divSearchVideoPane').show();
    }
    else {
        $('#divSearchVideoPane').css({ 'width': '', 'height': '' });
        $('#divSearchVideoPane #videoContainerSV').css({ 'width': 320, 'height': 232 });
        $('#divSearchVideoPane #videoContainerSV #divSearchVideoPlayer').css({ 'width': '', 'height': '' });
        $('#divSearchVideoPane #videoContainerSV object').attr({ 'width': 320, 'height': 240 });
        $('#divSearchVideoPane').show();
    }
}

function adjustQuadPlayer() {
    $('#divSearchVideoPane').css({ 'width': $('#callList').width(), 'height': $('#callList').height() });

    $('#divSearchVideoPane #videoContainerSV ').css({ 'width': $('#callList').width(), 'height': $('#callList').height() - 20 });

    $('#divSearchVideoPane #videoContainerSV #divSearchVideoPlayer').css({ 'width': $('#callList').width() - 1000, 'height': $('#callList').height() - 20 });

    //$('#divSearchVideoPane #videoContainerSV object').attr({ 'width': $('#videoContainerSV').width(), 'height': $('#videoContainerSV').height() });
}
//#endregion z-index's

//#region Share Event
$(function () {
    //$('.share-event-link').click(function ()
    $('a.share-event-link').on('click', function (e) {
        //$("#dialog-form").load('url').dialog("open");
        //$("#dialog").load(url, function () {
        //    $(this).dialog('open');
        //});

        $('<div>').dialog({
            modal: true,
            height: 400,
            width: 650,
            title: 'Send Event Invite',
            //open: function () {$(this).load('../Invite/EventGroup.html');},
            open: function (event, ui) {
                $(this).load('../Invite/EventGroup.html', function () {
                    JL(frontendLogger).info('Load was performed.');
                    filleventInviteRows();
                });
            },
            close: function (event, ui) {
                $(this).dialog('destroy').remove()
            },
            buttons: [
                    {
                        text: 'Send',
                        'class': 'send-Invite',
                        click: function () {
                            var dialog = $(this),
                                form = $('#frm-share-events'),
                                data = form.serialize();
                            //debugger
                            if (validateShareEventForm()) {
                                $('.off').remove();
                                var data2post = submitShareEventForm();
                                $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
                                $.ajax({
                                    type: 'POST',
                                    url: handlersBaseURL + '/InquireHandlers/InquireHandler.ashx',
                                    data: { method: 'ShareEvents', Invitation: JSON.stringify(data2post) },
                                    //cache: false,
                                    success: function (response) {
                                        var data = response.data;
                                        switch (response.success) {
                                            case true:
                                                $.unblockUI();
                                                $.growlUI(null, langKeys[currentLanguage]['sInvitationSaved']);
                                                sendShareEventInvite(data.Id, $("#txtEventInvitee").val());
                                                break;
                                            default:
                                                $.unblockUI();
                                                showErrorDialog(response.message);
                                                break;
                                        }
                                    },
                                    error: function (jqXHR, textStatus, errorThrown) {
                                        $.unblockUI();
                                        showExceptionDialog('An error has occurred event share(): ' + jqXHR.responseText);
                                    }
                                });
                            }
                        }
                    }
            ],
        });
    });

    $('#submit-event-invite').on('click', function () {
        //debugger

    });
});

function filleventInviteRows() {
    //$('#tblCalls tbody tr input.tblrowChk:checkbox:checked')
    var cntRows = 0;
    $('#tblCalls tbody tr .tblrowChk:checked').each(function () {
        var $tr = $(this).parent().parent().parent('tr');
        //JL(frontendLogger).info($tr);
        var chNum = $tr.find('td[abbr="colChannelNo"] div').html();
        var chName = $tr.find('td[abbr="colChannelName"] div').html();
        var startTime = $tr.find('td[abbr="colStartTime"] div').html();
        var duration = $tr.find('td[abbr="colDuration"] div').html();
        var eventName = $tr.find('td[abbr="colDuration"] div').html();

        var channel = chNum + ' - ' + chName;
        var row = "<tr calss='' e-id=" + $tr.attr('id') + ">"
            + "<td>" + ++cntRows + "</td>"
            + "<td class=''>" + channel + "</td>"
            + "<td class=''>" + startTime + "</td>"
            + "<td class=''>" + duration + "</td>"
            + "<td class=''>" + eventName + "</td>"
        + "</tr>";

        $('.table-events tbody').append(row);
    });
}

function validateShareEventForm() {
    var valid = true,
        errorMessage = '';

    if ($('form.share-invite input#txtEventInvitee').val() == '') {
        errorMessage = 'Please enter invitee email address </br>';
        valid = false;
    }

    if ($('.table-events > tbody > tr').length == 0) {
        errorMessage += 'Please select at least one event </br>';
        valid = false;
    }

    if (!valid && errorMessage.length > 0) {
        showErrorDialog(errorMessage);
    }
    return valid;
}

function submitShareEventForm() {
    var selectedCallids = getCallsIdsArray();//getRecorderCallsIds();
    var events2Share = {
        SentToEmail: $("#txtEventInvitee").val(),
        UserId: loginUserId,
        Name: $("#txtEventGroupName").val(),
        Comments: $('#txtEventGroupComments').val(),
        EventDetails: getSharedEvents()
    };

    return events2Share;
}

function getSharedEvents() {
    var rows = [];
    $('.table-events tbody tr').each(function () {
        JL(frontendLogger).info($(this));
        rows.push({
            //CallId: $(this).find('td:eq(0)').text(),
            CallId: $(this).attr('e-id')
        });
    });

    return rows;
}

function sendShareEventInvite(invitationId, inviteeEmail) {
    $.ajax({
        url: handlersBaseURL + '/InquireHandlers/InquireHandler.ashx',
        //data: { method: 'ShareEvents', Invitation: JSON.stringify(data2post) },
        data: { method: 'ShareEventInvitation', invitationId: invitationId, inviteeEmail: inviteeEmail },
        type: 'POST',
        success: function (response) {
            $.unblockUI();
            $.growlUI(null, response.message);
            updateInvitationStatus(invitationId, 2);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showErrorDialog('Error occurred in sendShareEventInvite() <br/>' + jqXHR.responseText.substring(jqXHR.responseText.indexOf('<body')));
        }
    });
}

function updateInvitationStatus(invitationId, statusId) {
    $.ajax({
        url: handlersBaseURL + '/InquireHandlers/InquireHandler.ashx',
        type: 'POST',
        //contentType: "application/json"
        data: { method: 'UpdateEventInvitationStatus', invitationId: invitationId, status: statusId },
        success: function (response) {
            $.unblockUI();
            //$.growlUI(null, response.message);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showErrorDialog('Error occurred in updateInvitationStatus() <br/>' + jqXHR.responseText.substring(jqXHR.responseText.indexOf('<body')));
        }
    });
}

//#endregion Share Event

//#region EC crieteria

function populateSearchCriteriaEC(index) {

    var startDate = ($('#txtDateStart').val().length) ? $('#txtDateStart').val() : new Date();
    var endDate = ($('#txtDateEnd').val().length) ? $('#txtDateEnd').val() : new Date();
    var startTime = ($('#txtTimeStart').val().length) ? $('#txtTimeStart').val() : "00:00:00";
    var endTime = ($('#txtTimeEnd').val().length) ? $('#txtTimeEnd').val() : "00:00:00";
    var startDuration = ($('#txtDurationStart').val().length) ? $('#txtDurationStart').val() : "00:00:00";
    var endDuration = ($('#txtDurationEnd').val().length) ? $('#txtDurationEnd').val() : "00:00:00";
    var advSearch = $('#txtAdvanceSearch').val();
    var searchType = getSearchType();
    
    var catGroupExtensionsEC = [];

    $.each(jsonRecorders, function (idx, obj) {
        //JL(frontendLogger).info(obj.Id + '-' + obj.Name + '-' + obj.IP);
        var catGrpExtNodesAudio = getAllNodesEC(obj.Id, 1); //getSelectedNodesEC(obj.Id, 1); //getAllNodesEC(obj.Id, 1);
        var catGrpExtNodesVideo = getAllNodesEC(obj.Id, 6); //getSelectedNodesEC(obj.Id, 6); //getAllNodesEC(obj.Id, 6);
        var catGrpExtNodesInquire = getAllNodesEC(obj.Id, 7); //getSelectedNodesEC(obj.Id, 6); //getAllNodesEC(obj.Id, 6); //KM For EC support Inquire
        if (searchType == 'Advance' && $('#tree7 .leaf input[type="checkbox"]:checked').length > 0) {
            catGrpExtNodesAudio = getSelectedNodesEC(obj.Id, 1);
            catGrpExtNodesVideo = getSelectedNodesEC(obj.Id, 6);
            catGrpExtNodesInquire = getSelectedNodesEC(obj.Id, 7);
        }
        var catGrpExtBoth = [];

        if (catGrpExtNodesAudio.length)
            catGrpExtBoth.push({ 'GroupType': 1, 'GroupExtensions': catGrpExtNodesAudio });
        if (catGrpExtNodesVideo.length)
            catGrpExtBoth.push({ 'GroupType': 6, 'GroupExtensions': catGrpExtNodesVideo });
        if (catGrpExtNodesInquire.length)
            catGrpExtBoth.push({ 'GroupType': 7, 'GroupExtensions': catGrpExtNodesInquire });
        if (catGrpExtBoth.length)
            catGroupExtensionsEC.push({ 'RecorderId': obj.Id, 'CategoryGroupExtensions': catGrpExtBoth });
    });

    if (endTime == '00:00:00')
        endTime = '23:59:59';
    pSize = (typeof pageSize === "undefined") ? 100 : pageSize;

    //JL(frontendLogger).info(catGroupExtensionsEC);
    var jsonStr = {
        PageNumber: index
        , PageSize: pSize //10
        , Criteria: {
            StartDate: startDate,
            EndDate: endDate,
            StartTime: startTime,
            EndTime: endTime,
            StartDuration: startDuration,
            EndDuration: endDuration,
            CustomText: advSearch,
            SearchType: searchType,
            RestrictionInHours: searchRest,
            //CategoryGroupExtensions: catGroupExtensions
            RecorderCategoryGroupExtensions: catGroupExtensionsEC
        }
    };
    var data = JSON.stringify(jsonStr);
    return data;
    //return encodeURIComponent(data);
}
function getSelectedNodesEC(recId, callType) {
    var chkNodes = [];
    var catgrpext = [];

    if (callType == 1 || callType == 6 || callType == 7) {
        $('#tree7 .leaf input[type="checkbox"][ctype="' + callType + '"][recId="' + recId + '"]:checked').each(function () {
            if ($(this).attr('eid') != undefined)
                chkNodes.push($(this).attr('gid') + $(this).attr('eid'));
            else
                chkNodes.push($(this).attr('gid') + emptyGrpExt);
        });
        //JL(frontendLogger).info(chkNodes);
        var unqGrp = getUniqueGroups(chkNodes);
        catgrpext = [];
        $.each(unqGrp, function (index, val) {
            //JL(frontendLogger).info('unqGrp=' + val);
            var chkext = $.map(chkNodes, function (v, i) {
                if (val == v.substring(0, 4))
                    return v.substring(4, 8);
            }).join(",");
            catgrpext.push({ 'GroupId': val, 'ExtensionIdsCSV': chkext }); //'ExtensionIds': chkext
        });
    }
    //JL(frontendLogger).info(catgrpext);
    return catgrpext;
}

function getAllNodesEC(recId, callType) {
    var chkNodes = [];
    var catgrpext = [];

    $('#tree7 .leaf input[type="checkbox"][recId="' + recId + '"][ctype="' + callType + '"]').each(function () {
        //chkNodes.push($(this).attr('gid') + $(this).attr('eid'));
        if ($(this).attr('eid') != undefined)
            chkNodes.push($(this).attr('gid') + $(this).attr('eid'));
        else
            chkNodes.push($(this).attr('gid') + emptyGrpExt);
    });
    //JL(frontendLogger).info(chkNodes);
    var unqGrp = getUniqueGroups(chkNodes);
    catgrpext = [];
    $.each(unqGrp, function (index, val) {
        var chkext = $.map(chkNodes, function (v, i) {
            if (val == v.substring(0, 4))
                return v.substring(4, 8);
        }).join(",");
        catgrpext.push({ 'GroupId': val, 'ExtensionIdsCSV': chkext }); //'ExtensionIds': chkext
    });
    //JL(frontendLogger).info(catgrpext);
    return catgrpext;
}


function loadCallSearchResultsEC(sQuery, method) {
    ////;
    //JL(frontendLogger).info(config.search[0].IsChainDBsConfigured);
    //$('.flex5').flexOptions({params: [ data, data2 ]}).flexReload();
    var vrBaseUrl = '../Handlers/VoiceRecHandlers/GuestHandler.ashx?method=' + method;
    var data = { name: 'eventId', value: $('#hdnEventId').val() };
    var columns = gridColModel;
    $("#tblCalls").flexigrid({
        colModel: columns,
        resizable: false,
        showToggleBtn: false,
        dataType: 'json',
        autoload: true,
        hideOnSubmit: true,
        nomsg: '<span class="ml" lkey="cNoItems">' + NoItems[currentLanguage] + '</span>', //'No items',
        query: sQuery,
        params: [data],
        useRp: false,
        rp: pageSize, //100,
        usepager: true,
        pagetext: Page[currentLanguage],
        outof: Of[currentLanguage],
        pagestat: '<span class="ml" lkey="cDisplaying">' + Displaying[currentLanguage] + '</span>: <b>{from}</b> <span class="ml" lkey="cTo">' + To[currentLanguage] + '</span> <b>{to}</b> <span class="ml" lkey="cOf">' + Of[currentLanguage] + '</span> <b>{total}</b> <span class="ml" lkey="cFiles">' + Files[currentLanguage] + '</span>.', //todo //'Displaying {from} to {total} items',
        procmsg: ProcessingWait[currentLanguage],
        url: vrBaseUrl,
        width: 'auto',
        height: $('#divCallSearchResults').parent().height() - 60,
        onError: function (xhr, status, errorThrown) { showExceptionDialog('Error occur loadCallSearchResults(): ' + status + ': ' + errorThrown + ': ' + xhr.responseText); $('#tblCalls').flexAddData({ rows: [], page: 1, total: 0 }); },
        onSubmit: function () {
            var totalRows = $('#tblCalls tbody tr').length;
            if (totalRows > 0) {
                if (boolIEBrowser) {
                    slReset_Silverlight();
                }
                else {
                    slReset();
                }
                //$("#tblCalls input[type='checkbox']:checkbox").attr('checked', false);
                $('input#chkAllRows').attr('checked', false);
            }
            return true;
        },
        onSuccess: function () {
            $('#tblCalls tbody tr input.tblrowChk').prop("checked", true);
            addtoplayer($('#tblCalls tr'));

            tableshiftcheckbox();
            resizeViewEventWindow();
        },
        minheight: 100,
        disableSelect: true,
        onChangeSort: function (name, order) {
            sortGrid('#tblCalls', order);
        }
    });
}


//#endregion EC crieteria

//#region EC Based function

function updateCallCustomFields(recCallIds, newVal, columnClass) {
    var jsonRecCallIds = { RecCallIds: recCallIds, FieldName: columnClass, FieldText: newVal, userId: $('#header-login-name').attr('uid') };
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: { method: 'UpdateCustomFieldsByRecorders', CallCustomField: JSON.stringify(jsonRecCallIds) },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sCfUpdate']);
                    $.each(recCallIds, function (key, val) {
                        $("#tblCalls tbody tr[id='" + val.CallId + "'] td[abbr='" + columnClass + "']").find('div').html(newVal);
                    });
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    $.each(recCallIds, function (key, val) {
                        $("#tblCalls tbody tr[id='" + val.CallId + "'] td[abbr='" + columnClass + "']").find('div').html('');
                    });
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred updateRowContents(): ' + jqXHR.responseText);
        }
    });
}

function updateRetainCallByRecorder(recid, callId, retainCall, startTime) {
    //var recid = $("#tblPlaylist tbody tr[callid=" + callId + "]").attr("recid");
    //JL(frontendLogger).info(recid + '-' + callId + '-' + retainCall);
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data:
            {
                method: 'UpdateRetainStatusByRecorder'
                , recId: recid
                , callId: callId
                , retainValue: retainCall
                , startTime: startTime
            },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sCfUpdate']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in updateRetainCallByRecorder(): ' + jqXHR.responseText);
        }
    });
}

function updateColCommentsContentsEC(recCallIds, newVal, columnClass) {
    var jsonRecCallIds = { RecCallIds: recCallIds, FieldName: columnClass, FieldText: newVal, userId: $('#header-login-name').attr('uid') };
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: { method: 'UpdateCustomFieldsByRecorders', CallCustomField: JSON.stringify(jsonRecCallIds) },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sCfUpdate']);
                    $.each(recCallIds, function (key, val) {
                        var html = '';
                        if (newVal.length) {
                            var initialText = newVal.substring(0, 16);
                            html = initialText;
                            html += '<a class="read-full-comments" style="padding-left: 3px; text-align: right;" href="javascript:void(0);"><img height="16" src="../assets/icons/search/comments-16.png" alt=""></a>';
                            html += '<p class="full-comments hide">' + newVal + '</p>';
                        }
                        $("#tblCalls tbody tr[id='" + val.CallId + "'] td[abbr='" + columnClass + "']").find('div').html(html);
                    });
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    $.each(recCallIds, function (key, val) {
                        $("#tblCalls tbody tr[id='" + val.CallId + "'] td[abbr='" + columnClass + "']").find('div').html('');
                    });
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred updateRowContents(): ' + jqXHR.responseText);
        }
    });
}

//#endregion EC Based function


//#region Search View

function blankGrid() {
    //if()
    $("#tblCalls").flexigrid({
        colModel: gridColModel,
        resizable: false,
        showToggleBtn: false,
        dataType: 'json',
        autoload: true,
        hideOnSubmit: true,
        nomsg: '<span class="ml" lkey="cNoItems">' + NoItems[currentLanguage] + '</span>', //'No items',
        useRp: false,
        rp: pageSize,
        usepager: true,
        pagetext: Page[currentLanguage],
        outof: Of[currentLanguage],
        pagestat: '<span class="ml" lkey="cDisplaying">' + Displaying[currentLanguage] + '</span>: <b>{from}</b> <span class="ml" lkey="cTo">' + To[currentLanguage] + '</span> <b>{to}</b> <span class="ml" lkey="cOf">' + Of[currentLanguage] + '</span> <b>{total}</b> <span class="ml" lkey="cFiles">' + Files[currentLanguage] + '</span>.', //todo //'Displaying {from} to {total} items',
        procmsg: ProcessingWait[currentLanguage],
        width: 'auto',
        height: $('#divCallSearchResults').parent().height() - 60,
        onError: function (xhr, status, errorThrown) { showExceptionDialog('Error occur loadCallSearchResults(): ' + status + ': ' + errorThrown + ': ' + xhr.responseText); $('#tblCalls').flexAddData({ rows: [], page: 1, total: 0 }); },
        minheight: 100,
        disableSelect: true,
        onChangeSort: function (name, order) {
            sortGrid('#tblCalls', order);
        }
    });
}

function getSearchType() {
    var searchType = 'Default';
    if ($('#tree7 .leaf input[type="checkbox"]:checked').length > 0 || (($('#txtDateStart').val().length > 0) && ($('#txtDateEnd').val().length > 0))) {
        searchType = 'Advance';
    }
    else if ($('#txtAdvanceSearch').val().length > 0) {
        searchType = 'Global';
    }

    return searchType;
}
function populateSearchCriteria(index) {
    //ieRemovePlaceHolderText();

    var startDate = ($('#txtDateStart').val().length) ? $('#txtDateStart').val() : new Date(); //$('#txtDateStart').val();
    var endDate = ($('#txtDateEnd').val().length) ? $('#txtDateEnd').val() : new Date(); //$('#txtDateEnd').val();
    var startTime = ($('#txtTimeStart').val().length) ? $('#txtTimeStart').val() : "00:00:00"; //$('#txtTimeStart').val();
    var endTime = ($('#txtTimeEnd').val().length) ? $('#txtTimeEnd').val() : "00:00:00"; //$('#txtTimeEnd').val();
    var startDuration = ($('#txtDurationStart').val().length) ? $('#txtDurationStart').val() : "00:00:00"; //$('#txtDurationStart').val();
    var endDuration = ($('#txtDurationEnd').val().length) ? $('#txtDurationEnd').val() : "00:00:00"; // $('#txtDurationEnd').val();
    var advSearch = $('#txtAdvanceSearch').val();

    var searchType = getSearchType();
    
    var catGroupExtensions = [];
    //var catGrpExtNodes = getSelectedNodes(1);
    //catGroupExtensions.push({ 'GroupType': 1, 'GroupExtensions': catGrpExtNodes });
    for (var i = 1; i <= 7; i++) {
        var catGrpExtNodes = getSelectedNodes(i);
        if (catGrpExtNodes != undefined && catGrpExtNodes.length !== 0)
            catGroupExtensions.push({ 'GroupType': i, 'GroupExtensions': catGrpExtNodes });
    }
    //JL(frontendLogger).info('catGroupExtensions=' + catGroupExtensions.length);
    //Non-Admin
    if (catGroupExtensions.length == 0 && $('#header-login-name').attr('utype') == 0 && $('#tree7 input[type="checkbox"]:checked').length == 0) {
        //JL(frontendLogger).info('Not an admin user');
        var catGrpExtNodes = getAllNodes(1);
        var catScrennGrpExtNodes = getAllNodes(6);
        var catInquireGrpExtNodes = getAllNodes(7);
        catGroupExtensions.push({ 'GroupType': 1, 'GroupExtensions': catGrpExtNodes });
        catGroupExtensions.push({ 'GroupType': 6, 'GroupExtensions': catScrennGrpExtNodes });
        catGroupExtensions.push({ 'GroupType': 7, 'GroupExtensions': catInquireGrpExtNodes });
        ////alert("" + catInquireGrpExtNodes);
    }
    //Admin without Root
    //if (catGroupExtensions.length == 0 && $('#header-login-name').attr('utype') == 1 && $('#header-login-name').attr('ugrp') != 1000 && $('#tree7 input[type="checkbox"]:checked').length == 0)
    if (catGroupExtensions.length == 0 && $('#header-login-name').attr('utype') == 1 && $('#header-login-name').attr('usertype') != 'AdminSuper' && $('#tree7 input[type="checkbox"]:checked').length == 0) {
        //JL(frontendLogger).info('Not an admin user');
        var catGrpExtNodes = getAllNodes(1);
        var catScrennGrpExtNodes = getAllNodes(6);
        var catInquireGrpExtNodes = getAllNodes(7);
        catGroupExtensions.push({ 'GroupType': 1, 'GroupExtensions': catGrpExtNodes });
        catGroupExtensions.push({ 'GroupType': 6, 'GroupExtensions': catScrennGrpExtNodes });
        catGroupExtensions.push({ 'GroupType': 7, 'GroupExtensions': catInquireGrpExtNodes });
    }

    if (endTime == '00:00:00')
        endTime = '23:59:59';
    pSize = (typeof pageSize === "undefined") ? 100 : pageSize;
    ////
    var jsonStr = {
        PageNumber: index
        , PageSize: pSize //10
        , Criteria: {
            StartDate: startDate,
            EndDate: endDate,
            StartTime: startTime,
            EndTime: endTime,
            StartDuration: startDuration,
            EndDuration: endDuration,
            CustomText: advSearch,
            RestrictionInHours: searchRest,
            SearchType: searchType,
            CategoryGroupExtensions: catGroupExtensions
        }
        // AllPages: false
        //, GroupExtensions: catgrpext
        //, ExtensionCallInfos: extcaltyp
    };
    var data = JSON.stringify(jsonStr);
    return data;
    //return encodeURIComponent(data);
}
function populateGetCriteria(eventId) {

    var jsonStr = {
        eventId: eventId
    };
    var data = JSON.stringify(jsonStr);
    return data;
    //return encodeURIComponent(data);
}

function loadCallSearchResults(sQuery, method) {

    ////;
    //JL(frontendLogger).info(config.search[0].IsChainDBsConfigured);
    //http://stackoverflow.com/questions/18773532/using-jquery-and-flexigrid-how-can-i-pass-additional-values-to-customize-the-gr
    //$('.flex5').flexOptions({params: [ data, data2 ]}).flexReload();
    var vrBaseUrl = '../Handlers/VoiceRecHandlers/GuestHandler.ashx?method=' + method;
    //vrBaseUrl = handlersBaseURL + '/VoiceRecHandlers/GuestHandler.ashx?method=' + method;
    //JL(frontendLogger).info(vrBaseUrl);
    var columns = gridColModel;
    var data = { name: 'eventId', value: $('#hdnEventId').val() };
    //$('.flex5').flexOptions({ params: [data] }).flexReload();
    $("#tblCalls").flexigrid({
        colModel: columns,
        resizable: false, //resizable table
        showToggleBtn: false,
        dataType: 'json',
        autoload: true,
        hideOnSubmit: true,
        nomsg: '<span class="ml" lkey="cNoItems">' + NoItems[currentLanguage] + '</span>', //'No items',
        //title: "Search Results",
        query: sQuery,
        //query: 'eventId='+$('#hdnEventId').val(),
        //query: data,
        params: [data, {name: 'tenantId', value: $('#hdnTenantId').val()}],
        useRp: false,
        rp: pageSize, //100,
        usepager: true,
        //pagestat: 'Displaying: <b>{from}</b> to <b>{to}</b> of <b>{total}</b> matches.', //'Displaying {from} to {total} items',
        pagetext: Page[currentLanguage],
        outof: Of[currentLanguage],
        pagestat: '<span class="ml" lkey="cDisplaying">' + Displaying[currentLanguage] + '</span>: <b>{from}</b> <span class="ml" lkey="cTo">' + To[currentLanguage] + '</span> <b>{to}</b> <span class="ml" lkey="cOf">' + Of[currentLanguage] + '</span> <b>{total}</b> <span class="ml" lkey="cFiles">' + Files[currentLanguage] + '</span>.', //e
        procmsg: ProcessingWait[currentLanguage],
        //method: 'GET', // data sending method
        url: vrBaseUrl,
        width: 'auto',
        height: $('#divCallSearchResults').parent().height() - 60,
        //onSubmit: function () { $.blockUI(); return true; }, //addFormData,
        //onError: function (xhr, status, errorThrown) { JL(frontendLogger).info('xhrError: '); console.dir(xhr); $('#tblCalls').flexAddData({ rows: [], page: 1, total: 0 }); },
        //preProcess: function (data) { JL(frontendLogger).info(columns[3].name); //; return data; }, //formatSearchResults,
        //preProcess: function (data) { if (data.status == false) showExceptionDialog(data.message); return data; },
        //preProcess: function (data) {
        //    $.each(data.rows, function (i, row) {
        //        //row.checked = row.checked ? 'Yes' : 'No';
        //        JL(frontendLogger).info(row.fname);
        //    });
        //    return data;
        //},
        onSubmit: function () {
            var totalRows = $('#tblCalls tbody tr').length;
            if (totalRows > 0) {
                if (boolIEBrowser) {
                    slReset_Silverlight();
                }
                else
                    slReset();
                //$("#tblCalls input[type='checkbox']:checkbox").attr('checked', false);
                $('input#chkAllRows').attr('checked', false);
            }
            return true;
        },
        onError: function (xhr, status, errorThrown) { showExceptionDialog('Error occur loadCallSearchResults(): ' + status + ': ' + errorThrown + ': ' + xhr.responseText); $('#tblCalls').flexAddData({ rows: [], page: 1, total: 0 }); },
        onSuccess: function () {
            //$("#tblCalls tbody tr td span.callTypeSprite").popover({ trigger: "hover", delay: { show: 200, hide: 100 }, html: 'true' });
            //$(".bDiv").css('height', $('#divCallSearchResults').parent().height() - 60);
            $('#tblCalls tbody tr input.tblrowChk').prop("checked", true);
            //addtoplayer($('#tblCalls tr'));
            $("#tblCalls tbody tr").each(function () {
                addtoplayer($(this));
            });

            tableshiftcheckbox();
            resizeViewEventWindow();
        },
        minheight: 100, //min height of columns
        disableSelect: true,
        onChangeSort: function (name, order) {
            sortGrid('#tblCalls', order);
        }
    });
}

function tableshiftcheckbox() {
    $('#tblCalls tbody tr td:first-child').shiftcheckbox({
        checkboxSelector: '.tblrowChk',
        selectAll: '.hDivBox .gridHeaderChk',
        onChange: function (checked) {
            var $tr = $(this).closest('tr');
            if ($tr.hasClass('trSelected') && checked) {
                return;
            }
            this.trigger('change');
        }
    });
}

function getSelectedNodes(callType) {
    var chkNodes = [];
    var catgrpext = [];

    if (callType == 1 || callType == 6 || callType == 7) {
        $('#tree7 .leaf input[type="checkbox"][ctype="' + callType + '"]:checked').each(function () {
            //chkNodes.push($(this).attr('id').substring(1, 10));
            //JL(frontendLogger).info('grp=' + $(this).attr('gid') + ' ext=' + $(this).attr('eid'));
            //JL(frontendLogger).info($(this).attr('eid'));
            if ($(this).attr('eid') != undefined)
                chkNodes.push($(this).attr('gid') + $(this).attr('eid'));
            else
                chkNodes.push($(this).attr('gid') + emptyGrpExt);
        });
        //JL(frontendLogger).info(chkNodes);
        ////;
        //JL(frontendLogger).info(chkNodes);
        var unqGrp = getUniqueGroups(chkNodes);
        catgrpext = [];
        $.each(unqGrp, function (index, val) {
            //JL(frontendLogger).info('unqGrp=' + val);
            var chkext = $.map(chkNodes, function (v, i) {
                if (val == v.substring(0, 4))
                    return v.substring(4, 8);
            }).join(",");
            catgrpext.push({ 'GroupId': val, 'ExtensionIdsCSV': chkext }); //'ExtensionIds': chkext
        });
    }
    else if (callType == 2 || callType == 3 || callType == 4 || callType == 5) {
        $('#tree7 .leaf input[type="checkbox"][ctype="' + callType + '"]:checked').each(function () {
            //chkNodes.push($(this).attr('id').substring(1, 8));
            chkNodes.push($(this).attr('gid') + $(this).attr('eid'));
        });
        //JL(frontendLogger).info(chkNodes);
        var unqGrpDemo = getUniqueGroupsDemo(chkNodes);
        catgrpext = [];
        $.each(unqGrpDemo, function (index, val) {
            //JL(frontendLogger).info('unqGrp=' + val);
            var chkextDemo = $.map(chkNodes, function (v, i) {
                if (val == v.substring(0, 3))
                    return v.substring(3, 6);
            }).join(",");
            catgrpext.push({ 'GroupId': val, 'ExtensionIdsCSV': chkextDemo }); //'ExtensionIds': chkext
        });
    }
    //JL(frontendLogger).info(catgrpext);
    return catgrpext;
}
function getUniqueGroups(inputArray) {
    var outputArray = [];
    for (var i = 0; i < inputArray.length; i++) {
        if (($.inArray(inputArray[i].substring(0, 4), outputArray)) == -1) {
            outputArray.push(inputArray[i].substring(0, 4));
        }
    }
    return outputArray;
}
function getUniqueGroupsDemo(inputArray) {
    var outputArray = [];
    for (var i = 0; i < inputArray.length; i++) {
        if (($.inArray(inputArray[i].substring(0, 3), outputArray)) == -1) {
            outputArray.push(inputArray[i].substring(0, 3));
        }
    }
    return outputArray;
}

function getAllNodes(callType) {
    var chkNodes = [];
    var catgrpext = [];

    $('#tree7 .leaf input[type="checkbox"][ctype="' + callType + '"]').each(function () {
        //chkNodes.push($(this).attr('gid') + $(this).attr('eid'));
        if ($(this).attr('eid') != undefined)
            chkNodes.push($(this).attr('gid') + $(this).attr('eid'));
        else
            chkNodes.push($(this).attr('gid') + emptyGrpExt);
    });
    //JL(frontendLogger).info(chkNodes);
    var unqGrp = getUniqueGroups(chkNodes);
    catgrpext = [];
    $.each(unqGrp, function (index, val) {
        var chkext = $.map(chkNodes, function (v, i) {
            if (val == v.substring(0, 4))
                return v.substring(4, 8);
        }).join(",");
        catgrpext.push({ 'GroupId': val, 'ExtensionIdsCSV': chkext }); //'ExtensionIds': chkext
    });
    //JL(frontendLogger).info(catgrpext);
    return catgrpext;
}

function updateRowContents(callIds, newVal, columnClass) {
    //JL(frontendLogger).info(callIds, newVal);
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: {
            method: 'UpdateCustomFields'
                , dataEntered: newVal
                , callIds: callIds
                , fieldName: columnClass
                , userId: $('#header-login-name').attr('uid')
            //, fieldType:
        },
        cache: false,
        success: function (response) {
            var substr = callIds.split(',');
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sCfUpdate']);
                    for (var i = 0; i < substr.length; i++) {
                        //JL(frontendLogger).info(substr[i].substring(1, 33);
                        //$("#tblCalls tbody tr[id='" + substr[i].substring(1, 33) + "'] td.colCallComments").html(newVal);
                        $("#tblCalls tbody tr[id='" + substr[i].substring(1, 33) + "'] td[abbr='" + columnClass + "']").find('div').html(newVal);
                    }
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    for (var i = 0; i < substr.length; i++) {
                        //JL(frontendLogger).info(substr[i].substring(1, 33);
                        //$("#tblCalls tbody tr[id='" + substr[i].substring(1, 33) + "'] td.colCallComments").html('');
                        $("#tblCalls tbody tr[id='" + substr[i].substring(1, 33) + "'] td[abbr='" + columnClass + "']").html('');
                    }
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred updateRowContents(): ' + jqXHR.responseText);
        }
    });
}

function updateColCommentsContents(callIds, newVal, columnClass) {
    //JL(frontendLogger).info(callIds, newVal);
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: {
            method: 'UpdateCustomFields'
                , dataEntered: newVal
                , callIds: callIds
                , fieldName: columnClass
                , userId: $('#header-login-name').attr('uid')
        },
        cache: false,
        success: function (response) {
            var substr = callIds.split(',');
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sCfUpdate']);
                    for (var i = 0; i < substr.length; i++) {
                        //JL(frontendLogger).info(substr[i].substring(1, 33);
                        //$("#tblCalls tbody tr[id='" + substr[i].substring(1, 33) + "'] td.colCallComments").html(newVal);
                        var html = '';
                        if (newVal.length) {
                            var initialText = newVal.substring(0, 16);
                            html = initialText;
                            html += '<a class="read-full-comments" style="padding-left: 3px; text-align: right;" href="javascript:void(0);"><img height="16" src="../assets/icons/search/comments-16.png" alt=""></a>';
                            html += '<p class="full-comments hide">' + newVal + '</p>';
                        }
                        $("#tblCalls tbody tr[id='" + substr[i].substring(1, 33) + "'] td[abbr='" + columnClass + "']").find('div').html(html);
                    }
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    for (var i = 0; i < substr.length; i++) {
                        //JL(frontendLogger).info(substr[i].substring(1, 33);
                        //$("#tblCalls tbody tr[id='" + substr[i].substring(1, 33) + "'] td.colCallComments").html('');
                        $("#tblCalls tbody tr[id='" + substr[i].substring(1, 33) + "'] td[abbr='" + columnClass + "']").html('');
                    }
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred updateRowContents(): ' + jqXHR.responseText);
        }
    });
}

var selectedSurveyIndex = null;
var evalCallId, surveyId, evalUserNum, recId;
function initializeSurvey() {
    if (boolIEBrowser) {
        $('#divSelectSurvey').dialog({
            autoOpen: false,
            modal: true,
            title: langKeys[currentLanguage]['msgDlgTitleSelectQA'],
            resizable: false,
            width: '400px',
            //show: { effect: 'fade', complete: function () { $(this).find("#txtPlaylistName").focus(); } },
            buttons: [{
                id: 'btnSearchEvaluate',
                text: langKeys[currentLanguage]['msgEvaluate'],
                click: function () {
                    // $(this).removeAttr('cID').dialog("close"); //$(this).dialog("close");
                    surveyId = $('#ddlSurveyFallback option:selected').val();
                    evalCallId = $(this).attr('cID');
                    evalUserNum = $(this).attr('uID');
                    recId = $(this).attr('recId');
                    showEvaluationsPopup(surveyId, evalCallId, evalUserNum, recId);
                    $("#ddlSurvey")[0].selectedIndex = 0;
                }
            }
                , {
                    text: langKeys[currentLanguage]['msgBtnOK'],
                    click: function () {
                        if ($('#ddlSurveyFallback option:selected').val() == -1) {
                            showInformationDialog(langKeys[currentLanguage]['imsgSelQA']);
                            return;
                        }
                        //JL(frontendLogger).info($(this).attr('cID'));JL(frontendLogger).info($(this).attr('uID'));
                        //$.ajax(options);
                        $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
                        //filterSurveyDropdown($(this).attr('recId'));
                        evaluateCall($('#ddlSurveyFallback option:selected').val(), $(this).attr('cID'), $(this).attr('uID'), $(this).attr('recId'));
                        ////alert('testing');
                        $(this).removeAttr('cID').removeAttr('uID').removeAttr('recId').dialog("close");
                        // $.unblockUI();
                    }
                }, {
                    text: langKeys[currentLanguage]['msgBtnCancel'],
                    click: function () {
                        $("#ddlSurvey")[0].selectedIndex = 0;
                        $(this).removeAttr('cID').dialog("close"); //$(this).dialog("close");
                    }
                }]
            //, open: function () {//alert($(this).attr('cID'));}
        });
    } else {


        $('#divSelectSurvey').dialog({
            autoOpen: false,
            modal: true,
            title: langKeys[currentLanguage]['msgDlgTitleSelectQA'],
            resizable: false,
            width: '400px',
            //show: { effect: 'fade', complete: function () { $(this).find("#txtPlaylistName").focus(); } },
            buttons: [{
                id: 'btnSearchEvaluate',
                text: langKeys[currentLanguage]['msgEvaluate'],
                click: function () {
                    // $(this).removeAttr('cID').dialog("close"); //$(this).dialog("close");
                    surveyId = $('#ddlSurvey option:selected').val();
                    evalCallId = $(this).attr('cID');
                    evalUserNum = $(this).attr('uID');
                    recId = $(this).attr('recId');
                    showEvaluationsPopup(surveyId, evalCallId, evalUserNum, recId);
                    $("#ddlSurvey")[0].selectedIndex = 0;
                }
            }
                , {
                    id: 'btnSearchOK',
                    text: langKeys[currentLanguage]['msgBtnOK'],
                    click: function () {
                        if ($('#ddlSurvey option:selected').val() == -1) {
                            showInformationDialog(langKeys[currentLanguage]['imsgSelQA']);
                            return;
                        }

                        //JL(frontendLogger).info($(this).attr('cID'));JL(frontendLogger).info($(this).attr('uID'));
                        //$.ajax(options);
                        $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
                        //filterSurveyDropdown($(this).attr('recId'));

                        evaluateCall($('#ddlSurvey option:selected').val(), $(this).attr('cID'), $(this).attr('uID'), $(this).attr('recId'));
                        ////alert('testing');
                        //  $(this).removeAttr('cID').removeAttr('uID').removeAttr('recId').dialog("close");
                        //  $.unblockUI();

                        $('#btnSearchEvaluate').show();
                        $('#btnSearchOK').hide();
                    }
                }, {
                    text: langKeys[currentLanguage]['msgBtnCancel'],
                    click: function () {
                        $("#ddlSurvey")[0].selectedIndex = 0;
                        $(this).removeAttr('cID').dialog("close"); //$(this).dialog("close");
                    }
                }]
            //, open: function () {//alert($(this).attr('cID'));}

        });
        $('#btnSearchEvaluate').hide();
    }
}
function filterSurveyDropdown(selectedRecorder) {
    if (boolIEBrowser) {
        JL(frontendLogger).info('inside filterSurveyDropdown');
        $('#ddlSurveyFallback').find('[RecId!="' + selectedRecorder + '"]').hide();
        $('#ddlSurveyFallback').find('[RecId="' + selectedRecorder + '"]').show();
        $('#ddlSurveyFallback').find('[value=-1]').show();
        $("#ddlSurveyFallback option").each(function () {
            $(this).css("display", "block");
        });
    } else {
        JL(frontendLogger).info('inside filterSurveyDropdown');
        $('#ddlSurvey').find('[RecId!="' + selectedRecorder + '"]').hide();
        $('#ddlSurvey').find('[RecId="' + selectedRecorder + '"]').show();
        $('#ddlSurvey').find('[value=-1]').show();
        $("#ddlSurvey option").each(function () {
            $(this).css("display", "block");
        });
    }

}
function evaluateCall(surveyId, callId, userNum, recId) {
    //JL(frontendLogger).info(surveyId + ' - ' + callId + ' - ' + userNum);
    if (boolIEBrowser) {
        $.ajax({
            type: 'POST',
            url: handlersBaseURL + '/EvaluationHandlers/EvaluationHandler.ashx',
            data: $(this).find('form').serialize(),
            data:
                    {
                        method: 'UpdateCallEvaluationStatus'
                        , SurveyId: surveyId
                        , SurveyName: $('#ddlSurveyFallback option:selected').text()
                        , CallId: callId
                        , AppUserId: userNum
                        , RecId: recId
                    },
            success: function (response) {
                switch (response.success) {
                    case true:
                        //$.unblockUI();
                        getEvalIds(surveyId, callId, userNum, recId);
                        $.growlUI(null, langKeys[currentLanguage]['sCallForQAAdded']);
                        //  $("#ddlSurveyFallback")[0].selectedIndex = 0;

                        break;
                    default:
                        $.unblockUI();
                        showErrorDialog(response.message);
                        break;
                }
            },
            error: function (response) {
                switch (response.success) {
                    case false:
                        showErrorDialog('There was a problem with your evaluation request.' + response.message);
                        $.unblockUI();
                        break;
                }
            }
        });
    } else {
        $.ajax({
            type: 'POST',
            url: handlersBaseURL + '/EvaluationHandlers/EvaluationHandler.ashx',
            data: $(this).find('form').serialize(),
            data:
                    {
                        method: 'UpdateCallEvaluationStatus'
                        , SurveyId: surveyId
                        , SurveyName: $('#ddlSurvey option:selected').text()
                        , CallId: callId
                        , AppUserId: userNum
                        , RecId: recId
                    },
            success: function (response) {
                switch (response.success) {
                    case true:
                        //$.unblockUI();
                        getEvalIds(surveyId, callId, userNum, recId);
                        $.growlUI(null, langKeys[currentLanguage]['sCallForQAAdded']);
                        //   $("#ddlSurvey")[0].selectedIndex = 0;

                        break;
                    default:
                        $.unblockUI();
                        showErrorDialog(response.message);
                        break;
                }
            },
            error: function (response) {
                switch (response.success) {
                    case false:
                        showErrorDialog('There was a problem with your evaluation request.' + response.message);
                        $.unblockUI();
                        break;
                }
            }
        });
    }
}

function playlistDialog() {
    $("#divPlaylist").dialog({
        autoOpen: false,
        modal: true,
        resizable: true,
        //title: 'testing',
        open: function () { $(this).dialog("option", "title", $(this).attr('plID') == 0 ? langKeys[currentLanguage]['msgDlgTitleCreatePl'] : langKeys[currentLanguage]['msgDlgTitleEditPl']); $('#lblPlErrorMsg').html(''); },
        show: { effect: 'fade', complete: function () { $(this).find("#txtPlaylistName").focus(); } },
        buttons: [
        {
            text: langKeys[currentLanguage]['msgBtnSave'],
            click: function () {
                if ($.trim($('#txtPlaylistName').val()) != "") {
                    //$.ajax(options);
                    $.blockUI();
                    if (validateIsSamePlName($(this).attr('plID'), $('#txtPlaylistName').val())) {
                        saveplaylist();
                    }
                    else {
                        showErrorDialog(langKeys[currentLanguage]['emsgSameNamePl']);
                        $.unblockUI();
                        return;
                    }
                    $(this).removeAttr('plID').dialog("close");
                    $('#txtPlaylistName').val('');
                    $('#txtPlComments').val('');
                    $('#lblPlErrorMsg').html('');
                }
                else
                    $('#lblPlErrorMsg').html(langKeys[currentLanguage]['emsgReqFieldsMissing']).show();
            }
        }, {
            text: langKeys[currentLanguage]['msgBtnCancel'],
            click: function () {
                $('#lblPlErrorMsg').html('');
                // $(this).dialog("close");$("#txtPlaylistName").val('');
                $(this).removeAttr('plID').dialog("close");
                $('#txtPlaylistName').val('');
                $('#txtPlComments').val('');
            }
        }
        ]
    });
    $("#divPlaylist").parent().appendTo($("form:first"));
}
function exportDialog() {
    $("#divExportData").dialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        title: langKeys[currentLanguage]['msgDlgTitleExportExcel'],
        //show: { effect: 'fade', complete: function () { $(this).find("#txtPlaylistName").focus(); } },
        buttons: [
        {
            text: langKeys[currentLanguage]['msgBtnExport'],
            click: function () {
                var radioGrp = $('input:radio[name="exportCall"]:checked');
                if (radioGrp.length == 0) {
                    $('#lblExportCallsErrorMsg').html(langKeys[currentLanguage]['emsgSelectExportOption']).show();
                    return;
                }
                var totalRows = $('#tblCalls tbody tr').length;
                //if (totalRows <= 1)
                if (totalRows < 1) {
                    $('#lblExportCallsErrorMsg').html(langKeys[currentLanguage]['emsgNoRecord']).show();
                    return;
                }
                ////alert(radioGrp.val());

                //exportDataCalls();
                ////alert(getSearchCriteria(1));
                if (radioGrp.val() == 'All') {
                    //if (isECEnabled && $('#header-login-name').attr('utype') == 1 && $('#header-login-name').attr('ugrp') == 1000)
                    if (bUserSearchLoaded) {
                        // No need to search if EC is enabled or not. Situation is handeled in server code. 
                        window.location = exportHandlerURL + "?exportOperation=exportUserSearchResults" + "&userSearchIndex=" + userSearchIndex + "&exportType=" + radioGrp.val() + "&data=";
                    }
                    else {
                        if (isECEnabled)
                            window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&jsonstr=" + populateSearchCriteriaEC(1) + "&exportType=" + radioGrp.val();
                        else
                            window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&jsonstr=" + populateSearchCriteria(1) + "&exportType=" + radioGrp.val();
                    }
                    $(this).dialog("close");
                    return;
                }
                if (radioGrp.val() == 'Current') {
                    //var page = $('#tblCalls')[0]['p']['newp'];var rowsPerPage = $('#tblCalls')[0]['p']['rp'];var query = $('#tblCalls')[0]['p']['query'];var qtype = $('#tblCalls')[0]['p']['qtype'];
                    var currentPageNo = $('#tblCalls')[0]['p']['newp'];
                    //JL(frontendLogger).info(currentPageNo);
                    //if (isECEnabled && $('#header-login-name').attr('utype') == 1 && $('#header-login-name').attr('ugrp') == 1000)
                    if (bUserSearchLoaded) {
                        window.location = exportHandlerURL + "?exportOperation=exportUserSearchResults" + "&userSearchIndex=" + userSearchIndex + "&exportType=" + radioGrp.val() + "&currentPageNumber=" + currentPageNo + "&data=";
                    }
                    else {
                        if (isECEnabled)
                            window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&jsonstr=" + populateSearchCriteriaEC(currentPageNo) + "&exportType=" + radioGrp.val();
                        else
                            window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&jsonstr=" + populateSearchCriteria(currentPageNo) + "&exportType=" + radioGrp.val();
                    }
                    $(this).dialog("close");
                    return;
                }
                var callToExport = exportDataCalls(radioGrp.val());
                //if (callToExport.length > 0)
                if (callToExport.length) {
                    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
                    ////window.location = "../Handlers/VoiceRecHandlers/ExportToExcelHandler.ashx?data=" + exportDataCalls(radioGrp.val());
                    //window.location = handlersBaseURL + '/VoiceRecHandlers/ExportToExcelHandler.ashx' + "?data=" + callToExport;
                    //window.location = handlersBaseURL + '/VoiceRecHandlers/ExportToExcelHandler.ashx' + "?startDate=" + $('#txtDateStart').val() + "&endDate=" + $('#txtDateEnd').val() + "&data=" + callToExport;
                    //window.location = handlersBaseURL + '/VoiceRecHandlers/ExportToExcelHandler.ashx' + "?jsonstr=" + getSearchCriteria(1) + "&data=" + callToExport;
                    window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&data=" + callToExport;
                    $.unblockUI();
                    $(this).dialog("close");
                } else {
                    showErrorDialog('No calls selected.Please select calls to export');
                }
                //ieAddPlaceHolders();
            }
        }, {
            text: langKeys[currentLanguage]['msgBtnCancel'],
            click: function () {
                $('#lblExportCallsErrorMsg').html('');
                //$('#divExportData').find("input:radio:checked").removeProp('checked');
                //$('#divExportData').find('input[type=radio]:first').attr('checked', 'checked');
                $('#divExportData input:radio:first').attr('checked', 'checked');
                $(this).dialog("close");
            }
        }]
    });
    $("#divExportData").parent().appendTo($("form:first"));
}


//#region DateTime Control
$(function () {
    $("#txtDateStart").datepicker({
        dateFormat: 'yy-mm-dd',
        onClose: function (dateText, inst) {
            var endDateTextBox = $("#txtDateEnd");
            if (endDateTextBox.val() != '') {
                var testStartDate = new Date(dateText);
                var testEndDate = new Date(endDateTextBox.val());
                if (testStartDate > testEndDate)
                    endDateTextBox.val(dateText);
            }
            else {
                //endDateTextBox.val(dateText);
                endDateTextBox.val($.datepicker.formatDate('yy-mm-dd', new Date()));
            }
        },
        onSelect: function (selectedDateTime) {
            var start = $(this).datepicker('getDate');
            $("#txtDateEnd").datepicker('option', 'minDate', new Date(start.getTime()));
        }
    });
    $("#txtDateEnd").datepicker({
        dateFormat: 'yy-mm-dd',
        onClose: function (dateText, inst) {
            var startDateTextBox = $("#txtDateStart");
            if (startDateTextBox.val() != '') {
                var testStartDate = new Date(startDateTextBox.val());
                var testEndDate = new Date(dateText);
                if (testStartDate > testEndDate)
                    startDateTextBox.val(dateText);
            }
            else {
                startDateTextBox.val(dateText);
            }
        },
        onSelect: function (selectedDateTime) {
            var end = $(this).datepicker('getDate');
            $("#txtDateStart").datepicker('option', 'maxDate', new Date(end.getTime()));
        }
            , beforeShow: function () {
                var d = $("#txtDateStart").datepicker('getDate');
                if (d) return { minDate: d }
            }
    });
});
//#endregion DateTime Control

//#region Time Control
$(function () {
    $("#txtTimeStart").timepicker({
        showSecond: true
                    , timeFormat: 'HH:mm:ss'
                    , hourGrid: 4
                    , minuteGrid: 10
                    , secondGrid: 15
    });

    $("#txtTimeEnd").timepicker({
        showSecond: true
                    , timeFormat: 'HH:mm:ss'
                    , hourGrid: 4
                    , minuteGrid: 10
                    , secondGrid: 15
    });

    $('#txtDurationStart').on('click', function () {
        $(jquerynowbutton).css('visibility', 'hidden');
    });

    $('#txtDurationEnd').on('click', function () {
        $(jquerynowbutton).css('visibility', 'hidden');
    });

    $("#txtDurationStart").timepicker({
        showSecond: true
                    , timeFormat: 'HH:mm:ss'
                    , hourGrid: 4
                    , minuteGrid: 10
                    , secondGrid: 15
                    , onClose: function (selectedDateTime) {
                        var secondsStart = $(this).val() != '' ? $(this).val().split(":")[0] * 3600 + $(this).val().split(":")[1] * 60 : 0;
                        var secondsEnd = $("#txtDurationEnd").val().split(":")[0] * 3600 + $("#txtDurationEnd").val().split(":")[1] * 60;
                        if (secondsStart >= secondsEnd)
                            $("#txtDurationEnd").val($(this).val());
                    }
    });

    $("#txtDurationEnd").timepicker({
        showSecond: true
                    , timeFormat: 'HH:mm:ss'
                    , hourGrid: 4
                    , minuteGrid: 10
                    , secondGrid: 15
                    , onClose: function (selectedDateTime) {
                        var secondsStart = $("#txtDurationStart").val() != '' ? $("#txtDurationStart").val().split(":")[0] * 3600 + $("#txtDurationStart").val().split(":")[1] * 60 : 0;
                        var secondsEnd = $(this).val().split(":")[0] * 3600 + $(this).val().split(":")[1] * 60;
                        if (secondsStart > secondsEnd)
                            $("#txtDurationStart").val($(this).val());
                    }
    });
});
//#endregion Time Control

function validateCriteria() {
    var returnVal = true;
    var startDate = $('#txtDateStart').val();
    var endDate = $('#txtDateEnd').val();
    var q = new Date();
    var mm = q.getMonth() + 1;
    var dd = q.getDate();
    var yyyy = q.getFullYear();

    var date = mm + '/' + dd + '/' + yyyy;  //new Date(yyyy,mm,dd);
    if (mm < 10)
        date = '0' + date;

    console.log(date);
    console.log(startDate);
    console.log(endDate)
    //if (date > startDate) {
    if (startDate > date) {
        showInformationDialog(langKeys[currentLanguage]['imsgInvSdate']);
        returnVal = false;
    }
    if (endDate > date) {
        showInformationDialog(langKeys[currentLanguage]['imsgInvEdate']);
        returnVal = false;
    }
    // Date
    if ((startDate == undefined || startDate == "") && (endDate == undefined || endDate == "")) {
        returnVal = true;
        return returnVal;
    }

    if (startDate == undefined || startDate == "") {
        showInformationDialog(langKeys[currentLanguage]['imsgInvSdate']);
        returnVal = false;
    }
    if (endDate == undefined || endDate == "") {
        showInformationDialog(langKeys[currentLanguage]['imsgInvEdate']);
        returnVal = false;
    }
    return returnVal;
}

function getSearchCriteria(index) {
    //ieRemovePlaceHolderText();

    var startDate = ($('#txtDateStart').val().length) ? $('#txtDateStart').val() : new Date(); //$('#txtDateStart').val();
    var endDate = ($('#txtDateEnd').val().length) ? $('#txtDateEnd').val() : new Date(); //$('#txtDateEnd').val();
    var startTime = ($('#txtTimeStart').val().length) ? $('#txtTimeStart').val() : "00:00:00"; //$('#txtTimeStart').val();
    var endTime = ($('#txtTimeEnd').val().length) ? $('#txtTimeEnd').val() : "00:00:00"; //$('#txtTimeEnd').val();
    var startDuration = ($('#txtDurationStart').val().length) ? $('#txtDurationStart').val() : "00:00:00"; //$('#txtDurationStart').val();
    var endDuration = ($('#txtDurationEnd').val().length) ? $('#txtDurationEnd').val() : "00:00:00"; // $('#txtDurationEnd').val();
    var advSearch = $('#txtAdvanceSearch').val();
    var isGlobalSearch = ($('#txtDateStart').val().length == 0) && ($('#txtAdvanceSearch').val().length > 0);
    //var startDate = "2013-10-29";var endDate = "2013-10-29";var startTime = "00:00:00";var endTime = "00:00:00";var startDuration = "00:00:00";var endDuration = "00:00:00";

    var searchType = 'none';
    var isRandom = false;
    var noofRandomCalls = 0;
    var isPercentage = false;

    var useGroup = false;
    var useAdvSearch = ($('#txtAdvanceSearch').val().length) ? true : false;
    var catgrpext = [];
    var extcaltyp = [];
    $('#tree7 .leaf input[type="checkbox"]:checked').each(function () {
        catgrpext.push($(this).attr('gid')); //catgrpext.push($(this).attr('id')); //needs to be replaced.
        extcaltyp.push({ 'GroupExtension': $(this).attr('gid'), 'CallType': parseInt($(this).attr('ctype'), 10) });
    });
    if (catgrpext.length) {// != 0) {// TreeNode Selected
        useGroup = true;
    }

    pSize = (typeof pageSize === "undefined") ? 100 : pageSize;
    //
    var jsonStr = {
        AllPages: false
            , StartDate: startDate
            , EndDate: endDate
            , StartTime: startTime
            , EndTime: endTime
            , StartDuration: startDuration
            , EndDuration: endDuration
            , SearchType: searchType
            , UseAdvanceSearch: useAdvSearch
            , AdvanceSearchData: advSearch
            , IsRandom: isRandom
            , NoOfRandomCalls: noofRandomCalls
            , IsPercentage: isPercentage
            , UseGroup: useGroup
            , GroupExtensions: catgrpext
            , ExtensionCallInfos: extcaltyp
            , PageNumber: index
            , PageSize: pSize //10
            , IsGlobalSearch: isGlobalSearch
    };
    var data = JSON.stringify(jsonStr);
    return data;
    //return encodeURIComponent(data);
}

function getCallsIds() {
    var hdntxt = '';
    $('#tblCalls tbody tr input.tblrowChk:checkbox:checked').each(function () {
        hdntxt += ",'" + $(this).parent().parent().parent('tr').attr('id') + "'";
    });
    if (hdntxt.length)
        return hdntxt.substring(1, hdntxt.length);
    else
        return hdntxt;
}

function getRecorderCallsIds() {
    var recCallIds = [];
    $('#tblCalls tbody tr input.tblrowChk:checkbox:checked').each(function () {
        recCallIds.push({ 'RecId': $(this).parent().parent().parent('tr').attr('recid'), 'CallId': $(this).parent().parent().parent('tr').attr('id') });
    });
    //;
    return recCallIds;
}

function getCallsIdsSingleString() {
    var hdntxt = '';
    $('#tblCalls tbody tr input.tblrowChk:checkbox:checked').each(function () {
        hdntxt += "," + $(this).parent().parent().parent('tr').attr('id') + "";
    });
    if (hdntxt.length)
        return hdntxt.substring(1, hdntxt.length);
    else
        return hdntxt;
}

function getCallsIdsArray() {
    var callids = [];
    $('#tblCalls tbody tr input.tblrowChk:checkbox:checked').each(function () {
        callids.push($(this).parent().parent().parent('tr').attr('id'));
    });
    //if (hdntxt.length)
    //    return hdntxt.substring(1, hdntxt.length);
    //else
    return callids;
}
function checkMSIEBrowser() {
    var ua = window.navigator.userAgent;
    var msie = ua.indexOf("MSIE ");

    if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) {
        return true;
    }
    else {
        return false;
    }
    return false;
}
function exportDataCalls(selectType) {
    var hdntxt = '';
    var exportedCounter = 0;
    var browserName = navigator.appName;
    var isIEBrowser = checkMSIEBrowser();
    switch (selectType) {
        case "All":
            $('#tblCalls tbody tr input.tblrowChk').each(function () {
                hdntxt += ",'" + $(this).parent().parent().parent('tr').attr('id') + "'";
            });
            break;
        case "Current":
            $('#tblCalls tbody tr input.tblrowChk').each(function () {
                if (browserName == "Microsoft Internet Explorer" && exportedCounter == 50) {
                    return hdntxt.substring(1, hdntxt.length);
                }
                hdntxt += ",'" + $(this).parent().parent().parent('tr').attr('id') + "'";
                exportedCounter++;
            });
            break;
        case "Selected":
            $('#tblCalls tbody tr input.tblrowChk:checkbox:checked').each(function () {
                if ((browserName == "Microsoft Internet Explorer" || isIEBrowser == true) && exportedCounter == 50) {
                    showInformationDialog('Due to limitation in IE, maximum 50 calls will be exported.');
                    return hdntxt.substring(1, hdntxt.length);
                }
                hdntxt += ",'" + $(this).parent().parent().parent('tr').attr('id').trim() + "'";
                exportedCounter++;
            });
            break;
        default:

            break;
    }
    if (hdntxt.length)
        return hdntxt.substring(1, hdntxt.length);
    else
        return hdntxt;
}

function uncheckAllSearchRows() {
    var callId = '';
    $('#tblCalls tbody tr .tblrowChk:checked').each(function () {
        //callId = $(this).attr('id');
        callId = $(this).parent().parent('tr').attr('id');
        $(this).attr('checked', false);
        $(this).closest("tr").toggleClass("trSelected", this.checked);

        //$(this).find('td:last').find('a[id=btnEvaluateCall]').remove(); //Eval button
        //$(this).parent().parent().find('td:last').find('a[id=btnEvaluateCall]').remove(); //Eval button last col
        $(this).parent().parent().parent().find('td[abbr="colSNo"]').find('a[id=btnEvaluateCall]').remove();

        if ($(this).closest('td').hasClass('playCol')) {
            $(this).closest('td').removeClass("playCol");
        }
        if (boolIEBrowser)
            slDeleteMedia_Silverlight(callId);
        else
            slDeleteMedia(callId);
    });
    //$('#tblCalls tbody tr').find('td:last').find('a[id=btnEvaluateCall]').remove();
    $('input#chkAllRows').attr('checked', false);
}

function loadSearchResults(sQuery) {
    //JL(frontendLogger).info(sQuery+' '+bogus);
    //JL(frontendLogger).info(pageSize);
    var vrBaseUrl = '../Handlers/VoiceRecHandlers/GuestHandler.ashx'; //  + '?bogusval=' + bogus;
    var data = { name: 'eventId', value: $('#hdnEventId').val() };
    var columns = gridColModel;
    $("#tblCalls").flexigrid({
        colModel: columns,
        resizable: false, //resizable table
        showToggleBtn: false,
        dataType: 'json',
        autoload: true,
        hideOnSubmit: true,
        nomsg: 'No items',
        //title: "Search Results",
        query: sQuery,
        params: [data],
        useRp: false,
        rp: pageSize,//100,
        usepager: true,
        pagestat: 'Displaying: <b>{from}</b> to <b>{to}</b> of <b>{total}</b> matches.', //'Displaying {from} to {total} items',
        //method: 'GET', // data sending method
        url: vrBaseUrl,
        width: 'auto',
        height: $('#divCallSearchResults').parent().height() - 60,
        //onSubmit: function () { $.blockUI(); return true; }, //addFormData,
        //onError: function (xhr, status, errorThrown) { JL(frontendLogger).info('xhrError: '); console.dir(xhr); $('#tblCalls').flexAddData({ rows: [], page: 1, total: 0 }); },
        //preProcess: function (data) { JL(frontendLogger).info('pre'); }, //formatSearchPhonoResults,
        onSubmit: function () {
            var totalRows = $('#tblCalls tbody tr').length;
            if (totalRows > 0) {
                if (boolIEBrowser) {
                    slReset_Silverlight();
                }
                else
                    slReset();
                //$("#tblCalls input[type='checkbox']:checkbox").attr('checked', false);
                $('input#chkAllRows').attr('checked', false);
            }
            return true;
        },
        onError: function (xhr, status, errorThrown) { showExceptionDialog('Error Occur loadSearchResults(): ' + status + ': ' + errorThrown + ': ' + xhr.responseText); $('#tblCalls').flexAddData({ rows: [], page: 1, total: 0 }); },
        onSuccess: function () {
            $("#tblCalls tbody tr td span.callTypeSprite").popover({ trigger: "hover", delay: { show: 200, hide: 100 }, html: 'true' });
        },
        minheight: 100, //min height of columns
        disableSelect: true,
        onChangeSort: function (name, order) {
            sortGrid('#tblCalls', order);
        }
    });
}

function updateRetaintion(currentRow) {
    var $tr = $(currentRow).closest('tr');
    var recid = $tr.attr('recid');
    var callId = $tr.attr('id');
    var cType = $tr.attr('ctype');
    var startTime = $tr.attr('sttime');
    var chkRetain = $tr.find("input[id='chkRetainCall']:checkbox");

    if (cType == "1" || cType == "6") {
        if (isECEnabled)
            updateRetainCallByRecorder(recid, callId, chkRetain.is(':checked'), startTime);
        else
            updateRetainCall(callId, chkRetain.is(':checked'), startTime);
    }
}

function showBookmarkDialog($tr) {
    var callId = $tr.attr('id');
    var startTime = $tr.find('td[abbr="colStartTime"] div').html();
    var bmXmlData = $tr.find('input[id=hdnCallBookmarks]').val();

    var xml = bmXmlData,
        xmlDoc = $.parseXML(xml.replace('&', '&amp;')),
        $xml = $(xmlDoc),
        $bmRecord = $xml.find("Record");

    var eTable = "<div><table class='table table-hover table-striped table-bordered table-condensed table-details' border='0'><thead><tr><th colspan='3'><small>Available bookmarks against StartTime: </small>" + startTime + "</th></tr><tr><th style='width:10%;'>#</th><th style='width:20%;'>Location</th><th>Text</th></tr></thead><tbody>";
    $.each($bmRecord, function (i, item) {
        var id = $(this).find('Id').text();
        var callIndex = $(this).find('CallIndex').text();
        var bmPosition = $(this).find('Pos').text();
        var bmText = $(this).find('Text').text();

        eTable += "<tr>";
        eTable += "<td>" + ++i + "</td>";
        eTable += "<td>" + toTimeString(bmPosition) + "</td>";
        eTable += "<td>" + bmText + "</td>";
        eTable += "</tr>";
    });
    eTable += "</tbody></table></div></div>";
    var $dialog = $(eTable).dialog({
        autoOpen: false,
        resizable: false,
        modal: true,
        title: 'Bookmarks',
        maxWidth: 'auto',
        width: 500,
        height: 400,
        close: function () {
            $(this).dialog('destroy').remove();
        },
        buttons: {
            "Close": function () {
                $(this).dialog('close');
            }
        },
    });
    $dialog.dialog('open');
}

//ADDED BY KM - IFUTURZ FOR BOOKMARK DIALOG IN LISTVIEW - START
function showBookmarkDialogListView($tr) {
    var callId = $tr.attr('id');
    var startTime = $tr.find('td[abbr="colStartTime"] div').html();
    var bmXmlData = $tr.find('input[id=hdnBookmarkXml]').val();

    var xml = bmXmlData,
        xmlDoc = $.parseXML(xml.replace('&', '&amp;')),
        $xml = $(xmlDoc),
        $bmRecord = $xml.find("Record");

    var eTable = "<div><table class='table table-hover table-striped table-bordered table-condensed table-details' border='0'><thead><tr><th colspan='3'><small>Available bookmarks against StartTime: </small>" + startTime + "</th></tr><tr><th style='width:10%;'>#</th><th style='width:20%;'>Location</th><th>Text</th></tr></thead><tbody>";
    $.each($bmRecord, function (i, item) {
        var id = $(this).find('Id').text();
        var callIndex = $(this).find('CallIndex').text();
        var bmPosition = $(this).find('Pos').text();
        var bmText = $(this).find('Text').text();

        eTable += "<tr>";
        eTable += "<td>" + ++i + "</td>";
        eTable += "<td>" + toTimeString(bmPosition) + "</td>";
        eTable += "<td>" + bmText + "</td>";
        eTable += "</tr>";
    });
    eTable += "</tbody></table></div></div>";
    var $dialog = $(eTable).dialog({
        autoOpen: false,
        resizable: false,
        modal: true,
        title: 'Bookmarks',
        maxWidth: 'auto',
        width: 500,
        height: 400,
        close: function () {
            $(this).dialog('destroy').remove();
        },
        buttons: {
            "Close": function () {
                $(this).dialog('close');
            }
        },
    });
    $dialog.dialog('open');
}
//ADDED BY KM - IFUTURZ FOR BOOKMARK DIALOG IN LISTVIEW - END

function toTimeString(seconds) {
    return (new Date(seconds * 1000)).toUTCString().match(/(\d\d:\d\d:\d\d)/)[0];

    /*var sec_num = parseInt(seconds, 10);
    var hours = Math.floor(sec_num / 3600);
    var minutes = Math.floor((sec_num - (hours * 3600)) / 60);
    var seconds = sec_num - (hours * 3600) - (minutes * 60);

    if (hours < 10) { hours = "0" + hours; }
    if (minutes < 10) { minutes = "0" + minutes; }
    if (seconds < 10) { seconds = "0" + seconds; }
    var time = hours + ':' + minutes + ':' + seconds;
    return time;*/
}

function showCommentsDialog($tr) {
    var callId = $tr.attr('id');
    var startTime = $tr.find('td[abbr="colComment"] div').html();
    //var fullComments = $tr.find('td[abbr="colComment"] div').siblings(".full-comments").text();
    var fullComments = $tr.find('td[abbr="colComment"] div p.full-comments').text()

    var comments = "<div>" + fullComments + "</div>";

    var $dialog = $(comments).dialog({
        autoOpen: false,
        resizable: false,
        modal: true,
        title: 'Event Comments',
        maxWidth: 'auto',
        width: 500,
        height: 250,
        close: function () {
            $(this).dialog('destroy').remove();
        },
        buttons: {
            "Close": function () {
                $(this).dialog('close');
            }
        }
    });
    $dialog.dialog('open');
}
function goFullScreen() {

    //var VideoPlayer = document.getElementById('videofileplayer');
    //var requestFullScreen = VideoPlayer.requestFullscreen || VideoPlayer.msRequestFullscreen || VideoPlayer.mozRequestFullScreen || VideoPlayer.webkitRequestFullscreen;
    //requestFullScreen.call(VideoPlayer);
    var isInFullScreen = (document.fullscreenElement && document.fullscreenElement !== null) ||
       (document.webkitFullscreenElement && document.webkitFullscreenElement !== null) ||
       (document.mozFullScreenElement && document.mozFullScreenElement !== null) ||
       (document.msFullscreenElement && document.msFullscreenElement !== null);

    var docElm = document.getElementById('videofileplayer');// document.documentElement;
    if (!isInFullScreen) {
        if (docElm.requestFullscreen) {
            docElm.requestFullscreen();
        } else if (docElm.mozRequestFullScreen) {
            docElm.mozRequestFullScreen();
        } else if (docElm.webkitRequestFullScreen) {
            docElm.webkitRequestFullScreen();
        } else if (docElm.msRequestFullscreen) {
            docElm.msRequestFullscreen();
        }
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
}


function getEventDetail(eventId) {
    var rxUrl = handlersBaseURL + '/InquireHandlers/GuestHandler.ashx';
    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: rxUrl,
        data: { 'method': 'GetCaseDetails', 'eventId': eventId, 'tenantId': $('#hdnTenantId').val() },
        success: function (response) {
            var data = response.data;
            clearCaseDetails();
            switch (response.success) {
                case true:
                    adjustEventMetadataWindow();
                    handleRightPanelCSS();

                    $('.case-no').text(data.Event.Id);
                    $('.case-location').text(data.Event.GPS);
                    $('.case-date').text(data.Event.DateTime);
                    $('.case-Interviewer').text(data.Event.Interviewer);
                    $('.case-Interviewee').text(data.Event.Interviewee);
                    $('.case-Type').text(data.Event.PrimaryArea);
                    $('.case-qb-dialogid').text(data.QBDialogId);
                    valusetfoeeventnotes(data.Event.Notes);
                    $('#hdnEventNotes').val(data.Event.Notes);
                    if (data.Event.IsVirtualInspection) $('#trLocation').hide();
                    
                    lastCallId = eventId;
                    var channelSid = $('.case-qb-dialogid').text();
                    if (channelSid) { setTimeout(function () { }, 1000); }
                    else JL(frontendLogger).info('Error : something went wrong while joining text conference. channelSid = ' + channelSid);

                    if (data.Inspection.PreInspectionDataList != null && data.Inspection.PreInspectionDataList.length > 0) UpdatePreInspectionData(data.Inspection.PreInspectionDataList, 'divCustomFields');
                    else if (data.CustomFields != null && data.CustomFields.length > 0) updateCustomFields(data.CustomFields);

                    if (data.Inspection.Sections != null && data.Inspection.Sections.length > 0) populateInquireInspectionBookmarkData(data.Inspection.Sections, 'divBookmarks');

                    if (data.ChatTranscript != null && data.ChatTranscript.length > 0) {
                        $('#messages-list').html('');
                        var $selector = $('#messages-list');

                        for (var i = 0; i < data.ChatTranscript.length; i++) {
                            var messages = JSON.parse(data.ChatTranscript[i].Transcript);
                            if (messages != null) {
                                for (var j = 0; j < messages.length; j++) {
                                    buildAndDisplayMessage(messages[j], $selector);
                                }
                            }
                        }
                    }
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred getEventDetail(): ' + jqXHR.responseText);
        }
    });
}

function updateCustomFields(CustomFields) {
   
    $('#divCustomFields').empty();
    for (i_val = 0; i_val < CustomFields.length ; i_val++) {
        var customFieldHtml =
                    '<div class="list-group-item msg-line" id="' + CustomFields[i_val].Id + '">' +
                            '<p class="list-group-item-text">' +
                                '<strong class="msg-sender">' + CustomFields[i_val].CustomFieldHeader + ':</strong> ' +
                                CustomFields[i_val].CustomFieldData +
                            '</p>' +
                    '</div>';
        $('#divCustomFields').append(customFieldHtml);
    }
}

function clearCaseDetails() {
    $('.case-no').text('');
    $('.case-location').text('');
    $('.case-date').text('');
    $('.case-patient-name').text('');
    $('.case-flight-no').text('');
    $('.case-qb-dialogid').text('');

    $('.case-patient-height').text('');
    $('.case-patient-weight').text('');
    $('.case-patient-dob').text('');
    $('.case-patient-gender').text('');
    $('.case-complaint').text('');

    //$('.case-vitals-info table tbody').empty();
    $('.case-vitals-info table tbody').html('');
    $('#messages-list').html('');
}

function getCaseChat() {
    var dialogId = "583d7c588db95726b4000006";
    var params = { chat_dialog_id: dialogId, sort_desc: 'date_sent', limit: 100, skip: 0 };
    QB.chat.message.list(params, function (err, messages) {
        if (messages) {
            JL(frontendLogger).info(messages);
        } else {
            JL(frontendLogger).info(err);
        }
    });
}

var getChatByDialogId = function (dialogId, callback) {
    //var loginParams = { email: QBAdminEmail, password: QBAdminPassword };
    var loginParams = { login: QBAdminEmail, password: QBAdminPassword };
    var TEST_USER = {
        login: 'sher1',
        password: '1234567a'
    };
    QB.createSession(TEST_USER, function (err, session) {
        if (err) {
            JL(frontendLogger).info("createSession failed for user " + params.username)
            JL(frontendLogger).info(err)
            callback(err, null)
        } else {
            //QB.login(params, function (err, user) {
            //});
            QB.chat.dialog.list({ chat_dialog_id: dialogId, sort_desc: 'date_sent', limit: 300, skip: 0 }, function (err, messages) {
                if (err) {
                    JL(frontendLogger).info("getDialog failed for user " + params.username)
                    JL(frontendLogger).info(err)
                    callback(err, null)
                } else {
                    JL(frontendLogger).info(messages);
                    messages.items.forEach(function (item, i, arr) {
                        JL(frontendLogger).info(item);
                    });
                }
            })
        }
    })
};


//#endregion Search View


//#region QB Text and Audio Conference
var QBApplication = null;
var config = null;
var currentUser = null;
var token = null;
var quickBloxUser = null;
var QB = null;
var bLogin = null;
var oldPassword = null;
var userObj = {
    users: []
};

function getChatDialogUsers(dialogId) {
    var dialogId = dialogId;
    var params = { chat_dialog_id: dialogId, sort_desc: 'date_sent', limit: 100, skip: 0 };
    QB.chat.message.list(params, function (err, messages) {
        if (messages) {
            JL(frontendLogger).info(messages);

            if (messages.items.length === 0) {
                $("#no-messages-label").removeClass('hide');
            } else {
                $("#no-messages-label").addClass('hide');

                var occupants = [];
                messages.items.forEach(function (item, i, arr) {
                    if ($.inArray(item.sender_id, occupants) === -1) {
                        occupants.push(item.sender_id);
                    }
                });
                var occupantsIds = occupants.join(",");
                getDialogsUsers(occupantsIds);
                //getChatDialogHistory(dialogId);
                window.setTimeout(function () {
                    if (users) {
                        if (bLogin)
                            getChatDialogHistory(dialogId);
                        else
                            JL(frontendLogger).info('No chat history. bLogin = ' + bLogin);
                    }
                }, 2000);
            }
        } else {
            JL(frontendLogger).info(err);
        }
    });
}
function getChatDialogHistory(dialogId) {
    var dialogId = dialogId;
    var params = { chat_dialog_id: dialogId, sort_desc: 'date_sent', limit: 100, skip: 0 };
    QB.chat.message.list(params, function (err, messages) {
        if (messages) {
            JL(frontendLogger).info(messages);

            if (messages.items.length === 0) {
                $("#no-messages-label").removeClass('hide');
            } else {
                $("#no-messages-label").addClass('hide');

                messages.items.forEach(function (item, i, arr) {
                    var messageId = item._id;
                    var messageText = item.message;
                    var messageSenderId = item.sender_id;
                    var messageDateSent = new Date(item.date_sent * 1000);
                    //var messageSenderLogin = getUserLoginById(messageSenderId);
                    var messageSenderName = getUserNameById(messageSenderId);

                    var messageAttachmentFileId = null;
                    if (item.hasOwnProperty("attachments")) {
                        if (item.attachments.length > 0) {
                            messageAttachmentFileId = item.attachments[0].id;
                        }
                    }
                    //var messageHtml = buildMessageHTML(messageText, messageSenderName, messageDateSent, messageAttachmentFileId, messageId);
                    var messageHtml = buildChatMessageHTML(messageText, messageSenderName, messageAttachmentFileId, messageId, messageDateSent);

                    $('#messages-list').prepend(messageHtml);

                    if (i > 5) { $('#messages-list').scrollTop($('#messages-list').prop('scrollHeight')); }
                });
            }
        } else {
            JL(frontendLogger).info(err);
        }
    });
}
function buildChatMessageHTML(messageText, messageSenderId, attachmentFileId, messageId, messageDateSent) {
    JL(frontendLogger).info('Inside buildChatMessageHTML');
    JL(frontendLogger).info(messageDateSent);
    var messageAttach;
    if (attachmentFileId) {
        messageAttach = '<img src="http://api.quickblox.com/blobs/' + attachmentFileId + '/download.xml?token=' + token + '" alt="attachment" class="attachments img-responsive" />';
    }

    var isMessageSticker = false;//stickerpipe.isSticker(messageText);

    var delivered = '<img class="icon-small" src="../assets/js/QB/chat/images/delivered.jpg" alt="" id="delivered_' + messageId + '">';
    var read = '<img class="icon-small" src="../assets/js/QB/chat/images/read.jpg" alt="" id="read_' + messageId + '">';

    var messageTextHtml = messageText;
    if (messageAttach) {
        messageTextHtml = messageAttach;
    }
    var messageHtml =
                    '<div class="list-group-item msg-line" id="msg-' + messageId + '">' +
                            '<time datetime="' + messageDateSent + '" class="pull-right-time" style="font-size: small;">'
                                    + getDateTimeFormat(messageDateSent) +
                            '</time>' +
                            '<p class="list-group-item-text">' +
                                '<strong class="msg-sender">' + messageSenderId + ':</strong> ' +
                                messageTextHtml +
                            '</p>'
    //'<p class="list-group-item-text">' + getCurrentDateTime(messageDateSent) + '</p>'
    '</div>';


    return messageHtml;
}

function retrieveChatDialogById(dialogId) {
    JL(frontendLogger).info('inside retrieveChatDialogById');
    //var filter = null;
    //filter = { "_id": dialogId };
    QB.chat.dialog.list({ _id: dialogId }, function (err, resDialogs) {
        if (err) {
            JL(frontendLogger).fatalException(err);
        } else {
            // repackage dialogs data and collect all occupants ids
            var occupantsIds = [];

            if (resDialogs.items.length === 0) {
                return;
            }
            JL(frontendLogger).info('retrieveChatDialogById(dialogId) :: dialog based on given dialogid has been found.');

            resDialogs.items.forEach(function (item, i, arr) {
                var dialogId = item._id;
                dialogs[dialogId] = item;

                // join room
                if (item.type != 3) {
                    QB.chat.muc.join(item.xmpp_room_jid, function () {
                        console.info('Joined dialog ' + dialogId);
                    });
                }

                item.occupants_ids.map(function (userId) {
                    occupantsIds.push(userId);
                });
            });
            JL(frontendLogger).info('occupantsIds:' + occupantsIds.length);
        }
    });
}

function getDialogsUsers(usersIds) {
    var params = { filter: { field: 'id', param: 'in', value: usersIds }, per_page: 100 };

    QB.users.listUsers(params, function (err, result) {
        JL(frontendLogger).info(result);
        if (result) {
            mergeUsers(result.items);
        }
    });
}

//#endregion QB Text and Audio Conference

//#region CryptoJS
function decryptPassword(encryptedPassword) {
    var decryptedText = null;
    try {
        //Creating the Vector Key
        var iv = CryptoJS.enc.Hex.parse('a5f8d2e9c1721ae0e84ad660c472c1f3');
        //Encoding the Password in from UTF8 to byte array
        var Pass = CryptoJS.enc.Utf8.parse(encryptionPassword);
        //Encoding the Salt in from UTF8 to byte array
        var Salt = CryptoJS.enc.Utf8.parse("RijndaelAlgorithm");
        //Creating the key in PBKDF2 format to be used during the decryption
        var key128Bits1000Iterations = CryptoJS.PBKDF2(Pass.toString(CryptoJS.enc.Utf8), Salt, { keySize: 128 / 32, iterations: 1000 });
        //Enclosing the test to be decrypted in a CipherParams object as supported by the CryptoJS libarary
        var cipherParams = CryptoJS.lib.CipherParams.create({
            ciphertext: CryptoJS.enc.Base64.parse(encryptedPassword)
        });

        //Decrypting the string contained in cipherParams using the PBKDF2 key
        var decrypted = CryptoJS.AES.decrypt(cipherParams, key128Bits1000Iterations, { mode: CryptoJS.mode.CBC, iv: iv, padding: CryptoJS.pad.Pkcs7 });
        decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
        return decryptedText;
    }
    //Malformed UTF Data due to incorrect password
    catch (err) {
        return "";
    }
}
//#endregion

//#region Transcription

function transcribe(callId, fileName, startTime) {
    var pdate = startTime.substring(0, 8);
    var ptime = startTime.substring(8, 14);
    $("#tblCalls tbody tr[id='" + callId + "'] td[abbr='colTranscription'] div").html(langKeys[currentLanguage]['lnkTranscriptionInProcess']);

    $.ajax({
        type: 'GET', //'POST'
        //contentType: "application/json; charset=utf-8",//dataType: "text",  // not "json" we'll parse
        dataType: 'json',
        url: sttServiceIp + 'GoogleSTT', //'GoogleSTTJSON',
        data: { callId: callId, recordDate: pdate, recordTime: ptime, audioFileName: fileName },
        success: function (response, status, jsonData) {
            if (response.Result.length == 0) {
                showErrorDialog('Processing finished and STT Service returns NO data. <br/>' + response.Status);
                $("#tblCalls tbody tr[id='" + response.CallID + "'] td[abbr='colTranscription'] div").html("STT Service returns NO data");
                return;
            }
            var confidenceHtml = '<strong>' + langKeys[currentLanguage]['transcriptConfidence'] + ':</strong> ';
            showTranscriptionDialog(langKeys[currentLanguage]['msgDlgTitleTranscript'], confidenceHtml + response.Result[0].Confidence + '<br/>' + response.Result[0].Transcript, response.CallID, 0, response.Result[0].Confidence);
            saveTranscription(response.CallID, startTime, 0, response.Result[0].Transcript, response.Result[0].Confidence);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            showExceptionDialog('Error occurred STT Service: ' + jqXHR.responseText.substring(jqXHR.responseText.indexOf('<body')));
            $("#tblCalls tbody tr[id='" + callId + "'] td[abbr='colTranscription'] div").html("<a id='lnkTranscribe' href='javascript:void(0);' class='ml' lkey='lnkTranscribe'>Transcribe</a>");
        }
    });
}

function saveTranscription(callId, startTime, transcriptionId, transcript, confidence) {
    var saveType = transcriptionId > 0 ? 'Update' : 'Insert'; //playlist.Id > 0 ? 1 : 0;
    $.ajax({
        type: 'POST',
        url: sttHandlerURL,
        data: { operation: saveType, method: 'SaveTranscription', callid: callId, startTime: startTime, transcriptionId: transcriptionId, transcript: transcript, confidence: confidence }, //JSON.stringify(transcribedText)
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI(null, langKeys[currentLanguage]['sTranscriptionSaved']); //$.growlUI(null, 'Transcription saved successfully');
                    $("#tblCalls tbody tr[id='" + callId + "'] td[abbr='colTranscription'] div").html(response.transcriptio + '<p class="full-transcript">' + transcript + '</p>');
                    $('.transcript_dialog').attr('transcriptid', response.data);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in during saveTranscription(): ' + jqXHR.responseText);
        }
    });
}

function showTranscriptionConfirmation(callId, fileName, startTime, durationInSec) {
    //Estimated Time to transcribe
    //Disclaimer
    $("#divInformationDialog").dialog({
        //autoOpen: false,
        maxWidth: 'auto',
        width: 500,
        height: 250,
        resizable: true,
        title: langKeys[currentLanguage]['msgDlgTitleInfo'],
        modal: true
    });
    var estimatedTimeHtml = '<span class="ml" lkey="estimatedTimeSTT">' + langKeys[currentLanguage]['estimatedTimeSTT'] + '</span>';
    var disclaimerHtml = '<p style="margin:0px;"><b><span class="ml" lkey="sttDisclaimer">' + langKeys[currentLanguage]['sttDisclaimer'] + '</span></b></p><ul><li><span class="ml" lkey="sttDisclaimer1">' + langKeys[currentLanguage]['sttDisclaimer1'] + '</span></li><li><span class="ml" lkey="sttDisclaimer2">' + langKeys[currentLanguage]['sttDisclaimer2'] + '</span></li></ul>';
    var infoContents = $("<div id='divTranscriptConfirmation' class=''><div id='divEstimatedTime' class='estimated-time'></div><br/><div id='divDisclaimer' class='disclaimer'></div><br /><div><span class='ml' lkey='cmsgDoYouWantToContinue'>" + langKeys[currentLanguage]['cmsgDoYouWantToContinue'] + "</span></div></div>");
    infoContents.find('.estimated-time').append(estimatedTimeHtml + ' <span style="font-weight:bold;"> ' + SecondsTohhmmss(durationInSec * 2) + '</span>');
    infoContents.find('.disclaimer').append(disclaimerHtml);

    $("#lblInformationDialog").html(infoContents);
    $("#divInformationDialog").dialog(
    {
        buttons: {
            'start-Transcription': {
                text: langKeys[currentLanguage]['msgBtnContinue'],
                class: 'left-button',
                click: function () {
                    transcribe(callId, fileName, startTime);
                    $(this).dialog("close");
                }
            },
            'close': {
                text: langKeys[currentLanguage]['msgBtnClose'],
                class: 'transcript-cancel',
                click: function () {
                    $(this).dialog("close");
                }
            }
        }
    });
}

function showTranscriptionDialog(InfoTitle, InfoMessage, callId, transcriptionId, confidence) {
    var transcript_dialog = $('<div class="transcript_dialog" transcriptid="' + transcriptionId + '"><span style="color: #000;"><label class="transcript_dialog-contents" /></span></div>');
    //var transcript_dialog = $('<div class="transcript_dialog">\
    //        <span style="color: #000;"><label class="transcript_dialog-contents" /></span>\
    //        </div>');
    transcript_dialog.dialog({
        //autoOpen: false,
        maxWidth: 'auto',
        width: 500,
        height: 300,
        resizable: true,
        title: InfoTitle,
        modal: true,
        //html: InfoMessage,
        open: function (event, ui) {
            $(this).find('.transcript_dialog-contents').html(InfoMessage); //$('.transcript_dialog-contents').html(InfoMessage);
            $("#btnEditTranscript").attr('state', 'edit');
        },
        buttons: {
            'edit-Transcript': {
                text: langKeys[currentLanguage]['imbEdit'],
                id: 'btnEditTranscript',
                class: transcriptionId == 0 ? 'hide edit-transcript' : 'edit-transcript',
                click: function () {
                    switch ($("#btnEditTranscript").attr('state')) {
                        case 'edit':
                            $(this).find(".transcript_dialog-contents").replaceWith(function () {
                                return "<textarea class=\"transcript_dialog-contents\" >" + $(this).html() + "</textarea>";
                            });
                            $('.edit-transcript > .ui-button-text').text(langKeys[currentLanguage]['imbUpdate']);
                            $("#btnEditTranscript").attr('state', 'update');
                            break;
                        case 'update':
                            var updatedTranscript = $(this).find('textarea.transcript_dialog-contents').val();
                            var updatedTranscriptionId = $('.transcript_dialog').attr('transcriptid');
                            $(this).find(".transcript_dialog-contents").replaceWith(function () {
                                return "<label class=\"transcript_dialog-contents\" >" + updatedTranscript + "</label>";
                            });
                            $('.edit-transcript > .ui-button-text').text(langKeys[currentLanguage]['imbEdit']);
                            $("#btnEditTranscript").attr('state', 'edit');
                            saveTranscription(callId, updatedTranscriptionId, updatedTranscript, confidence);
                            break;
                    }
                }
            },
            'email-Transcript': {
                text: langKeys[currentLanguage]['cdlblEmail'],
                class: 'left-button',
                click: function () {
                    var updatedTranscript = $(this).find('label.transcript_dialog-contents').text();
                    $('<div />').html('<input id="txtEmailAddress" class="email-text" type="text" placeholder="Email" required />').dialog({
                        title: langKeys[currentLanguage]['msgDlgTitleEmailRecipient'],
                        modal: true,
                        'buttons': [{
                            text: langKeys[currentLanguage]['btnSend'],
                            click: function () {
                                if (!IsEmail($('#txtEmailAddress').val())) {
                                    showErrorDialog(langKeys[currentLanguage]['errEmailInvalid']);
                                    return;
                                }
                                var $tr = $("#tblCalls tbody tr[id='" + callId + "']");
                                var chNo = $tr.children('td[abbr="colChannelNo"]').find("div").html();
                                var chName = $tr.children('td[abbr="colChannelName"]').find("div").html();
                                var startTime = $tr.children('td[abbr="colStartTime"]').find("div").html();
                                var duration = $tr.children('td[abbr="colDuration"]').find("div").html();
                                //JL(frontendLogger).info(chNo, chName, startTime, duration); 
                                $.ajax({
                                    type: 'POST',
                                    url: '../Handlers/Common/EmailHandler.ashx',
                                    data: { operation: 'EmailTranscript', callId: callId, transcript: updatedTranscript, recipient: $('#txtEmailAddress').val(), channelNo: chNo, channelName: chName, startTime: startTime, duration: duration },
                                    success: function (response) {
                                        showSuccessDialog(response.message);
                                    },
                                    error: function (jqXHR, textStatus, errorThrown) {
                                        $.unblockUI();
                                        showExceptionDialog('Error occurred email-Transcript: ' + jqXHR.responseText);
                                    }
                                });
                            }
                        },
                        {
                            text: langKeys[currentLanguage]['msgBtnClose'],
                            click: function () {
                                $(this).html('');
                                $(this).dialog('close');
                            }
                        }]
                    });
                }
            },
            'print-Transcript': {
                text: langKeys[currentLanguage]['btnPrint'],
                id: 'btnPrintTranscript',
                click: function () {
                    //$(this).closest('.ui-dialog-content').find('#lblInformationDialog').html()
                    var contents = $(this).closest('.ui-dialog-content').find('.transcript_dialog-contents').html();
                    var frame1 = document.createElement('iframe');
                    frame1.name = "frame1";
                    frame1.style.position = "absolute";
                    frame1.style.top = "-1000000px";
                    document.body.appendChild(frame1);
                    var frameDoc = frame1.contentWindow ? frame1.contentWindow : frame1.contentDocument.document ? frame1.contentDocument.document : frame1.contentDocument;
                    frameDoc.document.open();
                    frameDoc.document.write('<html><head><title>Call Transcription from Revcord</title>');
                    frameDoc.document.write('</head><body>');
                    frameDoc.document.write(contents);
                    frameDoc.document.write('</body></html>');
                    frameDoc.document.close();
                    setTimeout(function () {
                        window.frames["frame1"].focus();
                        window.frames["frame1"].print();
                        document.body.removeChild(frame1);
                    }, 500);
                    return false;
                }
            },
            'close': {
                text: langKeys[currentLanguage]['msgBtnClose'],
                class: 'transcript-close',
                click: function () {
                    $(this).dialog("close");
                    //$(".transcript_dialog").dialog("destroy");
                    $(this).dialog('destroy').remove();
                }
            }
        }
    });
    //transcript_dialog.html(InfoMessage);
}
function validateEmail($email) {
    return emailReg.test($email);
}
function IsEmail(email) {
    var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    return regex.test(email);
}
//#endregion Transcription

//#region PlayList

function showExportDialog() {
    var optionString = '<div>' +
                            '<div id=\"divErrMsg\" class=\"hide\">' +
                                '<span id=\"lblPlAddItemErrorMsg\" style=\"font-weight:bold; color:#b94a48;\" ></span>' +
                            '</div>' +
                            '<label style=\"margin:3px;\"><input type=\"radio\" name=\"rbAddPlaylistItem\" value=\"paperClip\" checked=\"checked\" /> ' + langKeys[currentLanguage]['cdAttachInFolder'] + '</label>' +
                            '</br>' +
                            '<label style=\"margin:3px;\"><input type=\"radio\" name=\"rbAddPlaylistItem\" value=\"manualFile\" /> ' + langKeys[currentLanguage]['cdInsertIntoPlaylist'] + '</label>' +
                        '</div>';
    var $dialog = $(optionString).dialog({
        autoOpen: false,
        resizable: false,
        title: langKeys[currentLanguage]['cdSelectOption'],//'Select option',
        modal: true,
        width: 300,
        close: function () {
            $(this).dialog('destroy').remove();
        },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnNext'],
                click: function () {
                    var radioGrp = $('input:radio[name="rbAddPlaylistItem"]:checked');
                    if (radioGrp.length == 0) {
                        $(this).find('#divErrMsg').removeClass('hide');
                        $('#lblPlAddItemErrorMsg').html(langKeys[currentLanguage]['emsgSelectOption']);
                        return;
                    }
                    var optionType = radioGrp.val();
                    JL(frontendLogger).info(optionType);
                    switch (optionType) {
                        case 'paperClip':
                            playlistPaperclip();
                            break;
                        case 'manualFile':
                            if (boolIEBrowser) slAddUserFile();
                            else playlist_addfiles();
                            break;
                        default:
                            throw "Invalid ModelType '" + modelType + "'";
                    }
                    $(this).dialog('close');
                }
            }
        ]
    });
    $dialog.dialog('open');
}



function saveplaylist() {
    var playlist = {
        Id: $('#divPlaylist').attr('plID'),
        Name: $("#txtPlaylistName").val(),
        Comments: $('#txtPlComments').val()
    };
    var saveType = playlist.Id > 0 ? 'Update' : 'Insert'; //playlist.Id > 0 ? 1 : 0;
    var oldPlName = playlist.Id > 0 ? $("#ulPlaylists > li[id='" + playlist.Id + "']").find('a').html() : '';
    $.ajax({
        type: 'POST',
        url: plHandlerURL,
        data: { operation: saveType, playlist: JSON.stringify(playlist), method: 'SavePlaylist', oldPlName: oldPlName },
        success: function (response) {
            switch (response.success) {
                case true:
                    updatePlView(playlist.Id != response.data ? response.data : playlist.Id, playlist.Name);
                    $.growlUI(null, langKeys[currentLanguage]['sPlSave']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred saveplaylist(): ' + jqXHR.responseText);
        }
    });
}

function deletePlaylist(plId) {
    var plName = $("#ulPlaylists > li[id='" + plId + "']").find('a').html();
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: plHandlerURL,
        data: { id: plId, name: plName, method: 'DeletePlaylist', operation: 'Delete' },
        success: function (response) {
            switch (response.success) {
                case true:
                    $("#ulPlaylists > li[id=" + plId + "]").remove(); //$("#ulPlaylists > li > span[id='" + plId + "']").parent().remove();
                    $("#cbxPlayList option[value='" + plId + "']").remove();
                    $.growlUI(null, langKeys[currentLanguage]['sPlDelete']);
                    if ($("#divPlaylists > ul > li").length == 0)
                        $("#divPlaylists #lblNoPlDataMsg").show();
                    //////$("#txtPlaylistName").val('');
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred deletePlaylist(): ' + jqXHR.responseText);
        }
    });
}

function removePlaylistItem(pldId) {
    //JL(frontendLogger).info(pldId); return;
    var plName = $('#cbxLoadPlayList').find(":selected").text(); //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    $.ajax({
        type: 'POST',
        url: plHandlerURL,
        data: { id: pldId, plName: plName, method: 'RemovePlaylistItem', operation: 'Delete' },
        success: function (response) {
            switch (response.success) {
                case true:
                    var callId = $("#tblPlaylist tbody tr[id='" + pldId + "']").attr('callId');
                    if (boolIEBrowser)
                        slDeleteMedia_Silverlight(callId);
                    else
                        slDeleteMedia(callId);
                    //$("#tblPlaylist tbody tr[id='" + pldId + "']").remove();
                    playlistTable.row("[callid='" + callId + "'").remove();
                    $.growlUI(null, langKeys[currentLanguage]['sPldItemDel']);

                    var existingItems = parseInt($("#ulPlaylists > li.active").attr('noofitems'), 10);
                    var newItems = existingItems - 1;
                    $("#ulPlaylists > li.active").attr('noofitems', newItems);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred removePlaylistItem(): ' + jqXHR.responseText);
        }
    });
}
function removePlaylistUserItem(callId) {
    //JL(frontendLogger).info(pldId); return;
    var plName = $('#cbxLoadPlayList').find(":selected").text(); //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    $.ajax({
        type: 'POST',
        url: plHandlerURL,
        data: { callId: callId, plName: plName, method: 'RemoveUserPlaylistItem' },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI(null, langKeys[currentLanguage]['sPldItemDel']);
                    $("#tblPlaylist tbody tr[callid='" + callId + "']").remove();
                    if (boolIEBrowser)
                        slDeleteMedia_Silverlight(callId);
                    else
                        slDeleteMedia(callId);

                    var existingItems = parseInt($("#ulPlaylists > li.active").attr('noofitems'), 10);
                    var deletedItems = response.data;
                    var newItems = existingItems - deletedItems;
                    $("#ulPlaylists > li.active").attr('noofitems', newItems);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred removePlaylistUserItem(): ' + jqXHR.responseText);
        }
    });
}

function removePlaylistItems(plId) {
    //JL(frontendLogger).info(plId); return;
    var plName = $('#cbxLoadPlayList').find(":selected").text(); //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    $.ajax({
        type: 'POST',
        url: plHandlerURL,
        data: { plId: plId, plName: plName, method: 'RemovePlaylistItems', operation: 'Delete' },
        success: function (response) {
            switch (response.success) {
                case true:
                    $("#tblPlaylist > tbody > tr").each(function () {
                        $(this).remove();
                    });
                    // $('#plList').html('No data available.'); ==> KM on 20180409
		    displaylink();
                    if (boolIEBrowser) {
                        slReset_Silverlight();
                    }
                    else
                        slReset();
                    $.growlUI(null, langKeys[currentLanguage]['sPldItemsDel']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred removePlaylistItems(): ' + jqXHR.responseText);
        }
    });
}


//For TimeLine View : Arivu

function callTypesEnum() {
    var callTypes = [];
    callTypes.push({
        'NOTSUPPORTED': -1,
        'DSF': 0,
        'WAV': 1,
        'WMA': 2,
        'WMV': 3,
        'ASF': 4,
        'MP3': 5,
        'SMS': 6,
        'EMAIL': 7,
        'SOCIAL': 8,
        'IMAGE': 9,
        'DOCUMENT': 10
    });
    return callTypes;
}

function getSelectedCallsForPl() {
    var plds = [];
    $('#tblCalls tbody tr .tblrowChk:checked').each(function () {
        var $tr = $(this).parent().parent().parent('tr');
        var msgBody = $tr.find('td[abbr="colType"] .callTypeSprite').attr('data-content');
        var fType = $tr.attr('ctype');
        //var st = parseDate("yyyy-mm-dd", $tr.attr('sttime'));
        plds.push({
            'CallId': $tr.attr('id'),
            'FileType': fType,
            'FileName': $tr.attr('fname'),
            'DurationInSec': $tr.attr('cduration'),
            'StartTimeString': $tr.attr('sttime'),
            'MessageBody': msgBody,
            'IsDemoItem': fType == 1 || fType == 6 || fType == 7 ? false : true,
            'RecorderId': $tr.attr('recid')
        });
    });
    //JL(frontendLogger).info(plds);
    return plds;
}


function addItemInPlaylist() {

}

function addItemsInPlaylist(playlistId, plds) {
    //JL(frontendLogger).info(playlistId + '-' + plds);return;
    var plName = $("#ulPlaylists > li[id='" + playlistId + "']").find('a').html();
    $.ajax({
        type: 'POST',
        url: plHandlerURL,
        data: { method: 'AddItemsInPlaylist', playlistId: playlistId, plName: plName, plds: JSON.stringify(plds) },
        success: function (response) {
            switch (response.success) {
                case true:
                    //$.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sPlCallsAdded']);
                    $("#cbxPlayList option:eq(0)").attr("selected", "selected");
                    var existingItems = parseInt($("#ulPlaylists > li[id='" + playlistId + "']").attr('noofitems'), 10);
                    var newlyAddedItems = response.data;
                    var newItems = existingItems + newlyAddedItems;
                    //JL(frontendLogger).info(newItems);
                    //if (plds.length != response.data)
                    $("#ulPlaylists > li[id='" + playlistId + "']").attr('noofitems', newItems);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    //$("#cbxPlayList option:contains(Add to Playlist)").prop('selected', 'selected'); //Select 0 index
                    $('#cbxPlayList').find('option:eq(0)').prop('selected', true);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error in addItemsInPlaylist()' + jqXHR.responseText);
        }
    });
}

function buildPlRow(userItem) {
    var imgMsgToolTip = '';
    if (userItem.FileType == 3 || userItem.FileType == 4 || userItem.FileType == 5 || userItem.FileType == 7) {
        imgMsgToolTip = ' msgBody="' + encodeURIComponent(userItem.sText) + '" data-content="' + userItem.sText + '" rel="popover" data-placement="right" data-original-title="Message"';
    }
    var $tr = $('<tr id="" isuserItem="true" chNum="" chName="" sname="" callid="' + userItem.sCallId + '" ctype="' + userItem.FileType + '" recid="' + userItem.nRecorderId + '" fname="' + userItem.sFileName + '" sttime="' + userItem.StartTimeString + '" cduration="' + userItem.nDurationSec + '" msgBody="' + userItem.sText + '" pages="' + userItem.nPageCount + '">').append(
            $('<td>').html($('#tblPlaylist tbody tr').length + 1),
            $('<td class="delItem">').html('<a id="lnkDelete" class="deleteIcon ml" style="float:left;" href="javascript:void(0);" lkey="imgDelete"></a>'),
            //$('<td>').html('<img id="imgMsg" src="../assets/icons/search/' + userItem.FileType + '.png"/>'),
            $('<td>').html('<img id="imgMsg" src="../assets/icons/search/' + userItem.FileType + '.png"' + imgMsgToolTip + '/>'),
            $('<td>').html(''),
            $('<td>').html(''),
            $('<td>').html(''),
            $('<td>').html(formatStringToDatetime(userItem.StartTimeString)),
            $('<td>').html(formatSecondsAsTime(userItem.nDurationSec)),
            $('<td>').html('<input id="chkPlRetain" class="gridrowChk" type="checkbox" disabled="" value="' + userItem.sCallId + '" onclick="javascript:plRetainCall(this)" name="chkPlRetain">'),
            $('<td>').html(''),
            $('<td>').html('')
        );
    //JL(frontendLogger).info($tr);
    //$tr.find('td:')
    $('#tblPlaylist').append($tr);
    $("#tblPlaylist img").popover({ trigger: "hover", delay: { show: 200, hide: 100 }, html: 'true' });
}
function formatStringToDatetime(dtString) {
    //20130314134436
    //JL(frontendLogger).info(dtString);
    var yr = dtString.substring(0, 4);
    var mon = dtString.substring(4, 6);
    var dy = dtString.substring(6, 8);
    var hr = dtString.substring(8, 10);
    var min = dtString.substring(10, 12);
    var sec = dtString.substring(12, 14);

    var formatedDate = mon + "-" + dy + "-" + yr + " " + hr + ":" + min + ":" + sec;
    //JL(frontendLogger).info(formatedDate);
    return formatedDate;
}
function formatSecondsAsTime(secs) {
    var hr = Math.floor(secs / 3600);
    var min = Math.floor((secs - (hr * 3600)) / 60);
    var sec = Math.floor(secs - (hr * 3600) - (min * 60));
    if (hr < 10) { hr = "0" + hr; }
    if (min < 10) { min = "0" + min; }
    if (sec < 10) { sec = "0" + sec; }
    if (hr) { hr = "00"; }
    //JL(frontendLogger).info(hr + ':' + min + ':' + sec);
    return hr + ':' + min + ':' + sec;
}

function plRetainCall(checkbox) {
    //var recid = $("#tblPlaylist tbody tr[callid=" + callId + "]").attr("recid");
    var $plRow = $(checkbox).closest('tr');
    var recid = $plRow.attr('recid');
    var callId = $plRow.attr('callid');//checkbox.value
    var startTime = $plRow.attr('sttime');
    var chkRetain = $(checkbox).attr('checked') ? true : false;
    if (isECEnabled)
        updateRetainCallByRecorder(recid, callId, chkRetain, startTime);
    else
        updateRetainCall(callId, chkRetain, startTime);
}

function updateRetainCall(callId, retainCall, startTime) {
    //JL(frontendLogger).info(callId + '-' + retainCall);
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data:
            {
                method: 'UpdateRetainStatus'
                , callId: callId
                , retainValue: retainCall
                , startTime: startTime
            },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sCfUpdate']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in updateRetainCall(): ' + jqXHR.responseText);
        }
    });
}

function updatePlRowContents(callId, newVal) {
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data:
            {
                method: 'UpdateCustomFields'
                , dataEntered: newVal
                , callIds: callId
                , fieldName: 'colCallComments'
                , userId: $('#header-login-name').attr('uid')
                //, fieldType:
            },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sCfUpdate']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in updatePlRowContents(): ' + jqXHR.responseText);
        }
    });
}



function updatePlView(id, name) {
    var ddlOp = $("#cbxPlayList option[value='" + id + "']").text();
    if (ddlOp != "") {//Update
        $("#cbxPlayList option[value='" + id + "']").text(name);
        //$("#divPlaylists ul li a id='" + id + "' p strong'").html(name);
        $("#ulPlaylists > li[id='" + id + "']").find('a').html(name);
    }
    else {//Insert
        var option = new Option(name, id);
        $(option).html(name);
        $("#cbxPlayList option:first").after(option); //$("#cbxPlayList").append(option); $(option).insertAfter("#history option:first");
        //$("#cbxLoadPlayList").append(option);//Load playlist
        if ($("#divPlaylists ul").length == 0)
            $("#divPlaylists").append('<ul id="ulPlaylists"></ul>');
        $("#divPlaylists ul").prepend('<li id=' + id + ' title="" comments="" noofitems="0"><a href="javascript:void(0);">' + name + '</a><span id="deletePl"></span></li>');
        $("#lblNoPlDataMsg").hide();
    }
}

function saveTracks() {
    //var plId = $("#ulPlaylists > li > a[class*=active]").attr("id");
    //showConfirmationDialog('Do you want to save track on local drive!', plId, saveTracksByPLId);

    $("#divExportPlaylist").dialog("open");
    $("#divExportPlaylist").parent().appendTo($("form:first"));
    return false;
}

function saveTracksByPLId() {
    var plurl = handlersBaseURL + '/VoiceRecHandlers/ExportPlaylistHandler.ashx';
    $("#divExportPlaylist").dialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        title: langKeys[currentLanguage]['msgDlgTitleExportPl'],
        width: '500px',
        dialogClass: 'plSave',
        //show: { effect: 'fade', complete: function () { $(this).find("#txtPlaylistName").focus(); } },
        open: function (event, ui) {
            $(this).parent().find(".ui-dialog-titlebar-close").hide();
            $('#chkProtectFromChange').attr('checked', false);

            videoPlayerIssue('open');
        },
        close: function () {
            videoPlayerIssue('close');
        },
        buttons: [
        {
            id: "button-PlSave",
            text: langKeys[currentLanguage]['msgBtnExport'],
            click: function () {
                var radioExportType = $('input:radio[name="rbExportPlaylist"]:checked');
                var protectChange = $('#chkProtectFromChange').prop('checked');
                if (radioExportType.length == 0) {
                    $('#lblExportPlaylistMsg').html(langKeys[currentLanguage]['emsgPlSelectExportType'] + " </br>").show();
                    return;
                }
                var totalRows = $("#tblPlaylist tbody tr").length;
                if (totalRows == 0) {
                    $('#lblExportPlaylistMsg').html(langKeys[currentLanguage]['emsgPlNoRecordInPL'] + " </br>").show();
                    return;
                }
                if ($("#tblPlaylist tbody tr").length) {
                    var plId = $("#ulPlaylists > li.active").attr("id");
                    var userId = $('#header-login-name').attr('uid');
                    if (plId > 0) {
                        $('#lblExportPlaylistMsg').html('').hide();
                        // Disable the buttons
                        $('#button-PlSave').attr('disabled', true);
                        $('#button-PlClose').attr('disabled', true);

                        $.ajax({
                            type: 'POST',
                            url: plurl,
                            data:
                                {
                                    MethodName: 'StartExportProcess'
                                    , uniqueId: uniqueId
                                    , playlistId: plId
                                    , userId: userId
                                    , exportMode: $('#hdnActiveTab').val()
                                    , exportType: radioExportType.val()
                                    , protectFromChange: protectChange
                                    , lvExportType: 'slPlayer'//'htmlPlayer'//
                                },
                            success: function (response) {
                                //$('#statusBorder').show();
                                //$('.ui-dialog-buttonpane button:first').addClass('ui-state-disabled');
                                zipFileName = response;
                                getStatus();
                                //updateProgressBar(1, $('#divExportprogressBar'));
                            },
                            error: function (response) {
                                //alert(response.statusText);
                            }
                        });
                    }
                }
                else {
                    $('#lblExportPlaylistMsg').html(langKeys[currentLanguage]['emsgPlNoExportableMedia'] + " </br>").show();
                }
                $(".ui-dialog").addClass("plSave");
            }
        },
        {
            id: "button-PlClose",
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $('#lblExportPlaylistMsg').html('').hide();
                $('#divExportPlaylist').find('input:radio:checked').removeAttr('checked');
                updateProgressBar(0, $('#divExportprogressBar'));
                //$('#divExportprogressBar').hide();
                $(this).dialog("close");
            }
        }
        ]
    });
    $("#divExportPlaylist").parent().appendTo($("form:first"));
}

function getStatus() {
    var plurl = handlersBaseURL + '/VoiceRecHandlers/ExportPlaylistHandler.ashx';
    $.ajax({
        type: "GET",
        url: plurl,
        data: { 'MethodName': 'GetCurrentProgress', 'uniqueId': uniqueId },
        //ifModified: true,
        //cache: false,
        success: function (response) {
            //JL(frontendLogger).info(response);
            updateProgressBar(response, $('#divExportprogressBar'));
            if (response != "100") {
                //$('#status').html(response);$('#statusFill').width(response);
                window.setTimeout("getStatus()", progressDuration);
            }
            else {
                //$('#status').html("Done");$('#statusBorder').hide();
                $.growlUI(null, langKeys[currentLanguage]['sPlExported']);
                $('#button-PlSave').removeAttr('disabled');
                $('#button-PlClose').removeAttr('disabled');
                window.setTimeout(function () {
                    downloadFile(zipFileName);
                    zipFileName = '';
                }, zipDownloadDur);
            };
        }
    });
}
function downloadFile(flName) {
    window.location = handlersBaseURL + '/VoiceRecHandlers/DownloadFile.ashx' + "?filename=" + flName;
    //logging
    var plId = $("#ulPlaylists > li.active").attr("id");
    var plName = $("#ulPlaylists > li.active").find('a').html();
    var userId = $('#header-login-name').attr('uid');
    var radioExportType = $('input:radio[name="rbExportPlaylist"]:checked');
    var protectChange = $('#chkProtectFromChange').prop('checked');

    var msgData = loginUserName + '¶' + plName + '¶' + radioExportType.val();
    var params = { MethodName: 'StartExportProcess', flName: flName + '.zip', playlistId: plId, userId: userId, exportMode: $('#hdnActiveTab').val(), exportType: radioExportType.val(), protectFromChange: protectChange, lvExportType: 'slPlayer' };
    if (protectChange) {
        logActivity(loginUserId, 60, msgData, JSON.stringify(params));
    }
    else {
        logActivity(loginUserId, 61, msgData, JSON.stringify(params));
    }

}
function updateProgressBar(percent, $element) {
    var progressBarWidth = percent * $element.width() / 100;
    $element.find('div').animate({ width: progressBarWidth }, 500).html(percent + " %"); // + " %&nbsp;");
}


function getplaylistDetail(id) {   

    var plName = $("#ulPlaylists > li[id='" + id + "']").find('a').html();
    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: pageURL,
        data: { playlistId: id, plName: plName, action: 'loadPL' },
        //contentType: "application/json; charset=utf-8",
        //dataType: "json",
        success: function (response) {

            var rtext = response.split("¶");
            if (rtext[0] == "Success") {
                $('#plList').html(rtext[1]); //$('#divListview #plList').html(rtext[1]);

                //$('#divListview #plList').css('height', $('#divData').parent().height() - 9); //- $('#divPlSearch').parent().height());
                //$('#divLvVideoPlayer').slideUp(1500);
                $.unblockUI();
                //sortPlListviewTable();
                $("#tblPlaylist img").popover({
                    trigger: "hover",
                    delay: { show: 200, hide: 100 }
                        , html: 'true'
                });
                ////alert($('#divSearch').height());
                changeLayout('playlistMode');


                $("#ulPlaylists > li.active").removeClass("active"); // remove the selected class according to dropdown
                $("#ulPlaylists > li[id='" + id + "']").addClass("active"); // add the selected class according to dropdown
                //
                var cult = $('.dropdown-menu li a[id=' + currentLanguage + ']').attr('cultlang');
                //Commented By Arivu : Should Revoke this..
                //switchLanguage(currentLanguage, cult, false);
                displaylink();
                var msgData = loginUserName + '¶' + plName;
                var params = '{Id:' + id + ',Name:' + plName + '}';
                logActivity(loginUserId, 50, msgData, params);
            }
            else {
                $.unblockUI();
                showErrorDialog(response);
            }
        },
        //error: function (exc) {$.unblockUI();showErrorDialog('Exception occurred: ' + exc);}
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred getplaylistDetail(): ' + jqXHR.responseText);
            $("#ulPlaylists > li.active").removeClass("active");
        }

    });
}



function validateIsSamePlName(plId, plName) {
    ////alert(plId + ' - ' + plName);
    var cPlId = 0;
    var cPlName = '';
    var returnVal = true;

    $("#divPlaylists ul span").each(function () {
        cPlId = $(this).attr('id');
        cPlName = $(this).find('p strong').html();
        //JL(frontendLogger).info(cPlId + " - " + cPlName);
        if (plName == cPlName) {
            returnVal = false;
            return returnVal;
        }
    });
    return returnVal;
}

function haveAudioItemsInsidePL() {
    var returnValue = false;
    var totalRows = $("#tblPlaylist tbody tr").length;
    if (totalRows) {//if (totalRows > 0)
        $("#tblPlaylist > tbody > tr").each(function () {
            if ($(this).attr('ctype') == "1") {
                returnValue = true;
                return true;
            }
        });
    }
    return returnValue;
}

function sortPlListviewTable() {
    $("#tblPlaylist").tablesorter({
        widgets: ['zebra'],
        headers: { 1: { sorter: false }/*, 4: { sorter: false}*/ }
        , textExtraction: function (s) {
            var $el = $(s),
                $img = $el.find('img');
            return $img.length ? $img[0].src : $el.text();
        }
    });
}

function searchPlListviewTable(inputVal) {
    var table = $('#tblPlaylist');
    table.find('tr').each(function (index, row) {
        var allCells = $(row).find('td');
        //if (allCells.length > 0) {
        if (allCells.length) {
            var found = false;
            allCells.each(function (index, td) {
                var regExp = new RegExp(inputVal, 'i');
                if (regExp.test($(td).text())) {
                    found = true;
                    return false;
                }
            });
            if (found == true)
                $(row).show();
            else
                $(row).hide();
        }
    });
}

function loadPlMapCtrl(plId) {
    plURL = 'LoadPlaylistForm.aspx';
    $.ajax({
        type: "POST",
        //url: (plURL + "?Show=" + ctrlName),
        url: plURL,
        //data: { playlistId: plId },
        success: function (html) {
            $("#divMapPlaylist").html(html);
            $("#divMapPlaylist").dialog("open");
        },
        error: function (msg) {
            showErrorDialog('There was a problem while loading playlist map control.' + msg);
        }
    });
}
function makePlMapDialog() {
    $("#divMapPlaylist").dialog({
        //autoOpen: false,
        title: langKeys[currentLanguage]['msgDlgTitleMapPl'],
        width: '99.5%',
        height: $(window).height() - 100, //-23,//'100%',
        modal: false, //true,
        resizable: false,
        position: [0, 0],
        draggable: false
        , beforeClose: function (e, ui) {
            ////alert($(e.currentTarget).parent().attr('id'));
        }
        , open: function () {
            $.unblockUI();
            videoPlayerIssue('open');
        }
        , close: function (e, ui) {
            $('#lblMapPlErrorMsg').hide();
            videoPlayerIssue('close');
        }
    });
}

//#endregion PlayList

//#region Player

function onSilverlightError(sender, args) {
    var appSource = "";
    if (sender != null && sender != 0) {
        appSource = sender.getHost().Source;
    }
    var errorType = args.ErrorType;
    var iErrorCode = args.ErrorCode;
    if (errorType == "ImageError" || errorType == "MediaError") {
        return;
    }
    var errMsg = "Unhandled Error in Silverlight Application " + appSource + "\n";
    errMsg += "Code: " + iErrorCode + "    \n";
    errMsg += "Category: " + errorType + "       \n";
    errMsg += "Message: " + args.ErrorMessage + "     \n";
    if (errorType == "ParserError") {
        errMsg += "File: " + args.xamlFile + "     \n";
        errMsg += "Line: " + args.lineNumber + "     \n";
        errMsg += "Position: " + args.charPosition + "     \n";
    }
    else if (errorType == "RuntimeError") {
        if (args.lineNumber != 0) {
            errMsg += "Line: " + args.lineNumber + "     \n";
            errMsg += "Position: " + args.charPosition + "     \n";
        }
        errMsg += "MethodName: " + args.methodName + "     \n";
    }
    throw new Error(errMsg);
}

var slCtl = null;
function slLoaded(sender, args) {
    slCtl = sender.getHost();
    slCtl.Content.JS2SL.SetRights(saveAndEmail);
}

function slFileSetCulture(cultureName) {
    //JL(frontendLogger).info(cultureName);
    if (typeof slCtl !== "undefined" && slCtl != null) {
        slCtl.Content.JS2SL.SetCulture(cultureName);

    }
}

function slDisableRightClick(e) {
    if (!e) e = window.event;
    if (typeof e.which == "undefined") {
        if (e.button == 2) {
            return false;
        }
    }
    else {
        if (e.which == 3) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }
}

function slDisableRightClick1(e) {
    e.preventDefault();
    return false;
}

// JS 2 SL interfaces
function slAddMedia1_Silverlight(callId, uri, bookmarks) {
    if (noOfPlayingCalls < noOfMaxCallsToPlay) {
        noOfPlayingCalls++;
        slCtl.Content.JS2SL.AddMedia(callId, uri);
        slCtl.Content.JS2SL.PutBookmarksXml(callId, bookmarks);
    }
}

function slAddMedia_Silverlight(callId, uri) {
    slCtl.Content.JS2SL.AddMedia(callId, uri);
}
function slAddInquireMedia(callId, uri, bookmark, interview_details, ftype) {
    slCtl.Content.JS2SL.AddInquireMedia(callId, uri, bookmark, interview_details, ftype);
}

// IGOR Start
function slAddMedia1(callId, uri, bookmarks) {
    if (noOfPlayingCalls < noOfMaxCallsToPlay) {
        noOfPlayingCalls++;
        AddMedia(callId, uri);
    }
}

function slAddMedia(callId, uri, isPictureEvent) {
    AddMedia(callId, uri, isPictureEvent);
}
//IGOR End

///Added by ifuturz for social media
function slAddSocialMedia(callId, uri) {
    AddSocialMedia(callId, uri);
}
///End by ifuturz for social media
///Added by ifuturz for ListView
function slAddMediaListView(callId, uri, sdate, edate) {
    AddMediaListView(callId, uri, sdate, edate);
}
///End by ifuturz for ListView
function slAddQuadMedia(callId, auri, suris) {
    enableQuadView = true;
    slCtl.Content.JS2SL.slAddQuadMedia(callId, auri + "&screen=" + suris[0], suris[1], suris[2], suris[3], enableQuadView);
}

function slAddQuadScreen(callId, suris) {
    enableQuadView = true;
    slCtl.Content.JS2SL.slAddQuadMedia(callId, suris[0], suris[1], suris[2], suris[3], enableQuadView);
}

function slPlayMedia_Silverlight(callId) {
    slCtl.Content.JS2SL.PlayMedia(callId);
}

function slDeleteMedia_Silverlight(callId) {
    noOfPlayingCalls--;
    slCtl.Content.JS2SL.DeleteMedia(callId);
}

function slReset_Silverlight() {
    slCtl.Content.JS2SL.Reset();
}

function slEnableTimeline_Silverlight() {
    slCtl.Content.JS2SL.EnableTimeline(true);
}

function slDisableTimeline_Silverlight() {
    slCtl.Content.JS2SL.EnableTimeline(false);
}

function slHaveVideo_Silverlight() {
    var haveVideo = slCtl.Content.JS2SL.HaveVideo();
    return haveVideo;
    //JL(frontendLogger).info(haveVideo);
}
//IGOR Start

function slPlayMedia(callId) {
    // slCtl.Content.JS2SL.
    if (boolIEBrowser)
        slPlayMedia_Silverlight(callId);
    else
        PlayMedia(callId);
}


function slPlayMedia_Inquire(callId) {
    $('#divSearchVideoPane').show();
    html5player_adjust();
}

function slDeleteMedia(callId) {
    noOfPlayingCalls--;
    DeleteMedia(callId);
}

function slReset() {
    Reset();
}

function slEnableTimeline() {
}

function slDisableTimeline() {
}

function slHaveVideo() {

    if (boolIEBrowser) {
        var haveVideo = slCtl.Content.JS2SL.HaveVideo();
        return haveVideo;
    } else return containsvideo;
}
//IGOR End
function slAddUserFile() {
    var existingItems = parseInt($("#ulPlaylists > li.active").attr('noofitems'), 10);
    if (existingItems <= 99)
        slCtl.Content.JS2SL.ShowAddUserFileDialog();
    else
        showErrorDialog(langKeys[currentLanguage]['emsgPlLimit']);
}

// SL 2 JS interfaces
function slFileEvent_OnSaveBookmark(callId, position, bookmark, saveType) {
    
    var recid = $("#tblCalls tbody tr[id=" + callId + "]").attr("recid");

    JL(frontendLogger).info('Position  ::  ' + position);
    JL(frontendLogger).info('bookmark  ::  ' + bookmark);

    var callType;
    $('#tblCalls tbody tr').each(function () {
        if (callId == $(this).attr('id')) {
            callType = $(this).attr('ctype');
        }
    });
    var jsonStr = {
        PersistType: saveType == 0 ? 'Update' : 'Insert'
    , RecorderId: recid
    , Bookmark: {
        CallId: callId,
        Position: position,
        Text: bookmark
    }
    };
    var data2send = JSON.stringify(jsonStr);
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: $(this).find('form').serialize(),
        //data: { method: 'SaveBookmarkByRecorder', bmRequest: data2send },
        data: { method: isECEnabled ? 'SaveBookmarkByRecorder' : 'SaveBookmark', bmRequest: data2send },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI(null, langKeys[currentLanguage]['sBmSave']);
                    updateRow(callId, response.xmldata, response.csvdata);
                    if (callType == 7) {
                        getInqFileVideoBookmarks(callId);

                    }
                    slCtl.Content.JS2SL.ResumePlay();
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in slFileEvent_OnSaveBookmark(): ' + jqXHR.responseText);
        }
    });
}
function slFileEvent_OnDialogStart() {
    document.onmousedown = slDisableRightClick;
    document.oncontextmenu = new Function("return false");
    var slDiv = document.getElementById('silverlightControlHost');
    //$("#divCallSearchResults").css('z-index', '2');//$("#divCallSearchResults").parent().css('z-index', '2');//$("#divCallSearchResults").parent().parent().css('z-index', '2');
    if (slDiv != null) {
        //slDiv.style.zIndex = 99;
        reOrderzIndex('SLDialogOpen');
    }
    //$("#divMainPlayerContainer").css('z-index', '3');
}
function slFileEvent_OnDialogEnd() {
    var slDiv = document.getElementById('silverlightControlHost');
    if (slDiv != null) {
        //slDiv.style.zIndex = 1;
        reOrderzIndex('SLDialogClose');
    }
    document.onmousedown = null;
    document.oncontextmenu = null;
    //$("#divMainPlayerContainer").css('z-index', '1');
}
function slFileEvent_OnPlay(callId, itemType) {
    callId = callId.trim();
    var callType;
    $('#tblCalls tbody tr input.tblrowChk').closest('td').removeClass('playCol');
    $('#tblCalls tbody tr[id=' + callId + '] input.tblrowChk').closest('td').addClass('playCol');


    if ($('#hdnActiveTab').val() == "Listview") {
        $('#tblPlaylist tbody tr').removeClass('selected');
        $("#tblPlaylist tbody tr[callid='" + callId + "']").addClass('selected');
        if (itemType == 3 || itemType == 9 || itemType == 10) {
            $('#divLvVideoPlayer').slideDown(3000);//slider working
            $('#divListViewTable').stop().animate({ top: 282 }, 3000);
        }
        else {
            $('#divLvVideoPlayer').slideUp(5000);//slider working
            $('#divListViewTable').stop().animate({ top: 0 }, 5000);
        }
    }
    $('#tblCalls tbody tr').each(function () {
        if (callId == $(this).attr('id')) {
            callType = $(this).attr('ctype');
        }
        if (callType == 7) {
            $("#InterviewBookmarkDetails").show();
            $("#divSearchVideoPane").show();
            DisplayInterviewDetails(callId);
            getInqFileVideoBookmarks(callId);
        }
        else if (callType == 6 || callType == 2) {
            $("#InterviewBookmarkDetails").hide();
            $("#divSearchVideoPane").show();
        }
    })
    if ($('#hdnActiveTab').val() == "Search" && itemType == 3) {
        //$('#videoContainerSV #divSearchVideoPlayer').html($('#divLvVideoPlayer').html());
        swapVideoPlayerContents('Search');
        reOrderzIndex('searchVideoView');
        callType = $('#tblCalls tbody tr[id=' + callId + ']').attr('ctype');
        adjustQuadPlayerSize(callId, itemType);
    }
    else
        reOrderzIndex('searchNonVideoView');
}

function slFileEvent_OnPlay_HTML5(callId, itemType) {
    JL(frontendLogger).info('inside slFileEvent_OnPlay_HTML5');
    callId = callId.trim();
    $('#tblCalls tbody tr input.tblrowChk').closest('td').removeClass('playCol');
    $('#tblCalls tbody tr[id=' + callId + '] input.tblrowChk').closest('td').addClass('playCol');

    $('#tblCalls tbody tr').each(function () {
        try {
            if (callId == $(this).attr('id')) {
                JL(frontendLogger).info('callId and id matched = ' + callId);
                callType = $(this).attr('ctype');
                JL(frontendLogger).info('callType = ' + callType);
                if (callType == 7 || callType == "7" || callType == 11 || callType == "11") {
                    if (boolIEBrowser) {
                        $("#InterviewBookmarkDetails").show();
                        DisplayInterviewDetails(callId);
                    }

                    else {
                        $('#divVideoColStyle').removeClass('col-md-12');
                        $('#divVideoColStyle').addClass('col-md-8');
                        $("#divVideoContainer").show();
                        $('#divVideoContent').show();
                        $('#divSearchToolsAndCallRecords').hide();
                        $('#divMediaMetaColStyle').show();
                        $("#divMediaMeta").show();
                        
                        if (callType == 11 || callType == "11") {
                            $('#trPatientName').show();
                            $('#trInterviewer').hide();
                            $('#trInterviewee').hide();
                            $('#trEventNotes').hide();
                            $('#h3PatientDetails').show();
                            $('#h3GeneralVitals').show();
                            getMDEventDetail(callId);
                        }
                        else if (callType == 7 || callType == "7") {
                            $('#trPatientName').hide();
                            $('#trInterviewer').show();
                            $('#trInterviewee').hide();
                            $('#trType').show();
                            $('#trEventNotes').show();
                            $('#h3PatientDetails').hide();
                            $('#h3GeneralVitals').hide();
                            getEventDetail(callId);
                        }
                    }
					//reOrderzIndex("searchVideoView");
                    getInqFileVideoBookmarks(callId);
                }
                else {
                    if (boolIEBrowser)
                        $("#InterviewBookmarkDetails").hide();
                    else
                        $("#divMediaMeta").hide();
                }
            }
        } catch (e) {
            JL(frontendLogger).info("Error in tblCalls tbody tr");
        }
    })
    $('#tblCalls tbody tr').each(function () {
        try {
            if (callId == $(this).attr('id'))
                callType = $(this).attr('ctype');

            if (callType == 2)
                $('#divSearchVideoPane').show();

        } catch (e) {
            JL(frontendLogger).info("Error in tblCalls tbody tr");
        }
    });

    //ADDED BY KM IFUTURZ - FOR BOOKMARKS IN SEARCH > FILE PLAYER - START
    ;
    var $tr = $('#tblCalls tbody tr[id=' + callId + ']').closest('tr');
    ShowBookmarksInPlayerDoubleClick($tr);
    //ADDED BY KM IFUTURZ - FOR BOOKMARKS IN SEARCH > FILE PLAYER - END
}

function slFileEvent_OnPlay_LV(callId, itemType) {
    callId = callId.trim();
    var callType;
    if ($('#hdnActiveTab').val() == "Listview") {
        $('#tblPlaylist tbody tr').removeClass('selected');
        $("#tblPlaylist tbody tr[callid='" + callId + "']").addClass('selected');
        if (itemType == 3 || itemType == 9 || itemType == 10) {
            $('#divLvVideoPlayer').slideDown(3000);//slider working
            $('#divListViewTable').stop().animate({ top: 282 }, 3000);
        }
        else {
            $('#divLvVideoPlayer').slideUp(5000);//slider working
            $('#divListViewTable').stop().animate({ top: 0 }, 5000);
        }
    }
    $('#tblCalls tbody tr').each(function () {
        if (callId == $(this).attr('id')) {
            callType = $(this).attr('ctype');
        }
        if (callType == 7) {
            $("#InterviewBookmarkDetails").show();
            $("#divSearchVideoPane").show();
            DisplayInterviewDetails(callId);
            getInqFileVideoBookmarks(callId);
        }
        else {
            $("#InterviewBookmarkDetails").hide();
        }
    })
    if ($('#hdnActiveTab').val() == "Search" && itemType == 3) {
        //$('#videoContainerSV #divSearchVideoPlayer').html($('#divLvVideoPlayer').html());
        //swapVideoPlayerContents('Search');
        reOrderzIndex('searchVideoView');
        callType = $('#tblCalls tbody tr[id=' + callId + ']').attr('ctype');
        adjustQuadPlayerSize(callId, itemType);
    }
    else
        reOrderzIndex('searchNonVideoView');
}

function slFileEvent_OnStop() {
    //JL(frontendLogger).info('slFileEvent_OnStop');
    reOrderzIndex('searchNonVideoView');
    //$('#tblCalls tbody tr td.chkCallCol').removeClass('playCol');
    $('#tblCalls tbody tr input.tblrowChk').closest('td').removeClass('playCol');

    $('#tblPlaylist tbody tr').removeClass('selected');
}
function slFileEvent_OnDelete(callId, userInitiated) {
    //JL(frontendLogger).info("callid " + callId + " deleted by user: " + userInitiated);
    if (userInitiated && $('#hdnActiveTab').val() == 'Timeline') {
        var pldId = $("#tblPlaylist tbody tr[callid='" + callId + "']").attr('id');
        if (pldId)
            removePlaylistItem(pldId);
        //showConfirmationDialog("Are you sure to delete the item from Playist permanently?", objDel, deletePlItem);
    }
}
function slFileEvent_OnRegisterUserItem(itemInfo) {
    var jsonObj = $.parseJSON(itemInfo);
    addUserItemInPlaylist(jsonObj);
    return 1;
}
function slFileEvent_OnRemoveUserItem(callId) {
    //JL(frontendLogger).info(callId);
    var plName = $('#cbxLoadPlayList').find(":selected").text(); //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    var response = 0;
    $.ajax({
        type: 'POST',
        url: plHandlerURL,
        data: { callId: callId, plName: plName, method: 'RemoveUserPlaylistItem' },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI(null, langKeys[currentLanguage]['sPldItemDel']);
                    $("#tblPlaylist tbody tr[callid='" + callId + "']").remove();
                    var existingItems = parseInt($("#ulPlaylists > li.active").attr('noofitems'), 10);
                    var deletedItems = response.data;
                    var newItems = existingItems - deletedItems;
                    $("#ulPlaylists > li.active").attr('noofitems', newItems);
                    response = 1;
                    return 1;
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    return 0;
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred slFileEvent_OnRemoveUserItem(): ' + jqXHR.responseText);
        }
    });
    return response;
}

function addUserItemInPlaylist(obj) {
    var plName = $('#cbxLoadPlayList').find(":selected").text(); //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    $.ajax({
        type: 'POST',
        url: plHandlerURL,
        data: { method: 'AddCustomItemInPlaylist', playlistId: obj.nPlaylistId, plName: plName, userId: obj.nUserId, userItem: JSON.stringify(obj).replace(/\/Date/g, "\\\/Date").replace(/\)\//g, "\)\\\/") }, //userItem: JSON.stringify(obj) },
        success: function (response) {
            switch (response.success) {
                case true:

                    var plItem = response.data;
                    $.unblockUI();
                    // $('#divAddManualFile').dialog("close");
                    $.growlUI(null, langKeys[currentLanguage]['sPlUserAddedItem']);

                    var existingItems = parseInt($("#ulPlaylists > li.active").attr('noofitems'), 10);
                    var newItems = existingItems + 1;
                    //JL(frontendLogger).info(newItems);
                    $("#ulPlaylists > li.active").attr('noofitems', newItems);

                    var vod = plItem.PlayerItemURI;
                    if (obj.nFileType == 1 || obj.nFileType == 3 || obj.nFileType == 5 || obj.nFileType == 9 || obj.nFileType == 10) {

                        if (isMTEnable == undefined || isMTEnable == false)
                            vod = docURL + vod;
                        else
                            vod = docURL + 'Tenant_' + tenantId + '/' + vod;

                        if ($('#hdnActiveTab').val() == 'Timeline')
                            $('#divTimeline').css('display', 'block');
                    }
                    else
                        vod = clientRootURL + vod;
                    //JL(frontendLogger).info(vod);

                    buildPlRow(plItem);
                    //  slAddMedia(plItem.sCallId, vod);
                    bIsAddUserFileDialog = false;
                    bIsHasFile = false;
                    bIsTLLoadedInAUF = false;
                    bIsDestroyTLInAUF = true;
                    displaylink();

                    //  getplaylistDetail(plItem.nPlaylistId);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error in addUserItemInPlaylist()' + jqXHR.responseText);
        }
    });
}

function updateRow(callId, xmlData, csvData) {
    //$("#tblCalls tbody tr[id='" + callId + "'] td:nth-child(9)").html(csvData);
    //$("#tblCalls tbody tr[id='" + callId + "']").find("input[id=hdnCallBookmarks]").val(xmlData);
    $("#tblCalls tbody tr[id='" + callId + "'] td[abbr='colBookmark']").find("div").html(csvData);
    //$("#tblCalls tbody tr[id='" + callId + "'] td[abbr='colBookmark']").find("input[id=hdnCallBookmarks]").val(xmlData); // COMMENTED BY KM IFUTURZ
    $("#tblCalls tbody tr[id='" + callId + "']").find("input[id=hdnCallBookmarks]").val(xmlData); // ADDED BY KM IFUTURZ
    $("#tblPlaylist > tbody > tr[callid='" + callId + "']").find('input[id=hdnBookmarkXml]').val(xmlData);
    $("#tblPlaylist > tbody > tr[callid='" + callId + "'] td[class=bookmark]").html(csvData);

    //ADDED BY KM IFUTURZ - FOR BOOKMARKS IN SEARCH > FILE PLAYER - START
    var $tr = $('#tblCalls tbody tr[id=' + callId + ']').closest('tr');
    ShowBookmarksInPlayerDoubleClick($tr);
    //ADDED BY KM IFUTURZ - FOR BOOKMARKS IN SEARCH > FILE PLAYER - END
}

//ADDED BY KM IFUTURZ - START
function updateRowBookmarks(callId, xmlData, csvData) {
    $("#tblCalls tbody tr[id='" + callId + "']").find("input[id=hdnCallBookmarks]").val(xmlData);
}
//ADDED BY KM IFUTURZ - STOP

//#endregion Player

//#region PlaceHolders
function ieRemovePlaceHolderText() {
    $("#divCallSearch").find('[placeholder]').each(function () {
        var input = $(this);
        if (input.val() == input.attr('placeholder')) {
            input.val('');
        }
    });
}

function ieAddPlaceHolders() {
    if ($.browser.msie) {
        $('#divCallSearch input[placeholder]').each(function () {
            var input = $(this);
            if (input.val() === '') {
                $(input).val(input.attr('placeholder'));
                $(input).focus(function () {
                    if (input.val() == input.attr('placeholder')) {
                        input.val('');
                    }
                });
                $(input).blur(function () {
                    if (input.val() == '' || input.val() == input.attr('placeholder')) {
                        input.val(input.attr('placeholder'));
                    }
                });
            }
        });
    };
}
//#endregion PlaceHolders

//#region Uplodify

//checks if flash is installed/enabled on the browser
function isFlashEnabled() {
    return true;
}
function playlistPaperclip() {

    if (!isFlashEnabled())
        showErrorDialog(langKeys[currentLanguage]['emsgFlashNotFound'] + "<a href='http://get.adobe.com/flashplayer/' target='_blank'>" + langKeys[currentLanguage]['emsgClickHereToInstall'] + "</a>");
    else {
        bindUploadify();
        $("#divPaperClip").dialog("open");
        var playlistId = $("#ulPlaylists > li.active").attr("id");
        var userId = $('#header-login-name').attr('uid');
        readDirectory(playlistId, userId);
    }
}

$(function () {
    $('#btnAttachFiles').live('click', function () {
        if (!isFlashEnabled())
            showErrorDialog(langKeys[currentLanguage]['emsgFlashNotFound'] + "<a href='http://get.adobe.com/flashplayer/' target='_blank'>" + langKeys[currentLanguage]['emsgClickHereToInstall'] + "</a>");
        else {
            bindUploadify();
            $("#divPaperClip").dialog("open");
            var playlistId = $("#ulPlaylists > li.active").attr("id");
            var userId = $('#header-login-name').attr('uid');
            readDirectory(playlistId, userId);
        }
    });
});

function bindUploadify() {
    $("#divPaperClip").dialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        title: langKeys[currentLanguage]['msgDlgTitlePaperClip'],
        width: '500',
        maxHeight: '400',
        height: '250',
        buttons: [{
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).dialog("close");
            }
        }],
        open: function () {
            videoPlayerIssue('open');
        },
        close: function () {
            videoPlayerIssue('close');
        }
    });
}

function readDirectory(plId, userId) {
    $('#ulUploadedFiles li').remove();
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        data: { methodName: 'getFiles', plId: plId, uId: userId },
        cache: false,
        success: function (response) {
            $.unblockUI();
            switch (response.success) {
                case true:
                    if (response.data.length == 0)//langKeys[currentLanguage]['imsgNoFileUpload']
                        $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    else {
                        buildUploadedFilesList(response.data);
                        $('#ulUploadedFiles > #liNoFileUploaded').remove();
                    }
                    break;
                case false:
                    $.unblockUI();
                    $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in readDirectory(): ' + jqXHR.responseText);
        }
    });

}

function deleteFile(plId, uId, dirName, fileName) {
    //$.blockUI();
    var plName = $('#cbxLoadPlayList').find(":selected").text(); //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        data: { methodName: 'delete', plId: plId, plName: plName, uId: uId, uName: $.trim($('#header-login-name').text()), dirName: dirName, filename: fileName },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $("#ulUploadedFiles > li[id='" + fileName + "']").remove();
                    if ($('#ulUploadedFiles > li').length == 0)
                        $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    $.growlUI(null, langKeys[currentLanguage]['sPlUplFileDel']/*response.message*/);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in deleteFile(): ' + jqXHR.responseText);
        }
    });

}
function deleteAddUserFile(plId, uId, dirName, fileName, lastItem, bIsCurrentUserAddedFile) {//arivu
    //$.blockUI();
    var plName = $('#cbxLoadPlayList').find(":selected").text(); //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        data: { methodName: 'delete_playlistaddfiles', plId: plId, plName: plName, uId: uId, uName: $.trim($('#header-login-name').text()), dirName: dirName, Filename: fileName, AddUserFile: bIsCurrentUserAddedFile },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:

                    $.unblockUI();
                    $("#ulUploadedFiles > li[id='" + fileName + "']").remove();
                    if ($('#ulUploadedFiles > li').length == 0)
                        $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    $.growlUI(null, langKeys[currentLanguage]['sPlUplFileDel']/*response.message*/);
                    bIsHasFile = false;
                    displaylink();
                    //if (lastItem) {
                    //    bIsZeroItem = true;
                    //}
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in deleteFile(): ' + jqXHR.responseText);
        }
    });

}
function buildUploadedFilesList(data) {
    //var $ul = $('<ul></ul>');
    $.each(data, function (key, val) {
        var counter = key + 1;
        //JL(frontendLogger).info(counter);
        $("#ulUploadedFiles").append("<li id='" + val.split(',')[1] + "'>" + counter + ' - ' + val.split(',')[1] + "<img dname='" + val.split(',')[0] + "' fname='" + val.split(',')[1] + "' width='20' height='20' src='../assets/icons/general/trash_24X24.png' style='cursor:pointer;'></li>");
        //var row = ('<li>' + val + '</li>');
        //$("#files").append(row);
    });
}
//#endregion Uplodify


//#region playlist Add Files

//checks if flash is installed/enabled on the browser
function isFlashEnabled() {
    return true;
}
function playlist_addfiles() {

    if (!isFlashEnabled())
        showErrorDialog(langKeys[currentLanguage]['emsgFlashNotFound'] + "<a href='http://get.adobe.com/flashplayer/' target='_blank'>" + langKeys[currentLanguage]['emsgClickHereToInstall'] + "</a>");
    else {
        ShowAddUserFileForm();//FilePlayer.js function
        var playlistId = $("#ulPlaylists > li.active").attr("id");
        var userId = $('#header-login-name').attr('uid');
        readDirectory_addfiles(playlistId, userId);
    }
}

$(function () {
    $('#btnPlaylistAddFiles').live('click', function () {
        if (!isFlashEnabled())
            showErrorDialog(langKeys[currentLanguage]['emsgFlashNotFound'] + "<a href='http://get.adobe.com/flashplayer/' target='_blank'>" + langKeys[currentLanguage]['emsgClickHereToInstall'] + "</a>");
        else {
            bindUploadify_addfiles();
            $("#divPlaylistAddFiles").dialog("open");
            var playlistId = $("#ulPlaylists > li.active").attr("id");
            var userId = $('#header-login-name').attr('uid');
            readDirectory_addfiles(playlistId, userId);
        }
    });
});

function clearSelectedFileinAUF() {
    $("#ulPlaylistAddFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
}

function bindUploadify_addfiles() {
    $("#fluTLAddUserFile").dialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        title: langKeys[currentLanguage]['msgAddUserFile'],
        width: '500',
        maxHeight: '400',
        height: '250',
        buttons: [{
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).dialog("close");
            }
        }],
        open: function () {
            videoPlayerIssue('open');
        },
        close: function () {
            videoPlayerIssue('close');
        }
    });
}

function readDirectory_addfiles(plId, userId) {
    $('#ulUploadedFiles li').remove();
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        data: { methodName: 'getFiles_playlistaddfiles', plId: plId, uId: userId },
        cache: false,
        success: function (response) {
            $.unblockUI();
            switch (response.success) {
                case true:
                    if (response.data.length == 0)//langKeys[currentLanguage]['imsgNoFileUpload']
                        $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    else {
                        buildUploadedFilesList_addfiles(response.data);
                        $('#ulUploadedFiles > #liNoFileUploaded').remove();
                    }
                    break;
                case false:
                    $.unblockUI();
                    $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in readDirectory(): ' + jqXHR.responseText);
        }
    });

}

function deleteFile_addfiles(plId, uId, dirName, fileName) {
    //$.blockUI();
    var plName = $('#cbxLoadPlayList').find(":selected").text(); //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        data: { methodName: 'delete_playlistaddfiles', plId: plId, plName: plName, uId: uId, uName: $.trim($('#header-login-name').text()), dirName: dirName, filename: fileName },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $("#ulUploadedFiles > li[id='" + fileName + "']").remove();
                    if ($('#ulUploadedFiles > li').length == 0)
                        $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    $.growlUI(null, langKeys[currentLanguage]['sPlUplFileDel']/*response.message*/);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in deleteFile(): ' + jqXHR.responseText);
        }
    });

}

function buildUploadedFilesList_addfiles(data) {
    //var $ul = $('<ul></ul>');
    $.each(data, function (key, val) {
        var counter = key + 1;
        //JL(frontendLogger).info(counter);
        $("#ulUploadedFiles").append("<li id='" + val.split(',')[1] + "'>" + counter + ' - ' + val.split(',')[1] + "<img dname='" + val.split(',')[0] + "' fname='" + val.split(',')[1] + "' width='20' height='20' src='../assets/icons/general/trash_24X24.png' style='cursor:pointer;'></li>");
        //var row = ('<li>' + val + '</li>');
        //$("#files").append(row);
    });
}
//#endregion playlist Add Files

//#region Grid Functions

function sortGrid(table, order) {
    var stripChar = function (s, c) {
        var r = "";
        for (var i = 0; i < s.length; i++) {
            r += c.indexOf(s.charAt(i)) >= 0 ? "" : s.charAt(i);
        }
        return r;
    }
    var isNumeric = function (s) {
        var valid = "0123456789.,- ";
        var result = true;
        var c;
        for (var i = 0; i < s.length && result; i++) {
            c = s.charAt(i);
            if (valid.indexOf(c) <= -1) {
                result = false;
            }
        }
        return result;
    }
    var asc = order == "asc";
    var rows = $(table).find("tbody > tr").get();
    var column = $(table).parent(".bDiv").siblings(".hDiv").find("table tr th").index($("th.sorted", ".flexigrid:has(" + table + ")"));
    rows.sort(function (a, b) {
        var keyA = $(asc ? a : b).children("td").eq(column).text().toUpperCase();
        var keyB = $(asc ? b : a).children("td").eq(column).text().toUpperCase();
        if ((isNumeric(keyA) || keyA.length < 1) && (isNumeric(keyB) || keyB.length < 1)) {
            keyA = stripChar(keyA, ", ");
            keyB = stripChar(keyB, ", ");
            if (keyA.length < 1) keyA = 0;
            if (keyB.length < 1) keyB = 0;
            keyA = new Number(parseFloat(keyA));
            keyB = new Number(parseFloat(keyB));
        }
        return keyA > keyB ? 1 : keyA < keyB ? -1 : 0;
    });
    $.each(rows, function (index, row) {
        $(table).children("tbody").append(row);
    });
    $(table).find("tr").removeClass("erow");
    $(table).find("tr:odd").addClass("erow");
    $(table).find("td.sorted").removeClass("sorted");
    $(table).find("tr").each(function () {
        $(this).find("td:nth(" + column + ")").addClass("sorted");
    });
}

function selectAll(e, id) {
    var grid = document.getElementById("tblCalls");
    var cell;

    if (grid.rows.length > 0) {
        for (i = 0; i < grid.rows.length; i++) {
            //var currentRow = grid.rows[i].closest('tr');
            cell = grid.rows[i].cells[0];
            for (j = 0; j < cell.childNodes.length; j++) {
                if (cell.childNodes[j].childNodes[0].type == "checkbox") {

                    cell.childNodes[j].childNodes[0].checked = ($(e).is(':checked'));


                }
            }
        }
    }

    $('#tblCalls tbody tr ').each(function () {

        if (($(e).is(':checked'))) {
            var callId = $(this).attr('id');
            if (boolIEBrowser)
                slDeleteMedia_Silverlight(callId);
            else {
                for (var i in loadQueue) {
                    if (loadQueue[i].sCallId == callId) {
                        slDeleteMedia(callId);
                    }
                }
            }
            $(this).addClass('trSelected');
            $(this).children('td[abbr="colSNo"]').find('a[id=btnEvaluateCall]').remove();
        }
        else {
            $(this).removeClass('trSelected');
        }
        addtoplayer($(this));
    });

    var selectedRows = $('#tblCalls tbody tr input.tblrowChk:checkbox:checked').length;
    if (selectedRows == 0)
        $('.share-event-link').addClass('hide');
    else
        $('.share-event-link').removeClass('hide');


}
function DeSelectHeaderSearch(advanceSearch) {

    if (advanceSearch) {
        $(".gridHeaderChk").prop('checked', false);
    }
    else {
        var grid = document.getElementById("tblCalls");
        var cell;
        var k = 0;
        var checkedRowsCount = 0;
        if (grid.rows.length > 0) {
            for (i = 0; i < grid.rows.length; i++) {
                //var currentRow = grid.rows[i].closest('tr');
                cell = grid.rows[i].cells[0];
                for (j = 0; j < cell.childNodes.length; j++) {
                    if (cell.childNodes[j].childNodes[0].type == "checkbox") {

                        if (cell.childNodes[j].childNodes[0].checked) {
                            checkedRowsCount++;
                        }

                    }
                }
            }

            if (grid.rows.length != checkedRowsCount) {

                $(".gridHeaderChk").prop('checked', false);
            }
            else {
                $(".gridHeaderChk").prop('checked', true);
            }
        }

    }
}

//#endregion Grid Functions

function videoPlayerIssue(dialogStatus) {
    switch (dialogStatus) {
        case 'open':
            $('#jqDialogStatus').attr('jqDialogStatus', 'open');
            //JL(frontendLogger).info($('#divLvVideoPlayer').css('display'));
            if (userBrowser == 'InternetExplorer') {
                if ($('#hdnActiveTab').val() == "Timeline")
                    $('#divTimeline').css('display', 'none'); //$('#divTimeline').hide();
                if ($('#hdnActiveTab').val() == "Listview")
                    $('#divLvVideoPlayer').css('display', 'none');
            }
            break;
        case 'close':
            //JL(frontendLogger).info($('#divLvVideoPlayer').css('display'));
            $('#jqDialogStatus').attr('jqDialogStatus', 'close');
            if (userBrowser == 'InternetExplorer') {
                if ($('#hdnActiveTab').val() == "Timeline" && slHaveVideo())
                    $('#divTimeline').css('display', 'block'); //$('#divTimeline').show();
                var plCallType = $('#tblPlaylist tbody tr.selected').attr('ctype');
                //JL(frontendLogger).info(plCallType);
                if (plCallType == undefined)
                    $('#divLvVideoPlayer').css('display', 'block');
                if ($('#hdnActiveTab').val() == "Listview" && (plCallType == 2 || plCallType == 6 || plCallType == 9 || plCallType == 10)) {
                    $('#divLvVideoPlayer').css('display', 'block');
                }
            }
            break;
        default:
            break;
    }
}

//region Inquire FileVideo Bookmark
function getInqFileVideoBookmarks(callId) {

    deleteBookmarkCallId = callId;
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/GuestHandler.ashx',
        data: $(this).find('form').serialize(),
        data: {
            method: 'FetchInqFileVideoBookmarks',
            CallId: callId,
            TenantId: $('#hdnTenantId').val()
        },
        success: function (response) {

            switch (response.success) {
                case true:
                    $.unblockUI();
                    var data = [];
                    data = response.data;
                    populateBookmarkData(data);
                    getData(data);

                    //ADDED BY KM IFUTURZ - FOR BOOKMARKS IN SEARCH > FILE PLAYER - START
                    try {
                        convertdataToXMLAndUpdateRow(callId, data);
                        ClearBookmarksInPlayer();
                        var $tr = $('#tblCalls tbody tr[id=' + callId + ']').closest('tr');
                        ShowBookmarksInPlayerDoubleClick($tr);
                    } catch (e) {

                    }
                    //ADDED BY KM IFUTURZ - FOR BOOKMARKS IN SEARCH > FILE PLAYER - END

                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (response) {
            switch (response.success) {
                case false:
                    showErrorDialog('There was a problem with your evaluation request.' + response.message);
                    $.unblockUI();
                    break;
            }
        }
    });
}

function populateBookmarkData(data) {
    try {
        $("#divBookmarks").empty();
        if (data != null) {
            data.sort(function (a, b) {
                return a.BookMarkPos - b.BookMarkPos;
            });
            for (var i = 0; i < data.length; i++) {

                var sortedData = [];
                //sortedData = getSortedData(data);
                sortedData = data;
                var hr = "" + Math.floor(sortedData[i].BookMarkPos / 3600);
                var hrlength = hr.length;
                if (hrlength == undefined) {
                    hr = "00";
                }
                else {
                    if (hrlength < 2) {
                        hr = "0" + hr;
                    }
                }
                var min = "" + Math.floor(sortedData[i].BookMarkPos / 60); //parseInt(data[i].BookMarkPos / 60, 10);
                var minlength = min.length;
                if (minlength == undefined) {
                    min = "00";
                }
                else {
                    if (minlength < 2) {
                        min = "0" + min;
                    }
                }
                var sec = "" + Math.floor(sortedData[i].BookMarkPos % 60);//data[i].BookMarkPos % 60;
                var seclength = sec.length;
                if (seclength == undefined) {
                    sec = "00";
                }
                else {
                    if (seclength < 2) {
                        sec = "0" + sec;
                    }
                }

                //sortedData = sortedData.reverse()
                var PosText = hr + ":" + min + ":" + sec;
                PosText = new Date(sortedData[i].BookMarkPos * 1000).toISOString().substr(11, 8);
                var bookmarkText = sortedData[i].BookMarkText;
                var cannedNote = sortedData[i].CannedNote;
                var Markermeasurement = sortedData[i].Markermeasurement;
                var bookmarkTextLen = sortedData[i].BookMarkText.length;
                var BookMarkNotes = sortedData[i].BookMarkNotes;
                var BookMarkNotesLen = sortedData[i].BookMarkNotes.length;
                var isPicture = sortedData[i].IsPicture;
                var pictureFileName = sortedData[i].PictureFileName;
                var eventDateTime = sortedData[i].EventDateTime;
                var eventDate = eventDateTime.substring(0, 8);
                var parentId = "bmParent_" + sortedData[i].ParentId;
                var fullPath = sortedData[i].FullPath;

                if (fullPath != null && fullPath != "undefined" && fullPath != "") {
                    fullPath = fullPath + bookmarkText;
                    bookmarkText = "";  // Deliberately set Bookmark text to empty. Since entire bookmark is already under the FullPath
                    var primaryArea = fullPath.split(";;")[0];
                    var path = fullPath.split(";;")[1];

                    if ($("#" + parentId).length == 0) {
                        var html = buildHTMLForGBMTitle(parentId, primaryArea, path);       //Title
                        $("#divBookmarks").append(html);
                    }
                    if ($("#" + parentId).length == 1) {
                        if ($("#" + parentId).find("#" + parentId).length == 0) {
                            var html = buildHTMLForGBMContents(parentId, path, false, false);       //Table
                            $("#" + parentId).find(".bm-contents").append(html);
                        }
                        if ($("#" + parentId).find("table#" + parentId).length == 1) {
                            var html = html = "<tr id='" + sortedData[i].Id + "' fullPath='" + path + "'><td style='display:none;'></td><td style='display:none;'></td><td colspan=5 style='font-weight:bold; font-style:italic;'>" + path + "</td></tr>";
                            $("#" + parentId).find("table#" + parentId).append(html);

                            var row = "";
                            var bmTime = $("#divBookmarks table[id='" + parentId + "'] tr[posText='" + PosText + "']").length;
                            if (bmTime == 0) {
                                row = "<tr style='border-bottom: 1px solid lightgray;' height=30px id='" + sortedData[i].Id + "' bmNotes='" + BookMarkNotes + "' position='" + sortedData[i].BookMarkPos + "' bookmark='" + sortedData[i].BookMarkText + "' posText='" + PosText + "' Markermeasurement='" + Markermeasurement + "' cannedNote='" + cannedNote + "'><td style='display:none;font-family: Georgia, 'Times New Roman', Times, serif;' > <input type='hidden' id='hdnMarkerId'  />" + sortedData[i].Id
                                + "</td><td width='70'  style='cursor:pointer;' onclick='sendBookmarkPosition(this);'>" + PosText + "</td><td style='cursor:pointer;' onclick='sendBookmarkPosition(this);'width='200' >"
                                + bookmarkText + "</td>"
                                + (isPicture == true ? "<td style='cursor:pointer; text-align: center; width: 20px;'><img class='bmicon' style='background:url(../assets/icons/monitor/bm-icon.png)  center center no-repeat; background-size:contain; width:16px; height:16px' src='" + inquireURL + '/' + eventDate + '/' + pictureFileName + "' fileName='" + pictureFileName + "' eventDate='" + eventDate + "' title='" + langKeys[currentLanguage]['lmViewPicture'] + "' onclick='viewBookmarkPicture(this);'/></td>" : "<td style='cursor:pointer; text-align: center; width: 20px;'></td>")
                                + "<td style='display:none;'><input id='hdnBookmark' type='hidden'/>"
                                + bookmarkText + "</td><td style='display:none;'><input id='hdnPlaybakPosition' type='hidden'/>"
                                + sortedData[i].BookMarkPos + "</td>"
                                + (BookMarkNotes.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='Notes' src='../assets/icons/um/notes.jpg' onclick='viewBookmarkNotes(this)';>" : "<td width=10px; style='cursor:pointer;'>") //"<td width=10px; style='cursor:pointer;'><img title='Notes' src='../assets/icons/um/notes.jpg' onclick='viewBookmarkNotes(this)';>"
                                + (Markermeasurement.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='" + langKeys[currentLanguage]['InqBookmarkMarkerMeasurement'] + "' src='../assets/icons/monitor/IQ3Measurement.png' onclick='viewMarkermeasurement(this)';>" : "<td width=10px; style='cursor:pointer;'>")
                                + (cannedNote.length > 0 ? "</td><td width=10px; style='cursor:pointer;'><i title='Canned Notes' class='fa fa-1-2x fa-book' style='padding-top: 4px;' onclick='viewBookmarkCannedNotes(this)';>" : "")
                                + "</td></tr>";
                                $("#" + parentId).find("table#" + parentId).append(row);
                            }
                        }
                    }
                }
                else {
                    if ($("#" + parentId).length == 0) {
                        var html = buildHTMLForGBMTitle(parentId, "", "");
                        $("#divBookmarks").append(html);
                        var tableHtml = buildHTMLForGBMContents(parentId, "", false, false);
                        $("#" + parentId).find(".bm-contents").append(tableHtml);
                    }
                    var bmTime = $("#divBookmarks table[id='" + parentId + "'] tr[posText='" + PosText + "']").length;
                    if (bmTime == 0) {
                        if (bookmarkTextLen < 18) {
                            $(".bm-contents table[id='" + parentId + "']").append("<tr height=30px id='" + sortedData[i].Id + "' position='" + sortedData[i].BookMarkPos + "' bmnotes='" + sortedData[i].BookMarkNotes + "' bookmark='" + sortedData[i].BookMarkText + "' posText='" + PosText + "' Markermeasurement='" + Markermeasurement + "' cannedNote='" + cannedNote + "' cannedNote='" + cannedNote + "'>"
                            + "<td style='display:none;font-family: Georgia, 'Times New Roman', Times, serif;' > <input type='hidden' id='hdnMarkerId'  />" + sortedData[i].MarkerId + "</td>"
                            + "<td width='70'  style='cursor:pointer;' onclick='sendBookmarkPosition(this);'>" + PosText + "</td>"
                            + "<td style='cursor:pointer;' onclick='sendBookmarkPosition(this);'width='200' >" + sortedData[i].BookMarkText + "</td>"
                            + (BookMarkNotesLen > 0 ? "<td style='cursor:pointer; vertical-align: middle; text-align: center; width: 20px;'><img class='bmicon' style='margin-bottom:2px;' src='../assets/icons/um/notes.jpg' bmnotes='" + BookMarkNotes + "' title='" + langKeys[currentLanguage]['InqBookmarkNotes'] + "' onclick='viewBookmarkNotes(this);'/></td>" : "")
                            + (Markermeasurement.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='" + langKeys[currentLanguage]['InqBookmarkMarkerMeasurement'] + "' src='../assets/icons/monitor/IQ3Measurement.png' onclick='viewMarkermeasurement(this)';>" : "<td width=10px; style='cursor:pointer;'>")
                            + (cannedNote.length > 0 ? "<td width=25px; style='cursor:pointer;'><i title='Canned Notes' class='fa fa-1-2x fa-book' style='padding-top: 4px;' onclick='viewBookmarkCannedNotes(this)';></td>" : "<td width=25px; style='cursor:pointer;'></td>")
                            + (isPicture == true ? "<td style='cursor:pointer; text-align: center; width: 100px;'><img class='bmicon' style='border: 1px solid black; margin-bottom:2px;' src='" + inquireURL + '/' + eventDate + '/' + pictureFileName + "' fileName='" + pictureFileName + "' eventDate='" + eventDate + "' title='" + langKeys[currentLanguage]['lmViewPicture'] + "' onclick='viewBookmarkPicture(this);'/></td>" : "<td style='cursor:pointer; text-align: center; width: 20px;'></td>")
                            + "<td style='display:none;'><input id='hdnBookmark' type='hidden'/>" + bookmarkText + "</td>"
                            + "<td style='display:none;'><input id='hdnPlaybakPosition' type='hidden'/>" + sortedData[i].BookMarkPos + "</td>"
                            + " </tr>");
                        }
                        else {
                            var truncatedBookmarkText = bookmarkText.substring(0, 17);
                            var NewBookmarkText = truncatedBookmarkText + ".."
                            $(".bm-contents table[id='" + parentId + "']").append("<tr height=30px id='" + sortedData[i].Id + "' position='" + sortedData[i].BookMarkPos + "' bmnotes='" + sortedData[i].BookMarkNotes + "' bookmark='" + sortedData[i].BookMarkText + "' posText='" + PosText + "' Markermeasurement='" + Markermeasurement + "' cannedNote='" + cannedNote + "' cannedNote='" + cannedNote + "'>"
                            + "<td style='display:none;font-family: Georgia, 'Times New Roman', Times, serif;' > <input type='hidden' id='hdnMarkerId'  />" + sortedData[i].MarkerId + "</td>"
                            + "<td width='70'  style='cursor:pointer;' onclick='sendBookmarkPosition(this);'>" + PosText + "</td>"
                            + "<td style='cursor:pointer;' onclick='sendBookmarkPosition(this);'width='200' white-space='nowrap' title='" + bookmarkText + "' >" + NewBookmarkText + "</td>"
                            + (BookMarkNotesLen > 0 ? "<td style='cursor:pointer; vertical-align: middle; text-align: center; width: 20px;'><img class='bmicon' style='margin-bottom:2px;' src='../assets/icons/um/notes.jpg' bmnotes='" + BookMarkNotes + "' title='" + langKeys[currentLanguage]['InqBookmarkNotes'] + "' onclick='viewBookmarkNotes(this);'/></td>" : "")
                            + (Markermeasurement.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='" + langKeys[currentLanguage]['InqBookmarkMarkerMeasurement'] + "' src='../assets/icons/monitor/IQ3Measurement.png' onclick='viewMarkermeasurement(this)';>" : "<td width=10px; style='cursor:pointer;'>")
                            + (cannedNote.length > 0 ? "<td width=25px; style='cursor:pointer;'><i title='Canned Notes' class='fa fa-1-2x fa-book' style='padding-top: 4px;' onclick='viewBookmarkCannedNotes(this)';></td>" : "<td width=25px; style='cursor:pointer;'></td>")
                            + (isPicture == true ? "<td style='cursor:pointer; text-align: center; width: 100px;'><img class='bmicon' style='border: 1px solid black; margin-bottom:2px;' src='" + inquireURL + '/' + eventDate + '/' + pictureFileName + "' fileName='" + pictureFileName + "' eventDate='" + eventDate + "' title='" + langKeys[currentLanguage]['lmViewPicture'] + "' onclick='viewBookmarkPicture(this);'/></td>" : "<td style='cursor:pointer; text-align: center; width: 20px;'></td>")
                            + "<td style='display:none;'><input id='hdnBookmark' type='hidden'/>" + bookmarkText + "</td>"
                            + "<td style='display:none;'><input id='hdnPlaybakPosition' type='hidden'/>" + sortedData[i].BookMarkPos + "</td>"
                            + " </tr>");
                        }
                    }
                }
            }
        }
    }
    catch (e) {
        console.error(e);
    }
}

function getSortedData(data) {
    var sortedData = [];
    var temp = [];
    for (i = 0; i < data.length; i++) {
        temp.push({
            BookMarkNotes: data[i].BookMarkNotes,
            BookMarkPos: data[i].BookMarkPos,
            BookMarkText: data[i].BookMarkText,
            CallId: data[i].CallId,
            Id: data[i].Id,
            MarkerId: data[i].MarkerId,
            IsPicture: data[i].IsPicture,
            ParentId: data[i].ParentId,
            PictureFileName: data[i].PictureFileName,
            EventDateTime: data[i].EventDateTime,
            FullPath: data[i].FullPath
        });
    }
    sortedData = temp.sort(sortByPosition);
    return sortedData;
}

function sortByPosition(a, b) {
    if (a.BookMarkPos < b.BookMarkPos)
        return -1;
    if (a.BookMarkPos > b.BookMarkPos)
        return 1;
    return 0;
}
var CallId;
function getCallIdForBookmarkAdd(callId) {
    this.CallId = callId;
}
var previousRow = [];
function sendBookmarkPosition(e) {

    if (previousRow.length !== 0) {
        var textPos = previousRow.length - 1;
        previousRow[textPos].removeClass("highlightRow");
    }

    var row = $(e).parent();
    previousRow.push(row);
    //var allText = row[0].innerText.split('~~~~~');

    // var position = allText[1];
    var position = row[0].cells[5].innerText;
    row.addClass("highlightRow");
    //  slCtl.Content.JS2SL.SetPlayBakPosition(position);
    // setPlaybackPosition(position);
    vplayer.currentTime = position;

}

function showInqFileVideoBookmarkAddForm(e) {
    if (boolIEBrowser)
        slCtl.Content.JS2SL.PauseMedia();
    else {
        emptyContents();
        vplayer.pause();
    }

    $("#divFileVideoAddBookmark").mmsDialog({
        autoOpen: true,
        closeOnEscape: true,
        draggable: true,
        resizable: false,
        title: langKeys[currentLanguage]['sFVNewBookmark'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        width: 'auto',
        height: 'auto',
        show: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        },
        hide: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        },
        open: function (event, ui) { $(".ui-dialog-titlebar-close", ui.dialog).hide(); }
    });
    $("#divFileVideoAddBookmark").mmsDialog("option", "buttons", [
        {
            text: langKeys[currentLanguage]['msgBtnSave'],
            click: function () {

                AddInqFileVideoBookmark();

                $(this).dialog("close");
                if (boolIEBrowser)
                    slCtl.Content.JS2SL.ResumePlay();
                else
                    vplayer.play();
                return true;
            }
        },
        {
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).mmsDialog("close");
                $.unblockUI();
                if (boolIEBrowser)
                    slCtl.Content.JS2SL.ResumePlay();
                else
                    vplayer.play();
                return false;
            }
        }
    ]);
}

function ShowEditBookmarkForm() {
    if (boolIEBrowser)
        slCtl.Content.JS2SL.PauseMedia();
    $("#divFPEditBookmark").mmsDialog({
        autoOpen: true,
        closeOnEscape: true,
        draggable: true,
        resizable: false,
        title: langKeys[currentLanguage]['sFVEditBookmark'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        width: 'auto',
        height: 'auto',
        show: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        },
        hide: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        }
    });
    $("#divFPEditBookmark").mmsDialog("option", "buttons", [
        {
            text: langKeys[currentLanguage]['msgBtnSave'],
            click: function () {

                updateFileVideoBookmark();

                $(this).dialog("close");
                if (boolIEBrowser)
                    slCtl.Content.JS2SL.ResumePlay();
                return true;
            }
        },
        {
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).dialog("close");
                $.unblockUI();
                if (boolIEBrowser)
                    slCtl.Content.JS2SL.ResumePlay();
                return false;
            }
        }
    ]);
}
function ShowInqFileVideoNotesForm() {
    if (boolIEBrowser)
        slCtl.Content.JS2SL.PauseMedia();
    $("#divInqFileVideoNotes").mmsDialog({
        autoOpen: true,
        closeOnEscape: true,
        draggable: true,
        resizable: false,
        title: langKeys[currentLanguage]['sBookMarkNotesEdit'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        width: 340,
        height: 230,
        show: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        },
        hide: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        }
    });
    $("#divInqFileVideoNotes").mmsDialog("option", "buttons", [
        {
            text: langKeys[currentLanguage]['msgBtnSave'],
            click: function () {

                updateFileVideoNotes();

                $(this).dialog("close");
                if (boolIEBrowser)
                    slCtl.Content.JS2SL.ResumePlay();
                return true;
            }
        },
        {
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).dialog("close");
                $.unblockUI();
                if (boolIEBrowser)
                    slCtl.Content.JS2SL.ResumePlay();
                return false;
            }
        }
    ]);
}

function RemoveInqFileVideoBookmark(e) {
    var row = $(e).parent().parent();
    //var row = $(e)("#hdnMarkerId").val();
    var markerId = row[0].cells[0].innerText;
    //var allText = row[0].innerText.split('^^^^');

    //var markerId = allText[0];
    showConfirmationDialog(langKeys[currentLanguage]['cmsgDelInqFVBookmark'], markerId, DeleteInqFileVideoBookmark)
}

function DeleteInqFileVideoBookmark(markerId) {

    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/VRHandler.ashx',
        data: $(this).find('form').serialize(),
        data:
        {
            method: 'InqFileVideoDeleteBookmark',
            MarkerId: markerId,
            callId: callId
        },
        success: function (response) {

            switch (response.success) {
                case true:

                    $.unblockUI();

                    getInqFileVideoBookmarks(callId);
                    $.growlUI(null, langKeys[currentLanguage]['sPldItemDel']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (response) {
            switch (response.success) {
                case false:
                    showErrorDialog('There was a problem with your evaluation request.' + response.message);
                    $.unblockUI();
                    break;
            }
        }
    });
}

function EventNotes() {
    if (boolIEBrowser)
        slCtl.Content.JS2SL.PauseMedia();
    else
        vplayer.pause();
    $("#divInquireEventNotes").mmsDialog({
        autoOpen: true,
        closeOnEscape: true,
        draggable: true,
        resizable: false,
        title: langKeys[currentLanguage]['sViewNotesEdit'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        //position: 'center',
        width: 360,
        height: 230,
        show: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        },
        hide: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        },
        adjustOnFullScreen: true,
        beforeClose: function () {
            try {
                if (boolIEBrowser)
                    slCtl.Content.JS2SL.ResumePlay();
                else if (nState == FilePlayerState.Play || nState == FilePlayerState.PendingPlay)
                    vplayer.play();
            } catch (e) {
                JL(frontendLogger).info('Error in inquireeventnotes : ' + e.toString());
            }
        },
        close: function () {
            $(this).mmsDialog("destroy");
        },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnSave'],
                click: function () {

                    //updateFileVideoNotes();
                    saveeventnotes();
                    $(this).mmsDialog("close");

                    return true;
                }
            },
            {
                text: langKeys[currentLanguage]['msgBtnClose'],
                click: function () {
                    $.unblockUI();
                    $(this).mmsDialog("close");
                 
                    return false;
                }
            }
        ]
    });
}

function viewEventNotes() {
    if ($('#hdnEventNotes').val().length)
        showEventNotesDialog('Event Notes', $('#hdnEventNotes').val());
    else
        showEventNotesDialog('Event Notes', 'Notes were not added.');
}

function eventnoteupdateintr(CallId, notestext) {
    $('#tblCalls tbody tr').each(function () {

        if (CallId == $(this).attr('id')) {
            $(this).attr('interview_Notes', notestext);
        }
    });
}
//endregion


///Added by ifuturz for get time in hh:mm:ss formate from sec
function timeFormate(s) {
    var fm = [
          Math.floor(s / 60 / 60) % 24, // HOURS
          Math.floor(s / 60) % 60, // MINUTES
           Math.floor(s) % 60 // SECONDS
    ];
    return $.map(fm, function (v, i) { return ((v < 10) ? '0' : '') + v; }).join(':');
}

function setTimelineIcon(cType, fileExt) {
    try {

        switch (cType) {
            case "1": //Audio
                switch (fileExt.toLowerCase()) {
                    case "dsf":
                        iconCss = "callTypeSprite callType1 call-type-pl";
                        break;
                    case "mp3":
                        if (isUserItemIcon) {
                            iconCss = "callTypeSprite callType1 call-type-pl";
                        }
                        else {
                            iconCss = "callTypeSprite callType1 call-type-pl";
                        }
                        break;
                    case "wav":
                        iconCss = "callTypeSprite callType1 call-type-pl";
                        break;
                }
                break;
            case "2": //Video
                if (isUserItemIcon) {
                    iconCss = "callTypeSprite callType2 call-type-pl";
                } else {
                    iconCss = "callTypeSprite callType6 call-type-pl";
                }
                break;
            case "3": //SMS
                iconCss = "callTypeSprite callType3 call-type-pl";
                break;
            case "4": //Social
                iconCss = "callTypeSprite callType4 call-type-pl";
                break;
            case "5": //Email
                iconCss = "callTypeSprite callType5 call-type-pl";
                break;
            case "6": //Screens
                iconCss = "callTypeSprite callType6 call-type-pl";
                break;
            case "7": //Inquire
                if (fileExt.toLowerCase() == "mov" || fileExt.toLowerCase() == "mp4") {
                    iconCss = "callTypeSprite callType8 call-type-pl";
                }
                else
                    iconCss = "callTypeSprite callType7 call-type-pl";
                break;
            case "8": //Inquire
                iconCss = "callTypeSprite callType7 call-type-pl";
                break;
            case "9": //User Added Image
                // durInSec = 60;
                iconCss = "callTypeSprite callType9 call-type-pl";
                break;
            case "10": //User Added Document
                iconCss = "callTypeSprite callType10 call-type-pl";
                break;
            case "13": //Teams
                iconCss = "callTypeSprite callType13 call-type-pl";
                break;
            default:
                break;

        }
    } catch (e) {
        //alert('setTimelineIcon(cType)' + e.message);
    }
    return iconCss;
}

window.onerror = function (msg, url, line, col, error) {
    // Note that col & error are new to the HTML 5 spec and may not be 
    // supported in every browser.  It worked for me in Chrome.
    var extra = !col ? '' : '\ncolumn: ' + col;
    extra += !error ? '' : '\nerror: ' + error;

    // You can view the information in an alert to see things working like this:
    //JL(frontendLogger).info("Error: " + msg + "\nurl: " + url + "\nline: " + line + extra);

    // TODO: Report this error via ajax so you can keep track
    //       of what pages have JS issues

    var suppressErrorAlert = true;
    // If you return true, then error alerts (like in older versions of 
    // Internet Explorer) will be suppressed.
    return suppressErrorAlert;
};

///Added by ifututz to solve error after merge
function sendStartTime(starttime) {
    var startDateTime = starttime;
    var dateTimeString = vis.moment(startDateTime).format('YYYY-MM-DD');
    var dateString = startDateTime.substring(0, 8);
    //var dateToDisplay = dateString.substring(0, 4) + "-" + dateString.substring(4, 6) + "-" + dateString.substring(6, 8);
    var startTimeString = startDateTime.substring(8, 14);
    var hr = startTimeString.substring(0, 2);
    var mins = startTimeString.substring(2, 4);
    var secs = startTimeString.substring(4, 6);

    $("#txtDateAddUserFile").val(dateTimeString);

    $("#txtHours").val(hr);
    $("#txtMinutes").val(mins);
    $("#txtSeconds").val(secs);

}

function getStartTimeDate(starttime) {
    var startDateTime = starttime;
    var dateTimeString = vis.moment(startDateTime).format('YYYY-MM-DD');
    var dateString = startDateTime.substring(0, 8);
    //var dateToDisplay = dateString.substring(0, 4) + "-" + dateString.substring(4, 6) + "-" + dateString.substring(6, 8);
    var startTimeString = startDateTime.substring(8, 14);
    var hr = startTimeString.substring(0, 2);
    var mins = startTimeString.substring(2, 4);
    var secs = startTimeString.substring(4, 6);
    return dateTimeString + ' ' + hr + ':' + mins + ':' + secs
}

//ADDED BY KM IFUTURZ FOR TOTAL DURATION IN TIMELINE - START
function totalDurationTimeline() {
    var totalTimelineDuration = 0;
    $("#tblPlaylist > tbody > tr").each(function () {
        var durInSec = $(this).attr('cDuration');
        totalTimelineDuration += durInSec * 1000;
        totalduration.html(msToTime(totalTimelineDuration));
    });
}
//ADDED BY KM IFUTURZ FOR TOTAL DURATION IN TIMELINE - END

//ADDED BY KM IFUTURZ TO REMOVE CONTAINS FROM VIDEO PANELS WHILE STOP - START
function removeContainersElementsTL() {
    try {

        for (k = 0; k < 4; k++) {
            //$('#divLvVideoPlayer' + k).html(''); // COMMENTED BY KM IFUTURZ - 30042018.
            $('#divLvVideoPlayer' + k + ' > video')[0].src = ''; // ADDED BY KM IFUTURZ - TO RESOLVE VIDEO PLAY ISSUE AFTER STOP AND PLAY - 30042018.
        }
    } catch (e) { }
}
//ADDED BY KM IFUTURZ TO REMOVE CONTAINS FROM VIDEO PANELS WHILE STOP - STOP

//ADDED BY KM IFUTURZ TO FOR FILE PLAYER LOCALIZATION - START
function SetFilePlayerTitlesLocalization() {
    //alert('SetFilePlayerTitlesLocalization');
    $('#btnPlay').attr('title', langKeys[currentLanguage]['Play']);
    $('#btnBegin').attr('title', langKeys[currentLanguage]['Previous']);
    $('#btnRewind').attr('title', langKeys[currentLanguage]['Rewind']);
    $('#btnStop').attr('title', langKeys[currentLanguage]['Stop']);
    $('#btnFF').attr('title', langKeys[currentLanguage]['FastForward']);
    $('#btnNext').attr('title', langKeys[currentLanguage]['Next']);
    $('#btnVolumedown').attr('title', langKeys[currentLanguage]['Mute']);
    $('#btnVolumeup').attr('title', langKeys[currentLanguage]['MaximumVolume']);
    $('#btnslowplay').attr('title', langKeys[currentLanguage]['SlowSpeed']);
    $('#btnFastplay').attr('title', langKeys[currentLanguage]['FastSpeed']);
    $('#btnLooping').attr('title', langKeys[currentLanguage]['Release']);
    $('#btnSearchSaveCalls').attr('title', langKeys[currentLanguage]['SaveCalls']);
    $('#btnSearchSettings').attr('title', langKeys[currentLanguage]['Settings']);

    //$("input[id='chkSkipSilence']")[0].nextSibling.nodeValue = langKeys[currentLanguage]['SkipSilence'];
}
//ADDED BY KM IFUTURZ TO FOR FILE PLAYER LOCALIZATION - END

// region Evaluate Call

//#region Evaluation Popup

//function showEvaluations(evalIds) {
var evalIds = null;
function showEvaluationsPopup(surveyId, evalCallId, evalUserNum, recId) {



    var logedinUserName = $.trim($('#header-login-name').text());
    var url = EvaluateCallsURL + '?eId=' + evalIds + '&enable=' + "false" + '&evalStatus=' + "InProgress" + '&evaluator=' + logedinUserName + '&recId=' + recId;
    // Create the container and the iframe, and attach them to the body.
    var $popup = $('<div id="popup_divEvaluations" class="dialogBox"></div>').prependTo('body');
    $popup.prepend('<iframe class="dialogIFrame" frameborder="0" marginheight="0" marginwidth="0"></iframe>');

    $popup.children('iframe').attr('src', url);

    $popup.dialog({
        title: langKeys[currentLanguage]['msgDlgTitleQAEval'], bgiframe: true, position: 'center', draggable: true, resizable: false, dialogClass: '', width: $(window).width() - 50, height: $(window).height() - 50, zIndex: 99999, modal: true, stack: true, autoOpen: true,
        open: function () { $.unblockUI(); }
                , close: function (event, ui) {
                    //debugger

                    if (!boolIEBrowser) {
                        $('#videofileplayer').css({ 'height': '365px' });
                        btnStop_Click();
                    }
                    $('#divSelectSurvey').dialog("close");
                }
                , buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {

                        var dlg = $(this);
                        dlg.dialog('close');
                        $('#divSelectSurvey').dialog("close");
                        $.unblockUI();
                    }
                }]
    });
}
function getEvalIds(surveyId, evalCallId, evalUserNum, recId) {

    $.blockUI();
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/EvaluationHandlers/EvaluationHandler.ashx',
        data: $(this).find('form').serialize(),
        data:
                {
                    method: 'getEvaluationId'
                    , SurveyId: surveyId
                    , SurveyName: $('#ddlSurveyFallback option:selected').text()
                    , CallId: evalCallId
                    , AppUserId: evalUserNum
                    , RecId: recId
                },
        success: function (response) {
            switch (response.success) {
                case true:
                    //$.unblockUI();

                    //  $.growlUI(null, langKeys[currentLanguage]['sCallForQAAdded']);
                    //  $("#ddlSurveyFallback")[0].selectedIndex = 0;
                    evalIds = response.data;
                    $.unblockUI();
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (response) {
            switch (response.success) {
                case false:
                    showErrorDialog('There was a problem with your evaluation request.' + response.message);
                    $.unblockUI();
                    break;
            }
        }
    });
}
//#endregion Evaluation Popup

//ADDED BY KM IFUTURZ - START
function convertdataToXMLAndUpdateRow(callId, data) {
    try {

        var doc = document.implementation.createDocument(null, "Root", null);
        $.each(data, function (i, item) {
            var RecordElement = doc.createElement("Record");

            var IdElement = doc.createElement("Id");
            var CallIndexElement = doc.createElement("CallIndex");
            var PosElement = doc.createElement("Pos");
            var TextElement = doc.createElement("Text");
            var NotesElement = doc.createElement("Notes");
            var MarkerIdElement = doc.createElement("Marker");

            var Id = doc.createTextNode(item.Id);
            var CallIndex = doc.createTextNode("2");
            var Pos = doc.createTextNode(item.BookMarkPos);
            var Text = doc.createTextNode(item.BookMarkText);
            var Notes = doc.createTextNode(item.BookMarkNotes);
            var Marker = doc.createTextNode(item.MarkerId);

            IdElement.appendChild(Id);
            CallIndexElement.appendChild(CallIndex);
            PosElement.appendChild(Pos);
            TextElement.appendChild(Text);
            NotesElement.appendChild(Notes);
            MarkerIdElement.appendChild(Marker);

            RecordElement.appendChild(IdElement);
            RecordElement.appendChild(CallIndexElement);
            RecordElement.appendChild(PosElement);
            RecordElement.appendChild(TextElement);
            RecordElement.appendChild(NotesElement);
            RecordElement.appendChild(MarkerIdElement);

            doc.documentElement.appendChild(RecordElement);
        });

        var serializer = new XMLSerializer();
        var xmlString = serializer.serializeToString(doc);

        updateRowBookmarks(callId, xmlString, '');
    } catch (e) {

    }
}
//ADDED BY KM IFUTURZ - STOP

//#region Twilio
// Global Variables to be used for Twilio
var activeRoom;
var previewTracks;
//var identity;
var roomName;
var chatAccessToken = null;
var joinedRoom = null;
var accessTokenDelay = 3600000;
var allUsers = [];
var applicationName = "InquireMD";
var SNNotation = "¶SN¶";
var endPointId = '';
var bChannelJoined = false;
var accessManager;
var activeChannel;
var client;
var bTextConferenceLoaded = false;

function initiateTwilio() {
    JL(frontendLogger).info('Inside initiateTwilio()');
    fingerprint.get(function (endpointId) {
        generateChatAccessToken(applicationName, identity, endpointId);
        endPointId = endpointId;
    });
    getAllUsers();
}

// Access Token For Text Conference
function generateChatAccessToken(appName, identity, endPoint) {
    JL(frontendLogger).info('inside generateChatAccessToken(' + identity + ')');
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: { method: 'GenerateChatAccessToken', AppName: appName, Identity: identity, EndPoint: endPoint },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    //JL(frontendLogger).info(response.message);
                    chatAccessToken = response.data;
                    break;
                default:
                    $.unblockUI();
                    JL(frontendLogger).info(response.message);
                    JL(frontendLogger).info(response.data);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            JL(frontendLogger).fatalException('Error occurred in generateChatAccessToken(identity): ', jqXHR);
            //showExceptionDialog('Error occurred in generateChatAccessToken(identity): ' + jqXHR.responseText);
        }
    });
}

function getAllUsers() {
    JL(frontendLogger).info('inside getAllUsers()');
    $.ajax({
        type: 'POST',
        url: "../Handlers/UserManagerHandlers/UserManagerHandler.ashx",
        data: { method: 'GetAllUsers' },
        cache: false,
        success: function (response) {
            switch (response.Status) {
                case "Success":
                    allUsers = response.Data;
                    break;
                default:
                    JL(frontendLogger).info('An error has occurred while fetching list of users.');
                    allUsers = [];
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            //showExceptionDialog('Error occurred in getAllUsers(): ' + jqXHR.responseText);
            JL(frontendLogger).info('Error occurred in getAllUsers()');
            JL(frontendLogger).info(jqXHR);
        }
    });
}

function getNameByEmailId(emailId) {
    var userName = "";
    for (i = 0; i < allUsers.length; i++) {
        //JL(frontendLogger).info('i = ' + i + ' Id = ' + allUsers[i].UserID.toLowerCase() + ' toFind = ' + emailId.toLowerCase());
        if (allUsers[i].UserID.toLowerCase() == emailId.toLowerCase()) {
            userName = allUsers[i].UserName;
            break;
        }
    }
    return userName;
}

function logInAndLoadTextConference(identity, displayName, roomSid) {
    JL(frontendLogger).info('inside logInAndLoadTextConference');
    if (chatAccessToken) {
        userContext.identity = identity;
        userContext.endpoint = endPointId;
        client = new Twilio.Chat.Client(chatAccessToken, { logLevel: 'debug' });
        accessManager = new Twilio.AccessManager(chatAccessToken);
        accessManager.on('tokenUpdated', function (accessManager) {
            client.updateToken(accessManager.token)
        });
        accessManager.on('tokenExpired', function () {
            generateChatAccessToken(applicationName, identity, endPointId);
            setTimeout(function () {
                accessManager.updateToken(chatAccessToken);
            }, 500);
        });
        client.getChannelBySid(roomSid).then(function (channel) {
            if (channel.status !== 'joined') {
                channel.join().then(loadChannel);
            }
            else
                loadChannel(channel);
        });
    }
    else {
        JL(frontendLogger).fatalException('chatAccessToken is null or undefined.');
    }
}

function loadChannel(channel) {
    JL(frontendLogger).info('inside loadChannel(channel)');
    if (activeChannel) {
        activeChannel.removeListener('messageAdded', buildAndShowMessage);
    }
    activeChannel = channel;

    channel.getMessages(1000).then(function (page) {
        activeChannelPage = page;
        page.items.forEach(buildAndShowMessage);
        channel.on('messageAdded', buildAndShowMessage);
        return channel.getMembers();
    });
}

function buildAndShowMessage(message) {
    JL(frontendLogger).info('inside buildAndShowMessage(message)');
    var isSystemNotification = false;
    if (message.body)
        isSystemNotification = message.body.substring(0, 4) === SNNotation ? true : false;
    if (isSystemNotification) {
        //JL(frontendLogger).info('A system message.');
    }
    else {
        //JL(frontendLogger).info('Not a system message.');
        var senderName = getNameByEmailId(message.author) !== "" ? getNameByEmailId(message.author) : message.author;
        var messageHtml = buildTwilioChatMessageHTML(message.body, senderName, message.timestamp, message.index);
        //$('#messages-list').prepend(messageHtml);
        $('#messages-list').append(messageHtml);
        //$('#messages-list').scrollTop($('#messages-list').prop('scrollHeight'));
    }
}

function buildTwilioChatMessageHTML(messageText, messageSenderId, dateTime, messageId) {
    JL(frontendLogger).info('Inside buildTwilioChatMessageHTML');
    var messageHtml =
                    '<div class="list-group-item msg-line" id="msg-' + messageId + '">' +
                            '<time datetime="' + dateTime + '" class="pull-right-time" style="font-size: small;">'
                                    + getDateTimeFormat(dateTime) +
                            '</time>' +
                            '<p class="list-group-item-text">' +
                                '<strong class="msg-sender">' + messageSenderId + ':</strong> ' +
                                messageText +
                            '</p>'
    '</div>';
    return messageHtml;
}
function getDateTimeFormat(dateTime) {
    JL(frontendLogger).info('inside getDateTimeFormat()');
    now = dateTime;
    year = "" + now.getFullYear();
    month = "" + (now.getMonth() + 1); if (month.length == 1) { month = "0" + month; }
    day = "" + now.getDate(); if (day.length == 1) { day = "0" + day; }
    hour = "" + now.getHours(); if (hour.length == 1) { hour = "0" + hour; }
    minute = "" + now.getMinutes(); if (minute.length == 1) { minute = "0" + minute; }
    second = "" + now.getSeconds(); if (second.length == 1) { second = "0" + second; }
    return hour + ":" + minute + ":" + second + " " + month + "/" + day + "/" + year.slice(-2);
}

function getChatTranscript(eventId, $selector) {
    $.ajax({
        type: 'POST',
        url: guestHandlerURL,
        data: { method: 'GetChatTranscript', EventId: eventId, TenantId: $('#hdnTenantId').val() },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    //JL(frontendLogger).info(response.message);
                    //JL(frontendLogger).info(response.data);
                    //$('#messages-list').html('');
                    $selector.html('');
                    if (response.data != null && response.data.length > 0) {
                        for (var i = 0; i < response.data.length; i++) {
                            var messages = JSON.parse(response.data[i].Transcript);
                            //JL(frontendLogger).info(messages);
                            for (var j = 0; j < messages.length; j++) {
                                //JL(frontendLogger).info(messages[i]);
                                buildAndDisplayMessage(messages[j], $selector)
                            }
                        }
                    }
                    discardDatePartFromChat($selector);
                    break;
                default:
                    $.unblockUI();
                    //JL(frontendLogger).info(response.message);
                    //JL(frontendLogger).info(response.data);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            JL(frontendLogger).fatalException('Error occurred in getChatTranscript(eventId): ', jqXHR);
        }
    });
}

function buildAndDisplayMessage(message, $selector) {
    //JL(frontendLogger).info('inside buildAndDisplayMessage(message)');
    var isSystemNotification = false;
    if (message.Body)
        isSystemNotification = message.Body.substring(0, 4) === SNNotation ? true : false;
    if (isSystemNotification) {
        JL(frontendLogger).info('A system message.');
    }
    else {
        var senderName = getNameByEmailId(message.From) !== "" ? getNameByEmailId(message.From) : message.From;
        var messageHtml = buildChatTranscriptHTML(message.Body, senderName, message.DateCreated, message.Index);
        $selector.append(messageHtml);
    }
}

function buildChatTranscriptHTML(messageText, messageSenderId, dateTime, messageId) {
    //JL(frontendLogger).info('Inside buildChatTranscriptHTML');
    var messageHtml =
                    '<div class="list-group-item msg-line" id="msg-' + messageId + '">' +
                            '<time datetime="' + dateTime + '" class="pull-right-time" style="font-size: small;">'
                                    + formatDate(dateTime) +
                            '</time>' +
                            '<p class="list-group-item-text">' +
                                '<strong class="msg-sender">' + messageSenderId + ':</strong> ' +
                                messageText +
                            '</p>' +
                    '</div>';
    return messageHtml;
}

function formatDate(data) {
    //JL(frontendLogger).info('inside formatDate');
    var dateString = data.substr(6);
    var currentTime = new Date(parseInt(dateString));
    var month = currentTime.getMonth() + 1;
    var day = currentTime.getDate();
    var year = currentTime.getFullYear();
    
    var hours = currentTime.getHours();
    var minutes = currentTime.getMinutes();
    var seconds = currentTime.getSeconds();

    //var time = currentTime.getHours() + ':' + currentTime.getMinutes() + ':' + currentTime.getSeconds();
    var time = (hours.toString().length > 1 ? hours : "0" + hours) + ':' + (minutes.toString().length > 1 ? minutes : "0" + minutes) + ':' + (seconds.toString().length > 1 ? seconds : "0" + seconds);

    return (month.toString().length > 1 ? month : "0" + month) +
    "/" +
    (day.toString().length > 1 ? day : "0" + day) +
    "/" +
    year + " " + time;
}

function discardDatePartFromChat($selector) {
    //$('#pl-messages-list .pull-right-time').each(function ()
    $selector.find('.pull-right-time').each(function () {
        //JL(frontendLogger).info($(this).html().substring(11));
        JL(frontendLogger).info($(this).html());
        $(this).html($(this).html().substring(11));
    });
}
//#endregion Twilio

//#region Full Screen
function fullScreenEventHandlers() {
    var bIEBrowser = checkMSIEBrowser();
    if (!bIEBrowser) {
        $("#silverlightControlHost").css('z-index', '0');
    }
    $(document).off('dblclick', '#divLvVideoPlayer0').on('dblclick', '#divLvVideoPlayer0', function (e) {
        if ($(this).children("video").length) {
            var vPlayer = $(this).children("video");
            //setFullScreen(vPlayer[0]);
            var vidName = vPlayer.attr('src').split('/').pop();
            toggleFullScreen(vidName);
            //toggleFullScreen(vidName, $(this));
        }
        else
            JL(frontendLogger).info('No video element found inside #divLvVideoPlayer0');
    });
    $(document).off('dblclick', '#divLvVideoPlayer1').on('dblclick', '#divLvVideoPlayer1', function (e) {
        if ($(this).children("video").length)
        {
            var vPlayer = $(this).children("video");
            //setFullScreen(vPlayer[0]);
            var vidName = vPlayer.attr('src').split('/').pop();
            toggleFullScreen(vidName);
        }
        else
            JL(frontendLogger).info('No video element found inside #divLvVideoPlayer1');
    });
    $(document).off('dblclick', '#divLvVideoPlayer2').on('dblclick', '#divLvVideoPlayer2', function (e) {
        if ($(this).children("video").length) {
            var vPlayer = $(this).children("video");
            //setFullScreen(vPlayer[0]);
            var vidName = vPlayer.attr('src').split('/').pop();
            toggleFullScreen(vidName);
        }
        else
            JL(frontendLogger).info('No video element found inside #divLvVideoPlayer2');
    });
    $(document).off('dblclick', '#divLvVideoPlayer3').on('dblclick', '#divLvVideoPlayer3', function (e) {
        if ($(this).children("video").length) {
            var vPlayer = $(this).children("video");
            //setFullScreen(vPlayer[0]);
            var vidName = vPlayer.attr('src').split('/').pop();
            toggleFullScreen(vidName);
        }
        else
            JL(frontendLogger).info('No video element found inside #divLvVideoPlayer3');
    });
    $(document).off('dblclick', '#videofileplayer').on('dblclick', '#videofileplayer', function (e) {
        var vPlayer = $(this);
        var existingHeight = $(this).css('height');
        var existingWidth = $(this).css('width');
        //setFullScreen(vPlayer[0]);
        var vidName = getParameterByName('file', vPlayer.attr('src'));
        JL(frontendLogger).info('vidName:' + vidName);
        if (vidName) {
            toggleFullScreen(vidName);
            //toggleFullScreen(vidName, $('#divLvVideoPlayer'));
        }
    });
}
function setFullScreen(vPlayer) {
    try {
        if (vPlayer.requestFullscreen) {
            vPlayer.requestFullscreen();
        } else if (vPlayer.mozRequestFullScreen) {
            vPlayer.mozRequestFullScreen();
        } else if (vPlayer.webkitRequestFullscreen) {
            vPlayer.webkitRequestFullscreen();
        }
    } catch (e) {
        JL(frontendLogger).fatalException("Exception inside setFullScreen: ", e);
    }
}

function toggleFullScreen(vidName) {
    var hfVal = $('#hdnActiveTab').val();

    var fullscreenElement = document.fullscreenElement || document.mozFullScreenElement ||
                document.webkitFullscreenElement || document.msFullscreenElement;
    if (fullscreenElement) {
        exitFullscreen();
    }
    else {
        if (hfVal == 'Timeline') {
            launchFullscreen(document.getElementById('container'));
            //$('#divFsMetadata').removeClass('hide');
            $('#divTimeline').css({ 'height': '100%' });
            $('#divLvVideoPlayer0').css({ 'height': '100%', 'width': '100%' });
            loadEventMetadataByName(vidName);
        }
    }
}

function launchFullscreen(element) {
    if (element.requestFullscreen) {
        $('#div-toggle-metadata').removeClass('hide');
        element.requestFullscreen();
    } else if (element.mozRequestFullScreen) {
        $('#div-toggle-metadata').removeClass('hide');
        element.mozRequestFullScreen();
    } else if (element.webkitRequestFullscreen) {
        $('#div-toggle-metadata').removeClass('hide');
        element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) {
        $('#div-toggle-metadata').removeClass('hide');
        element.msRequestFullscreen();
    } else {
        JL(frontendLogger).info("Fullscreen Unavailable");
    }
}

// Whack fullscreen
function exitFullscreen() {
    var hfVal = $('#hdnActiveTab').val();

    if (document.exitFullscreen) {
        document.exitFullscreen();
    }
    else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
    }
    else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    }
    $('#div-toggle-metadata').addClass('hide');
    $('.toggle-metadata').text('Show Event Details');
    $('#divFsMetadata').addClass('hide');
    $('#divTimeline').css({ 'height': '264px;' });
    $('#divLvVideoPlayer0').css({ 'height': '260px', 'width': '24.4%' });
}

if (document.addEventListener) {
    document.addEventListener('webkitfullscreenchange', exitHandler, false);
    document.addEventListener('mozfullscreenchange', exitHandler, false);
    document.addEventListener('fullscreenchange', exitHandler, false);
    document.addEventListener('MSFullscreenChange', exitHandler, false);
}
function exitHandler() {
    var hfVal = $('#hdnActiveTab').val();
    if (!document.webkitIsFullScreen && !document.mozFullScreen && !document.msFullscreenElement) {
        $('#div-toggle-metadata').addClass('hide');
        $('.toggle-metadata').text('Show Event Details');
        $('#divFsMetadata').addClass('hide');
        $('#divTimeline').css({ 'height': '264px;' });
        $('#divLvVideoPlayer0').css({ 'height': '260px', 'width': '24.4%' });
    }
}
$(function () {
    $('.toggle-metadata').on('click', function () {
        $('#divFsMetadata').css({ 'height': $(window).height() - 35 });
        $('#divFsMetadata').toggleClass('hide');
        if (!$('#divFsMetadata').hasClass('hide')) {
            $(this).text('Hide Event Details');
        }
        else {
            $(this).text('Show Event Details');
        }

    });
});

function loadEventMetadataByName(fileName) {
    var eventId = '';
    $.each(playbackItems, function (k, v) {
        //JL(frontendLogger).info(k +'-'+ k);
        var uri = v.uri;
        var fileParam = uri.split('&')[1];
        var fileParamName = fileParam.split('=')[1];

        if (fileParamName == fileName) {
            eventId = $.trim(v.sCallId);
            return false; // does nothing to stop it
        }
    });
    JL(frontendLogger).info('eventId:' + eventId);

    $('.evt-md-case-no').text('');
    $('.evt-md-case-location').text('');
    $('.evt-md-case-date').text('');
    $('.evt-md-case-Interviewer').text('');
    $('.evt-md-case-Interviewee').text('');
    $('.evt-md-case-Notes').text('');
    $('.evt-md-case-qb-dialogid').text('');
    $("#tbl-evt-md-Bookmarks tbody").html('');
    $('#evt-md-messages-list').html('');


    if (eventId) {
        $.ajax({
            url: handlersBaseURL + '/InquireHandlers/GuestHandler.ashx',
            data: { 'method': 'GetCaseDetails', 'eventId': eventId },
            type: 'POST',
        }).then(function (response) {
            var data = response.data;
            $('.evt-md-case-no').text(data.Event.Id);
            $('.evt-md-case-location').text(data.Event.GPS);
            $('.evt-md-case-date').text(data.Event.DateTime);
            $('.evt-md-case-Interviewer').text(data.Event.Interviewer);
            $('.evt-md-case-Interviewee').text(data.Event.Interviewee);
            $('.evt-md-case-Notes').text(data.Event.Notes);
            $('.evt-md-case-qb-dialogid').text(data.QBDialogId);

            return $.ajax({
                type: 'POST',
                url: handlersBaseURL + '/VoiceRecHandlers/VRHandler.ashx',
                data: { 'method': 'FetchInqFileVideoBookmarks', 'CallId': eventId },
            });
        }).then(function (response) {
            var bmdata = [];
            bmdata = response.data;
            var bmRows = "";
            $.each(bmdata, function (i, item) {
                var bmPosition = item.BookMarkPos;
                var bmText = item.BookMarkText;

                bmRows += "<tr>";
                //bmRows += "<td>" + toTimeString(bmPosition) + "</td>";
                bmRows += "<td class='meta-info-value text-left'>" + toTimeString(bmPosition) + "</td>";
                bmRows += "<td>" + bmText + "</td>";
                bmRows += "</tr>";
            });
            $("#tbl-evt-md-Bookmarks tbody").append(bmRows);
            return $.ajax({
                type: 'POST',
                url: vrHandlerURL,
                data: { 'method': 'GetChatTranscript', 'EventId': eventId },
                cache: false
            });
        }).then(function (response) {
            var $selector = $('#evt-md-messages-list');
            $selector.html('');

            if (response.data != null && response.data.length > 0) {
                for (var i = 0; i < response.data.length; i++) {
                    var messages = JSON.parse(response.data[i].Transcript);
                    //JL(frontendLogger).info(messages);
                    for (var j = 0; j < messages.length; j++) {
                        //JL(frontendLogger).info(messages[i]);
                        buildAndDisplayMessage(messages[j], $selector)
                    }
                }
            }
            discardDatePartFromChat($selector);
        }, function (jqXHR, textStatus, errorThrown) { // Failure
            // hang on this step if the error occur
            showErrorDialog('something went wrong:: loadEventMetadataByName request ::' + textStatus + " - " + errorThrown);
        });        
    }
}

function getPlaylistEventDetail(eventId) {
    if (eventId) {
        $.ajax({
            url: handlersBaseURL + '/InquireHandlers/GuestHandler.ashx',
            data: { 'method': 'GetCaseDetails', 'eventId': eventId },
            type: 'POST',
        }).then(function (response) {
            var data = response.data;
                    $('.pl-case-no').text(data.Event.Id);
                    $('.pl-case-location').text(data.Event.GPS);
                    $('.pl-case-date').text(data.Event.DateTime);
                    $('.pl-case-Interviewer').text(data.Event.Interviewer);
                    $('.pl-case-Interviewee').text(data.Event.Interviewee);
                    $('.pl-case-Notes').text(data.Event.Notes);
                    if (data.Event.Notes.trim() == "" || data.Event.Notes == null || data.Event.Notes == "(null)" || data.Event.Notes == "null") $('.pl-case-Notes').text("N/A");
                    $('.pl-case-qb-dialogid').text(data.QBDialogId);
            //valusetfoeeventnotes(data.Event.Notes);

            return $.ajax({
                type: 'POST',
                url: handlersBaseURL + '/VoiceRecHandlers/VRHandler.ashx',
                data: { 'method': 'FetchInqFileVideoBookmarks', 'CallId': eventId, 'IsGuest': 'true' },
            });
        }).then(function (response) {
            var bmdata = [];
            bmdata = response.data;
            var bmRows = "";
            $.each(bmdata, function (i, item) {
                var bmPosition = item.BookMarkPos;
                var bmText = item.BookMarkText;

                bmRows += "<tr>";
                //bmRows += "<td>" + toTimeString(bmPosition) + "</td>";
                bmRows += "<td class='meta-info-value text-left'>" + toTimeString(bmPosition) + "</td>";
                bmRows += "<td>" + bmText + "</td>";
                bmRows += "</tr>";
            });
            $("#tblPlBookmarkContent tbody").append(bmRows);

            return $.ajax({
                type: 'POST',
                url: vrHandlerURL,
                data: { 'method': 'GetChatTranscript', 'EventId': eventId },
                cache: false
            });
        }).then(function (response) {
            var $selector = $('#pl-messages-list');
            $selector.html('');

            if (response.data != null && response.data.length > 0) {
                for (var i = 0; i < response.data.length; i++) {
                    var messages = JSON.parse(response.data[i].Transcript);
                    //JL(frontendLogger).info(messages);
                    for (var j = 0; j < messages.length; j++) {
                        //JL(frontendLogger).info(messages[i]);
                        buildAndDisplayMessage(messages[j], $selector)
                    }
                }
            }
            discardDatePartFromChat($selector);
        }, function (jqXHR, textStatus, errorThrown) { // Failure
            // hang on this step if the error occur
            showExceptionDialog('Error occurred getPlaylistEventDetail(): ' + jqXHR.responseText);
            //showErrorDialog('something went wrong:: getPlaylistEventDetail request ::' + textStatus + " - " + errorThrown);
        });
    }
}


function getParameterByName(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
        results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, " "));
}


//#endregion

function handleRightPanelCSS() {
}

function calculateVideoFilePlayerHeight() {
    var tempHeight = $(window).height() - $('#logo').height() - $('#divVideoContainer div.widget-title').height() - $('#divFilePlayer').parent().parent().parent().height();
    $('#videofileplayer').css({ 'height': tempHeight - 60 });
}

function adjustEventMetadataWindow() {
}

function resizeViewEventWindow() {
  //  calculateVideoFilePlayerHeight();
    //  adjustEventMetadataWindow();

    let playerHeight = getPlayerHeight();
    let height = $(window).height() - $('#logo').height() - playerHeight - 20;

    $("#divVideoContainer").height(height);
}

function adjustMainPlayer() {
    var width = window.screen.width;
    var height = window.screen.height;
    if (width < 1100 && height < 800) {
        $('#divFilePlayer').css({ 'padding': '10px 0px' });
    }
    else {
        $('#divFilePlayer').css({ 'padding': '10px' });
    }
}

//#region MD Event
function getMDEventDetail(eventId) {
    var rxUrl = handlersBaseURL + '/InquireHandlers/InquireRxHandler.ashx';
    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: rxUrl,
        data: { 'method': 'GetCaseDetails', 'eventId': eventId },
        //dataType: "json",
        success: function (response) {
            //$.unblockUI();
            //JL(frontendLogger).info('Success');
            var data = response.data;
            clearCaseDetails();
            switch (response.success) {
                case true:
                    adjustEventMetadataWindow();
                    handleRightPanelCSS();
                    $('.case-no').text(data.Event.Id);
                    $('.case-location').text(data.Event.GPS);
                    $('.case-date').text(data.Event.DateTime);
                    $('.case-flight-no').text(data.FlightNo);
                    $('.case-qb-dialogid').text(data.QBDialogId);
                    
                    if (data.Patient) {
                        $('.case-patient-name').text(data.Patient.FullName);
                        $('.case-patient-height').text(data.Patient.Height);
                        $('.case-patient-weight').text(data.Patient.Weight);
                        if (data.Patient.DOB != null)
                            $('.case-patient-dob').text(parseDate(data.Patient.DOB, true));
                        $('.case-patient-gender').text(data.Patient.GenderString);
                    }
                    $('.case-complaint').text(data.PrimaryComplaint);
                    $.each(data.PatientVitals, function (key, val) {
                            // Gen Vitals repeating twice , Bug Reported by Sekar 2019/12/17
                        if ($('.case-vitals-info > table > tbody > tr').length < 4) {
                            var rowHtml = '<tr><td class="meta-label text-left">' + val.VitalSign.Name + ':</td><td class="meta-info-value text-left"><span class="">' + val.Reading + '</span></td></tr>';
                            $(".case-vitals-info table tbody").append(rowHtml);
                        }
                    });
                    var drugNames = '', drugImages = '', medNames = '', medImages = '', eegNames = '', eegImages = '';
                    if (data.Drugs) {
                        drugNames = $.map(data.Drugs, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.DrugImages) {
                        drugImages = $.map(data.DrugImages, function (v, i) {
                            return v;
                        }).join(",");
                    }
                    if (data.Medications) {
                        medNames = $.map(data.Medications, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.MedicationImages) {
                        medImages = $.map(data.MedicationImages, function (v, i) {
                            return v;
                        }).join(",");
                    }
                    if (data.EEGs) {
                        eegNames = $.map(data.EEGs, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.EEGImages) {
                        eegImages = $.map(data.EEGImages, function (v, i) {
                            return v;
                        }).join(",");
                    }

                    var dateString = data.CreatedDate.substr(6);//"\/Date(1334514600000)\/".substr(6);
                    var currentTime = new Date(parseInt(dateString));

                    var month = ("0" + (currentTime.getMonth() + 1)).slice(-2);
                    var day = ("0" + currentTime.getDate()).slice(-2);
                    var year = currentTime.getFullYear();
                    var date = year + month + day;

                    var medRow = '<tr><td class="meta-label text-left">Medications:</td><td class="meta-info-value text-left"><span class="case-medications"><a id="lnkVitalMedications" med-names="' + medNames + '" med-images="' + medImages + '" date="' + date + '" href="javascript:void(0);" style="text-decoration: underline;" class="" lkey="">view</a></span></td></tr>';
                    //var drugRow = '<tr><td class="meta-label text-left">Drug Use:</td><td class="meta-info-value text-left"><span class="case-drugs"><a id="lnkVitalDrugs" drug-names="' + drugNames + '" drug-images="' + drugImages + '" href="javascript:void(0);" style="text-decoration: underline;" class="" lkey="">view</a></span></td></tr>';
                    var eegRow = '<tr><td class="meta-label text-left">EEG:</td><td class="meta-info-value text-left"><span class="case-EEGs"><a id="lnkVitalEEG" eeg-names="' + eegNames + '" eeg-images="' + eegImages + '" date="' + date + '" href="javascript:void(0);" style="text-decoration: underline;" class="" lkey="">view</a></span></td></tr>';
                    $('.case-vitals-info > table > tbody').append(medRow);
                    //$('.case-vitals-info > table > tbody').append(drugRow);
                    $('.case-vitals-info > table > tbody').append(eegRow);


                    lastEventId = eventId;
                    
                    var channelSid = $('.case-qb-dialogid').text();
                    if (channelSid) {
                        setTimeout(function () {
                            getChatTranscript(eventId, $('#messages-list'));
                        }, 1000);
                    }
                    else {
                        JL(frontendLogger).info('Error : something went wrong while joining text conference. channelSid = ' + channelSid);
                    }

                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred getEventDetail(): ' + jqXHR.responseText);
        }
    });
}
function parseDate(jsonDate, bOnlyDateRequired) {
    JL(frontendLogger).info('Inside parseDate function.');
    var date = new Date(jsonDate.match(/\d+/)[0] * 1);
    if (bOnlyDateRequired) {
        var dd = date.getDate();
        var mm = date.getMonth() + 1;
        var hh = date.getHours();
        var min = date.getMinutes();
        var sec = date.getSeconds();
        var yyyy = date.getFullYear();
        if (dd < 10)
            dd = '0' + dd;
        if (mm < 10)
            mm = '0' + mm;
        if (hh < 10)
            hh = '0' + hh;
        if (min < 10)
            min = '0' + min;
        if (sec < 10)
            sec = '0' + sec;
        return yyyy + '-' + mm + '-' + dd;
    }
    else
        return date;
}

function getPlayerHeight() {
    var isIE = checkMSIEBrowser();
    var playerHeight;
    if (isIE) {
        playerHeight = 100;
    }
    else
        playerHeight = $('#divFilePlayer').parent().parent().parent().height();
    if (playerHeight == NaN || playerHeight == null)
        playerHeight = 0;
    JL(frontendLogger).info('playerHeight - ', playerHeight);
    return playerHeight;
}

function viewBookmarkPicture(e) {
    destroyDialog("#divInformationDialog");

    var fileName = $(e).attr('fileName');
    var eventDate = $(e).attr('eventDate');
    $("#divInformationDialog").mmsDialog({
        autoOpen: true,
        width: 600,
        height: 400,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['lmBookmarkPicture'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        close: function () {
            $("#lblInformationMessage").html('');
        }
    });

    

    if (tenantId > 0) {
        $("#lblInformationMessage").html('<img class="pictureBM" src="' + inquireURL + '/' + tenantId + '/' + eventDate + '/' + fileName + '" style="height: 100%; width: 100% !important;"/><br/>');
    }
    else {
        $("#lblInformationMessage").html('<img class="pictureBM" src="' + inquireURL + '/' + eventDate + '/' + fileName + '" style="height: 100%; width: 100% !important;"/><br/>');
    }

    $("#divInformationDialog").mmsDialog(
    {
        buttons: [{
            text: langKeys[currentLanguage]['msgBtnOK'],
            click: function () {
                $(this).mmsDialog("close");
                $("#lblInformationMessage").html('');
            }
        }]
    });
}

$('.pictureBM').live('dblclick', function () {
    viewBookmarkPictureInFullScreen(this);
});

function viewBookmarkPictureInFullScreen(e) {
    debugger
    var fileName = $(e).attr('fileName');
    var eventDate = $(e).attr('eventDate');
    if (e.requestFullscreen) {
        e.requestFullscreen();
    }
    else if (e.mozRequestFullScreen) {
        e.mozRequestFullScreen();
    }
    else if (e.webkitRequestFullscreen) {
        e.webkitRequestFullscreen();
    }
    else if (e.msRequestFullscreen) {
        e.msRequestFullscreen();
    }
}

function viewBookmarkNotes(e) {
    destroyDialog("#divInformationDialog");

    var viewBookmarkNotes = $(e).parent().parent().attr('bmnotes');
    $("#divInformationDialog").mmsDialog({
        autoOpen: true,
        width: 400,
        height: 'auto',
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['InqBookmarkNotes'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        close: function () {
            $("#lblInformationMessage").html('');
        }
    });

    //$("#lblInformationMessage").html('<img src="' + inquireURL + '/' + eventDate + '/' + fileName + '" style="height: 100%; width: 100%;"/><br/>');
    $("#lblInformationMessage").html(viewBookmarkNotes);

    $("#divInformationDialog").mmsDialog(
    {
        buttons: [{
            text: langKeys[currentLanguage]['msgBtnOK'],
            click: function () {
                $(this).mmsDialog("close");
                $("#lblInformationMessage").html('');
            }
        }]
    });
}

function viewBookmarkCannedNotes(e) {
    destroyDialog("#divInformationDialog");

    var viewBookmarkCannedNotes = $(e).parent().parent().attr('cannednote');
    $("#divInformationDialog").mmsDialog({
        autoOpen: true,
        width: 400,
        height: 'auto',
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['InqBookmarkCannedNotes'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        close: function () {
            $("#lblInformationMessage").html('');
        }
    });
    $("#lblInformationMessage").html(viewBookmarkCannedNotes);
    $("#divInformationDialog").mmsDialog(
        {
            buttons: [{
                text: langKeys[currentLanguage]['msgBtnOK'],
                click: function () {
                    $(this).mmsDialog("close");
                    $("#lblInformationMessage").html('');
                }
            }]
        });
}

function viewMarkermeasurement(e) {
    destroyDialog("#divInformationDialog");

    var Markermeasurement = $(e).parent().parent().attr('Markermeasurement');
    $("#divInformationDialog").mmsDialog({
        autoOpen: true,
        width: 400,
        height: 'auto',
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['InqBookmarkMarkerMeasurement'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        close: function () {
            $("#lblInformationMessage").html('');
        }
    });
    $("#lblInformationMessage").html(Markermeasurement);
    $("#divInformationDialog").mmsDialog(
        {
            buttons: [{
                text: langKeys[currentLanguage]['msgBtnOK'],
                click: function () {
                    $(this).mmsDialog("close");
                    $("#lblInformationMessage").html('');
                }
            }]
        });
}

function buildHTMLForGBMTitle(parentId, primaryAreaTitle, path) {
    if (primaryAreaTitle == null || primaryAreaTitle == "undefined" || primaryAreaTitle == "")
        primaryAreaTitle = "Bookmark List";
    var html = "<div class='widget widget-blue' id='" + parentId + "'><div class='widget-title'><div class='widget-controls'><a href='#' class='widget-control widget-control-minimize widget-control-minimize-bm' data-toggle='tooltip' data-placement='top' data-original-title='Minimize'><i class='fa fa-minus-circle'></i></a></div><h3 style='background: transparent !important;'><i class='fa fa-bookmark'></i><span>" + primaryAreaTitle + "</span></h3></div><div class='widget-content' style='padding: 0px;'><div class='table-responsive bm-contents'></div></div></div>";
    return html;
}

function buildHTMLForGBMContents(parentId, path, isInternalContent, isBookmark) {
    //var html = "<table id='" + parentId + "' style='table-layout:fixed;'><tr><td colspan=9 style='font-weight:bold; font-style:italic;'>" + path + "</td></tr></table>";
    var html = '';
    if (isInternalContent) {
        if (isBookmark)
            html = "<tr><td style='display:none;'></td><td colspan=5 style='font-weight:bold; font-style:italic;'>BUILD BOOKMARK HTML</td></tr>";
        else {
            html = "<tr fullPath='" + path + "'><td style='display:none;'></td><td style='display:none;'></td><td colspan=5 style='font-weight:bold; font-style:italic;'>" + path + "</td></tr>";
        }
    }
    else
        html = "<table id='" + parentId + "' style='width:100%;'></table>";
    return html;
}

function buildAndDisplayDialog() {
    $('#lblError').text('');
    $('#lblError').val('');
    $('#txtAccessCode').val('');

    var accessCodeDialog = $("#divValidateAccessCode").mmsDialog({
        width: 400,
        height: 220,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['tAccessCode'],
        modal: true,
        open: function () {
            $.unblockUI();
            $("a.ui-new-dialog-titlebar-close").hide();
            $("#txtAccessCode").attr("autocomplete", "off");
            setTimeout(function () {
                $('#txtAccessCode').val('');
            }, 100);
        },
    });

    accessCodeDialog = $("#divValidateAccessCode").mmsDialog("option", "buttons", [
        {
            id: 'btnValidate',
            text: langKeys[currentLanguage]['msgBtnValidate'],
            click: function () {
                var accessCode = $('#txtAccessCode').val();
                if (validateAccessCode(accessCode)) {
                    verifyAccessCode(accessCode)
                }
                else {
                    showInformationDialog(langKeys[currentLanguage]['imsgMinimumLengthForAccessCode']);
                }
                return true;
            }
        }
        //{
        //    text: langKeys[currentLanguage]['msgBtnClose'],
        //    click: function () {
        //        $(this).mmsDialog("close");
        //        return false;
        //    }
        //}
    ]);
    accessCodeDialog.mmsDialog("open");
}

function validateAccessCode(accessCode) {
    if (accessCode.length != 8) {
        $('#div-pwd-error').closest(".form-group").addClass('has-error');
        $('#div-pwd-error').html(langKeys[currentLanguage]['errAccessCode8CharLength']);
        return false;
    }
    return true;
}

function verifyAccessCode(accessCode) {
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    var guestHandlerURL = '../Handlers/InquireHandlers/GuestHandler.ashx'; //  + '?bogusval=' + bogus;

    $.ajax({
        cache: false,
        async: true,
        type: 'POST',
        url: guestHandlerURL,
        data: {
            method: "VerifyInvitationAccessCode",
            AccessCode: accessCode,
            TenantId: $('#hdnTenantId').val(),
            InvitationId: $('#hdnInvitationId').val()
        },
        success: function (response) {
            $('#div-password-errors').addClass('hide').html('');
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, response.message);
                    showOKDialog(langKeys[currentLanguage]['tAccessCode'], response.message, proceedToPageLoad);
                    $("#divValidateAccessCode").mmsDialog("close");
                    break;
                case false:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    return;
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.Message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred function verifyAccessCode ' + jqXHR.responseText);
        }
    });
}

function proceedToPageLoad() {
    loadCallSearchResults(populateGetCriteria($('#hdnEventId').val()), 'GetCallsByEvent');
}

function showEventNotesDialog(InfoTitle, InfoMessage) {
    notesDialog = $("#divInformationDialog").mmsDialog({
        maxWidth: 'auto',
        width: 400,
        height: 'auto',
        resizable: false,
        title: InfoTitle,
        closeText: langKeys[currentLanguage]['ttClose'],
        modal: true,
        adjustOnFullScreen: true,
        closeOnEscape: false, // Disable closing with the Escape key
        open: function (event, ui) {
            $("#lblInformationMessage").html(InfoMessage);
            // Hide the close button
            $(".fa.fa-times-circle.ui-new-dialog-titlebar-close").hide();
        },
        buttons: [{
            text: langKeys[currentLanguage]['msgBtnOK'],
            click: function () {
                notesDialog.mmsDialog("destroy");
            }
        }, {
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                notesDialog.mmsDialog("destroy");
            }
        }]
    });
}