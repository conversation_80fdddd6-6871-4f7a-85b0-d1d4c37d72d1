﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.SurveyEntities
{
    [Serializable]
    public class SurveySection
    {
        #region Properties

        public int Id { get; set; }
        public int RevSyncId { get; set; }
        public string Title { get; set; }
        public int SurveyId { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsDefaultSection { get; set; }
        public int RevSyncSurveyId { get; set; }
        public int TenantId { get; set; }

        #endregion

        #region Associations

        public int NumberOfQuestion { get; set; }

        #endregion

        public object Clone()
        {
            return ObjectUtil.DeepClone(this);
        }

    }
}
