﻿<%@ Page Title="" Language="C#" AutoEventWireup="true" EnableEventValidation="false" CodeBehind="ViewPlaylist.aspx.cs" Inherits="RevCord.VoiceRec.WebUIClient.Invite.ViewPlaylist" %>

<%@ Import Namespace="RevCord.Util" %>
<%@ Import Namespace="RevCord.VoiceRec.WebUIClient.Classes" %>
<%@ Import Namespace="System.IO" %>

<%@ Register TagName="ucFileVideoEditBookmark" TagPrefix="uc3" Src="~/UserControls/InquireControls/ucFileVideoEditBookmark.ascx" %>
<%@ Register TagName="ucFileVideoNotes" TagPrefix="uc4" Src="~/UserControls/InquireControls/ucFileVideoNotes.ascx" %>
<%@ Register TagName="ucFileVideoAddBookmark" TagPrefix="uc5" Src="~/UserControls/InquireControls/ucFileVideoAddBookmark.ascx" %>
<%@ Register TagName="ucEventNotes" TagPrefix="uc6" Src="~/UserControls/InquireControls/ucEventNotes.ascx" %>
<%@ Register TagName="ucFilePlayer" TagPrefix="uc7" Src="../UserControls/PlayerControls/FilePlayer/ucFilePlayer.ascx" %>
<%@ Register TagName="ucFilePlayerAddUserFile" TagPrefix="uc8" Src="~/UserControls/PlayerControls/FilePlayer/ucFilePlayerAddUserFile.ascx" %>

<%@ Register TagName="PictureEvent" TagPrefix="TInquire" Src="~/UserControls/InquireControls/ucPictureEvent.ascx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>

    <link rel="shortcut icon" href="../assets/images/favicon.ico" />
    <link rel='stylesheet' href='../assetsNew/css/plugins/fullcalendar.css'/>
    <%--<link rel='stylesheet' href='../assetsNew/css/plugins/datatables/datatables.css'/>--%>
    <%--<link rel='stylesheet' href='../assetsNew/css/plugins/datatables/bootstrap.datatables.css'/>--%>
    <link rel='stylesheet' href='../assetsNew/css/plugins/chosen.css'/>
    <link rel='stylesheet' href='../assetsNew/css/plugins/jquery.timepicker.css'/>
    <link rel='stylesheet' href='../assetsNew/css/plugins/daterangepicker-bs3.css'/>
    <link rel='stylesheet' href='../assetsNew/css/plugins/colpick.css'/>
    <%--<link rel='stylesheet' href='../assetsNew/css/plugins/dropzone.css'>--%>
    <link rel='stylesheet' href='../assetsNew/css/plugins/jquery.handsontable.full.css'/>
    <link rel='stylesheet' href='../assetsNew/css/plugins/jscrollpane.css'/>
    <link rel='stylesheet' href='../assetsNew/css/plugins/jquery.pnotify.default.css'/>
    <link rel='stylesheet' href='../assetsNew/css/plugins/jquery.pnotify.default.icons.css'/>
    <link rel="stylesheet" href="../assets/css/jQuery/jquery-ui-custom-mmsDialog.css" />
    <link rel='stylesheet' href='../assetsNew/css/app.css'/>
    <link rel="stylesheet" href="../assets/css/colorPick.css" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/DropzoneJS/assets/basic.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/DropzoneJS/assets/dropzone.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />

    <link href='https://fonts.googleapis.com/css?family=Roboto:100,300,400,700|Roboto+Condensed:300,400,700' rel='stylesheet' type='text/css'/>

    <%--<link href="../assetsNew/favicon.ico" rel="shortcut icon"/>--%>
    <%--<link href="../assetsNew/apple-touch-icon.png" rel="apple-touch-icon"/>--%>

    <%= JSNLog.JavascriptLogging.Configure() %>

    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery-1.8.2.min.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery-ui-custom-mmsDialog.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/Scripts/jsnlog.min.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/documentReady.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.blockUI.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.cookie.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/Store_JS/store.min.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/Store_JS/store_json2.min.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src='<%= Page.ResolveClientUrl("~/assets/js/localization/lang.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>' type="text/javascript"></script>
    <script type="text/javascript" src="<%=this.GetGoogleMapJsUrl %>"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/maps/markerclusterer_compiled.js")+ AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/maps/mapwithmarker.js")+ AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>

    <link rel="stylesheet" href="<%= Page.ResolveClientUrl("~/Templates/IQ3StandardReport/style.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel="stylesheet" href="<%= Page.ResolveClientUrl("~/Templates/IQ3StandardReport/lightbox.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/css/tables/jquery.dataTables.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/css/tables/colReorder.dataTables.min.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/css/tables/responsive.dataTables.min.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />

    <script src="<%= Page.ResolveClientUrl("~/assets/js/tables/jquery.dataTables.Search.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/tables/dataTables.colReorderWithResize.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/common/colorPick.js") %>" type="text/javascript"></script>
    <script src='<%= Page.ResolveClientUrl("~/assets/js/pages/IQ3InspectionReport.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>' type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/css/tableStyle_Modified.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/css/tables/jquery.dataTables.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/css/tables/colReorder.dataTables.min.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/css/tables/responsive.dataTables.min.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <%--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"/>--%>
    <script src="<%=Page.ResolveClientUrl("../assets/js/common/dialogManager.js") + AppSettingsUtil.GetString("versionNo", "?version=") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    
    <script src="<%= Page.ResolveClientUrl("~/assets/js/TimelineView/custom.js")+ AppSettingsUtil.GetString("versionNo", "?version=") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/TimelineView/timeline.js")+ AppSettingsUtil.GetString("versionNo", "?version=") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/TimelineView/underscore-min.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <link href="../assets/css/TimelineView/vis.css" rel="stylesheet" type="text/css" />

    <link href="<%= Page.ResolveClientUrl("~/assets/css/jQuery/jquery.checkboxtree.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" rel="stylesheet" type="text/css" />
    <link href="<%= Page.ResolveClientUrl("~/assets/css/jQuery/jquery.tooltip.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" rel="stylesheet" type="text/css" />
    <link href="<%= Page.ResolveClientUrl("~/assets/js/uploadify/uploadify.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" rel="stylesheet" type="text/css" />
    <script src="<%= Page.ResolveClientUrl("~/assets/js/ajax/xmlhttpRequestionMethod.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.checkboxtree.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.tooltip.min.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/uploadify/jquery.uploadify.v2.1.4.min.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/uploadify/swfobject.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/AES/pbkdf2.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/AES/aes.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src='<%= Page.ResolveClientUrl("~/assets/js/common/revview.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>' type="text/javascript"></script>
    <script src='<%= Page.ResolveClientUrl("~/assets/js/pages/playlist.js") + RevCord.Util.AppSettingsUtil.GetString("versionNo", "?version=") %>' type="text/javascript"></script>




    <%--ADDED BY KM IFUTURZ - FOR DROPZONE FILE UPLOADER - START--%>
    <%--<script src="<%= Page.ResolveClientUrl("~/assets/DropzoneJS/assets/dropzone.js") %>" type="text/javascript"></script>--%>
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/DropzoneJS/assets/basic.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/assets/DropzoneJS/assets/dropzone.css") + AppSettingsUtil.GetString("versionNo", "?version=") %>" />
    <link rel='stylesheet' type="text/css" href='../assetsNew/css/setup.css' />
    <%--ADDED BY KM IFUTURZ - FOR DROPZONE FILE UPLOADER - END--%>


    <%--<script src="<%= Page.ResolveClientUrl("~/assets/js/pages/setup.js") %>" type="text/javascript"></script>--%>
    <%--<script src="<%= Page.ResolveClientUrl("~/assets/js/QB/quickblox.min.js") %>" type="text/javascript"></script>--%>

    <%--<script src="<%= Page.ResolveClientUrl("~/assets/js/pages/UserManager.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>--%>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/enterpriseUser.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <%--<script src="//code.jquery.com/jquery-1.11.1.min.js"></script>--%>
    <%--<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>--%>
    <%--<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/js/bootstrap.min.js"></script>--%>

    <style>
        .float-container {
            /*border: 1px solid #000000;*/
            /*padding: 20px;*/
        }
        .float-child {
            width: 50%;
            float: left;
            /*padding: 20px;*/
            /*border: 1px solid #000000;*/
        }
        table.dataTable.row-border tbody th, table.dataTable.row-border tbody td, table.dataTable.display tbody th, table.dataTable.display tbody td {
            border-top: 1px solid #3f3c3c;
            border-left: 1px solid #3f3c3c;
            border-right: 1px solid #3f3c3c;
        }

        table.dataTable.display tbody tr:first-child td {
            width: 120px;
        }

        table.dataTable tbody td {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: normal;
        }

        .subTable table {
            border-collapse: collapse;
            height: 100%;
            width: 100%;
        }

        .subTable {
            padding: 0px !important;
            font-weight: bold;
            border-collapse: collapse;
            height: 100%;
            width: 100%;
        }
        #playlistindex {
            margin-left:0px !important;
            margin-right:0px !important;
        }

        [class*="col-"] {
            float: left;
        }

        .bm-photo {
            cursor: pointer;
            width: calc(100% - 15px);
            height: calc(100% - 2px);
        }
        .bm-img {
            font-size: 120px;
        }
    </style>

    <style type="text/css">
        .ui-new-dialog {
            /*z-index: 10000 !important;*/
        }

        /* enable absolute positioning */
        .inner-addon {
            position: relative;
        }
            /* style icon */
            .inner-addon .fa-search {
                position: absolute;
                padding: 10px;
                pointer-events: none;
            }
        /* align icon */
        .left-addon .glyphicon {
            left: 0px;
        }
        /* add padding  */
        .left-addon input {
            padding-left: 30px;
        }

        .ul-rpt-menu li {
            width: 100%;
        }

        button.ui-datepicker-current {
            /*display: none;*/
        }

        .rpt-name-link {
            font-weight: bold;
        }

        .panel-title > a {
            display: block;
            position: relative;
        }

            .panel-title > a:after {
                content: "\f078"; /* fa-chevron-down */
                font-family: 'FontAwesome';
                position: absolute;
                right: 0;
            }

            .panel-title > a[aria-expanded="true"]:after {
                content: "\f077"; /* fa-chevron-up */
            }

        .modal-lg {
            left: 0;
            width: 100% !important;
        }

        .modal-header .close {
            margin-top: -20px;
        }

        .report-section-h2 {
            margin-top: 14px;
        }

        .rpt-tree {
            background-color: #2c3e50;
            color: #fff;
        }

            .rpt-tree ul {
                padding-left: 10px !important;
                /*margin-left: 0px !important;*/
            }

                .rpt-tree ul li {
                    /*border-bottom: 1px solid #1F262E;*/
                    -webkit-box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.1);
                    -moz-box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.1);
                    box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.1);
                }

        .rpt-tree-panel {
            /*width: 50%;*/
            height: 310px;
            max-height: 320px;
            overflow-y: scroll;
        }

        .rpt-panel-body {
            padding: 5px;
        }

        .rpt-panel-heading {
            padding: 5px 7px;
        }

        .ui-state-hover, .ui-state-focus {
            background: none !important;
            color: #3189c3 !important;
        }
        /*.ui-state-active, .ui-widget-content .ui-state-active { background: #000 url("images/ui-bg_gloss-wave_50_009E0F_500x100.png") 50% 50% repeat-x !important; }*/
        .ui-state-active, .ui-widget-content .ui-state-active {
            background-color: #000 !important;
        }


        .wizard > .content {
            min-height: 27em !important;
        }

            .wizard > .content > .body {
                padding: 0 2% !important;
            }

        .wizard .content .body#divReportSteps-p-1 {
            overflow-y: auto;
        }

        .nav_pageNum {
            width: 50px !important;
            padding-left: 7px !important;
        }

        #cbxUsers.form-control {
            height: 46px !important;
        }

        .form-control[disabled] {
            background-color: #eeeeee !important;
        }

        @media (max-width:575px) {
            .hidden-xs {
                display: none !important;
            }
        }

        @media (min-width:576px) and (max-width:767px) {
            .hidden-xs, .hidden-sm {
                display: none !important;
            }
        }

        @media (min-width:768px) and (max-width:991px) {
            .hidden-md {
                display: none !important;
            }
        }

        @media (min-width:992px) and (max-width:1199px) {
            .hidden-lg {
                display: none !important;
            }
        }

        @media (min-width:1200px) {
            .hidden-xl {
                display: none !important;
            }
        }

        @media screen and (max-width: 1366px) {
            .eval-row {
                height: 52px;
                padding: 3px 5px;
            }

            .radio-inline + .radio-inline,
            .checkbox-inline + .checkbox-inline {
                margin-left: 0px;
            }
        }

        #divsavedreport {
            height: -moz-calc(100% - (50px + 30px));
            height: -webkit-calc(100% - (50px + 30px));
            height: calc(100% - (50px + 30px));
            display: block;
        }

        .table-bordered {
            box-sizing: border-box !important;
            -moz-box-sizing: border-box !important;
            -webkit-box-sizing: border-box !important;
        }

        /*#btnSearchSaveCalls {
            display: none !important;
        }*/

        .modal-open {
            overflow: auto !important;
        }

        .modal-header {
            background-color: #2184be;
            color: #fff;
        }

        .nav-tabs.nav-justified > li {
            border: 1px solid #fff;
        }

            .nav-tabs.nav-justified > li > a {
                border: none !important;
                border-radius: 0;
                padding: 0;
                text-transform: uppercase;
            }

                .nav-tabs.nav-justified > li > a:hover {
                    border: none;
                    border-radius: 0;
                    background-color: inherit !important;
                }

                .nav-tabs.nav-justified > li > a > .vessel-rpt-text {
                    border: none;
                    border-top: 4px solid #FFF !important;
                    display: block;
                    background-color: #217dbb;
                    color: #fff;
                    padding: 20px 0;
                }

            .nav-tabs.nav-justified > li.active > a > .vessel-rpt-text {
                border-top: 4px solid #217dbb !important;
                background-color: #FFF;
                color: #000;
            }

            .nav-tabs.nav-justified > li > a > .map {
                border: none;
                height: 90px;
                margin-bottom: 15px;
                display: block;
            }

            .nav-tabs.nav-justified > li.active > a {
                border-right: 1px solid #fff;
                background-color: #fff;
            }

        .title {
            margin-left: 0px;
        }

        .fa-image, .fa-youtube-play {
            font-size: 70px;
        }

        .bm-img {
            font-size: 120px;
        }

        .searchable-container {
            margin-top: 40px;
        }

        .table-condensed {
            margin-bottom: 0px !important;
        }

            .table-condensed th,
            .table-condensed td,
            .table-condensed tbody > tr > td {
                padding: 3px 5px !important;
            }

        .info-block {
            border-right: 5px solid #E6E6E6;
            margin-bottom: 15px;
        }

            .info-block .square-box {
                width: 80px;
                min-height: 80px;
                margin-right: 15px;
                text-align: center !important;
                background-color: #676767;
                padding: 5px 0;
            }

            .info-block:hover .info-block.block-info {
                border-color: #20819e;
            }

            .info-block.block-info .square-box, .info-block.block-info .bm-box {
                background-color: #5bc0de;
                color: #fff;
            }

            .info-block .bm-box {
                width: 140px;
                min-height: 140px;
                margin-right: 15px;
                text-align: center !important;
                background-color: #676767;
                padding: 5px 0;
            }


        label.text-bold {
            font-weight: bold;
            margin-bottom: 3px;
        }

        .survey-title-details {
            text-align: center;
        }

            .survey-title-details .survey-hire {
                margin-top: 0;
                margin-bottom: 0;
                text-transform: uppercase;
            }

        .survey-comp-name {
            margin-top: 0;
            color: #3989c6;
        }

        .text-heading {
            color: #3989c6;
        }

        .bm-photo {
            cursor: pointer;
            width: 125px;
            height: 125px;
        }

        .text-bold {
            font-weight: bold;
        }
        .dataTables_wrapper.no-footer .dataTables_scrollBody {
           
            border-bottom:none;
        }

        /*video::-webkit-media-controls,
        video::-webkit-media-controls-panel,
        video::-webkit-media-controls-play-button,
        video::-webkit-media-controls-start-play-button {
            display: none !important;
            -webkit-appearance: none;
        }*/
    </style>
   
    <script type="text/javascript">
        var currentLanguage = $.cookie('RVCLANG') != null ? $.cookie('RVCLANG') : 'en';
        var loginUserId, loginUserName = '';
        commonHandlerURL = '<%= ResolveClientUrl("~/Handlers/Common/CommonHandler.ashx") %>';
        umHandler1URL = '<%= ResolveClientUrl("~/Handlers/UserManagerHandlers/UserManagerHandler1.ashx") %>';
        playlistHandlerURL = '<%= ResolveClientUrl("~/Handlers/VoiceRecHandlers/PlaylistHandler.ashx") %>';
        vrHandlerURL = '<%= ResolveClientUrl("~/Handlers/VoiceRecHandlers/VRHandler.ashx") %>';
        clientRootURL = '<%= SiteConfig.WebURL %>';
        liveServerIP = '<%= SiteConfig.RealTimeServer %>';
        liveServerIPJS = liveServerIP.split('/')[2];
        vodServerURL = '<%= SiteConfig.VodServer %>';
        var rxUploadsRootPath = '<%=SiteConfig.WebURL%>';
        var userType = '<%= SessionHandler.UserInformation.UserType%>';
        var isECEnabled = <%= SiteConfig.IsECEnabled.ToString().ToLower() %>;
        var isInquire = false; <%--<%= SessionHandler.IsInquire.ToString().ToLower() %>;--%>
        var helperServiceIp = '<%= SiteConfig.HelperServiceAddress %>';
        var IsChained = <%= SiteConfig.IsChainDBsConfigured.ToString().ToLower() %>;
        var isIQ3View = <%= SessionHandler.UserInformation.IsIQ3ViewEnabled.ToString().ToLower() %>;
        var saveAndEmail = true;
        var searchRest = false;
        var isOnlyIQ3ModeEnabled = false;
        var isDemo = <%= AppSettingsUtil.GetString("isDemo", "false")%>;
        var videoURL = clientRootURL + 'Videos/';
		var isMTEnable = <%= SiteConfig.IsMTEnable.ToString().ToLower()%>;
        var docURL = '<%= SiteConfig.PlaylistUploadHttpRoot %>';
        var tenantId = 0;
        var uniqueId = '<%= Guid.NewGuid().ToString() %>';
        pageURL = "<%=Request.Url.AbsoluteUri.Split(new char[]{'?'})[0] %>";
        
        var boolIEBrowser = checkMSIEBrowser();
        
        if(boolIEBrowser)
            pageSize = 100;
        
        var screenURL = '<%=SiteConfig.WebURL%>'.split("/");
        
        if(screenURL[0]=="http:")
            screenURL = 'http://'+screenURL[2]+'/videofiles/';
        else
            screenURL = 'https://'+screenURL[2]+'/videofiles/';

        var inquireURL='<%=SiteConfig.WebURL%>'.split("/");
     
        JL(frontendLogger).info("inquireURL",inquireURL);
		
        var isHttps = false;
			
        if(inquireURL[0]== "http:")
            inquireURL = 'http://'+inquireURL[2]+'/Inquire/';
        else
        {
            isHttps = true;
            inquireURL = 'https://'+inquireURL[2]+'/Inquire/';
        }

        var RevcellURL = '<%=SiteConfig.WebURL%>'.split("/");
        var revviewURL = '<%=SiteConfig.WebURL%>'.split("/");
        if (!isHttps) {
            RevcellURL = 'http://' + RevcellURL[2] + '/Revcell/';
            revviewURL = 'http://' + revviewURL[2] + '/Revview/';
        }
        else {
            RevcellURL = 'https://' + RevcellURL[2] + '/Revcell/';
            revviewURL = 'https://' + revviewURL[2] + '/Revview/';
        }
        
        var currentloadedscreen = 'Playlist';
        var pageSize = <%= SiteConfig.SearchPageSize %>;
        handlersBaseURL = '<%= ResolveClientUrl("~/Handlers") %>';

        var identity = '<%=SessionHandler.UserInformation.UserID%>';
        identity = identity.replace(/ /g,'').toLowerCase();
        var siteconfiguration = '<%=SiteConfig.WebURL%>'.split("/");
        var ipforWebSocketConnect = siteconfiguration[2];
        
        var isSTTLicensed = "<% bool isSTTLicensed = new RevLicenseService.LicenseService().IsSTTLicensed(); Response.Write(isSTTLicensed.ToString().ToLower()); %>";

        var nDiffMinutes=<% TimeSpan tsDiff = DateTime.UtcNow - DateTime.Now;
        int nDiffMinutes = (int)Math.Round(tsDiff.TotalMinutes);
        Response.Write(nDiffMinutes);%>;
        var userBrowser = "<% String userBrowser = Request.Browser.Browser; Response.Write(userBrowser);%>";
        
        var UserName = '<%= SiteConfig.UserName.ToString().ToLower() %>';
        var userNum = <%= SessionHandler.UserInformation.UserNum %>;
        IRSlim = '<%= SessionHandler.UserInformation.AccessIRLite %>';
        IRFull = '<%= SessionHandler.UserInformation.AccessInstantRecall %>';
        var isViewPlaylist = <%= SessionHandler.UserInformation.IsViewPlaylist.ToString().ToLower() %>;
        const isSharedPage = true;
        var uniqueId = '<%= Guid.NewGuid().ToString() %>';
        var boolIEBrowser = checkMSIEBrowser();

        var licenseStatus = '<%= SessionHandler.LicenseStatus %>';
        var revshieldStatus = '<%= SessionHandler.RevshieldStatus %>';
        var bWarningIconStill = '<%= SessionHandler.bImageStill%>';
        var siteName = '<%= SessionHandler.SiteName%>';
        var systemSerialNo = '<%= SessionHandler.SystemSerialNumber%>';

        var rand = Math.floor(Math.random() * 100);

        var style = document.createElement('style');
        style.type = 'text/css';
        var imgPrefix = '../SystemUploadedFiles/MediaIcons/Tenant_'+ tenantId +'/';
        style.innerHTML = '.callType1 { background: url(' + imgPrefix +'1.png?random='+ rand +') center center no-repeat !important; }';
        style.innerHTML += '.callType3 { background: url(' + imgPrefix +'3.png?random='+ rand +') center center no-repeat !important; }';
        style.innerHTML += '.callType6 { background: url(' + imgPrefix + '6.png?random=' + rand + ') center center no-repeat !important; }';
        style.innerHTML += '.callType7 { background: url(' + imgPrefix + '7.png?random=' + rand + ') center center no-repeat !important; }';
        style.innerHTML += '.callType7_virtual { background: url(' + imgPrefix + '7_virtual.png?random=' + rand + ') center center no-repeat !important; }';
        style.innerHTML += '.callType8 { background: url(' + imgPrefix + '8.png?random=' + rand + ') center center no-repeat !important; }';
        style.innerHTML += '.callType8_virtual { background: url(' + imgPrefix + '8_virtual.png?random=' + rand + ') center center no-repeat !important; }';
        style.innerHTML += '.callType11 { background: url(' + imgPrefix +'11.png?random='+ rand +') center center no-repeat !important; }';
        style.innerHTML += '.callType12 { background: url(' + imgPrefix + '12.png?random=' + rand + ') center center no-repeat !important; }';
        style.innerHTML += '.callType13 { background: url(' + imgPrefix + '13.png?random=' + rand + ') center center no-repeat !important; }';
        style.innerHTML += '.callType1_revview { background: url(' + imgPrefix + '1_revview.png?random=' + rand + ') center center no-repeat !important; }';
        document.getElementsByTagName('head')[0].appendChild(style);
        
        $(document).ready(function () {
            activeMenuHandler();
            irLinkHandler();
            var autoLogin = $.cookie('RVCAUTOLOGIN');
            if (autoLogin == true || autoLogin == 'true') {
                $('#autoLogin').prop('checked', "checked");
                $("#liAutoLogin").css('display', 'inline-block');
                $("#sepAutoLogin").css('display', 'block');
            }
            $("#divTopNav a").on('click', function (e) {
                if (e.which == 2) {
                    $.unblockUI();
                }
            });
            loginUserId = $('#header-login-name').attr('uid');
            loginUserName = $.trim($('#header-login-name').text());
            isOnlyIQ3ModeEnabled = ($('#hdnIsOnlyIQ3ModeEnabled').val() == "true" || $('#hdnIsOnlyIQ3ModeEnabled').val() == "True") ? true : false;
            if (isOnlyIQ3ModeEnabled) {
                $('.plCalls').text('Playlist Events');
                $('#btnSearchSaveCalls').hide();
                setTimeout(function () {
                    $('#btnSearchSaveCalls').hide();
                }, 500);
            }
        });

        function activeMenuHandler() {
            var pathname = window.location.pathname.split('/');
            var controller = pathname.length == 4 ? pathname[2] : pathname[1];
            controller = controller != "Surveys" ? controller : "Evaluation";
            //$("li#" + controller + " a").addClass("active");
            $('li#' + controller + '').addClass("active");
        }

        function irLinkHandler() {
            if (IRSlim == 'True' && IRFull == 'True') {
                $('#divIRContainer').css('display', 'inline-block');
            }
            if (IRSlim == 'False' && IRFull == 'False') {
                $('#divIRContainer').css('display', 'none');
            }
            if (IRSlim == 'False' && IRFull == 'True') {
                $('#divIRContainer').css('display', 'inline-block');
                $('#liIRFull').css('display', 'inline-block');
                $('#liIRSlim').css('display', 'none');
                $('#liIRSeprator').css('display', 'none');
            }
            if (IRSlim == 'True' && IRFull == 'False') {
                $('#divIRContainer').css('display', 'inline-block');
                $('#liIRFull').css('display', 'none');
                $('#liIRSlim').css('display', 'inline-block');
                $('#liIRSeprator').css('display', 'none');
            }
        }
      
        $(function () {
           
            $("#logoutBtn.ClientID").click(function () {
                //$.blockUI();
                $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
                clearCookies();
            });
            $("#autoLogin").click(function () {
                clearCookies();
            });
            $.cookie('RVCTRY', "0", { path: '/', expires: 14 });
        });

        function clearCookies() {
            if (!$('#autoLogin').is(':checked')) {
                // reset cookies
                $.cookie('RVCUSER', null, { path: '/', expires: -5 });
                $.cookie('RVCPASS', null, { path: '/', expires: -5 });
                $.cookie('RVCAUTOLOGIN', false, { path: '/', expires: -5 });
            }
            $.cookie('RVCTRY', "0", { path: '/', expires: 14 });
        }

        function blockUIProcessing() {
            $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' })
        }

        $("#lblRefreshPage").on('click', function () {
            // window.parent.caches.delete('call');
            //  Cache.delete();
            window.location.reload(true);
        });
        
        $(function(){
            $('#lnkLoadIR').click(function myfunction() {
                //$.blockUI();
                openPopup();
                //loadIR('recordedCalls');
            });
            function openPopup() {
                //JL(frontendLogger).info($(window).width() + ' - ' + $(window).height());
                var left = ($(window).width() / 2),
                    top = ($(window).height() / 2) - (150 / 2),
                    pop = window.open("../InstantRecall/IR.aspx", "InstantRecall", "resizable=yes, location=no, width=950, height=625, top=" + top + ", left=" + left).focus();
                //targetWin = window.open("", "Instant Recall", 'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=600, height=450' + ', top=' + top + ', left=' + left);
            }
        
        });


    </script>
  
    <style>

        .twitter {
            font: normal normal 10px Arial;
            text-align: center;
            color: #998578;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        .twitter {
            color: #000000;
            text-decoration: none;
            display: block;
            padding: 14px;
            -webkit-transition: all .25s ease;
            -moz-transition: all .25s ease;
            -ms-transition: all .25s ease;
            -o-transition: all .25s ease;
            transition: all .25s ease;
        }

        .twitter:hover {
            color: #FF7D6D;
            text-decoration: none;
        }

        /*span {
            font-style: italic;
            display: block;
        }

        img {
            max-width: 100%;
        }*/

        /* Floating Social Media Bar Style Starts Here */

        .fl-fl {
            background: #292b2e;
            text-transform: uppercase;
            letter-spacing: 3px;
            padding: 4px;
            width: 200px;
            position: fixed;
            right: -160px;
            z-index: 1000;
            font: normal normal 10px Arial;
            -webkit-transition: all .25s ease;
            -moz-transition: all .25s ease;
            -ms-transition: all .25s ease;
            -o-transition: all .25s ease;
            transition: all .25s ease;
        }

        /*.fa {
            font-size: 20px;
            color: #fff;
            padding: 10px 0;
            width: 40px;
            margin-left: 8px;
        }*/

        .fl-fl:hover {
            right: 0;
        }

        .fl-fl a {
            color: #fff !important;
            text-decoration: none;
            text-align: center;
            line-height: 43px !important;
            /*vertical-align: top!important;*/
        }

        .float-fb {
            top: 160px;
        }

        .float-tw {
            top: 215px;
        }

        .float-gp {
            top: 270px;
        }

        .float-rs {
            top: 325px;
        }

        .float-ig {
            top: 380px;
        }

        .float-pn {
            top: 435px;
        }

    </style>

    <style>
        .callTypeSprite {
            background: url(../assets/images/callTypes.png) center center no-repeat;
            width: 16px;
            height:16px;
            margin: auto auto;
            display: block;
        }

        .callType2 {
            height: 14px;
            background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/2.png) center center no-repeat;
        }

        .callType4 {
            height: 10px;
            background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/4.png) center center no-repeat;
        }

        .callType5 {
            height: 13px;
            background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/5.png) center center no-repeat;
        }

        .callType9 {
            background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/9.png) center center no-repeat;
        }

        .callType10 {
            background: url(../SystemUploadedFiles/MediaIcons/DefaultIcons/10.png) center center no-repeat;
        }

        .groupTypeSprite {
            background: url(../assets/images/groupType_white.png) center center no-repeat;
            width: 14px;
            margin: auto auto;
            display: block;
        }

        li.groupType:before {
            background: url("../assets/images/groupType_white.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
            content: "";
            display: inline-block;
            margin-bottom: 0;
            /*margin-left: -35px;*/
            margin-top: 3px;
            min-height: 14px;
            position: absolute;
            vertical-align: middle;
            width: 14px;
        }

        table.dataTable thead th {
            vertical-align: middle;
            text-align: center;
            white-space: nowrap;
            min-width: fit-content;
        }

        .deleteIcon {
            background: url("../assets/icons/common/iconDelete.png") center center no-repeat transparent;
            display: block;
            height: 16px;
            margin-top: 2px;
            width: 16px;
        }

        #divuseraddeditem .vis-foreground {
            background-color: #DDDDDD;
            font-weight: bold;
        }

        #divuseraddeditem .vis-center > .vis-content {
            overflow-x: hidden;
            overflow-y: hidden;
        }

        #divuseraddeditem .vis-left .vis-content {
            background: none;
        }

        .vis-item.vis-range {
            border-radius: 8px !important;
        }

        .vis-item {
            border-color: #97B0F8 !important;
            background-color: #A5D8A2 !important;
        }

        .vis-center > .vis-content {
            overflow-x: hidden;
            overflow-y: scroll;
            height: 100%;
        }

        .vis-left > .vis-content {
            height: 100%;
            overflow-x: hidden;
            overflow-y: hidden;
        }

        #visualization_addfiles > .vis-timeline {
            max-height: 300px !important;
        }

        .vis-current-time {
            z-index: 1000 !important;
        }
        .dataTables_info:hover{
            background-color:#2b2b2b;
            color:#fff;
        }

        #divExportprogressBar div {
            /*color: #fff;*/
            background-color: #2980b9;
        }

        #divAdditionalButtons {
            height: auto !important;
        }
        .tooltip.right {
            margin-left: -7px;
        }
        /*.tooltip.left {
            margin-left: -40px;
        }*/

        .mg-top-0-16em {
            margin-top: 0.16em;
        }


        

        /**
            Playlist Fullscreen Metadata Changes
        */

        @media all and (display-mode: fullscreen) {
          .playlist-timeline-video-player {
              height: 100%;
              width: 66%;
              float: left;
          }
          .playlist-timeline-video-player-metadata {
              display: block;
          }
          .show-on-fullscreen-only {
              display: block;
          }
          .hide-on-fullscreen {
              display: none;
          }
        }

        @media not all and (display-mode: fullscreen) {
            .playlist-timeline-video-player {
                width: 100%;
                height: 100%;
            }
          .playlist-timeline-video-player-metadata {
              display: none;
          }
          .show-on-fullscreen-only {
              display: none;
          }
          .hide-on-fullscreen {
              display: block;
          }
        }

        
    </style>
      
</head>
<body class="glossed">

    <form id="form1" runat="server">
        <!-- To prevent autofill chrome -->
        <input type="text" style="display: none;" autocomplete="username" />
        <input type="password" style="display: none;" autocomplete="current-password" />
        <!-- To prevent autofill chrome -->

        
        <input id="hdnShareId" type="hidden" value="0" runat="server" clientidmode="Static" />
        <input id="hdnInviteeEmail" type="hidden" value="" runat="server" clientidmode="Static" />
        <input id="hdnTenantId" type="hidden" value="0" runat="server" clientidmode="Static" />
        <input id="hdnIsOnlyIQ3ModeEnabled" type="hidden" value="0" runat="server" clientidmode="Static" />
        <input id="hdnIsExpired" type="hidden" value="false" runat="server" clientidmode="Static" />
        <span id="header-login-name" usertype="<% = RevCord.VoiceRec.WebUIClient.Classes.SessionHandler.UserInformation.TypeOfUser %>" useltype="<% = RevCord.VoiceRec.WebUIClient.Classes.SessionHandler.UserInformation.SelectType %>" utype="<% = RevCord.VoiceRec.WebUIClient.Classes.SessionHandler.UserInformation.UserType %>" uid="<% = RevCord.VoiceRec.WebUIClient.Classes.SessionHandler.UserInformation.UserNum %>" ugrp="<% = RevCord.VoiceRec.WebUIClient.Classes.SessionHandler.UserInformation.GroupNum %>" style="display: none;"></span>

        <input type="hidden" id="hdnActiveTab" />
        <div class="all-wrapper fixed-header left-menu">
            
            <div class="page-header" style="margin: 0px;">
                <div class="header-links">
                    <div id="logo" class="dropdown" style="margin-right: 20px;">

                        <img id="lnkpluginclick" src="<%=ResolveClientUrl("~/assetsnew/images/iq3-btn.png")%>" alt="" title="IQ3 Plugin. Click here to know more.." style="cursor: pointer; height: 50px;" onclick="ShowInqPluginPopupUsingMMSDialog(); setTimeout(function(){$('.ui-new-dialog-titlebar-close').tooltip();},500);" />

                    </div>
                </div>
            </div>

        
            <div class="main-content no-library" style="overflow:auto; margin-left: 0px;">
               
                <div class="row" id="playlistindex">

                    <div class="col-md-12 col-xs-12" id="divPlaylists" style="height: 100%">
                        <div class="widget widget-blue">
                            <div class="widget-title">
                                <h3><i class="fa fa-table"></i><span class="ml" lkey="plPlaylistIndex">PLAYLIST INDEX</span></h3>
                            </div>
                            <div class="widget-content">
                                <div class="table-responsive" id="ulPlaylists">
                                    <table id="tPlaylist" class="table table-bordered table-hover display" style="table-layout: fixed; width: 100%;">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th><span class="ml" lkey="thColCheck"><input id="SelectAll" type="checkbox" onclick="javascript:selectAll(this,id);"/></span></th>
                                                <th><span class="ml" lkey="thColPlaylistName">Playlist Name</span></th>
                                                <th><span class="ml" lkey="thColCallsCount">Count</span></th>
                                                <th><span class="ml" lkey="thColCreatedDate">Created Date</span></th>
                                                <th><span class="ml" lkey="thColVisibility">Visibility</span></th>
                                                <th><span class="ml" lkey="thColSettings">Settings</span></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>

                                </div>
                            </div>
                            <label id="lblNoPlDataMsg" style="display: none;" class="ml" lkey="lPlIsEmpty">No Playlist exists. Please use Add Playlist button to Create.</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-xs-12" id="playlisttool" style="height: 100%; overflow: hidden; display: none;">

                    <%-- <div class="col-md-9 ">--%>
                    <div id="divPlSearch" class="widget widget-blue">

                        <div class="widget-title">
                            <div class="widget-controls">
                                <%--<a href="#" class="widget-control widget-control-minimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>--%>
                                <a href="JavaScript:void(0)" id="ancrClosePlaylistTool" class="widget-control" data-toggle="tooltip" data-placement="top" title="" data-original-title="Back to Playlists"><i class="fa fa-times-circle"></i></a>
                            </div>
                            <h3><i class="fa fa-ok-circle"></i>Repository Tool</h3>
                        </div>
                        <div class="widget-content">
                            <div class="row remove-row-effect mg-top-0-16em">
                                <div class="col-xs-3 remove-col-effect">
                                    <a id="lnkGoBack" href="javascript:void(0);" class="btn btn-iconed btn-warning btn-sm ml" lkey="tGoBack" style="display: none;"><i class="fa fa-cog"></i>Back to Search</a>
                                    <a id="lnkTimeline" class="btn btn-iconed btn-primary btn-sm ml" style="display: none;" lkey="tTimeline"><i class="fa fa-clock-o"></i>Timeline View</a>
                                    <a id="lnkListview" class="btn btn-iconed btn-primary btn-sm ml" style="width: 11.4em; " lkey="tListview" onclick="javascript:$('#hdnActiveTab').val('Listview'); hideMetadata(); displaylink();"><i class="fa fa-tasks"></i>List View</a>
                                </div>
                                <div class="col-xs-9 remove-col-effect text-right">
                                    <a id="btnMapPlaylist" class="btn btn-iconed btn-primary btn-sm"><i class="fa fa-map-marker"></i>Map Playlist</a>
                                    <a id="btnAddManualFile" class="btn btn-iconed btn-primary btn-sm"><i class="fa fa-files-o"></i>Add File</a>
                                    <a id="btnSaveCalls" class="btn btn-iconed btn-primary btn-sm"><i class="fa fa-save (alias)"></i>Save Playlist</a>

                                    <input id="txtPlSearch" type="text" class="form-control mg-top-0-16em" lkey="tSearchPl" placeholder="Search Playlist" style="height: 36px; width: 200px; float: left;" />
                                    <div class="btn-group">
                                        <select class="form-control mg-top-0-16em" id="cbxLoadPlayList" lkey="cbxLoadPlayList" style="margin-bottom: 0px;">
                                        </select>
                                    </div>
                                    <!-- /btn-group -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="float-sm">
                        <%--<div class="fl-fl float-fb">
                            <i>
                                <img src="../assetsNew/images/Metadata.png" /></i>
                            <a href="JavaScript:void(0)" id="Metadataa">MetaData</a>
                        </div>--%>
                        <div class="fl-fl float-tw">
                            <i>
                                <img src="../assetsNew/images/AttachFiles.png" /></i>
                            <a href="JavaScript:void(0)" id="Attachedfiless">Attached Files</a>
                        </div>
                        <div class="fl-fl float-gp">
                            <i>
                                <img src="../assetsNew/images/InternalNotes.png" /></i>
                            <a href="JavaScript:void(0)" id="Internalnotess">Internal Notes</a>
                        </div>
                        <div class="fl-fl float-rs">
                            <i>
                                <img src="../assetsNew/images/sharenotes.png" /></i>
                            <a href="JavaScript:void(0)" id="Sharednotess">Shared Notes</a>
                        </div>
                    </div>

                    <div id="divSearchVideoPlayer" class="" style="text-align: center; height: 0px; max-height: 0px; display: none;">
                        <video id="videofileplayer" oncontextmenu="return false;" disablePictureInPicture controlslist="nodownload" style="height: 100%; max-height: 100%; width: 100%; max-width: 100%;"></video>
                        <video id="videofileplayer1" oncontextmenu="return false;" disablePictureInPicture controlslist="nodownload" style="width: 0px; height: 0px;"></video>
                        <div style="position: absolute; top: calc(40% - 10px); right: 0; left: 0; display:none;" id="divVideoRecNotStarted">
                            <p style="color: black; font-size: 1.2em;">Video recording not started yet.</p>
                        </div>
                        <div style="position: absolute; top: 22%; right: 22%; background-color: black; display:none;" id="divRevViewClock">
                            <p style="color: white; font-size: 1.1em;width: 180px;margin: 0;padding: 4px;" id="pRevViewClock"></p>
                        </div>
                    </div>

                <% if (Request.Browser.Browser == "InternetExplorer")
                    { %>
                <div id="divTimeline" class="timelineVideoConatiner" style="display: none; margin: 10px; z-index: 3;">
                    <div style="" class="videoPanel">
                        <object data="data:application/x-silverlight-2," type="application/x-silverlight-2" width="100%" height="240">
                            <param name="source" value="../ClientBin/RevcordVideoPlayer.xap" />
                            <param name="onError" value="onSilverlightError" />
                            <param name="background" value="transparent" />

                            <param name="windowless" value="true" />
                            <param name="minRuntimeVersion" value="5.0.61118.0" />
                            <param name="autoUpgrade" value="true" />
                            <param name="initParams" value="instanceNo=1,pageGuid=<% Guid inst = Guid.NewGuid(); Response.Write(inst.ToString()); %>,viewType=timeline" />
                            <a href="http://go.microsoft.com/fwlink/?LinkID=149156&v=5.0.61118.0" style="text-decoration: none">
                                <img src="http://go.microsoft.com/fwlink/?LinkId=161376" alt="Get Microsoft Silverlight" style="border-style: none" />
                            </a>
                        </object>
                    </div>
                    <div style="" class="videoPanel">
                        <object data="data:application/x-silverlight-2," type="application/x-silverlight-2" width="100%" height="240">
                            <param name="source" value="../ClientBin/RevcordVideoPlayer.xap" />
                            <param name="onError" value="onSilverlightError" />
                            <param name="background" value="transparent" />

                            <param name="windowless" value="true" />
                            <param name="minRuntimeVersion" value="5.0.61118.0" />
                            <param name="autoUpgrade" value="true" />
                            <param name="initParams" value="instanceNo=2,pageGuid=<% Response.Write(inst.ToString()); %>,viewType=timeline" />
                            <a href="http://go.microsoft.com/fwlink/?LinkID=149156&v=5.0.61118.0" style="text-decoration: none">
                                <img src="http://go.microsoft.com/fwlink/?LinkId=161376" alt="Get Microsoft Silverlight" style="border-style: none" />
                            </a>
                        </object>
                    </div>
                    <div style="" class="videoPanel">
                        <object data="data:application/x-silverlight-2," type="application/x-silverlight-2" width="100%" height="240">
                            <param name="source" value="../ClientBin/RevcordVideoPlayer.xap" />
                            <param name="onError" value="onSilverlightError" />
                            <param name="background" value="transparent" />

                            <param name="windowless" value="true" />
                            <param name="minRuntimeVersion" value="5.0.61118.0" />
                            <param name="autoUpgrade" value="true" />
                            <param name="initParams" value="instanceNo=3,pageGuid=<% Response.Write(inst.ToString()); %>,viewType=timeline" />
                            <a href="http://go.microsoft.com/fwlink/?LinkID=149156&v=5.0.61118.0" style="text-decoration: none">
                                <img src="http://go.microsoft.com/fwlink/?LinkId=161376" alt="Get Microsoft Silverlight" style="border-style: none" />
                            </a>
                        </object>
                    </div>
                    <div style="" class="videoPanel">
                        <object data="data:application/x-silverlight-2," type="application/x-silverlight-2" width="100%" height="240">
                            <param name="source" value="../ClientBin/RevcordVideoPlayer.xap" />
                            <param name="onError" value="onSilverlightError" />
                            <param name="background" value="transparent" />

                            <param name="windowless" value="true" />
                            <param name="minRuntimeVersion" value="5.0.61118.0" />
                            <param name="autoUpgrade" value="true" />
                            <param name="initParams" value="instanceNo=4,pageGuid=<% Response.Write(inst.ToString()); %>,viewType=timeline" />
                            <a href="http://go.microsoft.com/fwlink/?LinkID=149156&v=5.0.61118.0" style="text-decoration: none">
                                <img src="http://go.microsoft.com/fwlink/?LinkId=161376" alt="Get Microsoft Silverlight" style="border-style: none" />
                            </a>
                        </object>
                    </div>
                </div>

                <div id="divListview" class="hide" style="display: none; margin-top: 10px;">
                    <div id="divLvVideoPlayer" style="" class="videoPanelSingle" jqdialogstatus="close">
                        <object id="objLVPlayer" data="data:application/x-silverlight-2," type="application/x-silverlight-2" width="320" height="240">
                            <param name="source" value="../ClientBin/RevcordVideoPlayer.xap" />
                            <param name="onError" value="onSilverlightError" />
                            <param name="background" value="transparent" />
                            <% if (Request.UserAgent.IndexOf("Windows NT 6.3") > 0 && Request.Browser.Browser == "InternetExplorer")
                                { %>
                            <param name="windowless" value="false" />
                            <% } %>
                            <% else
                                { %>
                            <param name="windowless" value="true" />
                            <% } %>
                            <param name="minRuntimeVersion" value="5.0.61118.0" />
                            <param name="autoUpgrade" value="true" />
                            <param name="initParams" value="instanceNo=5,pageGuid=<% Response.Write(inst.ToString()); %>,viewType=playlist" />
                            <a href="http://go.microsoft.com/fwlink/?LinkID=149156&v=5.0.61118.0" style="text-decoration: none">
                                <img src="http://go.microsoft.com/fwlink/?LinkId=161376" alt="Get Microsoft Silverlight" style="border-style: none" />
                            </a>
                        </object>
                    </div>
                    <div id="divListViewTable" style="width: 100%; background-color: #dddddd;">
                        <div class="fixed-table-container sort-decoration" style="height: 100%;">
                            <div class="header-background"></div>
                            <div id="plList" class="fixed-table-container-inner">
                                <table id="tblPlaylist" class="table table-bordered table-hover display playlistTable" style="width: 100%; table-layout: fixed;">
                                    <thead>
                                        <tr>
                                            <th style="width: 36px;">
                                                <div class="th-inner">
                                                    <span>#</span>
                                                    <span class="sortArrow">&nbsp;</span>
                                                </div>
                                            </th>
                                            <th id="emptyPlayList" style="width: 36px;">
                                                <div class="th-inner">
                                                    <span><a id="lnkDeleteAllPL" class="deleteIcon ml" lkey="imgDeleteAll" title="Delete All" href="javascript:void(0);" style="float: left;"></a></span>
                                                </div>
                                            </th>
                                            <th style="width: 56px;">
                                                <div class="th-inner"><span class="ml" lkey="cplType">Type</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 100px;">
                                                <div class="th-inner"><span class="ml" lkey="cplChannel">Channel</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 150px;">
                                                <div class="th-inner"><span class="ml" lkey="cplChannelName">Channel Name</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 150px;">
                                                <div class="th-inner"><span class="ml" lkey="cplGroupName">Group Name</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 130px;">
                                                <div class="th-inner"><span class="ml" lkey="cplStartTime">Start Time</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 80px;">
                                                <div class="th-inner"><span class="ml" lkey="tDuration">Duration</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 120px;">
                                                <div class="th-inner"><span class="ml" lkey="thcolCallerID">Caller ID</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 80px;">
                                                <div class="th-inner"><span class="ml" lkey="cplRetain">Retain</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 80px;">
                                                <div class="th-inner"><span class="ml" lkey="cplTag">Tag</span></div>
                                            </th>
                                            <th style="width: 140px;">
                                                <div class="th-inner"><span class="ml" lkey="cplBookmarks">Bookmarks</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 200px;">
                                                <div class="th-inner"><span class="ml" lkey="cplCallComments">Call Comments</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                            <th style="width: 140px;">
                                                <div class="th-inner"><span class="ml" lkey="cplText911Text">Text 911</span> <span class="sortArrow">&nbsp;</span></div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="abc" style="border: 1px solid black;">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="divtimelineviewdesign" style="position: absolute; vertical-align: bottom; z-index: 2; left: 0%; width: 100%; bottom: 12%; display: none;">
                    <%--Added by ifuturz for user added items--%>
                    <div id="divuseraddeditem" class="scorllDivUserAddedItem"></div>
                    <%--End by ifuturz for user added items--%>
                    <div id="visualization"></div>

                </div>

                <div class="footer" style="">
                    <%--<div id="silverlightControlHost" class="playerPane row-fluid" style="">--%>
                    <div id="silverlightControlHost" class="playerPane" style="width: 100%; height: 100%; position: absolute; bottom: 10px; z-index: 0">
                        <%--KM - IFUTURZ ADDED id TO object TO RESOLVE TIMELINE VIEW TIMELINE CHART GOES BEYOND VIDEO PANEL IN IE SILVERLIGHT FALLBACK.--%>
                        <object data="data:application/x-silverlight-2," type="application/x-silverlight-2" width="100%" height="100%" id="slvrObject">
                            <param name="source" value="../ClientBin/RevcordFilePlayer.xap" />
                            <param name="onError" value="onSilverlightError" />
                            <param name="background" value="transparent" />
                            <param name="windowless" value="true" />
                            <param name="minRuntimeVersion" value="5.0.61118.0" />
                            <param name="autoUpgrade" value="true" />
                            <param name="onLoad" value="slLoaded" />
                            <param name="Culture" value="<%=SessionHandler.CurrentLanguage %>" />
                            <param name="UICulture" value="<%=SessionHandler.CurrentLanguage %>" />
                            <%--<param name="initParams" value="localIP="***********" />--%>
                            <param name="initParams" value="localIP=<%
                                string IP4Address = String.Empty;
                                foreach (System.Net.IPAddress IPA in System.Net.Dns.GetHostAddresses(HttpContext.Current.Request.UserHostAddress))
                                {
                                    if (IPA.AddressFamily.ToString() == "InterNetwork")
                                    {
                                        IP4Address = IPA.ToString();
                                        break;
                                    }
                                }
                                if (IP4Address == String.Empty)
                                {
                                    foreach (System.Net.IPAddress IPA in System.Net.Dns.GetHostAddresses(System.Net.Dns.GetHostName()))
                                    {
                                        if (IPA.AddressFamily.ToString() == "InterNetwork")
                                        {
                                            IP4Address = IPA.ToString();
                                            break;
                                        }
                                    }
                                }
                                Response.Write(IP4Address);%>,pageGuid=<% Response.Write(inst.ToString()); %>" />
                            <a href="http://go.microsoft.com/fwlink/?LinkID=149156&v=5.0.61118.0" style="text-decoration: none">
                                <img src="http://go.microsoft.com/fwlink/?LinkId=161376" alt="Get Microsoft Silverlight" style="border-style: none" />
                            </a>
                        </object>
                        <iframe id="_sl_historyFrame" style="visibility: hidden; height: 0px; width: 0px; border: 0px"></iframe>
                    </div>
                </div>
                <% }%>

                <% else
                   { %>

                <div id="playlistContainer" style="overflow-x: hidden; overflow-y: auto;">
                    <div id="divTimeline" style="">
                        <div class="widget widget-blue">
                            <div class="widget-title">
                                <h3><i class="fa fa-ok-circle"></i>Video Players</h3>
                            </div>

                            <div class="widget-content" style="padding: 4px; overflow-y: auto; overflow-x: hidden; height: calc(100% - 41px)">
                                <div class="container-fluid">
                                    <div class="row">
                                        <div class="col-md-3 col-sm-3 col-xs-6">
                                            <div>
                                                <div class="widget widget-blue">
                                                    <div class="widget-title">
                                                        <h3><i class="fa fa-ok-circle"></i>Player 1</h3>
                                                    </div>
                                                    <div class="widget-content" style="padding: 0px; height: auto;">
                                                        <div id="divLvVideoPlayer0" class="videoPanel">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3 col-xs-6">
                                            <div class="widget widget-blue">
                                                <div class="widget-title">
                                                    <h3><i class="fa fa-ok-circle"></i>Player 2</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; height: auto;">
                                                    <div id="divLvVideoPlayer1" class="videoPanel">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3 col-xs-6">
                                            <div class="widget widget-blue">
                                                <div class="widget-title">
                                                    <h3><i class="fa fa-ok-circle"></i>Player 3</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; height: auto;">
                                                    <div id="divLvVideoPlayer2" class="videoPanel">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3 col-xs-6">
                                            <div class="widget widget-blue">
                                                <div class="widget-title">
                                                    <h3><i class="fa fa-ok-circle"></i>Player 4</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; height: auto;">
                                                    <div id="divLvVideoPlayer3" class="videoPanel">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="divPlMetadata" class="hide" style="margin-bottom: 10px; max-height: 100%;">
                        <div class="widget widget-blue">
                            <div class="widget-title">
                                <div class="widget-controls">
                                    <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                    <a href="#" class="widget-control" data-toggle="tooltip" data-placement="top" title="" onclick="hideMetadata();" data-original-title="Close"><i class="fa fa-times-circle"></i></a>
                                </div>
                                <h3><i class="fa fa-ok-circle"></i>Metadata</h3>
                            </div>

                            <div class="widget-content" style="padding: 4px;">
                                <div class="container-fluid">
                                    <div class="row inquire-case-info">
                                        <div class="col-md-3 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-event-details">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Interview Details</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto; overflow-x: hidden;">
                                                    <div class="meta_container case-info">
                                                        <table class="table meta-info-table">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="meta-label text-left">Event #: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-no"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Location: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-location"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Date: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-date"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">User: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-Interviewer"></span></td>
                                                                </tr>
                                                                <tr class="hide">
                                                                    <td class="meta-label text-left">Interviewee: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-Interviewee"></span></td>
                                                                </tr>
                                                                <tr id="trType">
                                                                    <td class="meta-label text-left">Type:</td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-Type"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Notes: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-Notes"></span></td>
                                                                </tr>
                                                                <tr class="hide">
                                                                    <td class="meta-label text-left">DialogId: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-qb-dialogid"></span></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-3 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-event-cf">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Custom Fields</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto;">
                                                    <div id="divcustomfieldsdetails" class="meta_container case-info">
		                                                		                                            
                                                        <div id="divCustomFields"></div>
		                                                <%--<table id="tblcustomfields" class="table meta-info-table">
			                                                </table>--%>
	                                                </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-3 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-event-bm">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Bookmarks</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto;">
                                                    <div id="divPlBookmarkDetails">
                                                        <div style="width: 100%; overflow: auto; float: left; font-size: small;" class="meta_container">
                                                            <%--<table id="tblPlBookmarkContent" class="table meta-info-table table-details" border="0">
                                                                <tbody>
                                                                </tbody>
                                                            </table>--%>
                                                            <div id="divBookmarks"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-3 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-event-chat">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Chat</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto;">
                                                    <div class="">
                                                        <div id="pl-messages-list" class="content list-group">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row revview-case-info">
                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-revview-event-details">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Revview Details</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto; overflow-x: hidden;">
                                                    <div class="meta_container case-info">
                                                        <table class="table meta-info-table">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="meta-label text-left">Call ID:</td>
                                                                    <td class="meta-info-value text-left"><span class="ID-CallID"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Caller ID:</td>
                                                                    <td class="meta-info-value text-left"><span class="ID-CallerID"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Start Time:</td>
                                                                    <td class="meta-info-value text-left"><span class="ID-StartTime"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Ext:</td>
                                                                    <td class="meta-info-value text-left"><span class="ID-Ext"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Ext Name:</td>
                                                                    <td class="meta-info-value text-left"><span class="ID-ExtName"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Agent Name:</td>
                                                                    <td class="meta-info-value text-left"><span class="ID-AgentName"></span></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-revview-event-bm">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Bookmarks</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto;">
                                                    <div id="divPlBookmarkDetails">
                                                        <div style="width: 100%; overflow: auto; float: left; font-size: small;" class="meta_container">
                                                            <%--<table id="tblPlBookmarkContent" class="table meta-info-table table-details" border="0">
                                                                <tbody>
                                                                </tbody>
                                                            </table>--%>
                                                            <div id="divRevViewBookmarks"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-4 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-revview-event-chat">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Chat</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto;">
                                                    <div class="">
                                                        <div id="pl-messages-list" class="content list-group">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row md-case-info">
                                        <div class="col-md-3 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-md-event-details">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Interview Details</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto; overflow-x: hidden;">
                                                    <div class="meta_container case-info">
                                                        <table class="table meta-info-table">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="meta-label text-left">Case #: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-no"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Location: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-location"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Date: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-date"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Patient Name:</td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-patient-name"></span></td>
                                                                </tr>
                                                                <tr class="hide">
                                                                    <td class="meta-label text-left">DialogId: </td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-qb-dialogid"></span></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-2 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-patient-details">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Patient Details</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto; overflow-x: hidden;">
                                                    <div class="meta_container case-info">
                                                        <table class="table meta-info-table">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="meta-label text-left">Height:</td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-patient-height"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Weight:</td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-patient-weight"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">DOB:</td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-patient-dob"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Gender:</td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-patient-gender"></span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="meta-label text-left">Primary Complaint:</td>
                                                                    <td class="meta-info-value text-left"><span class="pl-case-complaint"></span></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-2 col-sm-4 col-xs-12">
                                            <div class="widget widget-blue" id="pl-case-general-vitals">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>General Vitals</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto; overflow-x: hidden;">
                                                    <div class="meta_container pl-case-vitals-info">
                                                        <table class="table meta-info-table">
                                                            <tbody>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-2 col-sm-6 col-xs-12">
                                            <div class="widget widget-blue" id="pl-event-bm">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Bookmarks</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto;">
                                                    <div id="divPlBookmarkDetails">
                                                        <div style="width: 100%; overflow: auto; float: left; font-size: small;" class="meta_container">
                                                            <%--<table id="tblPlBookmarkContent" class="table meta-info-table table-details" border="0">
                                                                <tbody>
                                                                </tbody>
                                                            </table>--%>
                                                            <div id="divBookmarks"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-3 col-sm-6 col-xs-12">
                                            <div class="widget widget-blue" id="pl-event-chat">
                                                <div class="widget-title">
                                                    <div class="widget-controls">
                                                        <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                                    </div>
                                                    <h3><i class="fa fa-ok-circle"></i>Chat</h3>
                                                </div>
                                                <div class="widget-content" style="padding: 0px; overflow-y: auto;">
                                                    <div class="">
                                                        <div id="pl-messages-list" class="content list-group">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="divListview" class="widget widget-blue" style="display: none;">

                        <div id="divLvVideoPlayerContainer" style="display: none;">
                            <div class="widget widget-blue">
                                <div class="widget-title">
                                    <div class="widget-controls">
                                        <%--   <a href="#" class="widget-control widget-control-full-screen" data-toggle="tooltip" data-placement="top" title="" data-original-title="Full Screen"><i class="fa fa-expand"></i></a>
                                    <a href="#" class="widget-control widget-control-full-screen widget-control-show-when-full" data-toggle="tooltip" data-placement="left" title="" data-original-title="Exit Full Screen"><i class="fa fa-expand"></i></a>--%>


                                    </div>
                                    <h3><i class="fa fa-ok-circle"></i>Video Player</h3>
                                </div>
                                <div class="widget-content" style="padding: 0px; height: auto;">
                                    <div id="divLvVideoPlayer" class="videoPanel" style="height: 0px; text-align: center;">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="divListViewTable" class="widget widget-blue" style="width: 100%; background-color: #dddddd;">
                            <div class="widget-title">
                                <div class="widget-controls">
                                </div>
                                <h3><i class="fa fa-ok-circle"></i><span class="plCalls">Playlist Calls</span></h3>
                            </div>
                            <div class="widget-content" style="padding: 0px; height: 44px;">

                                <div class="" style="height: 100%;">
                                    <div id="plList" class="fixed-table-container-inner">
                                        <table id="tblPlaylist" class="table table-bordered table-hover display playlistTable" style="width: 100%; table-layout: fixed;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 36px;">
                                                        <div class="th-inner">
                                                            <span>#</span>
                                                            <span class="sortArrow">&nbsp;</span>
                                                        </div>
                                                    </th>
                                                    <th id="emptyPlayList" style="width: 36px;">
                                                        <div class="th-inner">
                                                            <span><a id="lnkDeleteAllPL" class="deleteIcon ml" lkey="imgDeleteAll" title="Delete All" href="javascript:void(0);" style="float: left;"></a></span>
                                                        </div>
                                                    </th>
                                                    <th style="width: 56px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplType">Type</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 100px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplChannel">Channel</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 150px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplChannelName">Channel Name</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 150px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplGroupName">Group Name</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 130px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplStartTime">Start Time</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 80px;">
                                                        <div class="th-inner"><span class="ml" lkey="tDuration">Duration</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 120px;">
                                                        <div class="th-inner"><span class="ml" lkey="thcolCallerID">Caller ID</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 80px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplRetain">Retain</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 80px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplTag">Tag</span></div>
                                                    </th>
                                                    <th style="width: 140px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplBookmarks">Bookmarks</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 200px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplCallComments">Call Comments</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                    <th style="width: 140px;">
                                                        <div class="th-inner"><span class="ml" lkey="cplText911Text">Text 911</span> <span class="sortArrow">&nbsp;</span></div>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody class="abc" style="border: 1px solid black;">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="divtimelineviewdesign" style="display: none;">
                        <div class="widget widget-blue">
                            <div class="widget-title">
                                <div class="widget-controls">
                                    <%--<a href="#" class="widget-control widget-control-refresh ml" lkey="ttRefresh" data-toggle="tooltip" data-placement="top" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>--%>
                                    <a href="#" class="widget-control widget-control-minimize ml" lkey="ttMinimize" data-toggle="tooltip" data-placement="top" title="" data-original-title="Minimize"><i class="fa fa-minus-circle"></i></a>
                                </div>
                                <h3><i class="fa fa-ok-circle"></i><span lkey="lCallsTimeLine" class="ml">Calls TimeLine</span></h3>
                            </div>
                            <div class="widget-content" style="padding: 0px;">
                                <div>
                                    <div id="divuseraddeditem" class="scorllDivUserAddedItem"></div>
                                    <div id="visualization" style=""></div>
                                    <link rel="stylesheet" type="text/css" href="../assets/css/TimelineView/custom.css" />
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                    
                <div class="widget widget-blue" style=" display: none; position: absolute; bottom: 0px; width: calc(100% - 20px)">

                    <div class="widget-content" style="background: #004d97;">
                        <div class="footer row">
                            <div id="silverlightControlHost" class="" style="">

                                <div id="divFilePlayer" style="padding: 10px;">
                                    <uc7:ucFilePlayer ID="ucFilePlayer2" runat="server" />
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <% } %>


                <div id="divFPEditBookmark" style="display: none;">
                    <uc3:ucFileVideoEditBookmark ID="ucFileVideoEditBookmark1" runat="server" />
                </div>

                <div id="divInqFileVideoNotes" style="display: none;">
                    <uc4:ucFileVideoNotes ID="ucFileVideoNotes1" runat="server" />
                </div>

                <div id="divFileVideoAddBookmark" style="display: none;">
                    <uc5:ucFileVideoAddBookmark ID="ucFileVideoAddBookmark1" runat="server" />
                </div>

                <div id="divInquireEventNotes" style="display: none;">
                    <uc6:ucEventNotes ID="ucEventNotes2" runat="server" />
                </div>

                
            </div>

            </div>

        </div>

        <% if (Request.Browser.Browser != "InternetExplorer")
          { %>
        <div id="divAddManualFile" style="display: none; overflow: hidden;">
            <uc8:ucFilePlayerAddUserFile ID="ucFilePlayerAddUserFile2" runat="server" />
        </div>
        <% } %>

        <div id="divExportData" title="Export to Excel" style="display: none;left:20%;">
            <span id="lblExportCallsErrorMsg" style="font-weight: bold; color: Red; display: none;"></span>
            <br />
            <input type="radio" name="exportCall" value="All" />
            All Pages<br />
            <input type="radio" name="exportCall" value="Current" />
            Current Page<br />
            <input type="radio" name="exportCall" value="Selected" />
            Selected Call(s)
        </div>
        <div id="divExportPlaylist" title="Export Playlist" style="display: none; height: 120px; min-height: 120px; overflow: hidden;" class="plSave">
            <span id="lblExportPlaylistMsg" style="font-weight: bold; color: Red; display: none;"></span>
            <input type="radio" name="rbExportPlaylist" checked="checked" value="Wav" style="margin-top: -4px" />
            WAV
            <br />
            <%--<input type="radio" name="rbExportPlaylist" value="Wma" style="margin-top: -4px" />
            WMA
            <br />--%>
            <input id="chkProtectFromChange" type="checkbox" name="chkProtectFromChange" value="IsProtect" style="margin-top: -4px" />
            <label for="chkProtectFromChange" class="ml" lkey="cdEnableWatermarking">Enable SHA256 Watermarking</label>
            <br />
            <br />
            <div id="divExportprogressBar">
                <div></div>
            </div>
        </div>
        <div id="divPlaylistAddFiles" title="Add Files" style="display: none;">
            <br />
            <br />
            <br />
        </div>

        <div id="divPlaylist" title="Create New Playlist" style="display: none;text-align:center;">
            <span id="lblPlErrorMsg" style="font-weight: bold; color: Red; display: none;"></span>
           
            <input id="txtPlaylistName" name="txtPlaylistName" style="text-indent: 0px;" type="text" placeholder="Playlist Name" class="ml" lkey="cdPlName" />
        </div>


        <!--start: dialog boxes-->

        <div id="divConfirmDialog" style="display: none;">
            <span style="color: #000;">
                <label id="lblConfirmMessage"></label>
            </span>
        </div>

        <div id="divSuccessDialog" style="display: none;">
            <span style="color: #000;">
                <label id="lblSuccessMessage"></label>
            </span>
        </div>

        <div id="divInformationDialog" style="display: none;">
            <span style="color: #000;">
                <label id="lblInformationMessage"></label>
            </span>
        </div>

        <div id="divErrorDialog" style="display: none;">
            <span style="color: #000;">
                <label id="lblErrorMessage"></label>
            </span>
        </div>

        <div id="divPopupBookmarks" style="display: none;">
        </div>

        <div id="modalVesselReport" style="display: none;">
            <div role="tabpanel">
                <ul class="nav nav-tabs nav-justified" role="tablist">
                    <li role="presentation" class="active">
                        <a href="#tabvesselhome" aria-controls="tabvesselhome" role="tab" data-toggle="tab">
                            <span class="vessel-rpt-text"><span class="fa fa-info-circle fa-2x" style="vertical-align: middle;">&nbsp;</span> Basic Information</span>
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#tabvessel" aria-controls="tabvessel" role="tab" data-toggle="tab">
                            <span class="vessel-rpt-text"><span class="fa fa-ship fa-2x" style="vertical-align: middle;">&nbsp;</span> Vessel Information</span>
                        </a>
                    </li>
                    <li role="presentation" class="hide">
                        <a href="#tabcertificate" aria-controls="tabcertificate" role="tab" data-toggle="tab">
                            <span class="vessel-rpt-text"><span class="fa fa-certificate fa-2x" style="vertical-align: middle;">&nbsp;</span>Certifications</span>
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#tabbookmark" aria-controls="tabbookmark" role="tab" data-toggle="tab">
                            <span class="vessel-rpt-text"><span class="fa fa-bookmark fa-2x" style="vertical-align: middle;">&nbsp;</span>Inspection</span>
                        </a>
                    </li>
                </ul>

                <div class="tab-content">

                    <div role="tabpanel" class="tab-pane active" id="tabvesselhome">
                        <div class="panel panel-primary">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="container">
                                        <div class=" col-md-12 col-lg-12">
                                            <div class="row vessel-title">
                                                <div class="col survey-title-details">
                                                    <h1 class="survey-comp-name" id="lblSurveyCompanyName"></h1>
                                                    <h2 class="survey-hire">ON HIRE SURVEY REPORT</h2>
                                                </div>
                                            </div>
                                                    
                                            <table class="table">
                                                <tbody>
                                                    <tr class="hide">
                                                        <td>Interviewee</td>
                                                        <td><label id="lblInterviewee" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr class="hide">
                                                        <td>Report Date</td>
                                                        <td><label id="lblReportDate" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Survey Date</td>
                                                        <td><label id="lblSurveyDate" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Inspection Number</td>
                                                        <td><label id="lblInspectionNumber" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Type</td>
                                                        <td><label id="lblVRPrimaryArea" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Vessel Name</td>
                                                        <td><label id="lblVesselNameTitle" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Vessel Id</td>
                                                        <td><label id="lblVesselId" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr id="tr-custom-field-before">
                                                        <td>Inspector Name</td>
                                                        <td><label id="lblUserName" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Inspector Id</td>
                                                        <td><label id="lblUserId" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Inspector Phone</td>
                                                        <td><label id="lblUserPhone" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>GPS</td>
                                                        <td><label id="lblLocation" class="text-bold"></label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Title Notes</td>
                                                        <td><label id="lblTitleNotes" class="text-bold"></label></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                                    
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div role="tabpanel" class="tab-pane" id="tabvessel">
                        <div class="container">
                            <div class="row">
                                <div class="col-xs-12 col-sm-12 col-md-10 col-lg-10 col-xs-offset-0 col-sm-offset-0 col-md-offset-1 col-lg-offset-1">
                                    <div class="panel panel-primary">
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="col-xs-10 col-sm-10 hidden-md hidden-lg">
                                                    <dl>
                                                        <dt>VNAME:</dt>
                                                        <dd><label id="lblVName1"></label></dd>
                                                        <dt>Vendor Name:</dt>
                                                        <dd><label id="lblVendorName1"></label></dd>
                                                        <dt>Contact Information</dt>
                                                        <dd><label id="lblContactInformation1"></label></dd>
                                                        <dt>Type</dt>
                                                        <dd><label id="lblVesselType1"></label></dd>
                                                        <dt>Lngth</dt>
                                                        <dd><label id="lblLngth1"></label></dd>
                                                        <dt>Width</dt>
                                                        <dd><label id="lblWidth1"></label></dd>
                                                        <dt>Depth</dt>
                                                        <dd><label id="lblDepth1"></label></dd>
                                                        <dt>Horse</dt>
                                                        <dd><label id="lblHorse1"></label></dd>
                                                        <dt>Bollard</dt>
                                                        <dd><label id="lblBollard1"></label></dd>
                                                        <dt>POB</dt>
                                                        <dd><label id="lblPOB1"></label></dd>
                                                        <dt>DP</dt>
                                                        <dd><label id="lblDP1"></label></dd>
                                                        <dt>Size</dt>
                                                        <dd><label id="lblSize1"></label></dd>
                                                        <dt>Crane</dt>
                                                        <dd><label id="lblCrane1"></label></dd>
                                                        <dt>ROV</dt>
                                                        <dd><label id="lblROV1"></label></dd>
                                                        <dt>HELI</dt>
                                                        <dd><label id="lblHELI1"></label></dd>
                                                        <dt>Flag</dt>
                                                        <dd><label id="lblFlag1"></label></dd>
                                                        <dt>HomeBase</dt>
                                                        <dd><label id="lblHomeBase1"></label></dd>
                                                    </dl>
                                                </div>
                                                <div class=" col-md-12 col-lg-12">
                                                    <table class="table table-condensed table-vessel-information">
                                                        <tbody>
                                                            <tr>
                                                                <td>VNAME:</td>
                                                                <td><label id="lblVName" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Vendor Name:</td>
                                                                <td><label id="lblVendorName" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Contact Information</td>
                                                                <td><label id="lblContactInformation" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Type</td>
                                                                <td><label id="lblVesselType" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Lngth</td>
                                                                <td><label id="lblLngth" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Width</td>
                                                                <td><label id="lblWidth" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Depth</td>
                                                                <td><label id="lblDepth" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Horse</td>
                                                                <td><label id="lblHorse" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Bollard</td>
                                                                <td><label id="lblBollard" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>POB</td>
                                                                <td><label id="lblPOB" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>DP</td>
                                                                <td><label id="lblDP" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Size</td>
                                                                <td><label id="lblSize" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Crane</td>
                                                                <td><label id="lblCrane" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>ROV</td>
                                                                <td><label id="lblROV" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>HELI</td>
                                                                <td><label id="lblHELI" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>Flag</td>
                                                                <td><label id="lblFlag" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td>HomeBase</td>
                                                                <td><label id="lblHomeBase" class="text-bold"></label></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div role="tabpanel" class="tab-pane" id="tabcertificate">
                        <div class="container">
                            <div class="row">
                                <div class="panel panel-primary">
                                    <div class="panel-body">
                                        <div class="certificate-container">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div role="tabpanel" class="tab-pane" id="tabbookmark">
                        <div class="container">
                            <div class="row">
                                <h2 class="text-primary title"><span class="fa fa-align-justify"></span>Inspected Areas</h2>
                                <div class="panel panel-primary">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="container">
                                                <div class="col-md-12 col-lg-12">
                                                    <div class="inspection-container">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <h2 class="text-danger title hide"><span class="fa fa-align-justify"></span>Non-Inspected Areas</h2>
                                <div class="panel panel-primary hide">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="container">
                                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                                    <div class="non-inspection-container hide">
                                                        <ul></ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div id="modalStandardReport" style="display: none;">

            <div class="container font">
                    <div class="row justify-content-between align-items-center">
                        <div class="col-md-6">
                            <img id="imgIQ3Repots" src="<%= SessionHandler.UserInformation.TenantId > 0 ? (File.Exists(Page.ResolveClientUrl("~/Uploads/UserImages/Tenant_" + SessionHandler.UserInformation.TenantId + "/IQ3Reports.jpg")) ? Page.ResolveClientUrl("~/Uploads/UserImages/Tenant_" + SessionHandler.UserInformation.TenantId + "/IQ3Reports.jpg") : Page.ResolveClientUrl("~/Templates/IQ3StandardReport/images/IQ3Reports.jpg")) : Page.ResolveClientUrl("~/Templates/IQ3StandardReport/images/IQ3Reports.jpg") %>" style="width: 100px; height: 100px; visibility: visible;" alt="IQ3 Reports Picture"/>
                        </div>
                        <div class="col-md-6">
                            <div class="address pull-right">
                                <address>
                                    <p class="address-1">Address 1: </p>
                                    <p class="address-2">Address 2: </p>
                                    <p class="address-3">Address 3: </p>
                                    <p class="city-state-zip">City: </p>
                                    <p class="country-name">Country: </p>
                                </address>
                            </div>
                        </div>
                    </div>
        
                   <div class="container">
                    <div class="row justify-content-between align-items-center py-4 font">
                        <h1 class="text-center header-text">The IQ3 Enterprise Inspection System</h1>
                        <h6 class="text-center">
                            <span class="customer-name-heading"></span><br>
                            <span class="date-time-of-event-heading"></span><br>
                            <span class="inspection-number-heading">INSPECTION NUMBER:</span><br>
                            <span class="inspector-name-heading"></span><br>
                            <span class="inspector-email-heading">Email: </span><br>
                            <span class="inspector-phone-heading">Phone: </span><br>
                            <span class="inspector-location-heading">Location: </span><br>
                        </h6>
                    </div>
                   </div>

                    <div class="row">
                        <div class="col-md-3"></div>
                        <div class="col-md-6">
                            <form action="" class="form-margin">
                                  <div class="input-group mb-3">
                                    <input id="txtReportsName" type="text" class="form-control saveInput" onkeyup="txtReportsNamehandle(event)" placeholder="Report Name" aria-label="Report Name"/>
                                    <span class="input-group-btn">
                                        <button class="btn btn-outline-secondary cusbtn" type="button" id="button-addon2" onclick="UpdateReportName()">SAVE</button>
                                    </span>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-3"></div>
                    </div>
                </div>

                <div class="container font">
                    <div>
                    <div role="tabpanel">
                        <div class="tab-content">
                            <div class="panel panel-primary">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="container">
                                                <div class=" col-md-12 col-lg-12">
                                                    <div class="row vessel-title">
                                                        <div class="col survey-title-details">
                                                            <h1 class="survey-comp-name"></h1>
                                                            <h2 class="survey-hire text-center">STANDARD REPORT</h2>
                                                        </div>
                                                    </div>
    
                                                    <table class="table">
                                                        <tbody>
                                                            <tr style="display:none;">
                                                                <td width="50%">Interviewee</td>
                                                                <td width="50%"><label id="lblSRInterviewee" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr style="display:none;">
                                                                <td width="50%">Report Date</td>
                                                                <td width="50%"><label id="lblSRReportDate" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td width="50%">Survey Date</td>
                                                                <td width="50%"><label id="lblSRSurveyDate" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td width="50%">Inspection Number</td>
                                                                <td width="50%"><label id="lblSRInspectionNumber" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td width="50%">Inspection Name</td>
                                                                <td width="50%"><label id="lblSRInspectionNameTitle" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td width="50%">Type</td>
                                                                <td width="50%"><label id="lblSRPrimaryArea" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr id="trSR-custom-field-before">
                                                                <td width="50%">Inspector Name</td>
                                                                <td width="50%"><label id="lblSRUserName" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td width="50%">Inspector Id</td>
                                                                <td width="50%"><label id="lblSRUserId" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td width="50%">Inspector Phone</td>
                                                                <td width="50%"><label id="lblSRUserPhone" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td width="50%">GPS</td>
                                                                <td width="50%"><label id="lblSRLocation" class="text-bold"></label></td>
                                                            </tr>
                                                            <tr>
                                                                <td width="50%">Title Notes</td>
                                                                <td width="50%"><label id="lblSRTitleNotes" class="text-bold"></label></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            </div>


                            <div class="container">
                                <div class="row justify-content-between align-items-center py-4">
                                    <h1 class="text-center header-text">Inspection Summary</h1>
                                </div>
                            </div>


                            <div class="panel panel-primary box">
                                <div class="panel-body">
                                    <div class="container">
                                        <div class="row">
                                            <div class="row vessel-title">
                                                <div class="col survey-title-details">
                                                    <h1 class="survey-comp-name"></h1>
                                                    <h2 class="survey-hire text-center">INSPECTED AREAS</h2>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row inspection-container">
                            
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

        </div>


        <%--<div id="divPlaylistVisibility" title="Playlist Visibility" style="display: none; text-align: center; overflow:hidden;">
            <div class="row">
                <span id="lblVisibilityErrorMsg" style="font-weight: bold; color: Red; display: none;"></span>
            </div>
            <div class="float-container" id="divVisibilityType">
                <div class="float-child">
                    <input id="radPrivateVisibility" type="radio" name="visibilityType" checked="checked" title="Private" value="0" /><label for="radPrivateVisibility" class="ml" lkey="plPrivateVisibility">Private Visibility</label> &nbsp;&nbsp;
                </div>
                <div class="float-child">
                    <input id="radPublicVisibility" type="radio" name="visibilityType" title="Public" value="1" /><label for="radPublicVisibility" class="ml" lkey="plPublicVisibility">Public Visibility</label>
                </div>
            </div>
            <div class="">
                <textarea id="txtPLComments" name="txtPLComments" placeholder="Playlist Comments" class="ml" lkey="plComments" cols="6" rows="3"></textarea>
            </div>
        </div>--%>

        <!--end: dialog boxes-->
        
        <div id="divInqMessage" style="display:none">
            <span class="ml" lkey="spnInqMessage">
                Experience the power of the Revcord Multimedia Logger for all your Interviews, Investigations, and Inspections.  Revcord has written a plugin to the Inquire technology.  Inquire creates interviews, investigations, and incidents using an Android or IOS application.  Stream them live to the Revcord server, record with bookmarks and notes, and automatically upload to the Revcord Server when complete.  Manage all of your interviews, investigations, and incidents in one location.  Watch them live or later search on date, time, case ID, interviewer/investigator, interviewee, bookmarks, bookmark notes, GPS location, and device ID.  In addition, perform QA evaluations and run reports on any of the Search indices. 
                <br/><br/>
                 Your Revcord system is preconfigured and ready to go.  All you have to do is go into the Setup Tab and begin to Invite Inquire Users free for the first 60 days.  If you are interested in continuing with Inquire after 60 days, please contact your dealer or Revcord. 
                <br/><br/> For further information you can visit the Inquire website at www.inquiresys.com or <a style="color: #CC0000" href="https://www.youtube.com/watch?v=vfx5ppp_8x0&feature=youtu.be">click here</a> for a three minute video. 
                <br/><br/> Verticals include Insurance Companies, Police Departments, Child Protective Services, Marine Industry, Oil Field Industry, Fire Departments, CSI Investigators, Inspectors, and Law Firms. <br/><br/><br/> <input type="checkbox" id="plugincheckbox" style="margin-bottom: 5px;" class="chckbx" value="1" /> Dismiss this message on the Revcord Web User Interface
            </span>
        </div>

        <div id="divmetadataa" title="Internal notes" style="display: none;">
            <ul class="activity-list" style="overflow: auto; height: 350px;">
                <li>
                    <div class="row" style="margin: 0px;">
                        <p><i class="fa fa-bell activity-image"></i>You have 5 pending alerts for account</p>

                    </div>
                </li>
                <li>
                    <div class="row" style="margin: 0px;">
                        <p><i class="fa fa-fire activity-image"></i>Server crash happened <span class="label label-danger">warning</span></p>

                    </div>
                </li>
                <li>
                    <div class="row" style="margin: 0px;">
                        <p><i class="fa fa-flag-o activity-image"></i>You have 5 pending alerts for account</p>

                    </div>
                </li>
                <li>
                    <div class="row" style="margin: 0px;">
                        <p><i class="fa fa-smile-o activity-image"></i>New user registration <span class="label label-success">great</span></p>

                    </div>
                </li>
                <li>
                    <div class="row">
                        <p><i class="fa fa-bell activity-image"></i>You have 5 pending alerts for account</p>

                    </div>
                </li>
            </ul>
        </div>
        <div id="divattachedfiless" title="Attached Files" style="display: none;">
            <div id="divIQ3Report" style="display:none;">
                <div class="row">
                    <div class="col-xs-8">
                        <p><i class="fa fa-file-word-o" style="font-size: 28px; color: blue"></i>Document</p>
                    </div>
                    <div class="col-xs-4 text-right">
                        <a id="lnkPDFDownload" href="#" class="btn btn-primary btn-xs remove-tr"><i class="fa fa-download"></i></a>
                    </div>
                </div>
            </div>
            <div id="divAttachedFilesWrapper" style="overflow: auto; height: 350px;">
                <ul id="ulPlaylistExportReportFiles" class="activity-list" style="height: auto;">
                </ul>
                <ul id="ulPlaylistUserAddedFiles" class="activity-list" style="height: auto;">
                </ul>
                <ul id="ulPaperClip" class="activity-list" style="height: auto;">
                    <li>
                        <div class="row">
                            <div class="col-xs-8">
                                <p><i class="fa fa fa-file-word-o" style="font-size: 28px; color: blue"></i>Document</p>
                            </div>
                            <div class="col-xs-4 text-right">
                                <a id="lnkRemovePlAttachedFile" href="#" class="btn btn-danger btn-xs remove-tr"><i class="fa fa-times"></i></a>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div id="divinternalnotess" title="Internal notes" style="display: none;">
            <div class="input-group">
                <%--<input id="txtInternalNote" type="text" class="form-control nrb" />--%>
                <textarea id="txtInternalNote" name="internalNotes" rows="3" cols="30"></textarea>
                <span class="input-group-btn">
                    <button id="btnAddInternalNote" class="ml btn btn-primary" lkey="ucCMAdd" type="button" style="margin-left:30px;">ADD</button>
                </span>
            </div>
            <ul id="ulInternalNote" class="activity-list" style="overflow-y: auto; overflow-x: hidden; height: auto; max-height:200px;">
            </ul>

        </div>
        <div id="divSharednotess" title="Shared notes" style="display: none;">
            <div class="input-group">
                <%--<input id="txtSharedNote" type="text" class="form-control nrb" />--%>
                <textarea id="txtSharedNote" name="sharedNotes" rows="3" cols="30"></textarea>
                <span class="input-group-btn">
                    <button id="btnAddSharedNote" class="ml btn btn-primary" lkey="ucCMAdd" type="button" style="margin-left:30px;">ADD</button>
                </span>
            </div>
            <ul id="ulSharedNote" class="activity-list" style="overflow-y: auto; overflow-x:hidden; height: auto; max-height:200px;">
            </ul>
        </div>

        <TInquire:PictureEvent ID="pictureEvent" runat="server" />

        <div id="divLicenseStatusMessage" style="display: none">
            <div id="divRevshieldMessage">
                <span class="ml" lkey="SiteName">Site Name : </span>
                <span id="lblSiteName" class="bold large"></span>
                <br />
                <span class="ml" lkey="YourSerialNumber">Your Revcord System Serial Number Is: </span>
                <span id="lblSerialNo" class="bold large"></span>
                <br />
                <span id="spanRevshieldActive">
                    <span class="ml" lkey="YourRevshieldStatus">Your Revshield Maintenance Status Is: </span>
                    <span id="lblRevshieldStatus" class="bold large"></span>
                    <%--<br />
                    <span class="ml" lkey="YourRevshieldExpiresOn">RevShield Expires On: </span>
                    <span id="lblRevshieldExpiryDate" class="bold large"></span>--%>
                </span>
                <br />
                <span class="ml" lkey="YourLicenseStatus">Your License Status Is: </span>
                <span id="lblLicenseStatus" class="bold large"></span>
                <br />
            </div>
            <div id="divServiceContract">
                <span class="ml" lkey="spnLicenseStatusMessage">
                    <br />
                    Your service contract for Revcord has expired. 
                <br />
                    <br />
                    To speak with someone in regards to getting your service contract up to date please contact your sales associate directly or call 281-404-7040
                <br />
                    <br />
                </span>
                <div id="divRemoveBlinkImage">
                    <a id="lnkRemoveBlinkImage" style="color: cornflowerblue; cursor: pointer;" class="ml" lkey="tClick">Click</a> &nbsp;<span class="ml" lkey="spnRemoveBlinkImageMessage">here to to turn off the blinking icon with one that does not blink.</span>
                </div>
            </div>
            <div id="divOnsiteContactInformationView">
                <a id="lnkOnsiteContactInformation" style="color: cornflowerblue; cursor: pointer;" class="ml" lkey="tClick">Click</a> &nbsp;<span class="ml" lkey="spnOnsiteContactInformationMsg">here to view onsite contact information.</span>
            </div>
        </div>
        <div id="divEditPlaylist" title="Edit Playlist Name" style="display: none; text-align: center; overflow:hidden;">
            <div class="row">
                <span id="lblPlaylistErrorMsg" style="font-weight: bold; color: Red; display: none;"></span>
            </div>
            <div class="row" id="divVisibilityType">
                <div class="float-child">
                    <input id="radPrivateVisibility" type="radio" name="visibilityType" checked="checked" title="Private" value="0" /><label for="radPrivateVisibility" class="ml" lkey="plPrivateVisibility">Private Visibility</label>
                </div>
                <div class="float-child">
                    <input id="radPublicVisibility" type="radio" name="visibilityType" title="Public" value="1" /><label for="radPublicVisibility" class="ml" lkey="plPublicVisibility">Public Visibility</label>
                </div>
            </div>
            <div class="row">
                <input id="txtEditPlaylistName" name="txtEditPlaylistName" style="text-indent: 0px; width: 90% !important; " type="text" placeholder="Playlist Name" class="ml" lkey="cdPlName" />
            </div>
            <div class="row">
                <textarea id="txtPLComments" name="txtPLComments" style="width: 90% !important;" placeholder="Playlist Comments" class="ml" lkey="plComments" cols="6" rows="3"></textarea>
            </div>
        </div>

        <div id="dlgPlaylistShare" style="display: none;">
            <div class="">
                <label id="lblPlaylistShareError" class="danger"></label>
            </div>
            <div class="">
                <div class="col-md-12">
                    <div class="row" style="margin: 0em; margin-top: 0.8em;">
                        <div class="col-md-8 col-sm-8" style="padding-left: 0px;">
                            <div class="input-group" style="width: 100%;">
                                <input id="txtAddEmailRecipient" type="text" placeholder="Recipient Email" class="form-control ml" lkey="srRecipientEmail" />
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-4" style="padding-right: 0px;">
                            <div class="btnCenterAlign" style="width: 100%;">
                                <span id="btnAddEmailRecipient" class="btn btn-primary btn-sm ml" lkey="srAddRecipient" style="margin-right: 5px; line-height: 25px; width: 100%;">Add Recipient</span>
                            </div>
                        </div>
                    </div>

                    <div class="row" style="margin: 0em; margin-top: 0.8em; height: 450px;">
                        <div class="col-md-6" style="padding: 0px; padding-right: 4px; height: 100%;">
                            <div class="input-group" style="width: 100%">
                                <table id="tblSysUsersForEmail" class="table table-hover tblEmailUser">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" /></th>
                                            <th>#</th>
                                            <th>System User's Email</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="col-md-6" style="padding: 0px; padding-left: 4px; height: 100%;">
                            <div class="input-group" style="width: 100%;">
                                <table id="tblRecipientsForEmail" class="table table-hover tblEmailUser">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" /></th>
                                            <th>#</th>
                                            <th>Recipient Email</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="divEditPlaylistNote" title="Edit Playlist Note" style="display: none; text-align: center;">
            <span id="lblEditPlaylistNoteErrorMsg" style="font-weight: bold; color: Red; display: none;"></span>
            <%--<input id="txtEditedNote" name="txtEditedNote" style="text-indent: 0px;" type="text" />--%>
            <textarea id="txtEditedNote" name="txtEditedNote" rows="3" cols="30"></textarea>
        </div>
        <div id="divPaperClip" title="Paper Clip" style="display: none;">
            <div id="message_box"></div>
            <br />
            <div id="dZUploadpaperClip" class="dropzone dZUploadpaperClip">
                <table style="float: left; margin-right: 35px;">
                    <tr>
                        <td>
                            <input type="button" class="fileinput-button-paperclip btn btn-primary btn-medium ml" value="Browse" style="" />
                        </td>
                        <td>
                            <div class="dz-default dz-message">
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div id="divMapPlaylist" title="Map Playlist" style="display: none;">
        </div>
        <!--<div id="divExportPlaylist" title="Export Playlist" style="display: none; height: 120px; min-height: 120px; overflow: hidden;" class="plSave">
            <span id="lblExportPlaylistMsg" style="font-weight: bold; color: Red; display: none;"></span>
            <input type="radio" name="rbExportPlaylist" checked="checked" value="Wav" style="margin-top: -4px" />
            WAV
            <br />
            <input id="chkProtectFromChange" type="checkbox" name="chkProtectFromChange" value="IsProtect" style="margin-top: -4px" />
            <label for="chkProtectFromChange" class="ml" lkey="cdEnableWatermarking">Enable SHA256 Watermarking</label>
            <br />
            <div id="divExportprogressBar">
                <div></div>
            </div>
        </div>
    </div>-->
    </form>
        
</body>
<script src='../assetsNew/js/plugins/jquery.pnotify.js'></script>
<script src='../assetsNew/js/plugins/jquery.sparkline.min.js'></script>
<script src='../assetsNew/js/plugins/mwheelIntent.js'></script>
<script src='../assetsNew/js/plugins/mousewheel.js'></script>
<script src='../assetsNew/js/bootstrap/tab.js'></script>
<script src='../assetsNew/js/bootstrap/dropdown.js'></script>
<script src='../assetsNew/js/bootstrap/tooltip.js'></script>
<script src='../assetsNew/js/bootstrap/collapse.js'></script>
<script src='../assetsNew/js/bootstrap/scrollspy.js'></script>
<script src='../assetsNew/js/bootstrap/popover.js'></script>
<script src='../assetsNew/js/plugins/bootstrap-datepicker.js'></script>
<script src='../assetsNew/js/bootstrap/transition.js'></script>
<script src='../assetsNew/js/plugins/jquery.knob.js'></script>
<script src='../assetsNew/js/plugins/jquery.flot.min.js'></script>
<script src='../assetsNew/js/plugins/fullcalendar.js'></script>
<%--<script src='../assetsNew/js/plugins/datatables/datatables.min.js'></script>--%>
<script src='../assetsNew/js/plugins/chosen.jquery.min.js'></script>
<script src='../assetsNew/js/plugins/jquery.timepicker.min.js'></script>
<script src='../assetsNew/js/plugins/daterangepicker.js'></script>
<script src='../assetsNew/js/plugins/colpick.js'></script>
<script src='../assetsNew/js/plugins/moment.min.js'></script>
<%--<script src='../assetsNew/js/plugins/datatables/bootstrap.datatables.js'></script>--%>
<script src='../assetsNew/js/bootstrap/modal.js'></script>
<script src='../assetsNew/js/plugins/raphael-min.js'></script>
<script src='../assetsNew/js/plugins/morris-0.4.3.min.js'></script>
<script src='../assetsNew/js/plugins/justgage.1.0.1.min.js'></script>
<script src='../assetsNew/js/plugins/jquery.maskedinput.min.js'></script>
<script src='../assetsNew/js/plugins/jquery.maskmoney.js'></script>
<script src='../assetsNew/js/plugins/summernote.js'></script>
<%--<script src='../assetsNew/js/plugins/dropzone-amd-module.js'></script>--%>
<script src='../assetsNew/js/plugins/jquery.validate.min.js'></script>
<script src='../assetsNew/js/plugins/jquery.bootstrap.wizard.min.js'></script>
<script src='../assetsNew/js/plugins/jscrollpane.min.js'></script>
<script src='../assetsNew/js/application.js'></script>

<%--<script src='../assetsNew/js/template_js/table.js'></script>--%>

<% if (Request.Browser.Browser != "InternetExplorer")
{ %>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/zip.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/zip-ext.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/lame.all.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/MediaExportProcessor.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/recorder.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/FilePlayer.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/StreamSaver.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/FileSaver.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/Blob.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/pdfobject.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/prism.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/web-streams-polyfill.js") + AppSettingsUtil.GetString("versionNo", "?version=")%>" type="text/javascript"></script>
    <%--<script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/require.js")+ AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>--%>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/CommonConstants.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/FilePlaybackItem3.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/html5fileplayer.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/Sequencer.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/html5player/ScreenVideo.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
<% } %>

<script src="<%= Page.ResolveClientUrl("~/assets/js/common/masterPage.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
<script src="<%= Page.ResolveClientUrl("~/assets/DropzoneJS/assets/dropzone.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
<script src="<%= Page.ResolveClientUrl("~/assets/js/jQuery/jquery.contextmenu.js") + AppSettingsUtil.GetString("versionNo", "?version=") %>" type="text/javascript"></script>
<script type="text/javascript" id="zsiqchat">var $zoho=$zoho || {};$zoho.salesiq = $zoho.salesiq || {widgetcode: "3b92f7954c8a215562ad2d89fac735cf67d6b187d79a5d21dfe0c6a59a59d65f", values:{},ready:function(){}};var d=document;s=d.createElement("script");s.type="text/javascript";s.id="zsiqscript";s.defer=true;s.src="https://salesiq.zoho.com/widget";t=d.getElementsByTagName("script")[0];t.parentNode.insertBefore(s,t);</script>     
</html>
