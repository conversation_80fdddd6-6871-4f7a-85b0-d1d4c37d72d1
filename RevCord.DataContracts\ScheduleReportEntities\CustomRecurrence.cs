﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.ScheduleReportEntities
{
    public class CustomRecurrence
    {
        public Repeat objRepeat { get; set; }
        public RepeatEnd objRepeatEnd { get; set; }

        public DateTime? GetNextReportOn(DateTime? LastReportOn = null, int SendCounter = 0)
        {
            DateTime? NextReportOn = null;

            if (objRepeatEnd.EndStatus(LastReportOn, SendCounter) == true)
            {
                NextReportOn = null;
            }
            else
            {
                NextReportOn = objRepeat.GetNextReportOn(LastReportOn, SendCounter);
            }

            return NextReportOn;
        }
    }
}
