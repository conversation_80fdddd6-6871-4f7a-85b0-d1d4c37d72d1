﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RevcellEntities
{
    [JsonObject("data")]
    public class NumberGroupMembership
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("number_group_id")]
        public string NumberGroupId { get; set; }

        [JsonProperty("phone_number")]
        public PhoneNumber PhoneNumber { get; set; }
    }
}