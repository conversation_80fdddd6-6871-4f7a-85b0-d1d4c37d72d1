﻿<%@ Page Title="MMS::Manage Welders" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="ManageWelders.aspx.cs" Inherits="RevCord.VoiceRec.WebUIClient.Iwb.ManageWelders" %>

<%@ Import Namespace="RevCord.Util" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        .select2-results__options {
            max-height: 10rem !important;
        }

        .dropzone .dz-default.dz-message {
            display: none;
        }

        .form-check {
            padding-right: 20px;
        }
        div.dt-scroll-body thead {
            display: none;
        }
        .table-container {
           overflow-x: unset;
        }
    </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
    <!-- Filter Row -->
    <div class="row filter-row g-2 align-items-center">
        <!-- Root Filter -->
        <div class="col-xl-2 col-lg-3 col-md-3 col-sm-3">
            <label for="dateTimeRange" class="form-label">Welder Name</label>
            <div class="input-group ">
                <input id="txtFilterWelderName" type="text" class="form-control" id="searchInput" placeholder="Welder Name">
            </div>
        </div>


        <!-- Other Filters -->
        <div class="col-xl-2 col-lg-3 col-md-3 col-sm-3">
            <label for="dateTimeRange" class="form-label">Stencil Number</label>
            <div class="input-group">
                <input id="txtFilterStencilNo" type="text" class="form-control" placeholder="Stencil Number">
            </div>
        </div>



        <!-- Action Buttons -->
        <div class="col-xl-4 col-lg-6 col-md-4 d-flex gap-2" style="margin-top: 40px;">
            <button id="btnSearchWelder" class="btn btn-primary flex-grow-1"><i class="fa-solid fa-magnifying-glass"></i>&nbsp;Search</button>
            <button id="btnResetWelder" class="btn flex-grow-1" style="border: 1px solid red;"><i class="fas fa-undo"></i><span>&nbsp;Reset Filter</span></button>
        </div>

    </div>

    <!-- Second Row with Three Search Fields and Buttons -->
    <div class="row search-row g-2 align-items-center mb-2 mt-2">
        <!-- Left Side -->
        <div class="col-md-7 d-flex align-items-center">
            <div class="me-3 fw-bold fs-5">
                Welder Records
            </div>
        </div>

        <!-- Right Side -->
        <div class="col-md-5 d-flex justify-content-end">
            <a id="btnAddWelder" class="add-welder btn btn-primary"><i class="fa fa-plus-circle"></i>
                <span lkey="" class="ml">Add New Welder</span>
            </a>
        </div>
    </div>


    <!-- Upload WPQ Modal -->
    <div class="modal fade" id="uploadWPQModal" tabindex="-1" role="dialog" aria-labelledby="uploadWPQModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg custom-width" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Upload WPQ</h5>
                </div>

                <div class="modal-body">
                    <div class="uploadwpq-validation-summary text-danger mb-2"></div>
                    <!-- Select Job -->
                    <%--<div class="form-group">
                          <label for="selectJob">Select Job<span class="text-danger fw-bold">*</span></label>
                            <select id="selectJob" class="form-control">
                                <option value="">Select Job</option>
               
                            </select>
                        </div>--%>

                    <div class="form-group">
                        <label class="control-label" style="font-weight: bold;">Select Job<span class="text-danger fw-bold">*</span> :</label>
                        <select id="selectJob" style="width: 250px; line-height: 1.5; min-height: calc(1.5em + 0.75rem + 2px);" class="select2-selection__arrow">
                            <option value="">Select Job</option>
                        </select>
                    </div>


                    <!-- Upload Type -->
                    <label>Select Upload Type<span class="text-danger fw-bold">*</span></label>
                    <div class="form-group d-flex">
                        <div class="form-check mr-3">
                            <input class="form-check-input" type="radio" name="uploadType" value="upload" id="uploadRadio">
                            <label class="form-check-label" for="uploadRadio">Upload</label>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="uploadType" value="manual" id="manualRadio">
                            <label class="form-check-label" for="manualRadio">Manual</label>
                        </div>
                    </div>

                    <input type="hidden" id="userIdHidden" />

                    <!-- Upload Fields (file + date) -->
                    <div id="uploadFields" class="mt-3 d-none">
                        <div class="form-group">
                            <label for="wpqFile">Choose File</label>
                            <input type="file" id="wpqFile" class="form-control-file">
                        </div>

                        <%-- <div class="form-group">
            <label for="txtExpDate">Expired Date</label>
            <input type="text" id="txtExpDate" class="form-control datepicker">
          </div>--%>
                        <div class="form-group">
                            <label class="form-label">Expired Date <span class="text-danger fw-bold">*</span> :</label>
                            <div class="input-group">
                                <input id="txtExpDate" class="form-control deadline-date" readonly />
                                <span class="input-group-text" id="expDateCalendarIcon">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="modal-footer">
                    <button id="btnUploadWpq" type="button" class="btn btn-primary">Submit</button>
                    <button type="button" class="btn btn-secondary" id="closeModalBtn">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Job Modal -->
<div class="modal fade" id="addJobModal" tabindex="-1" aria-labelledby="addJobModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="addJobModalLabel">Add Job</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <form id="jobForm">

                    <div class="col-md-12 mt-3">
                        <div class="form-group">
                            <label class="control-label" style="font-weight: bold;">Job Title<span class="text-danger fw-bold">*</span> :</label>
                            <select id="ddlJobTitle" style="width: 250px; line-height: 1.5; min-height: calc(1.5em + 0.75rem + 2px);" class="select2-selection__arrow">
                                <option value="">Choose Job Title</option>
                            </select>
                        </div>
                    </div>

                    <%--<div class="col-md-12 mt-3">
                        <div class="form-group">
                            <label class="form-label">Job Title <span class="text-danger fw-bold">*</span> :</label>
                            <select id="ddlJobTitle" required class="form-control select2 ddl-TestType col-md-8">
                                <option value="">Choose Job Title</option>
                            </select>
                        </div>
                    </div>--%>

                    <div class="col-md-12 mb-2">
                        <div class="form-group">
                            <label class="form-label" id="lblJobStartDate">Start Date: </label>
                        </div>
                    </div>

                    <div class="col-md-12 mb-2">
                        <div class="form-group">
                            <label class="form-label" id="lblJobEndDate">End Date: </label>
                        </div>
                    </div>

                    <div class="col-md-12 mb-2">
                        <div class="form-group">
                            <label class="form-label" id="lblJobLocations">Location: </label>
                        </div>
                    </div>

                    <div class="col-md-12 mb-2">
                        <div class="form-group">
                            <label class="form-label" id="lblJobWPS">WPS</label>
                        </div>
                    </div>

                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="assignJob()">Assign Job</button>
            </div>

        </div>
    </div>
</div>


</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">

    <div class="table-container">
        <table id="tblWelders" class="table-bordered text-center" style="width: 100%;">
            <thead class="header-row">
                <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>SSN</th>
                    <th>Stencil Number</th>
                    <th>DOB</th>
                    <th>City</th>
                    <th>Action(s)</th>
                </tr>
            </thead>
            <tbody class="table-body">
            </tbody>
        </table>
    </div>


</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/dropzone/dropzone.min.js") %>" type="text/javascript"></script>
    <script>
        const siteBaseUrl = "<%= SiteConfig.WebURL %>";
    </script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/iwb.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/datatables.net-bs5/dataTables.bootstrap5.min.js") %>" type="text/javascript"></script>
</asp:Content>
