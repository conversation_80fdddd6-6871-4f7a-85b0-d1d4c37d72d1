﻿using RevCord.VoiceRec.WebUIClient.Classes;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace RevCord.VoiceRec.WebUIClient.Iwb
{
    public partial class ManageDocuments : PageBase
    {
        public int TenantId { get { return SessionHandler.UserInformation.TenantId; } }
        protected void Page_Load(object sender, EventArgs e)
        {
            var docPermissions = SessionHandler.UserInformation.IwbUserRole.Modules.FirstOrDefault(d => d.Name == "Manage Documents").Permissions;
            var permissions = Newtonsoft.Json.JsonConvert.SerializeObject(docPermissions);
            ClientScript.RegisterClientScriptBlock(this.GetType(), "IwbPermissions", string.Format("var permissions = {0};", permissions), true);
        }


        #region Rendering

        [WebMethod]
        public static string LoadUserControlAddNew()
        {
            // Since AddDocument is now a web page (.aspx), return the URL for navigation
            return "AddDocument.aspx";
        }

        public override void VerifyRenderingInServerForm(Control control) { }

        #endregion
    }
}