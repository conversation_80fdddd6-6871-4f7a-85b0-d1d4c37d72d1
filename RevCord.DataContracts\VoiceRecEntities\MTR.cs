﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using RevCord.DataContracts.IQ3InspectionEntities;

namespace RevCord.DataContracts.VoiceRecEntities
{
    public class MTRCertificate
    {
        public string CompanyName { get; set; }
        public string HeatAndPiece { get; set; }
        public DateTime Date { get; set; }
        public CustomerDetails SoldTo { get; set; }
        public CustomerDetails ShipTo { get; set; }
        public Specification Specification { get; set; }
        public ManufacturingDetails ManufacturingDetails { get; set; }
        public List<MechanicalTest> MechanicalTests { get; set; }
        public List<ChemicalComposition> ChemicalCompositions { get; set; }
        public Certification Certification { get; set; }
        public Signature Signature { get; set; }
    }

    public class CustomerDetails
    {
        public string Name { get; set; }
        public string Address { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class Specification
    {
        public string Grade { get; set; }
        public string Dimensions { get; set; }
        public string Standards { get; set; }
    }

    public class ManufacturingDetails
    {
        public string ProcessDetails { get; set; }
    }

    public class MechanicalTest
    {
        public string TestName { get; set; }
        public string ResultValue { get; set; }
        public string StandardValue { get; set; }
    }

    public class ChemicalComposition
    {
        public string Element { get; set; }
        public string ReportValue { get; set; }
        public string StandardValue { get; set; }
    }

    public class Certification
    {
        public string ManagerName { get; set; }
        public string ComplianceDetails { get; set; }
    }

    public class Signature
    {
        public int SignatureID { get; set; }
        public int ReportID { get; set; }       
        public string SignatoryName { get; set; }
        public string Role { get; set; }
        public DateTime SignatureDate { get; set; }
        public string SignatureDateString { get; set; }
    }

    public class Checklist
    {
        public int ChecklistID { get; set; }
        public int ReportID { get; set; }
        public bool IsVerified { get; set; }
        public DateTime? VerifiedDate { get; set; }
        public int VerifiedBy { get; set; }
    }

}
