﻿<%@ Page Title="MMS::My WPQ" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="MyWPQ.aspx.cs" Inherits="RevCord.VoiceRec.WebUIClient.Iwb.MyWPQ" %>

<%@ Import Namespace="RevCord.Util" %>
<%@ Register TagName="ucSignOffDialog" TagPrefix="uc8" Src="~/UserControls/RevSignControls/ucSignOffControl.ascx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
       .table-container {
           overflow-x: unset;
        }
</style>
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/asset/lib/dropzone/basic.min.css")%>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/asset/lib/dropzone/dropzone.min.css")%>" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
   <!-- Second Row with Three Search Fields and Buttons -->
    <div class="row search-row g-2 align-items-center mb-4 mt-0">
        <!-- Left Side -->
        <div class="col-md-7 d-flex align-items-center">
            <div class="me-3 fw-bold fs-5">
                WPQ Records
            </div>
        </div>
    </div>
    
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">
    <div class="table-container">
        <table id="tblWPQRecods" class="table-bordered text-center" style="width: 100%;">
            <thead class="header-row">
                <tr>
                    <th>#</th>
                    <th>Job Id</th>
                    <th>Expiry Date</th>
                    <th>Certificate Authority</th>
                    <th>Inspector Name</th>
                    <th>Lab No</th>
                    <th>Notes</th>
                    <th>Action(s)</th>
                </tr>
            </thead>
            <tbody class="table-body">
            </tbody>
        </table>
    </div>

    
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/common.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/mywpq.js") %>" type="text/javascript"></script>
    <script>
        const siteBaseUrl = "<%= SiteConfig.WebURL %>";
        getList();
    </script>
  <script src="<%= Page.ResolveClientUrl("~/asset/lib/datatables.net-bs5/dataTables.bootstrap5.min.js") %>" type="text/javascript"></script>
</asp:Content>

