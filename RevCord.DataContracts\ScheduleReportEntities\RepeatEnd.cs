﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.ScheduleReportEntities
{
    public class RepeatEnd
    {
        public bool? Never { get; set; }
        public string EndsOn { get; set; }
        public int? EndsAfter { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public RepeatEndType objRepeatEndType { get; set; }
        public RepeatEnd()
        {
            this.objRepeatEndType = RepeatEndType.Never;
        }
        public RepeatEnd(int EndsAfter)
        {
            this.EndsAfter = EndsAfter;
            this.objRepeatEndType = RepeatEndType.After;
        }

        public RepeatEnd(string EndsOn)
        {
            if (!string.IsNullOrEmpty(EndsOn))
            {
                this.EndsOn = EndsOn;
                this.objRepeatEndType = RepeatEndType.EndsOn;
            }
            else
            {
                this.objRepeatEndType = RepeatEndType.Never;
            }
        }
        public bool EndStatus(DateTime? LastReportOn = null, int SendCounter = 0)
        {
            switch (objRepeatEndType)
            {
                case RepeatEndType.Never:
                    return false;

                case RepeatEndType.EndsOn:
                    if (Convert.ToDateTime(EndsOn).Subtract(DateTime.Now).Days > 0)
                    {
                        return false;
                    }
                    return true;

                case RepeatEndType.After:
                    if (EndsAfter >= SendCounter)
                    {
                        return false;
                    }
                    return true;
            }

            return false;
        }
    }

    public enum RepeatEndType
    {
        [DescriptionAttribute("Recurrence Ends Never")]
        Never,
        [DescriptionAttribute("Recurrence Ends On Date")]
        EndsOn,
        [DescriptionAttribute("Recurrence Ends After #times")]
        After
    }
}
