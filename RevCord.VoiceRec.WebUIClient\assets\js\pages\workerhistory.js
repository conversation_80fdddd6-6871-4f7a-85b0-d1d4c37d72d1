const iwbWorkerBaseUrl = "../Handlers/IwbHandlers/WorkerHistoryHandler.ashx";

let isActiveWelderDownloaded = false;
let isPreviousWelderDownloaded = false;

function getHistory() {
    debugger;
    callAjax("POST", iwbWorkerBaseUrl + "?method=contractor-history", "application/json", null, function (result) {
        if (result != undefined && result != null && result != "") {
            let response = JSON.parse(result);
            populateWeldersTable(response.data);
        }
        else {
            console.log("getHistory => No Result => ", result);
        }
    });
}

function populateWeldersTable(resultData) {
    $('#tblWelders').dataTable({
        destroy: true,
        processing: true,
        serverSide: false,
        ordering: true,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: true,
        pageLength: pageSize,
        responsive: true,
        data: resultData,
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.Id);
        },
        columns: [
            {
                data: '#', sortable: false, width: "5%", class: "",
                //render: function (data, type, row, meta) {
                //    return meta.row + meta.settings._iDisplayStart + 1;
                //}

                 render: function (data, type, row, meta) {
                    return meta.row + 1;
                }
            },
            { data: 'Title', width: '10%', class: "" },
            { data: 'OrganizationName', width: '10%', class: "" },
            { data: 'StartDate', width: '10%', class: "" },
            { data: 'EndDate', width: '10%', class: "" },
            { data: 'Location', width: '10%', class: "" },
            { data: 'Rating', width: '10%', class: "" }
        ],
        language: {
            paginate: {
                previous: 'Prev',
            },
            processing: "Loading Data...",
            zeroRecords: "No matching records found",
            emptyTable: "No data available in the table"
        },

    });
}

function getWelderHistory(isActive) {
    debugger;
    if ((isActive == 1 && !isActiveWelderDownloaded) || (isActive == 0 && !isPreviousWelderDownloaded)) {
        var inputData = {
            isActive
        };

        callAjax("POST", iwbWorkerBaseUrl + "?method=welder-history", "application/x-www-form-urlencoded", inputData, function (result) {
            if (result != undefined && result != null && result != "") {
                let response = JSON.parse(result);

                let tableControlId = isActive == 1 ? "#tblActiveWelders" : "#tblPreviousWelders";
                populateWelderHistoryTable(response.data, tableControlId);

                if (isActive = 1) {
                    isActiveWelderDownloaded = true;
                } else {
                    isPreviousWelderDownloaded = true;
                }
            }
            else {
                debugger;
                console.log("getWelderHistory => No Result => ", result);
            }
        });
    }
}

function populateWelderHistoryTable(resultData, tableControlId) {
    $(tableControlId).dataTable({
        destroy: true,
        processing: true,
        serverSide: false,
        ordering: true,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: true,
        pageLength: pageSize,
        responsive: true,
        data: resultData,
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.Id);
        },
        columns: [
            {
                data: '#', sortable: false, width: "5%", class: "",
                //render: function (data, type, row, meta) {
                //    return meta.row + meta.settings._iDisplayStart + 1;
                //}

                render: function (data, type, row, meta) {
                    return meta.row + 1;
                }
            },
            { data: 'UserName', width: '10%', class: "" },
            { data: 'UserEmail', width: '10%', class: "" },
            { data: 'SocialSecurityNumber', width: '10%', class: "" },
            { data: 'WelderStencilNumber', width: '10%', class: "" },
            { data: 'City', width: '10%', class: "" },
            { data: 'ExtName', width: '10%', class: "" },
            {
                data: 'Id',
                title: 'Action(s)',
                width: '25%',
                class: '',
                render: function (data, type, row, meta) {
                    return '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm" onclick="getWelderInfo(' + row.UserNum + ');">View</a>';
                },
            }
        ],
        language: {
            paginate: {
                previous: 'Prev',
            },
            processing: "Loading Data...",
            zeroRecords: "No matching records found",
            emptyTable: "No data available in the table"
        },

    });
}


function getWelderInfo(userId) {
    var inputData = {
        id: userId
    };

    callAjax("POST", iwbWorkerBaseUrl + "?method=welder-info", "application/x-www-form-urlencoded", inputData, function (result) {
        if (result != undefined && result != null && result != "") {
            let response = JSON.parse(result);
            welderData = response.data;
            console.log("Welder data => ", welderData);
            $("#welderInfoContainer").empty();
            var template = $("#welderInfoTemplate").tmpl(welderData);
            $("#welderInfoContainer").append(template);

            updateWelderInfoModal(true);
        }
        else {
            console.log("No Result => ", result);
        }
    });
}

function updateWelderInfoModal(isShow) {
    $("#welderInfoModal").modal(isShow ? "show" : "hide");
}