﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RoleManagement
{
    public class RoleChannelPermission
    {
        public int Id { get; set; }
        public int RoleId { get; set; }
        public int PermissionId { get; set; }
        public int ChannelNum { get; set; }
        //public string ChannelNumCSV { get; set; }
        //public bool IsDeletionRetains { get; set; }
        public string ChannelesToAllocate { get; set; }
        public string ChannelesToDeAllocate { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
    }
}
