﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{15E060E9-FB6A-4FBC-8580-15917DC850C3}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RevCord.VoiceRec.WebUIClient</RootNamespace>
    <AssemblyName>RevCord.VoiceRec.WebUIClient</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <IISExpressSSLPort>44301</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
    <TypeScriptToolsVersion>3.1</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AxInterop.WMPLib, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\WMPLib.1.0.0\lib\AxInterop.WMPLib.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <HintPath>..\packages\BouncyCastle.1.8.9\lib\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Common.Logging, Version=3.3.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.3.3.1\lib\net40\Common.Logging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Common.Logging.Core, Version=3.3.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.Core.3.3.1\lib\net40\Common.Logging.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Common.Logging.NLog20, Version=3.3.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.NLog20.3.3.1\lib\net40\Common.Logging.NLog20.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.5\lib\DocumentFormat.OpenXml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EPPlus, Version=4.5.3.1, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\EPPlus.4.5.3.1\lib\net40\EPPlus.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ExtraEmailConfigurationLib, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\ExtraEmailConfigurationLib.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis, Version=********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.40.2\lib\net45\Google.Apis.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.40.2\lib\net45\Google.Apis.Auth.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Auth.PlatformServices, Version=********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.40.2\lib\net45\Google.Apis.Auth.PlatformServices.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Calendar.v3, Version=1.40.2.1620, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Calendar.v3.1.40.2.1620\lib\net45\Google.Apis.Calendar.v3.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Core.1.40.2\lib\net45\Google.Apis.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.PlatformServices, Version=********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.40.2\lib\net45\Google.Apis.PlatformServices.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\SharpZipLib.1.1.0\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Interop.WMPLib, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\WMPLib.1.0.0\lib\Interop.WMPLib.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <Private>True</Private>
    </Reference>
    <Reference Include="itextsharp, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.********\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp.xmlworker, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itextsharp.xmlworker.********\lib\itextsharp.xmlworker.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp.xtra, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itextsharp.xtra.********\lib\itextsharp.xtra.dll</HintPath>
    </Reference>
    <Reference Include="JSNLog, Version=2.22.1.0, Culture=neutral, PublicKeyToken=742e180f6c5ccd0d, processorArchitecture=MSIL">
      <HintPath>..\packages\JSNLog.2.22.1\lib\net40\JSNLog.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="JWT">
      <HintPath>..\packages\JWT.6.1.4\lib\net46\JWT.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MediaExport.NET, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\MediaExport.NET.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Client, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Client.2.2.0\lib\net45\Microsoft.AspNet.SignalR.Client.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Core.2.2.0\lib\net45\Microsoft.AspNet.SignalR.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Owin, Version=1.2.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Owin.1.2.1\lib\net45\Microsoft.AspNet.SignalR.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.SystemWeb, Version=1.2.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.SystemWeb.1.2.1\lib\net45\Microsoft.AspNet.SignalR.SystemWeb.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=6.8.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.6.8.0\lib\net45\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=6.8.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.6.8.0\lib\net45\Microsoft.IdentityModel.Logging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=6.8.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.6.8.0\lib\net45\Microsoft.IdentityModel.Tokens.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Word, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Office.Interop.Word.15.0.4797.1003\lib\net20\Microsoft.Office.Interop.Word.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Cors.3.0.1\lib\net45\Microsoft.Owin.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.HttpListener, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.HttpListener.2.0.2\lib\net45\Microsoft.Owin.Host.HttpListener.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.3.0.1\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Hosting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Hosting.2.0.2\lib\net45\Microsoft.Owin.Hosting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.3.0.1\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.340.80\lib\net40\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.DataVisualization, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.340.80\lib\net40\Microsoft.ReportViewer.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Design, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.340.80\lib\net40\Microsoft.ReportViewer.Design.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.ProcessingObjectModel, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.340.80\lib\net40\Microsoft.ReportViewer.ProcessingObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebDesign, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.340.80\lib\net40\Microsoft.ReportViewer.WebDesign.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportViewer.WebForms.v12.12.0.0.0\lib\Microsoft.ReportViewer.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.340.80\lib\net40\Microsoft.ReportViewer.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.314.76\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions.Desktop, Version=1.0.168.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.Desktop.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=2.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.2.0.0.2000\lib\net40\NLog.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="PdfSharp, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp.1.50.5147\lib\net20\PdfSharp.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp.Charting, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp.1.50.5147\lib\net20\PdfSharp.Charting.dll</HintPath>
    </Reference>
    <Reference Include="Plivo, Version=4.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Plivo.4.15.1\lib\netstandard1.3\Plivo.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="PLUSManaged, Version=5.15.1.0, Culture=neutral, PublicKeyToken=5d94a04f4eca59fc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\PLUSManaged.dll</HintPath>
    </Reference>
    <Reference Include="PLUSManaged.XmlSerializers, Version=5.15.1.0, Culture=neutral, PublicKeyToken=5d94a04f4eca59fc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\PLUSManaged.XmlSerializers.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="RestSharp, Version=106.15.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.106.15.0\lib\net452\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RevLicenseService, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RevLicenseService.dll</HintPath>
    </Reference>
    <Reference Include="SautinSoft.PdfFocus, Version=8.6.1.18, Culture=neutral, PublicKeyToken=0b79b934109b3e9e, processorArchitecture=MSIL">
      <HintPath>..\packages\sautinsoft.pdffocus.8.6.1.18\lib\net452\SautinSoft.PdfFocus.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.DirectoryServices.AccountManagement" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=5.1.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.5.1.2\lib\net451\System.IdentityModel.Tokens.Jwt.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.FileSystem, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.1\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net46\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net46\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Cors, Version=5.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.0.0\lib\net45\System.Web.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.Owin, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Owin.5.2.3\lib\net45\System.Web.Http.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.3.0\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.3.0\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Twilio, Version=6.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Twilio.6.2.0\lib\net451\Twilio.dll</HintPath>
    </Reference>
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0\lib\net40\WebActivatorEx.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="Xceed.Document.NET, Version=1.8.0.0, Culture=neutral, PublicKeyToken=3e4669d2f30244f4, processorArchitecture=MSIL">
      <HintPath>..\packages\DocX.1.8.0\lib\net40\Xceed.Document.NET.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xceed.Words.NET, Version=1.8.0.0, Culture=neutral, PublicKeyToken=3e4669d2f30244f4, processorArchitecture=MSIL">
      <HintPath>..\packages\DocX.1.8.0\lib\net40\Xceed.Words.NET.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>

    <Content Include="assetsNew\apple-touch-icon.png" />
    <Content Include="assetsNew\css\repositories.css" />
    <Content Include="assetsNew\css\app.css" />
    <Content Include="assetsNew\css\checkTree.css" />
    <Content Include="assetsNew\css\daterangepicker.css" />
    <Content Include="assetsNew\css\evaluation.css" />
    <Content Include="assetsNew\css\font-awesome\brands.css" />
    <Content Include="assetsNew\css\font-awesome\fontawesome.css" />
    <Content Include="assetsNew\css\font-awesome\regular.css" />
    <Content Include="assetsNew\css\font-awesome\solid.css" />
    <Content Include="assetsNew\css\font-awesome\svg-with-js.css" />
    <Content Include="assetsNew\css\font-awesome\v4-font-face.css" />
    <Content Include="assetsNew\css\font-awesome\v4-shims.css" />
    <Content Include="assetsNew\css\images\ui-bg_glass_45_21543F_1x400.png" />
    <Content Include="assetsNew\css\images\ui-bg_gloss-wave_50_009E0F_500x100.png" />
    <Content Include="assetsNew\css\images\ui-bg_inset-hard_100_fcfdfd_1x100.png" />
    <Content Include="assetsNew\css\img\glyphicons-halflings-white.png" />
    <Content Include="assetsNew\css\img\glyphicons-halflings.png" />
    <Content Include="assetsNew\css\intlTelInput.css" />
    <Content Include="assetsNew\css\intlTelInput.min.css" />
    <Content Include="assetsNew\css\monitor.css" />
    <Content Include="assetsNew\css\player.css" />
    <Content Include="assetsNew\css\plugins\animate.min.css" />
    <Content Include="assetsNew\css\plugins\chosen.css" />
    <Content Include="assetsNew\css\plugins\colpick.css" />
    <Content Include="assetsNew\css\plugins\datatables\bootstrap.datatables.css" />
    <Content Include="assetsNew\css\plugins\datatables\datatables.css" />
    <Content Include="assetsNew\css\plugins\daterangepicker-bs3.css" />
    <Content Include="assetsNew\css\plugins\dropzone.css" />
    <Content Include="assetsNew\css\plugins\fullcalendar.css" />
    <Content Include="assetsNew\css\plugins\jquery.handsontable.full.css" />
    <Content Include="assetsNew\css\plugins\jquery.pnotify.default.css" />
    <Content Include="assetsNew\css\plugins\jquery.pnotify.default.icons.css" />
    <Content Include="assetsNew\css\plugins\jquery.timepicker.css" />
    <Content Include="assetsNew\css\plugins\jscrollpane.css" />
    <Content Include="assetsNew\css\setup.css" />
    <Content Include="assetsNew\favicon.ico" />
    <Content Include="assetsNew\icons\common\agent.png" />
    <Content Include="assetsNew\icons\common\arrow.png" />
    <Content Include="assetsNew\icons\common\audio_green.png" />
    <Content Include="assetsNew\icons\common\audio_list.png" />
    <Content Include="assetsNew\icons\common\black-asc.gif" />
    <Content Include="assetsNew\icons\common\black-desc.gif" />
    <Content Include="assetsNew\icons\common\black-unsorted.gif" />
    <Content Include="assetsNew\icons\common\borderIcon.jpg" />
    <Content Include="assetsNew\icons\common\calendar.png" />
    <Content Include="assetsNew\icons\common\clock.png" />
    <Content Include="assetsNew\icons\common\deleteIcon.png" />
    <Content Include="assetsNew\icons\common\downArrow.gif" />
    <Content Include="assetsNew\icons\common\email_list.png" />
    <Content Include="assetsNew\icons\common\excel-icon-white.png" />
    <Content Include="assetsNew\icons\common\excel-icon.png" />
    <Content Include="assetsNew\icons\common\iconDelete.png" />
    <Content Include="assetsNew\icons\common\inquire_audio.png" />
    <Content Include="assetsNew\icons\common\Inquire_Event.png" />
    <Content Include="assetsNew\icons\common\inquire_video.png" />
    <Content Include="assetsNew\icons\common\mag-glass.png" />
    <Content Include="assetsNew\icons\common\phoneIcon.png" />
    <Content Include="assetsNew\icons\common\phone_12X12.png" />
    <Content Include="assetsNew\icons\common\rightArrow.gif" />
    <Content Include="assetsNew\icons\common\screen.png" />
    <Content Include="assetsNew\icons\common\screens_list.png" />
    <Content Include="assetsNew\icons\common\search-icon.png" />
    <Content Include="assetsNew\icons\common\social_list.png" />
    <Content Include="assetsNew\icons\common\stopwatch.png" />
    <Content Include="assetsNew\icons\common\text_list.png" />
    <Content Include="assetsNew\icons\common\video_list.png" />
    <Content Include="assetsNew\icons\common\white-asc.gif" />
    <Content Include="assetsNew\icons\common\white-desc.gif" />
    <Content Include="assetsNew\icons\common\white-unsorted.gif" />
    <Content Include="assetsNew\icons\common\white_arrow.png" />
    <Content Include="assetsNew\icons\eval\deleteIcon.png" />
    <Content Include="assetsNew\icons\eval\deleteIconDisable.png" />
    <Content Include="assetsNew\icons\eval\down1.png" />
    <Content Include="assetsNew\icons\eval\e.png" />
    <Content Include="assetsNew\icons\eval\editIcon.png" />
    <Content Include="assetsNew\icons\eval\editIconDisable.png" />
    <Content Include="assetsNew\icons\eval\evalCallUser.png" />
    <Content Include="assetsNew\icons\eval\evalComplete.png" />
    <Content Include="assetsNew\icons\eval\evalEmail.png" />
    <Content Include="assetsNew\icons\eval\evalModify.png" />
    <Content Include="assetsNew\icons\eval\evalReject.png" />
    <Content Include="assetsNew\icons\eval\evalShare.png" />
    <Content Include="assetsNew\icons\eval\evalShare16.png" />
    <Content Include="assetsNew\icons\eval\evalUnshare.png" />
    <Content Include="assetsNew\icons\eval\evalUnshare16.png" />
    <Content Include="assetsNew\icons\eval\evalUser.png" />
    <Content Include="assetsNew\icons\eval\evalView.png" />
    <Content Include="assetsNew\icons\eval\exportIcon.png" />
    <Content Include="assetsNew\icons\eval\publishIcon.png" />
    <Content Include="assetsNew\icons\eval\publishIconDisable.png" />
    <Content Include="assetsNew\icons\eval\QA1_20X17.png" />
    <Content Include="assetsNew\icons\eval\QA_16X16.gif" />
    <Content Include="assetsNew\icons\eval\QA_20X20.gif" />
    <Content Include="assetsNew\icons\eval\share.png" />
    <Content Include="assetsNew\icons\eval\slide-left.png" />
    <Content Include="assetsNew\icons\eval\slide-right.png" />
    <Content Include="assetsNew\icons\eval\unshare.png" />
    <Content Include="assetsNew\icons\eval\up1.png" />
    <Content Include="assetsNew\icons\eval\viewHistory_16X16.png" />
    <Content Include="assetsNew\icons\eval\_Q.png" />
    <Content Include="assetsNew\icons\general\ajax-loader.gif" />
    <Content Include="assetsNew\icons\general\ajax-loader_16x16.gif" />
    <Content Include="assetsNew\icons\general\ajax-load_16x16.gif" />
    <Content Include="assetsNew\icons\general\ajax-load_24x24.gif" />
    <Content Include="assetsNew\icons\general\borderIcon.jpg" />
    <Content Include="assetsNew\icons\general\deleteIcon.png" />
    <Content Include="assetsNew\icons\general\editIcon.png" />
    <Content Include="assetsNew\icons\general\icons7-invite-16.png" />
    <Content Include="assetsNew\icons\general\icons7-invite-20.png" />
    <Content Include="assetsNew\icons\general\pArrowHoverIcon.png" />
    <Content Include="assetsNew\icons\general\plusBtnImg.png" />
    <Content Include="assetsNew\icons\general\trash_24X24.png" />
    <Content Include="assetsNew\icons\monitor\microphone.png" />
    <Content Include="assetsNew\icons\monitor\muted.png" />
    <Content Include="assetsNew\icons\monitor\overlayDVR.png" />
    <Content Include="assetsNew\icons\monitor\playing_arrow_blue.png" />
    <Content Include="assetsNew\icons\monitor\playing_arrow_white.png" />
    <Content Include="assetsNew\icons\monitor\RotateLeft.png" />
    <Content Include="assetsNew\icons\monitor\RotateRight.png" />
    <Content Include="assetsNew\icons\monitor\status_active-old.png" />
    <Content Include="assetsNew\icons\monitor\status_active.png" />
    <Content Include="assetsNew\icons\monitor\status_active_white.png" />
    <Content Include="assetsNew\icons\monitor\status_inactive-old.png" />
    <Content Include="assetsNew\icons\monitor\status_inactive.png" />
    <Content Include="assetsNew\icons\monitor\status_offline.png" />
    <Content Include="assetsNew\icons\monitor\status_pause.png" />
    <Content Include="assetsNew\icons\monitor\status_paused.png" />
    <Content Include="assetsNew\icons\search\1.png" />
    <Content Include="assetsNew\icons\search\10.png" />
    <Content Include="assetsNew\icons\search\2.png" />
    <Content Include="assetsNew\icons\search\3.png" />
    <Content Include="assetsNew\icons\search\4.png" />
    <Content Include="assetsNew\icons\search\5.png" />
    <Content Include="assetsNew\icons\search\6.png" />
    <Content Include="assetsNew\icons\search\7.png" />
    <Content Include="assetsNew\icons\search\8.png" />
    <Content Include="assetsNew\icons\search\9.png" />
    <Content Include="assetsNew\icons\search\addPlaylistIcon16.gif" />
    <Content Include="assetsNew\icons\search\arrowSe24.gif" />
    <Content Include="assetsNew\icons\search\bookmark-icon-16.png" />
    <Content Include="assetsNew\icons\search\comments-16.png" />
    <Content Include="assetsNew\icons\search\iconPL_ListViewActive.PNG" />
    <Content Include="assetsNew\icons\search\iconPL_ListViewInActive.PNG" />
    <Content Include="assetsNew\icons\search\iconPL_TimelineViewActive.PNG" />
    <Content Include="assetsNew\icons\search\iconPL_TimelineViewInActive.PNG" />
    <Content Include="assetsNew\icons\search\list_blue.png" />
    <Content Include="assetsNew\icons\search\list_grey.png" />
    <Content Include="assetsNew\icons\search\paperClip22.png" />
    <Content Include="assetsNew\icons\search\uploadFile48.png" />
    <Content Include="assetsNew\icons\search\uploadifyCancel16.png" />
    <Content Include="assetsNew\icons\survey\addBtn.png" />
    <Content Include="assetsNew\icons\survey\fieldDeletebtn.png" />
    <Content Include="assetsNew\icons\survey\moreOptionsIcon.png" />
    <Content Include="assetsNew\icons\survey\qMultiSelectIcon.png" />
    <Content Include="assetsNew\icons\survey\qSingleSelectIcon.png" />
    <Content Include="assetsNew\icons\survey\qtxtIcon.png" />
    <Content Include="assetsNew\icons\survey\removeBtn.png" />
    <Content Include="assetsNew\icons\um\Activate.jpg" />
    <Content Include="assetsNew\icons\um\add-user-24.png" />
    <Content Include="assetsNew\icons\um\addNew.jpg" />
    <Content Include="assetsNew\icons\um\addUserIcon24.png" />
    <Content Include="assetsNew\icons\um\assignChannelsIcon.png" />
    <Content Include="assetsNew\icons\um\assignGroupIcon.png" />
    <Content Include="assetsNew\icons\um\assignPermissionsIcon.png" />
    <Content Include="assetsNew\icons\um\blue.png" />
    <Content Include="assetsNew\icons\um\button.jpg" />
    <Content Include="assetsNew\icons\um\buttonOriginal.jpg" />
    <Content Include="assetsNew\icons\um\deactivate.jpg" />
    <Content Include="assetsNew\icons\um\dot1.gif" />
    <Content Include="assetsNew\icons\um\dot2.gif" />
    <Content Include="assetsNew\icons\um\edit.jpg" />
    <Content Include="assetsNew\icons\um\editgreen.jpg" />
    <Content Include="assetsNew\icons\um\group16.png" />
    <Content Include="assetsNew\icons\um\invite.png" />
    <Content Include="assetsNew\icons\um\loginPasswordIcon.png" />
    <Content Include="assetsNew\icons\um\loginUserIcon.png" />
    <Content Include="assetsNew\icons\um\marker.jpg" />
    <Content Include="assetsNew\icons\um\marker.png" />
    <Content Include="assetsNew\icons\um\marker1.png" />
    <Content Include="assetsNew\icons\um\new-post-24.png" />
    <Content Include="assetsNew\icons\um\notes.jpg" />
    <Content Include="assetsNew\icons\um\profileDetailIcon.png" />
    <Content Include="assetsNew\icons\um\recover-user-24.png" />
    <Content Include="assetsNew\icons\um\remove1.png" />
    <Content Include="assetsNew\icons\um\removebookmark.png" />
    <Content Include="assetsNew\icons\um\user-at-phone.png" />
    <Content Include="assetsNew\icons\um\user16.png" />
    <Content Include="assetsNew\icons\um\userIconDelete.png" />
    <Content Include="assetsNew\icons\um\userIconRecover.png" />
    <Content Include="assetsNew\images---\avatar-large.jpg" />
    <Content Include="assetsNew\images---\avatar-small.jpg" />
    <Content Include="assetsNew\images---\avatar.png" />
    <Content Include="assetsNew\images---\bg-wood.jpg" />
    <Content Include="assetsNew\images---\body-linen.jpg" />
    <Content Include="assetsNew\images---\chosen\chosen-sprite%402x.png" />
    <Content Include="assetsNew\images---\chosen\chosen-sprite.png" />
    <Content Include="assetsNew\images---\datatables\sort_asc.png" />
    <Content Include="assetsNew\images---\datatables\sort_asc_disabled.png" />
    <Content Include="assetsNew\images---\datatables\sort_both.png" />
    <Content Include="assetsNew\images---\datatables\sort_desc.png" />
    <Content Include="assetsNew\images---\datatables\sort_desc_disabled.png" />
    <Content Include="assetsNew\images---\shadow-separator-narrow-right.png" />
    <Content Include="assetsNew\images---\spritemap%402x.png" />
    <Content Include="assetsNew\images---\spritemap.png" />
    <Content Include="assetsNew\images\AttachFiles.png" />
    <Content Include="assetsNew\images\avatar-large.jpg" />
    <Content Include="assetsNew\images\avatar-small.jpg" />
    <Content Include="assetsNew\images\avatar.png" />
    <Content Include="assetsNew\images\bg-wood.jpg" />
    <Content Include="assetsNew\images\body-linen.jpg" />
    <Content Include="assetsNew\images\chosen\chosen-sprite%402x.png" />
    <Content Include="assetsNew\images\chosen\chosen-sprite.png" />
    <Content Include="assetsNew\images\common\large-pic.png" />
    <Content Include="assetsNew\images\common\medium-pic.png" />
    <Content Include="assetsNew\images\common\small-pic.png" />
    <Content Include="assetsNew\images\datatables\sort_asc.png" />
    <Content Include="assetsNew\images\datatables\sort_asc_disabled.png" />
    <Content Include="assetsNew\images\datatables\sort_both.png" />
    <Content Include="assetsNew\images\datatables\sort_desc.png" />
    <Content Include="assetsNew\images\datatables\sort_desc_disabled.png" />
    <Content Include="assetsNew\images\favicon - Copy.png" />
    <Content Include="assetsNew\images\favicon.png" />
    <Content Include="assetsNew\images\home-icon.png" />
    <Content Include="assetsNew\images\images\avatar-large.jpg" />
    <Content Include="assetsNew\images\images\avatar-small.jpg" />
    <Content Include="assetsNew\images\images\avatar.png" />
    <Content Include="assetsNew\images\images\bg-wood.jpg" />
    <Content Include="assetsNew\images\images\body-linen.jpg" />
    <Content Include="assetsNew\images\images\chosen\chosen-sprite%402x.png" />
    <Content Include="assetsNew\images\images\chosen\chosen-sprite.png" />
    <Content Include="assetsNew\images\images\datatables\sort_asc.png" />
    <Content Include="assetsNew\images\images\datatables\sort_asc_disabled.png" />
    <Content Include="assetsNew\images\images\datatables\sort_both.png" />
    <Content Include="assetsNew\images\images\datatables\sort_desc.png" />
    <Content Include="assetsNew\images\images\datatables\sort_desc_disabled.png" />
    <Content Include="assetsNew\images\images\favicon.png" />
    <Content Include="assetsNew\images\images\home-icon.png" />
    <Content Include="assetsNew\images\images\iq3-btn.png" />
    <Content Include="assetsNew\images\images\library-icon.png" />
    <Content Include="assetsNew\images\images\logo_no-bg-lg.png" />
    <Content Include="assetsNew\images\images\logo_no-bg-white-sm.png" />
    <Content Include="assetsNew\images\images\Monitor-icon.png" />
    <Content Include="assetsNew\images\images\Playlist-icon.png" />
    <Content Include="assetsNew\images\images\QA-icon.png" />
    <Content Include="assetsNew\images\images\Refresh-icon.png" />
    <Content Include="assetsNew\images\images\reports-icon.png" />
    <Content Include="assetsNew\images\images\Rev-R-icon.png" />
    <Content Include="assetsNew\images\images\search-icon.png" />
    <Content Include="assetsNew\images\images\Setup-icon.png" />
    <Content Include="assetsNew\images\images\shadow-separator-narrow-right.png" />
    <Content Include="assetsNew\images\images\spritemap%402x.png" />
    <Content Include="assetsNew\images\images\spritemap.png" />
    <Content Include="assetsNew\images\inspection.png" />
    <Content Include="assetsNew\images\inspection_.png" />
    <Content Include="assetsNew\images\InternalNotes.png" />
    <Content Include="assetsNew\images\iq3-btn-old.png" />
    <Content Include="assetsNew\images\iq3-btn.png" />
    <Content Include="assetsNew\images\landscape-limitation.png" />
    <Content Include="assetsNew\images\library-icon.png" />
    <Content Include="assetsNew\images\Login-BG.png" />
    <Content Include="assetsNew\images\logo_no-bg-white-sm.png" />
    <Content Include="assetsNew\images\Metadata.png" />
    <Content Include="assetsNew\images\Monitor-icon.png" />
    <Content Include="assetsNew\images\Playlist-icon.png" />
    <Content Include="assetsNew\images\playlist_icon.png" />
    <Content Include="assetsNew\images\QA-icon.png" />
    <Content Include="assetsNew\images\Refresh-icon.png" />
    <Content Include="assetsNew\images\reports-icon.png" />
    <Content Include="assetsNew\images\Rev-R-icon.png" />
    <Content Include="assetsNew\images\schedule.png" />
    <Content Include="assetsNew\images\search-icon.png" />
    <Content Include="assetsNew\images\Setup-icon.png" />
    <Content Include="assetsNew\images\shadow-separator-narrow-right.png" />
    <Content Include="assetsNew\images\sharenotes.png" />
    <Content Include="assetsNew\images\spritemap%402x.png" />
    <Content Include="assetsNew\images\spritemap.png" />
    <Content Include="assetsNew\images\UpToDate.png" />
    <Content Include="assetsNew\images\Warring_still.png" />
    <Content Include="assetsNew\images\Warrning_Blink.gif" />
    <Content Include="assetsNew\img\checktree_icon.png" />
    <Content Include="assetsNew\img\flags%402x.png" />
    <Content Include="assetsNew\img\flags.png" />
    <Content Include="assetsNew\img\glyphicons-halflings-white.png" />
    <Content Include="assetsNew\img\glyphicons-halflings.png" />
    <Content Include="assetsNew\js\application.js" />
    <Content Include="assetsNew\js\bootstrap\affix.js" />
    <Content Include="assetsNew\js\bootstrap\alert.js" />
    <Content Include="assetsNew\js\bootstrap\button.js" />
    <Content Include="assetsNew\js\bootstrap\carousel.js" />
    <Content Include="assetsNew\js\bootstrap\collapse.js" />
    <Content Include="assetsNew\js\bootstrap\dropdown.js" />
    <Content Include="assetsNew\js\bootstrap\modal.js" />
    <Content Include="assetsNew\js\bootstrap\popover.js" />
    <Content Include="assetsNew\js\bootstrap\scrollspy.js" />
    <Content Include="assetsNew\js\bootstrap\tab.js" />
    <Content Include="assetsNew\js\bootstrap\tooltip.js" />
    <Content Include="assetsNew\js\bootstrap\transition.js" />
    <Content Include="assetsNew\js\checkTree.js" />
    <Content Include="assetsNew\js\daterangepicker.min.js" />
    <Content Include="assetsNew\js\intlTelInput.js" />
    <Content Include="assetsNew\js\plugins\bootstrap-datepicker.js" />
    <Content Include="assetsNew\js\plugins\chosen.jquery.min.js" />
    <Content Include="assetsNew\js\plugins\colpick.js" />
    <Content Include="assetsNew\js\plugins\datatables\bootstrap.datatables.js" />
    <Content Include="assetsNew\js\plugins\datatables\datatables.min.js" />
    <Content Include="assetsNew\js\plugins\daterangepicker.js" />
    <Content Include="assetsNew\js\plugins\dropzone-amd-module.js" />
    <Content Include="assetsNew\js\plugins\fullcalendar.js" />
    <Content Include="assetsNew\js\plugins\fullcalendar.min.js" />
    <Content Include="assetsNew\js\plugins\gauge.js" />
    <Content Include="assetsNew\js\plugins\html5shiv.js" />
    <Content Include="assetsNew\js\plugins\jquery-ui.custom.min.js" />
    <Content Include="assetsNew\js\plugins\jquery-ui.min.js" />
    <Content Include="assetsNew\js\plugins\jquery.bootstrap.wizard.min.js" />
    <Content Include="assetsNew\js\plugins\jquery.flot.min.js" />
    <Content Include="assetsNew\js\plugins\jquery.handsontable.full.js" />
    <Content Include="assetsNew\js\plugins\jquery.js" />
    <Content Include="assetsNew\js\plugins\jquery.knob.js" />
    <Content Include="assetsNew\js\plugins\jquery.maskedinput.min.js" />
    <Content Include="assetsNew\js\plugins\jquery.maskmoney.js" />
    <Content Include="assetsNew\js\plugins\jquery.pnotify.js" />
    <Content Include="assetsNew\js\plugins\jquery.sparkline.min.js" />
    <Content Include="assetsNew\js\plugins\jquery.timepicker.min.js" />
    <Content Include="assetsNew\js\plugins\jquery.validate.min.js" />
    <Content Include="assetsNew\js\plugins\jscrollpane.min.js" />
    <Content Include="assetsNew\js\plugins\justgage.1.0.1.min.js" />
    <Content Include="assetsNew\js\plugins\moment-2.17.1.min.js" />
    <Content Include="assetsNew\js\plugins\moment-duration-format.min.js" />
    <Content Include="assetsNew\js\plugins\moment.min.js" />
    <Content Include="assetsNew\js\plugins\morris-0.4.3.min.js" />
    <Content Include="assetsNew\js\plugins\mousewheel.js" />
    <Content Include="assetsNew\js\plugins\mwheelIntent.js" />
    <Content Include="assetsNew\js\plugins\raphael-min.js" />
    <Content Include="assetsNew\js\plugins\respond.min.js" />
    <Content Include="assetsNew\js\plugins\summernote.js" />
    <Content Include="assetsNew\js\template_js\calendar.js" />
    <Content Include="assetsNew\js\template_js\chart.js" />
    <Content Include="assetsNew\js\template_js\dashboard.js" />
    <Content Include="assetsNew\js\template_js\elements.js" />
    <Content Include="assetsNew\js\template_js\forms.js" />
    <Content Include="assetsNew\js\template_js\form_validation.js" />
    <Content Include="assetsNew\js\template_js\form_wizard.js" />
    <Content Include="assetsNew\js\template_js\gmaps.js" />
    <Content Include="assetsNew\js\template_js\profile.js" />
    <Content Include="assetsNew\js\template_js\table.js" />
    <Content Include="assetsNew\js\utils.js" />
    <Content Include="assetsNew\morris-js-license.txt" />
    <Content Include="assets\css\AddFiles\calendar-blue.css" />
    <Content Include="assets\css\BookmarkFlagcolorPick.css" />
    <Content Include="assets\css\colorPick.css" />
    <Content Include="assets\css\custom.css" />
    <Content Include="assets\css\e-sign.css" />
    <Content Include="assets\css\fixHeader.css" />
    <Content Include="assets\css\google\fonts.css" />
    <Content Include="assets\css\grid\flexigrid.pack.css" />
    <Content Include="assets\css\grid\screenVideo.css" />
    <Content Include="assets\css\img\glyphicons-halflings-white.png" />
    <Content Include="assets\css\img\glyphicons-halflings.png" />
    <Content Include="assets\css\jm.spinner.css" />
    <Content Include="assets\css\jQuery\fonts\jsglyph.svg" />
    <Content Include="assets\css\jQuery\images\ui-bg_glass_45_21543F_1x400.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_glass_55_009E0F_1x400.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_glass_75_009E0F_1x400.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_gloss-wave_45_e14f1c_500x100.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_gloss-wave_50_009E0F_500x100.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_gloss-wave_75_006B3F_500x100.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_inset-hard_100_fcfdfd_1x100.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_21543F_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_444444_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_c1dcd0_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_fcd113_256x240.png" />
    <Content Include="assets\css\jQuery\jquery-ui-1.9.2.custom.css" />
    <Content Include="assets\css\jQuery\jquery-ui-custom-mmsDialog.css" />
    <Content Include="assets\css\jQuery\jquery-ui-timepicker-addon.css" />
    <Content Include="assets\css\jQuery\jquery-weekdays.css" />
    <Content Include="assets\css\jQuery\jquery.checkboxtree.css" />
    <Content Include="assets\css\jQuery\jquery.jspanel.min.css" />
    <Content Include="assets\css\jQuery\jquery.reject.css" />
    <Content Include="assets\css\jQuery\jquery.tooltip.css" />
    <Content Include="assets\css\lightgallery\lg-fb-comment-box.min.css" />
    <Content Include="assets\css\lightgallery\lg-transitions.min.css" />
    <Content Include="assets\css\lightgallery\lightgallery.min.css" />
    <Content Include="assets\css\Live_Video_Player\screen.css" />
    <Content Include="assets\css\Live_Video_Player\wowza.css" />
    <Content Include="assets\css\loginStyle.css" />
    <Content Include="assets\css\nav.css" />
    <Content Include="assets\css\panes.css" />
    <Content Include="assets\css\playlist.css" />
    <Content Include="assets\css\select2\select2.min.css" />
    <Content Include="assets\css\sidenav.css" />
    <Content Include="assets\css\survey.css" />
    <Content Include="assets\css\tableStyle_Modified.css" />
    <Content Include="assets\css\tableStyle.css" />
    <Content Include="assets\css\tables\awesome-bootstrap-checkbox.css" />
    <Content Include="assets\css\tables\colReorder.dataTables.min.css" />
    <Content Include="assets\css\tables\dataTables.checkboxes.css" />
    <Content Include="assets\css\tables\font-awesome.min.css" />
    <Content Include="assets\css\tables\jquery.dataTables.css" />
    <Content Include="assets\css\tables\responsive.dataTables.min.css" />
    <Content Include="assets\css\tables\select.dataTables.min.css" />
    <Content Include="assets\css\tablet.css" />
    <Content Include="assets\css\TimelineView\custom.css" />
    <Content Include="assets\css\TimelineView\vis.css" />
    <Content Include="assets\DropzoneJS\assets\basic.css" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\.gitignore" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\basic.css" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\dropzone-amd-module.js" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\dropzone.css" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\dropzone.js" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\min\basic.min.css" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\min\dropzone-amd-module.min.js" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\min\dropzone.min.css" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\min\dropzone.min.js" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\index.js" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\test\test.html" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\test\test.js" />
    <Content Include="assets\DropzoneJS\assets\dropzone.css" />
    <Content Include="assets\DropzoneJS\assets\dropzone.js" />
    <Content Include="assets\DropzoneJS\latestJs_1.11\jquery-ui.css" />
    <Content Include="assets\DropzoneJS\latestJs_1.11\jquery-ui.min.js" />
    <Content Include="assets\DropzoneJS\latestJs_1.11\jquery.min.js" />
    <Content Include="assets\eqcss\js\EQCSS.min.js" />
    <Content Include="assets\eqcss\livePlayer\styles.css" />
    <Content Include="assets\fonts\lg.svg" />
    <Content Include="assets\icons\common\agent.png" />
    <Content Include="assets\icons\common\audio_green.png" />
    <Content Include="assets\icons\common\audio_list.png" />
    <Content Include="assets\icons\common\black-asc.gif" />
    <Content Include="assets\icons\common\black-desc.gif" />
    <Content Include="assets\icons\common\black-unsorted.gif" />
    <Content Include="assets\icons\common\borderIcon.jpg" />
    <Content Include="assets\icons\common\calendar.png" />
    <Content Include="assets\icons\common\clock.png" />
    <Content Include="assets\icons\common\deleteIcon.png" />
    <Content Include="assets\icons\common\downArrow-old.gif" />
    <Content Include="assets\icons\common\downArrow.gif" />
    <Content Include="assets\icons\common\email_list.png" />
    <Content Include="assets\icons\common\excel-icon-white.png" />
    <Content Include="assets\icons\common\excel-icon.png" />
    <Content Include="assets\icons\common\iconDelete.png" />
    <Content Include="assets\icons\common\inquire_audio.png" />
    <Content Include="assets\icons\common\inquire_video.png" />
    <Content Include="assets\icons\common\mag-glass.png" />
    <Content Include="assets\icons\common\md.png" />
    <Content Include="assets\icons\common\phoneIcon.png" />
    <Content Include="assets\icons\common\phone_12X12.png" />
    <Content Include="assets\icons\common\rightArrow-old.gif" />
    <Content Include="assets\icons\common\rightArrow.gif" />
    <Content Include="assets\icons\common\rightArrow_white.gif" />
    <Content Include="assets\icons\common\screen.png" />
    <Content Include="assets\icons\common\screens_list.png" />
    <Content Include="assets\icons\common\search-icon.png" />
    <Content Include="assets\icons\common\social_list.png" />
    <Content Include="assets\icons\common\stopwatch.png" />
    <Content Include="assets\icons\common\text_list.png" />
    <Content Include="assets\icons\common\video_list.png" />
    <Content Include="assets\icons\common\white-asc.gif" />
    <Content Include="assets\icons\common\white-desc.gif" />
    <Content Include="assets\icons\common\white-unsorted.gif" />
    <Content Include="assets\icons\common\white_arrow.png" />
    <Content Include="assets\icons\common\Yes.png" />
    <Content Include="assets\icons\eval\deleteIcon.png" />
    <Content Include="assets\icons\eval\deleteIconDisable.png" />
    <Content Include="assets\icons\eval\down1.png" />
    <Content Include="assets\icons\eval\e.png" />
    <Content Include="assets\icons\eval\editIcon.png" />
    <Content Include="assets\icons\eval\editIconDisable.png" />
    <Content Include="assets\icons\eval\evalEmail.png" />
    <Content Include="assets\icons\eval\exportIcon.png" />
    <Content Include="assets\icons\eval\QA1_20X17.png" />
    <Content Include="assets\icons\eval\QA_16X16.gif" />
    <Content Include="assets\icons\eval\QA_20X20.gif" />
    <Content Include="assets\icons\eval\share.png" />
    <Content Include="assets\icons\eval\slide-left.png" />
    <Content Include="assets\icons\eval\slide-right.png" />
    <Content Include="assets\icons\eval\unshare.png" />
    <Content Include="assets\icons\eval\up1.png" />
    <Content Include="assets\icons\eval\_Q.png" />
    <Content Include="assets\icons\eval\evalCallUser.png" />
    <Content Include="assets\icons\eval\evalComplete.png" />
    <Content Include="assets\icons\eval\evalModify.png" />
    <Content Include="assets\icons\eval\evalReject.png" />
    <Content Include="assets\icons\eval\evalShare.png" />
    <Content Include="assets\icons\eval\evalShare16.png" />
    <Content Include="assets\icons\eval\evalUnshare.png" />
    <Content Include="assets\icons\eval\evalUnshare16.png" />
    <Content Include="assets\icons\eval\evalUser.png" />
    <Content Include="assets\icons\eval\evalView.png" />
    <Content Include="assets\icons\eval\publishIcon.png" />
    <Content Include="assets\icons\eval\publishIconDisable.png" />
    <Content Include="assets\icons\eval\viewHistory_16X16.png" />
    <Content Include="assets\icons\general\ajax-loader.gif" />
    <Content Include="assets\icons\general\ajax-loader_16x16.gif" />
    <Content Include="assets\icons\general\ajax-load_16x16.gif" />
    <Content Include="assets\icons\general\ajax-load_24x24.gif" />
    <Content Include="assets\icons\general\borderIcon.jpg" />
    <Content Include="assets\icons\general\deleteIcon.png" />
    <Content Include="assets\icons\general\drag-reorder.png" />
    <Content Include="assets\icons\general\editIcon.png" />
    <Content Include="assets\icons\general\icons7-invite-16.png" />
    <Content Include="assets\icons\general\icons7-invite-20.png" />
    <Content Include="assets\icons\general\NoImageAvailable.jpg" />
    <Content Include="assets\icons\general\NoImageAvailable.png" />
    <Content Include="assets\icons\general\NoImageAvailable1.png" />
    <Content Include="assets\icons\general\pArrowHoverIcon.png" />
    <Content Include="assets\icons\general\plusBtnImg.png" />
    <Content Include="assets\icons\general\trash_24X24.png" />
    <Content Include="assets\icons\general\view25.png" />
    <Content Include="assets\icons\inspection\checkbox.png" />
    <Content Include="assets\icons\inspection\PhotoMarkerIcon.png" />
    <Content Include="assets\icons\inspection\MultiSelectMarkerIcon.png" />
    <Content Include="assets\icons\inspection\radio.png" />
    <Content Include="assets\icons\inspection\signer-icon.png" />
    <Content Include="assets\icons\inspection\SingleSelectMarkerIcon.png" />
    <Content Include="assets\icons\inspection\TextMarkerIcon.png" />
    <Content Include="assets\icons\jstree\Bookmark Color-01.jpg" />
    <Content Include="assets\icons\jstree\Bookmark-01.jpg" />
    <Content Include="assets\icons\jstree\Bookmark-Color-01.png" />
    <Content Include="assets\icons\jstree\customIcon.png" />
    <Content Include="assets\icons\jstree\Groups-Color-01.jpg" />
    <Content Include="assets\icons\jstree\Groups-01.jpg" />
    <Content Include="assets\icons\jstree\Groups-Color-01.png" />
    <Content Include="assets\icons\jstree\Markers-Color-01.jpg" />
    <Content Include="assets\icons\jstree\Markers-01.jpg" />
    <Content Include="assets\icons\jstree\Markers-Color-01.png" />
    <Content Include="assets\icons\marker\addCheckbox.png" />
    <Content Include="assets\icons\marker\addImage.png" />
    <Content Include="assets\icons\marker\addLabel.png" />
    <Content Include="assets\icons\marker\addPage.png" />
    <Content Include="assets\icons\marker\addRadioButton.png" />
    <Content Include="assets\icons\marker\addTextbox.png" />
    <Content Include="assets\icons\marker\downloadForm.png" />
    <Content Include="assets\icons\marker\previewForm.png" />
    <Content Include="assets\icons\marker\removePage.png" />
    <Content Include="assets\icons\marker\saveForm.png" />
    <Content Include="assets\icons\marker\zoomIn.png" />
    <Content Include="assets\icons\marker\zoomOut.png" />
    <Content Include="assets\icons\monitor\add_icon.png" />
    <Content Include="assets\icons\monitor\Blank_user.png" />
    <Content Include="assets\icons\monitor\bm-icon.png" />
    <Content Include="assets\icons\monitor\GPS_View.png" />
    <Content Include="assets\icons\monitor\GraphicMarker.png" />
    <Content Include="assets\icons\monitor\microphone.png" />
    <Content Include="assets\icons\monitor\muted.png" />
    <Content Include="assets\icons\monitor\OpenPlusIcon.png" />
    <Content Include="assets\icons\monitor\overlayDVR.png" />
    <Content Include="assets\icons\monitor\playing_arrow_blue.png" />
    <Content Include="assets\icons\monitor\playing_arrow_white.png" />
    <Content Include="assets\icons\monitor\RotateLeft.png" />
    <Content Include="assets\icons\monitor\RotateRight.png" />
    <Content Include="assets\icons\monitor\IQ3Measurement.png" />
    <Content Include="assets\icons\monitor\settings.png" />
    <Content Include="assets\icons\monitor\setting_white.png" />
    <Content Include="assets\icons\monitor\status_active.png" />
    <Content Include="assets\icons\monitor\status_active_white.png" />
    <Content Include="assets\icons\monitor\status_offline.png" />
    <Content Include="assets\icons\monitor\status_inactive.png" />
    <Content Include="assets\icons\monitor\status_paused.png" />
    <Content Include="assets\icons\monitor\view_icon.png" />
    <Content Include="assets\icons\search\11.png" />
    <Content Include="assets\icons\search\11_.png" />
    <Content Include="assets\icons\search\12.png" />
    <Content Include="assets\icons\search\7.png" />
    <Content Include="assets\icons\search\8.png" />
    <Content Include="assets\icons\search\9.png" />
    <Content Include="assets\icons\search\1.png" />
    <Content Include="assets\icons\search\10.png" />
    <Content Include="assets\icons\search\2.png" />
    <Content Include="assets\icons\search\3.png" />
    <Content Include="assets\icons\search\4.png" />
    <Content Include="assets\icons\search\5.png" />
    <Content Include="assets\icons\search\6.png" />
    <Content Include="assets\icons\search\addPlaylistIcon16.gif" />
    <Content Include="assets\icons\search\arrowSe24.gif" />
    <Content Include="assets\icons\search\bookmark-icon-16.png" />
    <Content Include="assets\icons\search\iconPL_ListViewActive.PNG" />
    <Content Include="assets\icons\search\iconPL_ListViewInActive.PNG" />
    <Content Include="assets\icons\search\iconPL_TimelineViewActive.PNG" />
    <Content Include="assets\icons\search\iconPL_TimelineViewInActive.PNG" />
    <Content Include="assets\icons\search\IQ3Logo.png" />
    <Content Include="assets\icons\search\list_blue.png" />
    <Content Include="assets\icons\search\list_grey.png" />
    <Content Include="assets\icons\search\paperClip22.png" />
    <Content Include="assets\icons\search\comments-16.png" />
    <Content Include="assets\icons\search\uploadFile48.png" />
    <Content Include="assets\icons\search\uploadifyCancel16.png" />
    <Content Include="assets\icons\survey\addBtn.png" />
    <Content Include="assets\icons\survey\fieldDeletebtn.png" />
    <Content Include="assets\icons\survey\moreOptionsIcon.png" />
    <Content Include="assets\icons\survey\qMultiSelectIcon.png" />
    <Content Include="assets\icons\survey\qSingleSelectIcon.png" />
    <Content Include="assets\icons\survey\qtxtIcon.png" />
    <Content Include="assets\icons\survey\removeBtn.png" />
    <Content Include="assets\icons\um\Activate.jpg" />
    <Content Include="assets\icons\um\add-user-24.png" />
    <Content Include="assets\icons\um\addNew.jpg" />
    <Content Include="assets\icons\um\addUserIcon24.png" />
    <Content Include="assets\icons\um\assignChannelsIcon.png" />
    <Content Include="assets\icons\um\assignGroupIcon.png" />
    <Content Include="assets\icons\um\assignPermissionsIcon.png" />
    <Content Include="assets\icons\um\blue.png" />
    <Content Include="assets\icons\um\button.jpg" />
    <Content Include="assets\icons\um\buttonOriginal.jpg" />
    <Content Include="assets\icons\um\deactivate.jpg" />
    <Content Include="assets\icons\um\edit.jpg" />
    <Content Include="assets\icons\um\editgreen.jpg" />
    <Content Include="assets\icons\um\group16.png" />
    <Content Include="assets\icons\um\invite.png" />
    <Content Include="assets\icons\um\loginPasswordIcon.png" />
    <Content Include="assets\icons\um\loginUserIcon.png" />
    <Content Include="assets\icons\um\dot1.gif" />
    <Content Include="assets\icons\um\dot2.gif" />
    <Content Include="assets\icons\um\marker.jpg" />
    <Content Include="assets\icons\um\marker.png" />
    <Content Include="assets\icons\um\marker1.png" />
    <Content Include="assets\icons\um\new-post-24.png" />
    <Content Include="assets\icons\um\notes.jpg" />
    <Content Include="assets\icons\um\profileDetailIcon.png" />
    <Content Include="assets\icons\um\recover-user-24.png" />
    <Content Include="assets\icons\um\remove1.png" />
    <Content Include="assets\icons\um\removebookmark.png" />
    <Content Include="assets\icons\um\user-at-phone.png" />
    <Content Include="assets\icons\um\user16.png" />
    <Content Include="assets\icons\um\userIconDelete.png" />
    <Content Include="assets\icons\um\userIconRecover.png" />
    <Content Include="assets\images\app_store_badge.png" />
    <Content Include="assets\images\avatar-small.jpg" />
    <Content Include="assets\images\blank-picture.png" />
    <Content Include="assets\images\callTypes.png" />
    <Content Include="assets\images\callTypesList.png" />
    <Content Include="assets\images\callTypes_new.png" />
    <Content Include="assets\images\callTypes_old.png" />
    <Content Include="assets\images\favicon.ico" />
    <Content Include="assets\images\flexigrid\bg.gif" />
    <Content Include="assets\images\flexigrid\btn-sprite.gif" />
    <Content Include="assets\images\flexigrid\ddn.png" />
    <Content Include="assets\images\flexigrid\dn.png" />
    <Content Include="assets\images\flexigrid\fhbg.gif" />
    <Content Include="assets\images\flexigrid\first.gif" />
    <Content Include="assets\images\flexigrid\hl.png" />
    <Content Include="assets\images\flexigrid\last.gif" />
    <Content Include="assets\images\flexigrid\line.gif" />
    <Content Include="assets\images\flexigrid\load.gif" />
    <Content Include="assets\images\flexigrid\load.png" />
    <Content Include="assets\images\flexigrid\magnifier.png" />
    <Content Include="assets\images\flexigrid\next.gif" />
    <Content Include="assets\images\flexigrid\prev.gif" />
    <Content Include="assets\images\flexigrid\up.png" />
    <Content Include="assets\images\flexigrid\uup.png" />
    <Content Include="assets\images\flexigrid\wbg.gif" />
    <Content Include="assets\images\groupType.png" />
    <Content Include="assets\images\groupType_white.png" />
    <Content Include="assets\images\Inquire-logo-small.png" />
    <Content Include="assets\images\Inquire-logo.png" />
    <Content Include="assets\images\inquire\AppleAppStore.png" />
    <Content Include="assets\images\inquire\delete.png" />
    <Content Include="assets\images\inquire\delete2.jpg" />
    <Content Include="assets\images\inquire\Edit.png" />
    <Content Include="assets\images\inquire\edit2.jpg" />
    <Content Include="assets\images\inquire\edit3.png" />
    <Content Include="assets\images\inquire\GooglePlayStore.png" />
    <Content Include="assets\images\inquire\IQ3AndroidQR.jpg" />
    <Content Include="assets\images\inquire\IQ3IOSQR.jpg" />
    <Content Include="assets\images\inquire\MDAndroidQR.jpg" />
    <Content Include="assets\images\IQ3.png" />
    <Content Include="assets\images\jReject\background_browser.gif" />
    <Content Include="assets\images\jReject\browser_chrome.gif" />
    <Content Include="assets\images\jReject\browser_firefox.gif" />
    <Content Include="assets\images\jReject\browser_konqueror.gif" />
    <Content Include="assets\images\jReject\browser_msie.gif" />
    <Content Include="assets\images\jReject\browser_opera.gif" />
    <Content Include="assets\images\jReject\browser_safari.gif" />
    <Content Include="assets\images\loading-poster.png" />
    <Content Include="assets\images\loginHeader.gif" />
    <Content Include="assets\images\loginHeader.png" />
    <Content Include="assets\images\Login_Page_Graphic01 - Copy.png" />
    <Content Include="assets\images\Login_Page_Graphic01.png" />
    <Content Include="assets\images\maps\m1.png" />
    <Content Include="assets\images\maps\m2.png" />
    <Content Include="assets\images\maps\m3.png" />
    <Content Include="assets\images\maps\m4.png" />
    <Content Include="assets\images\maps\m5.png" />
    <Content Include="assets\images\paging\NavFirstPage.png" />
    <Content Include="assets\images\paging\NavFirstPageDisabled.png" />
    <Content Include="assets\images\paging\NavLastPage.png" />
    <Content Include="assets\images\paging\NavLastPageDisabled.png" />
    <Content Include="assets\images\paging\NavNextPage.png" />
    <Content Include="assets\images\paging\NavNextPageDisabled.png" />
    <Content Include="assets\images\paging\NavPreviousPage.png" />
    <Content Include="assets\images\paging\NavPreviousPageDisabled.png" />
    <Content Include="assets\images\phoneImage.png" />
    <Content Include="assets\images\play-poster.png" />
    <Content Include="assets\images\player\alarm.mp3" />
    <Content Include="assets\images\player\attach2.png" />
    <Content Include="assets\images\player\balance_left.png" />
    <Content Include="assets\images\player\balance_left_active.png" />
    <Content Include="assets\images\player\balance_right.png" />
    <Content Include="assets\images\player\balance_right_active.png" />
    <Content Include="assets\images\player\begin - Original.png" />
    <Content Include="assets\images\player\begin.png" />
    <Content Include="assets\images\player\begin_active.png" />
    <Content Include="assets\images\player\begin_Ex.png" />
    <Content Include="assets\images\player\bookmark -Original.png" />
    <Content Include="assets\images\player\bookmark.png" />
    <Content Include="assets\images\player\bookmark_active.png" />
    <Content Include="assets\images\player\browse3.png" />
    <Content Include="assets\images\player\browse4.jpg" />
    <Content Include="assets\images\player\button - Copy.jpg" />
    <Content Include="assets\images\player\button-1.jpg" />
    <Content Include="assets\images\player\button.jpg" />
    <Content Include="assets\images\player\dp2.jpg" />
    <Content Include="assets\images\player\end -Original.png" />
    <Content Include="assets\images\player\end.png" />
    <Content Include="assets\images\player\end_active.png" />
    <Content Include="assets\images\player\establishingconnection.gif" />
    <Content Include="assets\images\player\export.png" />
    <Content Include="assets\images\player\export_active.png" />
    <Content Include="assets\images\player\fast_active.png" />
    <Content Include="assets\images\player\fast_forward -Original.png" />
    <Content Include="assets\images\player\fast_forward.png" />
    <Content Include="assets\images\player\fast_forward_active.png" />
    <Content Include="assets\images\player\loop.png" />
    <Content Include="assets\images\player\loop_active.png" />
    <Content Include="assets\images\player\marker.png" />
    <Content Include="assets\images\player\max_volume.png" />
    <Content Include="assets\images\player\max_volume_active.png" />
    <Content Include="assets\images\player\min_volume.png" />
    <Content Include="assets\images\player\min_volume_active.png" />
    <Content Include="assets\images\player\pause -Original.png" />
    <Content Include="assets\images\player\pause-2.png" />
    <Content Include="assets\images\player\pause.png" />
    <Content Include="assets\images\player\pause_active.png" />
    <Content Include="assets\images\player\pause_Test.png" />
    <Content Include="assets\images\player\play -Original.png" />
    <Content Include="assets\images\player\play.png" />
    <Content Include="assets\images\player\play2.png" />
    <Content Include="assets\images\player\play3.png" />
    <Content Include="assets\images\player\player_bg.png" />
    <Content Include="assets\images\player\play_active.png" />
    <Content Include="assets\images\player\play_new.png" />
    <Content Include="assets\images\player\rewind -Original.png" />
    <Content Include="assets\images\player\rewind.png" />
    <Content Include="assets\images\player\rewind_active.png" />
    <Content Include="assets\images\player\Save_Button.png" />
    <Content Include="assets\images\player\settings-original.png" />
    <Content Include="assets\images\player\settings.png" />
    <Content Include="assets\images\player\settings_active.png" />
    <Content Include="assets\images\player\slow.png" />
    <Content Include="assets\images\player\slow_active.png" />
    <Content Include="assets\images\player\stop -Original.png" />
    <Content Include="assets\images\player\stop.png" />
    <Content Include="assets\images\player\stop_active.png" />
    <Content Include="assets\images\player\time_indicator.png" />
    <Content Include="assets\images\player\video.jpg" />
    <Content Include="assets\images\player_bg_1030.png" />
    <Content Include="assets\images\blue_bg.png" />
    <Content Include="assets\images\blue_bg130.png" />
    <Content Include="assets\images\playEvent.png" />
    <Content Include="assets\images\play_store_badge-old.png" />
    <Content Include="assets\images\play_store_badge.png" />
    <Content Include="assets\images\revcord-monochrome-white.png" />
    <Content Include="assets\images\revcordHeader.png" />
    <Content Include="assets\images\revcordSmall.png" />
    <Content Include="assets\css\bootstrap\bootstrap-responsive.css" />
    <Content Include="assets\css\bootstrap\bootstrap.css" />
    <Content Include="assets\css\jQuery\images\ui-bg_flat_55_999999_40x100.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_flat_75_aaaaaa_40x100.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_glass_45_0078ae_1x400.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_glass_55_f8da4e_1x400.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_glass_75_79c9ec_1x400.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_gloss-wave_45_e14f1c_500x100.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_gloss-wave_50_000000_500x100.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_gloss-wave_75_2191c0_500x100.png" />
    <Content Include="assets\css\jQuery\images\ui-bg_inset-hard_100_fcfdfd_1x100.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_0078ae_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_056b93_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_d8e7f3_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_e0fdff_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_f5e175_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_f7a50d_256x240.png" />
    <Content Include="assets\css\jQuery\images\ui-icons_fcd113_256x240.png" />
    <Content Include="assets\css\jQuery\jquery-ui-1.9.0.custom.min.css" />
    <Content Include="assets\css\style.css" />
    <Content Include="assets\images\ex_button_bg.png" />
    <Content Include="assets\images\header_bg.png" />
    <Content Include="assets\images\header_select.png" />
    <Content Include="assets\images\revcord.png" />
    <Content Include="assets\images\phoneIcon.jpg" />
    <Content Include="assets\images\revcord_new.png" />
    <Content Include="assets\images\revcord_old.png" />
    <Content Include="assets\images\survey\step1Bg.png" />
    <Content Include="assets\images\survey\step2Bg.png" />
    <Content Include="assets\images\survey\step2BgHover.png" />
    <Content Include="assets\images\tables\sort_asc.png" />
    <Content Include="assets\images\tables\sort_asc_disabled.png" />
    <Content Include="assets\images\tables\sort_both.png" />
    <Content Include="assets\images\tables\sort_desc.png" />
    <Content Include="assets\images\tables\sort_desc_disabled.png" />
    <Content Include="assets\js\AddFiles\calendar-en.min.js" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\.tagconfig" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\.travis.yml" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\AMD_footer" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\AMD_header" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\bower.json" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\component.json" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\composer.json" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\CONTRIBUTING.md" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\dist\readme.md" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\Gruntfile.coffee" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\LICENSE" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\package.json" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\README.md" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\src\basic.scss" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\src\dropzone.coffee" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\src\dropzone.scss" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\test.sh" />
    <Content Include="assets\DropzoneJS\assets\dropzone-master\test\test.coffee" />
    <Content Include="assets\DropzoneJS\hn_SimpeFileUploader.ashx" />
    <Content Include="assets\css\jQuery\fonts\jsglyph.eot" />
    <Content Include="assets\css\jQuery\fonts\jsglyph.ttf" />
    <Content Include="assets\css\jQuery\fonts\jsglyph.woff" />
    <Content Include="assetsNew\images.zip" />
    <Content Include="assetsNew\less\app.less" />
    <Content Include="assetsNew\less\bootstrap\alerts.less" />
    <Content Include="assetsNew\less\bootstrap\badges.less" />
    <Content Include="assetsNew\less\bootstrap\bootstrap.less" />
    <Content Include="assetsNew\less\bootstrap\breadcrumbs.less" />
    <Content Include="assetsNew\less\bootstrap\button-groups.less" />
    <Content Include="assetsNew\less\bootstrap\buttons.less" />
    <Content Include="assetsNew\less\bootstrap\carousel.less" />
    <Content Include="assetsNew\less\bootstrap\close.less" />
    <Content Include="assetsNew\less\bootstrap\code.less" />
    <Content Include="assetsNew\less\bootstrap\component-animations.less" />
    <Content Include="assetsNew\less\bootstrap\dropdowns.less" />
    <Content Include="assetsNew\less\bootstrap\forms.less" />
    <Content Include="assetsNew\less\bootstrap\glyphicons.less" />
    <Content Include="assetsNew\less\bootstrap\grid.less" />
    <Content Include="assetsNew\less\bootstrap\input-groups.less" />
    <Content Include="assetsNew\less\bootstrap\jumbotron.less" />
    <Content Include="assetsNew\less\bootstrap\labels.less" />
    <Content Include="assetsNew\less\bootstrap\list-group.less" />
    <Content Include="assetsNew\less\bootstrap\media.less" />
    <Content Include="assetsNew\less\bootstrap\mixins.less" />
    <Content Include="assetsNew\less\bootstrap\modals.less" />
    <Content Include="assetsNew\less\bootstrap\navbar.less" />
    <Content Include="assetsNew\less\bootstrap\navs.less" />
    <Content Include="assetsNew\less\bootstrap\normalize.less" />
    <Content Include="assetsNew\less\bootstrap\pager.less" />
    <Content Include="assetsNew\less\bootstrap\pagination.less" />
    <Content Include="assetsNew\less\bootstrap\panels.less" />
    <Content Include="assetsNew\less\bootstrap\popovers.less" />
    <Content Include="assetsNew\less\bootstrap\print.less" />
    <Content Include="assetsNew\less\bootstrap\progress-bars.less" />
    <Content Include="assetsNew\less\bootstrap\responsive-utilities.less" />
    <Content Include="assetsNew\less\bootstrap\scaffolding.less" />
    <Content Include="assetsNew\less\bootstrap\tables.less" />
    <Content Include="assetsNew\less\bootstrap\thumbnails.less" />
    <Content Include="assetsNew\less\bootstrap\tooltip.less" />
    <Content Include="assetsNew\less\bootstrap\type.less" />
    <Content Include="assetsNew\less\bootstrap\utilities.less" />
    <Content Include="assetsNew\less\bootstrap\variables.less" />
    <Content Include="assetsNew\less\bootstrap\wells.less" />
    <Content Include="assetsNew\less\plugins\datepicker.less" />
    <Content Include="assetsNew\less\plugins\family_tree.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\bordered-pulled.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\core.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\fixed-width.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\font-awesome.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\icons.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\larger.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\list.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\mixins.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\path.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\rotated-flipped.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\spinning.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\stacked.less" />
    <Content Include="assetsNew\less\plugins\font-awesome\variables.less" />
    <Content Include="assetsNew\less\plugins\morris.core.less" />
    <Content Include="assetsNew\less\plugins\summernote.less" />
    <Content Include="assetsNew\less\plugins\summernote_elements.less" />
    <Content Include="assetsNew\less\plugins\ui-slider.less" />
    <Content Include="assetsNew\less\template_less\bootstrap-related.less" />
    <Content Include="assetsNew\less\template_less\glossed.less" />
    <Content Include="assetsNew\less\template_less\header.less" />
    <Content Include="assetsNew\less\template_less\main.less" />
    <Content Include="assetsNew\less\template_less\page-invoice.less" />
    <Content Include="assetsNew\less\template_less\page-login.less" />
    <Content Include="assetsNew\less\template_less\page-order-details.less" />
    <Content Include="assetsNew\less\template_less\page-profile.less" />
    <Content Include="assetsNew\less\template_less\saturn_mixins.less" />
    <Content Include="assetsNew\less\template_less\sidebar.less" />
    <Content Include="assetsNew\less\template_less\widgets.less" />
    <Content Include="APIKeys\GoogleCalendar\developer\events-244417-645bbbbd214a.p12">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="APIKeys\GoogleCalendar\developer\events-244417-747d5e9b2a7f.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="assets\eqcss\player\styles.css" />
    <Content Include="assets\fonts\lg.eot" />
    <Content Include="assets\fonts\lg.ttf" />
    <Content Include="assets\fonts\lg.woff" />
    <Content Include="assetsNew\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="assets\fonts\ARIAL.TTF" />
    <Content Include="assetsNew\fonts\font-awesome\fa-brands-400.ttf" />
    <Content Include="assetsNew\fonts\font-awesome\fa-brands-400.woff2" />
    <Content Include="assetsNew\fonts\font-awesome\fa-regular-400.ttf" />
    <Content Include="assetsNew\fonts\font-awesome\fa-regular-400.woff2" />
    <Content Include="assetsNew\fonts\font-awesome\fa-solid-900.ttf" />
    <Content Include="assetsNew\fonts\font-awesome\fa-solid-900.woff2" />
    <Content Include="assetsNew\fonts\font-awesome\fa-v4compatibility.ttf" />
    <Content Include="assetsNew\fonts\font-awesome\fa-v4compatibility.woff2" />
    <None Include="assets\js\AddFiles\jquery-1.4.1-vsdoc.js" />
    <Content Include="assets\js\AddFiles\jquery-1.4.1.js" />
    <Content Include="assets\js\AddFiles\jquery-1.4.1.min.js" />
    <Content Include="assets\js\AddFiles\jquery.dynDateTime.min.js" />
    <Content Include="assets\js\AES\aes.js" />
    <Content Include="assets\js\AES\pbkdf2.js" />
    <Content Include="assets\js\ajax\xmlhttpRequestionMethod.js" />
    <Content Include="assets\js\bootstrap-paginator\bootstrap-paginator.js" />
    <Content Include="assets\js\bootstrap-paginator\bootstrap-paginator.min.js" />
    <Content Include="assets\js\bootstrap\bootstrap.js" />
    <Content Include="assets\js\canvasjs.min.js" />
    <Content Include="assets\js\charts\drilldown.js" />
    <Content Include="assets\js\charts\highcharts.js" />
    <Content Include="assets\js\charts\highcharts.v7.1.1.js" />
    <Content Include="assets\js\charts\highcharts4.2.1.js" />
    <Content Include="assets\js\common\BookmarkFlagcolorPick.js" />
    <Content Include="assets\js\common\CallAudit.js" />
    <Content Include="assets\js\common\colorPick.js" />
    <Content Include="assets\js\common\dialogManager.js" />
    <Content Include="assets\js\common\jspdf.debug.js" />
    <Content Include="assets\js\common\masterPage.js" />
    <Content Include="assets\js\common\player.js" />
    <Content Include="assets\js\common\revview.js" />
    <Content Include="assets\js\ctrls\mmsSearchCtrls.js" />
    <Content Include="assets\js\date.js" />
    <Content Include="assets\js\detect.js" />
    <Content Include="assets\js\documentReady.js" />
    <Content Include="assets\js\File_Player_HTML5\jquery.jplayer.min.js" />
    <Content Include="assets\js\File_Player_HTML5\jquery.min.js" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\css\jplayer.blue.monday.css" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\css\jplayer.blue.monday.min.css" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\image\jplayer.blue.monday.jpg" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\image\jplayer.blue.monday.seeking.gif" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\image\jplayer.blue.monday.video.play.png" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\image\revcord.png" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\mustache\jplayer.blue.monday.audio.playlist.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\mustache\jplayer.blue.monday.audio.single.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\mustache\jplayer.blue.monday.audio.stream.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\mustache\jplayer.blue.monday.video.playlist.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\blue.monday\mustache\jplayer.blue.monday.video.single.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\css\jplayer.pink.flag.css" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\css\jplayer.pink.flag.min.css" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\image\jplayer.pink.flag.jpg" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\image\jplayer.pink.flag.seeking.gif" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\image\jplayer.pink.flag.video.play.png" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\mustache\jplayer.pink.flag.audio.playlist.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\mustache\jplayer.pink.flag.audio.single.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\mustache\jplayer.pink.flag.audio.stream.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\mustache\jplayer.pink.flag.video.playlist.html" />
    <Content Include="assets\js\File_Player_HTML5\skin\pink.flag\mustache\jplayer.pink.flag.video.single.html" />
    <Content Include="assets\js\grid\colResizable-1.3.min.js" />
    <Content Include="assets\js\grid\flexigridIR.js" />
    <Content Include="assets\js\grid\flexigridNew.js" />
    <Content Include="assets\js\grid\flexigrid.js" />
    <Content Include="assets\js\grid\jquery.flexigrid_mjs.js" />
    <None Include="assets\js\jQuery\jquery-1.10.2.intellisense.js" />
    <Content Include="assets\js\Hls\hls.js" />
    <Content Include="assets\js\html2pdf\html2pdf.bundle.min.js" />
    <Content Include="assets\js\html5player\Blob.js" />
    <Content Include="assets\js\html5player\CommonConstants.js" />
    <Content Include="assets\js\html5player\deflate.js" />
    <Content Include="assets\js\html5player\DocViewer.js" />
    <Content Include="assets\js\html5player\Edit\js\ACAAFilter.js" />
    <Content Include="assets\js\html5player\Edit\js\ACFFT.js" />
    <Content Include="assets\js\html5player\Edit\js\ACFIRFilter.js" />
    <Content Include="assets\js\html5player\Edit\js\ACSpectrum.js" />
    <Content Include="assets\js\html5player\Edit\js\audioLayerControl.js" />
    <Content Include="assets\js\html5player\Edit\js\audioplayback.js" />
    <Content Include="assets\js\html5player\Edit\js\audiosequence.js" />
    <Content Include="assets\js\html5player\Edit\js\AudioSequenceEditor.js" />
    <Content Include="assets\js\html5player\Edit\js\binarytoolkit.js" />
    <Content Include="assets\js\html5player\Edit\js\editorapp.js" />
    <Content Include="assets\js\html5player\Edit\js\fft.js" />
    <Content Include="assets\js\html5player\Edit\js\filedropbox.js" />
    <Content Include="assets\js\html5player\Edit\js\filedropbox_20170320.js" />
    <Content Include="assets\js\html5player\Edit\js\filesystemutility.js" />
    <Content Include="assets\js\html5player\Edit\js\mathutilities.js" />
    <Content Include="assets\js\html5player\Edit\js\recorder.js" />
    <Content Include="assets\js\html5player\Edit\js\recorderWorker.js" />
    <Content Include="assets\js\html5player\Edit\js\SpectrumDisplay.js" />
    <Content Include="assets\js\html5player\Edit\js\SpectrumWorker.js" />
    <Content Include="assets\js\html5player\Edit\js\wavetrack.js" />
    <Content Include="assets\js\html5player\Edit\mainstyle.css" />
    <Content Include="assets\js\html5player\FilePlaybackItem3.js" />
    <Content Include="assets\js\html5player\FilePlayer.js" />
    <Content Include="assets\js\html5player\FileSaver.js" />
    <Content Include="assets\js\html5player\html5fileplayer.js" />
    <Content Include="assets\js\html5player\html5player.js" />
    <Content Include="assets\js\html5player\inflate.js" />
    <Content Include="assets\js\html5player\JavaScript.js" />
    <Content Include="assets\js\html5player\JavaScript1.js" />
    <Content Include="assets\js\html5player\lame.all.js" />
    <Content Include="assets\js\html5player\MediaExportProcessor.js" />
    <Content Include="assets\js\html5player\mp3encoder.js" />
    <Content Include="assets\js\html5player\pdf.js" />
    <Content Include="assets\js\html5player\pdfobject.js" />
    <Content Include="assets\js\html5player\polyfill.min.js" />
    <Content Include="assets\js\html5player\prism.js" />
    <Content Include="assets\js\html5player\recorder.js" />
    <Content Include="assets\js\html5player\require.js" />
    <Content Include="assets\js\html5player\Resampler.js" />
    <Content Include="assets\js\html5player\SaveCallWorker.js" />
    <Content Include="assets\js\html5player\ScreenVideo.js" />
    <Content Include="assets\js\html5player\SearchPlayer.js" />
    <Content Include="assets\js\html5player\SegmentWorker.js" />
    <Content Include="assets\js\html5player\Sequencer.js" />
    <Content Include="assets\js\html5player\SliceWav.js" />
    <Content Include="assets\js\html5player\StreamSaver.js" />
    <Content Include="assets\js\html5player\TalkingClockWorker.js" />
    <Content Include="assets\js\html5player\timelineplayer.js" />
    <Content Include="assets\js\html5player\wav.js" />
    <Content Include="assets\js\html5player\web-streams-polyfill.js" />
    <Content Include="assets\js\html5player\ws.js" />
    <Content Include="assets\js\html5player\z-worker.js" />
    <Content Include="assets\js\html5player\zip-ext.js" />
    <Content Include="assets\js\html5player\zip.js" />
    <Content Include="assets\js\jm.spinner.js" />
    <Content Include="assets\js\jplayer\jquery.jplayer.js" />
    <Content Include="assets\js\jplayer\jquery.jplayer.min.js" />
    <Content Include="assets\js\jplayer\jquery.jplayer.swf" />
    <Content Include="assets\js\jquery-steps\jquery-1.9.1.min.js" />
    <Content Include="assets\js\jquery-steps\jquery.steps.css" />
    <Content Include="assets\js\jquery-steps\jquery.steps.js" />
    <Content Include="assets\js\jquery-steps\jquery.steps.min.js" />
    <Content Include="assets\js\jquery-steps\main.css" />
    <Content Include="assets\js\jQuery\jquery-1.10.2.js" />
    <Content Include="assets\js\jQuery\jquery-1.10.2.min.js" />
    <Content Include="assets\js\jQuery\jquery-1.6.4-vsdoc.js" />
    <Content Include="assets\js\jQuery\jquery-1.8.2.min.js" />
    <Content Include="assets\js\jQuery\jquery-ui-1.9.0.custom.min.js" />
    <Content Include="assets\js\jQuery\jquery-ui-custom-mmsDialog.js" />
    <Content Include="assets\js\jQuery\jquery-ui-sliderAccess.js" />
    <Content Include="assets\js\jQuery\jquery-ui-timepicker-addon.js" />
    <Content Include="assets\js\jQuery\jquery-weekdays.js" />
    <Content Include="assets\js\jQuery\jquery.blockUI.js" />
    <Content Include="assets\js\jQuery\jquery.checkboxtree.js" />
    <Content Include="assets\js\jQuery\jquery.contextmenu.js" />
    <Content Include="assets\js\jQuery\jquery.cookie.js" />
    <Content Include="assets\js\jQuery\jquery.frontend-logger.js" />
    <Content Include="assets\js\jQuery\jquery.jspanel-compiled.min.js" />
    <Content Include="assets\js\jQuery\jquery.mobile-events.js" />
    <Content Include="assets\js\jQuery\jquery.reject.js" />
    <Content Include="assets\js\jQuery\jquery.shiftcheckbox.js" />
    <Content Include="assets\js\jQuery\jquery.signalR-2.1.2.js" />
    <Content Include="assets\js\jQuery\jquery.signalR-2.1.2.min.js" />
    <Content Include="assets\js\jQuery\jquery.stopwatch.js" />
    <Content Include="assets\js\jQuery\jquery.tooltip.min.js" />
    <Content Include="assets\js\jQuery\jquery.ui.touch-punch.js" />
    <Content Include="assets\js\jQuery\jqueryintellisense.js" />
    <Content Include="assets\js\jQuery\tableSorter2.7.3min.js" />
    <Content Include="assets\js\lightgallery\lightgallery.js" />
    <Content Include="assets\js\lightgallery\modules\lg-autoplay.js" />
    <Content Include="assets\js\lightgallery\modules\lg-fullscreen.js" />
    <Content Include="assets\js\lightgallery\modules\lg-hash.js" />
    <Content Include="assets\js\lightgallery\modules\lg-thumbnail.js" />
    <Content Include="assets\js\lightgallery\modules\lg-zoom.js" />
    <Content Include="assets\js\Live_Monitor\nDVR\jquery\jquery-3.1.1.min.js" />
    <Content Include="assets\js\Live_Monitor\nDVR\jquery\jquery.swfobject.1-1-1.min.js" />
    <Content Include="assets\js\Live_Monitor\nDVR\js\swfobject.js" />
    <Content Include="assets\js\Live_Monitor\nDVR\swf\StrobeMediaPlayback.swf" />
    <Content Include="assets\js\Live_Monitor\nDVR\videojs-contrib-hls.min.js" />
    <Content Include="assets\js\Live_Monitor\overlay\videojs-overlay-hyperlink.css" />
    <Content Include="assets\js\Live_Monitor\overlay\videojs-overlay-hyperlink.js" />
    <Content Include="assets\js\Live_Monitor\video-js.css" />
    <Content Include="assets\js\Live_Monitor\video.js" />
    <Content Include="assets\js\Live_Monitor\videojs-ie8.min.js" />
    <Content Include="assets\js\Live_Video_Player\AC_RunActiveContent.js" />
    <Content Include="assets\js\localization\datePicker.js" />
    <Content Include="assets\js\localization\lang.js" />
    <Content Include="assets\js\localization\datetimePicker.js" />
    <Content Include="assets\js\maps\infobox.js" />
    <Content Include="assets\js\maps\mapwithmarker.js" />
    <Content Include="assets\js\maps\markerclusterer.js" />
    <Content Include="assets\js\maps\markerclusterer_compiled.js" />
    <Content Include="assets\js\maps\oms.min.js" />
    <Content Include="assets\js\modernizr.js" />
    <Content Include="assets\js\mpegdashplayer\dash.all.js" />
    <Content Include="assets\js\mpegdashplayer\shaka-player.js" />
    <Content Include="assets\js\pages\BookmarkFlag.js" />
    <Content Include="assets\js\pages\CallTagging.js" />
    <Content Include="assets\js\pages\common.js" />
    <Content Include="assets\js\pages\emailControl.js" />
    <Content Include="assets\js\pages\IQ3InspectionCustomFields.js" />
    <Content Include="assets\js\pages\IQ3InspectionReport.js" />
    <Content Include="assets\js\pages\IQ3InspectionCustomerDBView.js" />
    <Content Include="assets\js\pages\IQ3InspectionCustomerDB.js" />
    <Content Include="assets\js\pages\IQ3RVIMessage.js" />
    <Content Include="assets\js\pages\IQ3UsageReports.js" />
    <Content Include="assets\js\pages\managerepository.js" />
    <Content Include="assets\js\pages\report.js" />
    <Content Include="assets\js\pages\workerhistory.js" />
    <Content Include="assets\js\pages\mywpq.js" />
    <Content Include="assets\js\pages\teamsChannels.js" />
    <Content Include="assets\js\pages\CustomSettings.js" />
    <Content Include="assets\js\pages\adgroupsync.js" />
    <Content Include="assets\js\pages\EditInspectionTemplate.js" />
    <Content Include="assets\js\pages\inspection.js" />
    <Content Include="assets\js\pages\InspectionMonitor.js" />
    <Content Include="assets\js\pages\InspectionTemplate.js" />
    <Content Include="assets\js\pages\IQ3Inspection.js" />
    <Content Include="assets\js\pages\IQ3InspectionLive.js" />
    <Content Include="assets\js\pages\IQ3InspectionTitle.js" />
    <Content Include="assets\js\pages\mmsReports.js" />
    <Content Include="assets\js\pages\multichannelplayback.js" />
    <Content Include="assets\js\pages\RemoteInspection.js" />
    <Content Include="assets\js\pages\revcell.js" />
    <Content Include="assets\js\pages\rolemanagement.js" />
    <Content Include="assets\js\pages\standardReport.js" />
    <Content Include="assets\js\pages\advanceReport.js" />
    <Content Include="assets\js\pages\agentEval.js" />
    <Content Include="assets\js\pages\calendar.js" />
    <Content Include="assets\js\pages\callAuditReport.js" />
    <Content Include="assets\js\pages\channels.js" />
    <Content Include="assets\js\pages\commonMonitor.js" />
    <Content Include="assets\js\pages\dashboard.js" />
    <Content Include="assets\js\pages\enterpriseUser.js" />
    <Content Include="assets\js\pages\evalSearchCalls.js" />
    <Content Include="assets\js\pages\evaluationReport.js" />
    <Content Include="assets\js\pages\eventmonitor.js" />
    <Content Include="assets\js\pages\eventShare.js" />
    <Content Include="assets\js\pages\extraEmailConfiguration.js" />
    <Content Include="assets\js\pages\instantRecall\ir.js" />
    <Content Include="assets\js\pages\instantRecall\instantRecall.js" />
    <Content Include="assets\js\pages\invitation.js" />
    <Content Include="assets\js\pages\login.js" />
    <Content Include="assets\js\pages\monitor.js" />
    <Content Include="assets\js\pages\playlist.js" />
    <Content Include="assets\js\pages\ScheduleEvent.js" />
    <Content Include="assets\js\pages\scheduleReport.js" />
    <Content Include="assets\js\pages\SearchDefault.js" />
    <Content Include="assets\js\pages\search.js" />
    <Content Include="assets\js\pages\setup.js" />
    <Content Include="assets\js\pages\survey\createSurvey.js" />
    <Content Include="assets\js\pages\survey\editSurvey.js" />
    <Content Include="assets\js\pages\survey\survey.js" />
    <Content Include="assets\js\pages\survey\surveyCommon.js" />
    <Content Include="assets\js\pages\survey\surveySection.js" />
    <Content Include="assets\js\pages\UserManager.js" />
    <Content Include="assets\js\select2\select2-custom.js" />
    <Content Include="assets\js\select2\select2.min.js" />
    <Content Include="assets\js\shaka_player\dash.all.js" />
    <Content Include="assets\js\shaka_player\LiveVideoWrapper.js" />
    <Content Include="assets\js\shaka_player\shaka-player.js" />
    <Content Include="assets\js\shaka_player\shaka-player_unmini.js" />
    <Content Include="assets\js\simplemodal\jquery.simplemodal.js" />
    <Content Include="assets\js\simplemodal\osx.css" />
    <Content Include="assets\js\simplemodal\osx.js" />
    <Content Include="assets\js\Store_JS\store_json2.min.js" />
    <Content Include="assets\js\Store_JS\store.min.js" />
    <Content Include="assets\js\tables\dataTables.checkboxes.min.js" />
    <Content Include="assets\js\tables\dataTables.colReorder.min.js" />
    <Content Include="assets\js\tables\dataTables.colReorderWithResize.js" />
    <Content Include="assets\js\tables\dataTables.fixedHeader.min.js" />
    <Content Include="assets\js\tables\dataTables.responsive.min.js" />
    <Content Include="assets\js\tables\dataTables.select.min.js" />
    <Content Include="assets\js\tables\jquery.dataTables.SearchMedia.js" />
    <Content Include="assets\js\tables\jquery.dataTables.Search.js" />
    <Content Include="assets\js\tables\jquery.dataTables.js" />
    <Content Include="assets\js\TimelineView\custom.js" />
    <Content Include="assets\js\TimelineView\timeline.js" />
    <Content Include="assets\js\TimelineView\underscore-min.js" />
    <Content Include="assets\js\Twilio\js\textConference.js" />
    <Content Include="assets\js\Twilio\js\twilioUtil.js" />
    <Content Include="assets\js\Twilio\js\videoConference.js" />
    <Content Include="assets\js\Twilio\lib\fingerprint2.js" />
    <Content Include="assets\js\Twilio\lib\md5.js" />
    <Content Include="assets\js\Twilio\lib\superagent.js" />
    <Content Include="assets\js\Twilio\lib\twilio-chat.min.js" />
    <Content Include="assets\js\Twilio\lib\twilio-common.min.js" />
    <Content Include="assets\js\Twilio\lib\twilio-video.min.js" />
    <Content Include="assets\js\uploadify\jquery.uploadify.v2.1.4.min.js" />
    <Content Include="assets\js\uploadify\swfobject.js" />
    <Content Include="assets\js\uploadify\uploadify.css" />
    <Content Include="assets\js\uploadify\uploadify.swf" />
    <Content Include="assets\js\silverlight.js" />
    <Content Include="assets\js\util\date.js" />
    <Content Include="assets\js\WebRtcPlayer\js\jquery-3.1.0.min.js" />
    <Content Include="assets\js\WebRtcPlayer\js\jquery.cookie.js" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\customcontrols.css" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\customcontrols.js" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\fullscreen %282%29.jpg" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\fullscreen.jpg" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\images\min_volume.png" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\images\play.png" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\max_volume_active.png" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\min_volume.png" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\min_volume_active.png" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\pause_active.png" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\play_active.png" />
    <Content Include="assets\js\WebRtcPlayer\PlayerCustomControls\swap.png" />
    <Content Include="assets\js\WebRtcPlayer\play\webrtc.js" />
    <Content Include="assets\js\WebRtcPlayer\play\webrtc_IOS.js" />
    <Content Include="assets\lib\fullcalendarjs\fullcalendar.css" />
    <Content Include="assets\lib\fullcalendarjs\fullcalendar.js" />
    <Content Include="assets\lib\fullcalendarjs\fullcalendar.min.css" />
    <Content Include="assets\lib\fullcalendarjs\fullcalendar.min.js" />
    <Content Include="assets\lib\fullcalendarjs\fullcalendar.print.css" />
    <Content Include="assets\lib\fullcalendarjs\fullcalendar.print.min.css" />
    <Content Include="assets\lib\fullcalendarjs\gcal.js" />
    <Content Include="assets\lib\fullcalendarjs\gcal.min.js" />
    <Content Include="assets\lib\fullcalendarjs\locale-all.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\af.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ar-dz.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ar-kw.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ar-ly.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ar-ma.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ar-sa.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ar-tn.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ar.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\be.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\bg.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\bs.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ca.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\cs.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\da.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\de-at.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\de-ch.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\de.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\el.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\en-au.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\en-ca.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\en-gb.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\en-ie.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\en-nz.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\es-do.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\es-us.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\es.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\et.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\eu.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\fa.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\fi.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\fr-ca.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\fr-ch.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\fr.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\gl.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\he.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\hi.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\hr.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\hu.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\id.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\is.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\it.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ja.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ka.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\kk.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ko.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\lb.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\lt.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\lv.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\mk.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ms-my.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ms.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\nb.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\nl-be.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\nl.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\nn.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\pl.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\pt-br.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\pt.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ro.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\ru.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\sk.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\sl.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\sq.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\sr-cyrl.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\sr.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\sv.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\th.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\tr.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\uk.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\vi.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\zh-cn.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\zh-hk.js" />
    <Content Include="assets\lib\fullcalendarjs\locale\zh-tw.js" />
    <Content Include="assets\lib\fullcalendar\bootstrap\main.css" />
    <Content Include="assets\lib\fullcalendar\bootstrap\main.js" />
    <Content Include="assets\lib\fullcalendar\core\locales-all.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\af.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ar-dz.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ar-kw.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ar-ly.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ar-ma.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ar-sa.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ar-tn.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ar.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\bg.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\bs.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ca.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\cs.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\da.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\de.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\el.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\en-au.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\en-gb.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\en-nz.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\es-us.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\es.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\et.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\eu.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\fa.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\fi.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\fr-ca.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\fr-ch.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\fr.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\gl.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\he.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\hi.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\hr.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\hu.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\id.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\is.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\it.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ja.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ka.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\kk.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ko.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\lb.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\lt.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\lv.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\mk.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ms.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\nb.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\nl.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\nn.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\pl.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\pt-br.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\pt.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ro.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\ru.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\sk.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\sl.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\sq.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\sr-cyrl.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\sr.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\sv.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\th.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\tr.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\uk.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\vi.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\zh-cn.js" />
    <Content Include="assets\lib\fullcalendar\core\locales\zh-tw.js" />
    <Content Include="assets\lib\fullcalendar\core\main.css" />
    <Content Include="assets\lib\fullcalendar\core\main.js" />
    <Content Include="assets\lib\fullcalendar\daygrid\main.css" />
    <Content Include="assets\lib\fullcalendar\daygrid\main.js" />
    <Content Include="assets\lib\fullcalendar\google-calendar\main.js" />
    <Content Include="assets\lib\fullcalendar\interaction\main.js" />
    <Content Include="assets\lib\fullcalendar\list\main.css" />
    <Content Include="assets\lib\fullcalendar\list\main.js" />
    <Content Include="assets\lib\fullcalendar\luxon\main.js" />
    <Content Include="assets\lib\fullcalendar\moment-timezone\main.js" />
    <Content Include="assets\lib\fullcalendar\moment\main.js" />
    <Content Include="assets\lib\fullcalendar\rrule\main.js" />
    <Content Include="assets\lib\fullcalendar\timegrid\main.css" />
    <Content Include="assets\lib\fullcalendar\timegrid\main.js" />
    <Content Include="assets\lib\jstree\jstree.js" />
    <Content Include="assets\lib\jstree\jstree.min.js" />
    <Content Include="assets\lib\jstree\themes\default-dark\32px.png" />
    <Content Include="assets\lib\jstree\themes\default-dark\40px.png" />
    <Content Include="assets\lib\jstree\themes\default-dark\style.css" />
    <Content Include="assets\lib\jstree\themes\default-dark\style.min.css" />
    <Content Include="assets\lib\jstree\themes\default-dark\throbber.gif" />
    <Content Include="assets\lib\jstree\themes\default\32px.png" />
    <Content Include="assets\lib\jstree\themes\default\40px.png" />
    <Content Include="assets\lib\jstree\themes\default\style.css" />
    <Content Include="assets\lib\jstree\themes\default\style.min.css" />
    <Content Include="assets\lib\jstree\themes\default\throbber.gif" />
    <Content Include="assets\lib\mask\jquery.mask.min.js" />
    <Content Include="assets\lib\momentjs\locale\af.js" />
    <Content Include="assets\lib\momentjs\locale\ar-dz.js" />
    <Content Include="assets\lib\momentjs\locale\ar-kw.js" />
    <Content Include="assets\lib\momentjs\locale\ar-ly.js" />
    <Content Include="assets\lib\momentjs\locale\ar-ma.js" />
    <Content Include="assets\lib\momentjs\locale\ar-sa.js" />
    <Content Include="assets\lib\momentjs\locale\ar-tn.js" />
    <Content Include="assets\lib\momentjs\locale\ar.js" />
    <Content Include="assets\lib\momentjs\locale\az.js" />
    <Content Include="assets\lib\momentjs\locale\be.js" />
    <Content Include="assets\lib\momentjs\locale\bg.js" />
    <Content Include="assets\lib\momentjs\locale\bm.js" />
    <Content Include="assets\lib\momentjs\locale\bn.js" />
    <Content Include="assets\lib\momentjs\locale\bo.js" />
    <Content Include="assets\lib\momentjs\locale\br.js" />
    <Content Include="assets\lib\momentjs\locale\bs.js" />
    <Content Include="assets\lib\momentjs\locale\ca.js" />
    <Content Include="assets\lib\momentjs\locale\cs.js" />
    <Content Include="assets\lib\momentjs\locale\cv.js" />
    <Content Include="assets\lib\momentjs\locale\cy.js" />
    <Content Include="assets\lib\momentjs\locale\da.js" />
    <Content Include="assets\lib\momentjs\locale\de-at.js" />
    <Content Include="assets\lib\momentjs\locale\de-ch.js" />
    <Content Include="assets\lib\momentjs\locale\de.js" />
    <Content Include="assets\lib\momentjs\locale\dv.js" />
    <Content Include="assets\lib\momentjs\locale\el.js" />
    <Content Include="assets\lib\momentjs\locale\en-au.js" />
    <Content Include="assets\lib\momentjs\locale\en-ca.js" />
    <Content Include="assets\lib\momentjs\locale\en-gb.js" />
    <Content Include="assets\lib\momentjs\locale\en-ie.js" />
    <Content Include="assets\lib\momentjs\locale\en-il.js" />
    <Content Include="assets\lib\momentjs\locale\en-nz.js" />
    <Content Include="assets\lib\momentjs\locale\en-SG.js" />
    <Content Include="assets\lib\momentjs\locale\eo.js" />
    <Content Include="assets\lib\momentjs\locale\es-do.js" />
    <Content Include="assets\lib\momentjs\locale\es-us.js" />
    <Content Include="assets\lib\momentjs\locale\es.js" />
    <Content Include="assets\lib\momentjs\locale\et.js" />
    <Content Include="assets\lib\momentjs\locale\eu.js" />
    <Content Include="assets\lib\momentjs\locale\fa.js" />
    <Content Include="assets\lib\momentjs\locale\fi.js" />
    <Content Include="assets\lib\momentjs\locale\fo.js" />
    <Content Include="assets\lib\momentjs\locale\fr-ca.js" />
    <Content Include="assets\lib\momentjs\locale\fr-ch.js" />
    <Content Include="assets\lib\momentjs\locale\fr.js" />
    <Content Include="assets\lib\momentjs\locale\fy.js" />
    <Content Include="assets\lib\momentjs\locale\ga.js" />
    <Content Include="assets\lib\momentjs\locale\gd.js" />
    <Content Include="assets\lib\momentjs\locale\gl.js" />
    <Content Include="assets\lib\momentjs\locale\gom-latn.js" />
    <Content Include="assets\lib\momentjs\locale\gu.js" />
    <Content Include="assets\lib\momentjs\locale\he.js" />
    <Content Include="assets\lib\momentjs\locale\hi.js" />
    <Content Include="assets\lib\momentjs\locale\hr.js" />
    <Content Include="assets\lib\momentjs\locale\hu.js" />
    <Content Include="assets\lib\momentjs\locale\hy-am.js" />
    <Content Include="assets\lib\momentjs\locale\id.js" />
    <Content Include="assets\lib\momentjs\locale\is.js" />
    <Content Include="assets\lib\momentjs\locale\it-ch.js" />
    <Content Include="assets\lib\momentjs\locale\it.js" />
    <Content Include="assets\lib\momentjs\locale\ja.js" />
    <Content Include="assets\lib\momentjs\locale\jv.js" />
    <Content Include="assets\lib\momentjs\locale\ka.js" />
    <Content Include="assets\lib\momentjs\locale\kk.js" />
    <Content Include="assets\lib\momentjs\locale\km.js" />
    <Content Include="assets\lib\momentjs\locale\kn.js" />
    <Content Include="assets\lib\momentjs\locale\ko.js" />
    <Content Include="assets\lib\momentjs\locale\ku.js" />
    <Content Include="assets\lib\momentjs\locale\ky.js" />
    <Content Include="assets\lib\momentjs\locale\lb.js" />
    <Content Include="assets\lib\momentjs\locale\lo.js" />
    <Content Include="assets\lib\momentjs\locale\lt.js" />
    <Content Include="assets\lib\momentjs\locale\lv.js" />
    <Content Include="assets\lib\momentjs\locale\me.js" />
    <Content Include="assets\lib\momentjs\locale\mi.js" />
    <Content Include="assets\lib\momentjs\locale\mk.js" />
    <Content Include="assets\lib\momentjs\locale\ml.js" />
    <Content Include="assets\lib\momentjs\locale\mn.js" />
    <Content Include="assets\lib\momentjs\locale\mr.js" />
    <Content Include="assets\lib\momentjs\locale\ms-my.js" />
    <Content Include="assets\lib\momentjs\locale\ms.js" />
    <Content Include="assets\lib\momentjs\locale\mt.js" />
    <Content Include="assets\lib\momentjs\locale\my.js" />
    <Content Include="assets\lib\momentjs\locale\nb.js" />
    <Content Include="assets\lib\momentjs\locale\ne.js" />
    <Content Include="assets\lib\momentjs\locale\nl-be.js" />
    <Content Include="assets\lib\momentjs\locale\nl.js" />
    <Content Include="assets\lib\momentjs\locale\nn.js" />
    <Content Include="assets\lib\momentjs\locale\pa-in.js" />
    <Content Include="assets\lib\momentjs\locale\pl.js" />
    <Content Include="assets\lib\momentjs\locale\pt-br.js" />
    <Content Include="assets\lib\momentjs\locale\pt.js" />
    <Content Include="assets\lib\momentjs\locale\ro.js" />
    <Content Include="assets\lib\momentjs\locale\ru.js" />
    <Content Include="assets\lib\momentjs\locale\sd.js" />
    <Content Include="assets\lib\momentjs\locale\se.js" />
    <Content Include="assets\lib\momentjs\locale\si.js" />
    <Content Include="assets\lib\momentjs\locale\sk.js" />
    <Content Include="assets\lib\momentjs\locale\sl.js" />
    <Content Include="assets\lib\momentjs\locale\sq.js" />
    <Content Include="assets\lib\momentjs\locale\sr-cyrl.js" />
    <Content Include="assets\lib\momentjs\locale\sr.js" />
    <Content Include="assets\lib\momentjs\locale\ss.js" />
    <Content Include="assets\lib\momentjs\locale\sv.js" />
    <Content Include="assets\lib\momentjs\locale\sw.js" />
    <Content Include="assets\lib\momentjs\locale\ta.js" />
    <Content Include="assets\lib\momentjs\locale\te.js" />
    <Content Include="assets\lib\momentjs\locale\tet.js" />
    <Content Include="assets\lib\momentjs\locale\tg.js" />
    <Content Include="assets\lib\momentjs\locale\th.js" />
    <Content Include="assets\lib\momentjs\locale\tl-ph.js" />
    <Content Include="assets\lib\momentjs\locale\tlh.js" />
    <Content Include="assets\lib\momentjs\locale\tr.js" />
    <Content Include="assets\lib\momentjs\locale\tzl.js" />
    <Content Include="assets\lib\momentjs\locale\tzm-latn.js" />
    <Content Include="assets\lib\momentjs\locale\tzm.js" />
    <Content Include="assets\lib\momentjs\locale\ug-cn.js" />
    <Content Include="assets\lib\momentjs\locale\uk.js" />
    <Content Include="assets\lib\momentjs\locale\ur.js" />
    <Content Include="assets\lib\momentjs\locale\uz-latn.js" />
    <Content Include="assets\lib\momentjs\locale\uz.js" />
    <Content Include="assets\lib\momentjs\locale\vi.js" />
    <Content Include="assets\lib\momentjs\locale\x-pseudo.js" />
    <Content Include="assets\lib\momentjs\locale\yo.js" />
    <Content Include="assets\lib\momentjs\locale\zh-cn.js" />
    <Content Include="assets\lib\momentjs\locale\zh-hk.js" />
    <Content Include="assets\lib\momentjs\locale\zh-tw.js" />
    <Content Include="assets\lib\momentjs\moment-with-locales.js" />
    <Content Include="assets\lib\momentjs\moment-with-locales.min.js" />
    <Content Include="assets\lib\momentjs\moment.js" />
    <Content Include="assets\lib\momentjs\moment.min.js" />
    <Content Include="assets\sound\alarm.mp3" />
    <Content Include="assets\sound\Alarm.WAV" />
    <Content Include="asset\css\site.css" />
    <Content Include="asset\images\download %281%29.jpg" />
    <Content Include="asset\images\favicon.ico" />
    <Content Include="asset\images\logo.png" />
    <Content Include="asset\images\revcord.png" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker.css" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker.min.css" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker.standalone.css" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker.standalone.min.css" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker3.css" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker3.min.css" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker3.standalone.css" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker3.standalone.min.css" />
    <Content Include="asset\lib\bootstrap-datepicker\js\bootstrap-datepicker.js" />
    <Content Include="asset\lib\bootstrap-datepicker\js\bootstrap-datepicker.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker-en-CA.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ar-DZ.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ar-tn.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ar.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.az.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.bg.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.bm.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.bn.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.br.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.bs.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ca.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.cs.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.cy.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.da.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.de.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.el.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.en-AU.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.en-CA.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.en-GB.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.en-IE.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.en-NZ.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.en-US.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.en-ZA.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.eo.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.es.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.et.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.eu.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.fa.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.fi.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.fo.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.fr-CH.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.fr.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.gl.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.he.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.hi.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.hr.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.hu.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.hy.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.id.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.is.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.it-CH.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.it.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ja.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ka.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.kh.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.kk.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.km.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ko.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.kr.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.lt.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.lv.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.me.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.mk.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.mn.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.mr.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ms.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.nl-BE.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.nl.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.no.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.oc.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.pl.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.pt-BR.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.pt.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ro.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.rs-latin.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.rs.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ru.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.si.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.sk.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.sl.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.sq.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.sr-latin.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.sr.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.sv.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.sw.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.ta.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.tg.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.th.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.tk.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.tr.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.uk.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.uz-cyrl.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.uz-latn.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.vi.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.zh-CN.min.js" />
    <Content Include="asset\lib\bootstrap-datepicker\locales\bootstrap-datepicker.zh-TW.min.js" />
    <Content Include="asset\lib\bootstrap-daterangepicker\daterangepicker.css" />
    <Content Include="asset\lib\bootstrap-daterangepicker\daterangepicker.js" />
    <Content Include="asset\lib\bootstrap-daterangepicker\daterangepicker.min.css" />
    <Content Include="asset\lib\bootstrap-daterangepicker\daterangepicker.min.js" />
    <Content Include="asset\lib\bootstrap-daterangepicker\moment.min.js" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-grid.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-grid.min.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-grid.rtl.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-grid.rtl.min.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-reboot.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-reboot.min.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-reboot.rtl.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-reboot.rtl.min.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-utilities.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-utilities.min.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-utilities.rtl.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-utilities.rtl.min.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap.min.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap.rtl.css" />
    <Content Include="asset\lib\bootstrap\css\bootstrap.rtl.min.css" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.bundle.js" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.bundle.min.js" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.esm.js" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.esm.min.js" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.js" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.min.js" />
    <Content Include="asset\lib\datatables.net-bs5\dataTables.bootstrap5.css" />
    <Content Include="asset\lib\datatables.net-bs5\dataTables.bootstrap5.js" />
    <Content Include="asset\lib\datatables.net-bs5\dataTables.bootstrap5.min.css" />
    <Content Include="asset\lib\datatables.net-bs5\dataTables.bootstrap5.min.js" />
    <Content Include="asset\lib\datatables.net\dataTables.js" />
    <Content Include="asset\lib\datatables.net\dataTables.min.js" />
    <Content Include="asset\lib\dropzone\basic.css" />
    <Content Include="asset\lib\dropzone\basic.min.css" />
    <Content Include="asset\lib\dropzone\dropzone-amd-module.js" />
    <Content Include="asset\lib\dropzone\dropzone-amd-module.min.js" />
    <Content Include="asset\lib\dropzone\dropzone.css" />
    <Content Include="asset\lib\dropzone\dropzone.js" />
    <Content Include="asset\lib\dropzone\dropzone.min.css" />
    <Content Include="asset\lib\dropzone\dropzone.min.js" />
    <Content Include="asset\lib\dropzone\min\basic.min.css" />
    <Content Include="asset\lib\dropzone\min\dropzone-amd-module.min.js" />
    <Content Include="asset\lib\dropzone\min\dropzone.min.css" />
    <Content Include="asset\lib\dropzone\min\dropzone.min.js" />
    <Content Include="asset\lib\font-awesome\css\all.css" />
    <Content Include="asset\lib\font-awesome\css\all.min.css" />
    <Content Include="asset\lib\font-awesome\css\brands.css" />
    <Content Include="asset\lib\font-awesome\css\brands.min.css" />
    <Content Include="asset\lib\font-awesome\css\fontawesome.css" />
    <Content Include="asset\lib\font-awesome\css\fontawesome.min.css" />
    <Content Include="asset\lib\font-awesome\css\regular.css" />
    <Content Include="asset\lib\font-awesome\css\regular.min.css" />
    <Content Include="asset\lib\font-awesome\css\solid.css" />
    <Content Include="asset\lib\font-awesome\css\solid.min.css" />
    <Content Include="asset\lib\font-awesome\css\svg-with-js.css" />
    <Content Include="asset\lib\font-awesome\css\svg-with-js.min.css" />
    <Content Include="asset\lib\font-awesome\css\v4-font-face.css" />
    <Content Include="asset\lib\font-awesome\css\v4-font-face.min.css" />
    <Content Include="asset\lib\font-awesome\css\v4-shims.css" />
    <Content Include="asset\lib\font-awesome\css\v4-shims.min.css" />
    <Content Include="asset\lib\font-awesome\css\v5-font-face.css" />
    <Content Include="asset\lib\font-awesome\css\v5-font-face.min.css" />
    <Content Include="asset\lib\font-awesome\js\all.js" />
    <Content Include="asset\lib\font-awesome\js\all.min.js" />
    <Content Include="asset\lib\font-awesome\js\brands.js" />
    <Content Include="asset\lib\font-awesome\js\brands.min.js" />
    <Content Include="asset\lib\font-awesome\js\conflict-detection.js" />
    <Content Include="asset\lib\font-awesome\js\conflict-detection.min.js" />
    <Content Include="asset\lib\font-awesome\js\fontawesome.js" />
    <Content Include="asset\lib\font-awesome\js\fontawesome.min.js" />
    <Content Include="asset\lib\font-awesome\js\regular.js" />
    <Content Include="asset\lib\font-awesome\js\regular.min.js" />
    <Content Include="asset\lib\font-awesome\js\solid.js" />
    <Content Include="asset\lib\font-awesome\js\solid.min.js" />
    <Content Include="asset\lib\font-awesome\js\v4-shims.js" />
    <Content Include="asset\lib\font-awesome\js\v4-shims.min.js" />
    <Content Include="asset\lib\font-awesome\sprites\brands.svg" />
    <Content Include="asset\lib\font-awesome\sprites\regular.svg" />
    <Content Include="asset\lib\font-awesome\sprites\solid.svg" />
    <Content Include="asset\lib\jQuery-blockUI\jquery.blockUI.js" />
    <Content Include="asset\lib\jQuery-blockUI\jquery.blockUI.min.js" />
    <Content Include="asset\lib\jquery-cookie\jquery.cookie.js" />
    <Content Include="asset\lib\jquery-cookie\jquery.cookie.min.js" />
    <Content Include="asset\lib\jquery-datetimepicker\jquery.datetimepicker.css" />
    <Content Include="asset\lib\jquery-datetimepicker\jquery.datetimepicker.full.js" />
    <Content Include="asset\lib\jquery-datetimepicker\jquery.datetimepicker.full.min.js" />
    <Content Include="asset\lib\jquery-datetimepicker\jquery.datetimepicker.js" />
    <Content Include="asset\lib\jquery-datetimepicker\jquery.datetimepicker.min.css" />
    <Content Include="asset\lib\jquery-datetimepicker\jquery.datetimepicker.min.js" />
    <Content Include="asset\lib\jQuery-slimScroll\jquery.slimscroll.js" />
    <Content Include="asset\lib\jQuery-slimScroll\jquery.slimscroll.min.js" />
    <Content Include="asset\lib\jquery.bsmodal\jquery.bsmodal.js" />
    <Content Include="asset\lib\jquery.bsmodal\jquery.bsmodal.min.js" />
    <Content Include="asset\lib\jquery\jquery.js" />
    <Content Include="asset\lib\jquery\jquery.min.js" />
    <Content Include="asset\lib\jquery\jquery.slim.js" />
    <Content Include="asset\lib\jquery\jquery.slim.min.js" />
    <Content Include="asset\lib\moment.js\locale\af.js" />
    <Content Include="asset\lib\moment.js\locale\af.min.js" />
    <Content Include="asset\lib\moment.js\locale\ar-dz.js" />
    <Content Include="asset\lib\moment.js\locale\ar-dz.min.js" />
    <Content Include="asset\lib\moment.js\locale\ar-kw.js" />
    <Content Include="asset\lib\moment.js\locale\ar-kw.min.js" />
    <Content Include="asset\lib\moment.js\locale\ar-ly.js" />
    <Content Include="asset\lib\moment.js\locale\ar-ly.min.js" />
    <Content Include="asset\lib\moment.js\locale\ar-ma.js" />
    <Content Include="asset\lib\moment.js\locale\ar-ma.min.js" />
    <Content Include="asset\lib\moment.js\locale\ar-ps.js" />
    <Content Include="asset\lib\moment.js\locale\ar-ps.min.js" />
    <Content Include="asset\lib\moment.js\locale\ar-sa.js" />
    <Content Include="asset\lib\moment.js\locale\ar-sa.min.js" />
    <Content Include="asset\lib\moment.js\locale\ar-tn.js" />
    <Content Include="asset\lib\moment.js\locale\ar-tn.min.js" />
    <Content Include="asset\lib\moment.js\locale\ar.js" />
    <Content Include="asset\lib\moment.js\locale\ar.min.js" />
    <Content Include="asset\lib\moment.js\locale\az.js" />
    <Content Include="asset\lib\moment.js\locale\az.min.js" />
    <Content Include="asset\lib\moment.js\locale\be.js" />
    <Content Include="asset\lib\moment.js\locale\be.min.js" />
    <Content Include="asset\lib\moment.js\locale\bg.js" />
    <Content Include="asset\lib\moment.js\locale\bg.min.js" />
    <Content Include="asset\lib\moment.js\locale\bm.js" />
    <Content Include="asset\lib\moment.js\locale\bm.min.js" />
    <Content Include="asset\lib\moment.js\locale\bn-bd.js" />
    <Content Include="asset\lib\moment.js\locale\bn-bd.min.js" />
    <Content Include="asset\lib\moment.js\locale\bn.js" />
    <Content Include="asset\lib\moment.js\locale\bn.min.js" />
    <Content Include="asset\lib\moment.js\locale\bo.js" />
    <Content Include="asset\lib\moment.js\locale\bo.min.js" />
    <Content Include="asset\lib\moment.js\locale\br.js" />
    <Content Include="asset\lib\moment.js\locale\br.min.js" />
    <Content Include="asset\lib\moment.js\locale\bs.js" />
    <Content Include="asset\lib\moment.js\locale\bs.min.js" />
    <Content Include="asset\lib\moment.js\locale\ca.js" />
    <Content Include="asset\lib\moment.js\locale\ca.min.js" />
    <Content Include="asset\lib\moment.js\locale\cs.js" />
    <Content Include="asset\lib\moment.js\locale\cs.min.js" />
    <Content Include="asset\lib\moment.js\locale\cv.js" />
    <Content Include="asset\lib\moment.js\locale\cv.min.js" />
    <Content Include="asset\lib\moment.js\locale\cy.js" />
    <Content Include="asset\lib\moment.js\locale\cy.min.js" />
    <Content Include="asset\lib\moment.js\locale\da.js" />
    <Content Include="asset\lib\moment.js\locale\da.min.js" />
    <Content Include="asset\lib\moment.js\locale\de-at.js" />
    <Content Include="asset\lib\moment.js\locale\de-at.min.js" />
    <Content Include="asset\lib\moment.js\locale\de-ch.js" />
    <Content Include="asset\lib\moment.js\locale\de-ch.min.js" />
    <Content Include="asset\lib\moment.js\locale\de.js" />
    <Content Include="asset\lib\moment.js\locale\de.min.js" />
    <Content Include="asset\lib\moment.js\locale\dv.js" />
    <Content Include="asset\lib\moment.js\locale\dv.min.js" />
    <Content Include="asset\lib\moment.js\locale\el.js" />
    <Content Include="asset\lib\moment.js\locale\el.min.js" />
    <Content Include="asset\lib\moment.js\locale\en-au.js" />
    <Content Include="asset\lib\moment.js\locale\en-au.min.js" />
    <Content Include="asset\lib\moment.js\locale\en-ca.js" />
    <Content Include="asset\lib\moment.js\locale\en-ca.min.js" />
    <Content Include="asset\lib\moment.js\locale\en-gb.js" />
    <Content Include="asset\lib\moment.js\locale\en-gb.min.js" />
    <Content Include="asset\lib\moment.js\locale\en-ie.js" />
    <Content Include="asset\lib\moment.js\locale\en-ie.min.js" />
    <Content Include="asset\lib\moment.js\locale\en-il.js" />
    <Content Include="asset\lib\moment.js\locale\en-il.min.js" />
    <Content Include="asset\lib\moment.js\locale\en-in.js" />
    <Content Include="asset\lib\moment.js\locale\en-in.min.js" />
    <Content Include="asset\lib\moment.js\locale\en-nz.js" />
    <Content Include="asset\lib\moment.js\locale\en-nz.min.js" />
    <Content Include="asset\lib\moment.js\locale\en-sg.js" />
    <Content Include="asset\lib\moment.js\locale\en-sg.min.js" />
    <Content Include="asset\lib\moment.js\locale\eo.js" />
    <Content Include="asset\lib\moment.js\locale\eo.min.js" />
    <Content Include="asset\lib\moment.js\locale\es-do.js" />
    <Content Include="asset\lib\moment.js\locale\es-do.min.js" />
    <Content Include="asset\lib\moment.js\locale\es-mx.js" />
    <Content Include="asset\lib\moment.js\locale\es-mx.min.js" />
    <Content Include="asset\lib\moment.js\locale\es-us.js" />
    <Content Include="asset\lib\moment.js\locale\es-us.min.js" />
    <Content Include="asset\lib\moment.js\locale\es.js" />
    <Content Include="asset\lib\moment.js\locale\es.min.js" />
    <Content Include="asset\lib\moment.js\locale\et.js" />
    <Content Include="asset\lib\moment.js\locale\et.min.js" />
    <Content Include="asset\lib\moment.js\locale\eu.js" />
    <Content Include="asset\lib\moment.js\locale\eu.min.js" />
    <Content Include="asset\lib\moment.js\locale\fa.js" />
    <Content Include="asset\lib\moment.js\locale\fa.min.js" />
    <Content Include="asset\lib\moment.js\locale\fi.js" />
    <Content Include="asset\lib\moment.js\locale\fi.min.js" />
    <Content Include="asset\lib\moment.js\locale\fil.js" />
    <Content Include="asset\lib\moment.js\locale\fil.min.js" />
    <Content Include="asset\lib\moment.js\locale\fo.js" />
    <Content Include="asset\lib\moment.js\locale\fo.min.js" />
    <Content Include="asset\lib\moment.js\locale\fr-ca.js" />
    <Content Include="asset\lib\moment.js\locale\fr-ca.min.js" />
    <Content Include="asset\lib\moment.js\locale\fr-ch.js" />
    <Content Include="asset\lib\moment.js\locale\fr-ch.min.js" />
    <Content Include="asset\lib\moment.js\locale\fr.js" />
    <Content Include="asset\lib\moment.js\locale\fr.min.js" />
    <Content Include="asset\lib\moment.js\locale\fy.js" />
    <Content Include="asset\lib\moment.js\locale\fy.min.js" />
    <Content Include="asset\lib\moment.js\locale\ga.js" />
    <Content Include="asset\lib\moment.js\locale\ga.min.js" />
    <Content Include="asset\lib\moment.js\locale\gd.js" />
    <Content Include="asset\lib\moment.js\locale\gd.min.js" />
    <Content Include="asset\lib\moment.js\locale\gl.js" />
    <Content Include="asset\lib\moment.js\locale\gl.min.js" />
    <Content Include="asset\lib\moment.js\locale\gom-deva.js" />
    <Content Include="asset\lib\moment.js\locale\gom-deva.min.js" />
    <Content Include="asset\lib\moment.js\locale\gom-latn.js" />
    <Content Include="asset\lib\moment.js\locale\gom-latn.min.js" />
    <Content Include="asset\lib\moment.js\locale\gu.js" />
    <Content Include="asset\lib\moment.js\locale\gu.min.js" />
    <Content Include="asset\lib\moment.js\locale\he.js" />
    <Content Include="asset\lib\moment.js\locale\he.min.js" />
    <Content Include="asset\lib\moment.js\locale\hi.js" />
    <Content Include="asset\lib\moment.js\locale\hi.min.js" />
    <Content Include="asset\lib\moment.js\locale\hr.js" />
    <Content Include="asset\lib\moment.js\locale\hr.min.js" />
    <Content Include="asset\lib\moment.js\locale\hu.js" />
    <Content Include="asset\lib\moment.js\locale\hu.min.js" />
    <Content Include="asset\lib\moment.js\locale\hy-am.js" />
    <Content Include="asset\lib\moment.js\locale\hy-am.min.js" />
    <Content Include="asset\lib\moment.js\locale\id.js" />
    <Content Include="asset\lib\moment.js\locale\id.min.js" />
    <Content Include="asset\lib\moment.js\locale\is.js" />
    <Content Include="asset\lib\moment.js\locale\is.min.js" />
    <Content Include="asset\lib\moment.js\locale\it-ch.js" />
    <Content Include="asset\lib\moment.js\locale\it-ch.min.js" />
    <Content Include="asset\lib\moment.js\locale\it.js" />
    <Content Include="asset\lib\moment.js\locale\it.min.js" />
    <Content Include="asset\lib\moment.js\locale\ja.js" />
    <Content Include="asset\lib\moment.js\locale\ja.min.js" />
    <Content Include="asset\lib\moment.js\locale\jv.js" />
    <Content Include="asset\lib\moment.js\locale\jv.min.js" />
    <Content Include="asset\lib\moment.js\locale\ka.js" />
    <Content Include="asset\lib\moment.js\locale\ka.min.js" />
    <Content Include="asset\lib\moment.js\locale\kk.js" />
    <Content Include="asset\lib\moment.js\locale\kk.min.js" />
    <Content Include="asset\lib\moment.js\locale\km.js" />
    <Content Include="asset\lib\moment.js\locale\km.min.js" />
    <Content Include="asset\lib\moment.js\locale\kn.js" />
    <Content Include="asset\lib\moment.js\locale\kn.min.js" />
    <Content Include="asset\lib\moment.js\locale\ko.js" />
    <Content Include="asset\lib\moment.js\locale\ko.min.js" />
    <Content Include="asset\lib\moment.js\locale\ku-kmr.js" />
    <Content Include="asset\lib\moment.js\locale\ku-kmr.min.js" />
    <Content Include="asset\lib\moment.js\locale\ku.js" />
    <Content Include="asset\lib\moment.js\locale\ku.min.js" />
    <Content Include="asset\lib\moment.js\locale\ky.js" />
    <Content Include="asset\lib\moment.js\locale\ky.min.js" />
    <Content Include="asset\lib\moment.js\locale\lb.js" />
    <Content Include="asset\lib\moment.js\locale\lb.min.js" />
    <Content Include="asset\lib\moment.js\locale\lo.js" />
    <Content Include="asset\lib\moment.js\locale\lo.min.js" />
    <Content Include="asset\lib\moment.js\locale\lt.js" />
    <Content Include="asset\lib\moment.js\locale\lt.min.js" />
    <Content Include="asset\lib\moment.js\locale\lv.js" />
    <Content Include="asset\lib\moment.js\locale\lv.min.js" />
    <Content Include="asset\lib\moment.js\locale\me.js" />
    <Content Include="asset\lib\moment.js\locale\me.min.js" />
    <Content Include="asset\lib\moment.js\locale\mi.js" />
    <Content Include="asset\lib\moment.js\locale\mi.min.js" />
    <Content Include="asset\lib\moment.js\locale\mk.js" />
    <Content Include="asset\lib\moment.js\locale\mk.min.js" />
    <Content Include="asset\lib\moment.js\locale\ml.js" />
    <Content Include="asset\lib\moment.js\locale\ml.min.js" />
    <Content Include="asset\lib\moment.js\locale\mn.js" />
    <Content Include="asset\lib\moment.js\locale\mn.min.js" />
    <Content Include="asset\lib\moment.js\locale\mr.js" />
    <Content Include="asset\lib\moment.js\locale\mr.min.js" />
    <Content Include="asset\lib\moment.js\locale\ms-my.js" />
    <Content Include="asset\lib\moment.js\locale\ms-my.min.js" />
    <Content Include="asset\lib\moment.js\locale\ms.js" />
    <Content Include="asset\lib\moment.js\locale\ms.min.js" />
    <Content Include="asset\lib\moment.js\locale\mt.js" />
    <Content Include="asset\lib\moment.js\locale\mt.min.js" />
    <Content Include="asset\lib\moment.js\locale\my.js" />
    <Content Include="asset\lib\moment.js\locale\my.min.js" />
    <Content Include="asset\lib\moment.js\locale\nb.js" />
    <Content Include="asset\lib\moment.js\locale\nb.min.js" />
    <Content Include="asset\lib\moment.js\locale\ne.js" />
    <Content Include="asset\lib\moment.js\locale\ne.min.js" />
    <Content Include="asset\lib\moment.js\locale\nl-be.js" />
    <Content Include="asset\lib\moment.js\locale\nl-be.min.js" />
    <Content Include="asset\lib\moment.js\locale\nl.js" />
    <Content Include="asset\lib\moment.js\locale\nl.min.js" />
    <Content Include="asset\lib\moment.js\locale\nn.js" />
    <Content Include="asset\lib\moment.js\locale\nn.min.js" />
    <Content Include="asset\lib\moment.js\locale\oc-lnc.js" />
    <Content Include="asset\lib\moment.js\locale\oc-lnc.min.js" />
    <Content Include="asset\lib\moment.js\locale\pa-in.js" />
    <Content Include="asset\lib\moment.js\locale\pa-in.min.js" />
    <Content Include="asset\lib\moment.js\locale\pl.js" />
    <Content Include="asset\lib\moment.js\locale\pl.min.js" />
    <Content Include="asset\lib\moment.js\locale\pt-br.js" />
    <Content Include="asset\lib\moment.js\locale\pt-br.min.js" />
    <Content Include="asset\lib\moment.js\locale\pt.js" />
    <Content Include="asset\lib\moment.js\locale\pt.min.js" />
    <Content Include="asset\lib\moment.js\locale\ro.js" />
    <Content Include="asset\lib\moment.js\locale\ro.min.js" />
    <Content Include="asset\lib\moment.js\locale\ru.js" />
    <Content Include="asset\lib\moment.js\locale\ru.min.js" />
    <Content Include="asset\lib\moment.js\locale\sd.js" />
    <Content Include="asset\lib\moment.js\locale\sd.min.js" />
    <Content Include="asset\lib\moment.js\locale\se.js" />
    <Content Include="asset\lib\moment.js\locale\se.min.js" />
    <Content Include="asset\lib\moment.js\locale\si.js" />
    <Content Include="asset\lib\moment.js\locale\si.min.js" />
    <Content Include="asset\lib\moment.js\locale\sk.js" />
    <Content Include="asset\lib\moment.js\locale\sk.min.js" />
    <Content Include="asset\lib\moment.js\locale\sl.js" />
    <Content Include="asset\lib\moment.js\locale\sl.min.js" />
    <Content Include="asset\lib\moment.js\locale\sq.js" />
    <Content Include="asset\lib\moment.js\locale\sq.min.js" />
    <Content Include="asset\lib\moment.js\locale\sr-cyrl.js" />
    <Content Include="asset\lib\moment.js\locale\sr-cyrl.min.js" />
    <Content Include="asset\lib\moment.js\locale\sr.js" />
    <Content Include="asset\lib\moment.js\locale\sr.min.js" />
    <Content Include="asset\lib\moment.js\locale\ss.js" />
    <Content Include="asset\lib\moment.js\locale\ss.min.js" />
    <Content Include="asset\lib\moment.js\locale\sv.js" />
    <Content Include="asset\lib\moment.js\locale\sv.min.js" />
    <Content Include="asset\lib\moment.js\locale\sw.js" />
    <Content Include="asset\lib\moment.js\locale\sw.min.js" />
    <Content Include="asset\lib\moment.js\locale\ta.js" />
    <Content Include="asset\lib\moment.js\locale\ta.min.js" />
    <Content Include="asset\lib\moment.js\locale\te.js" />
    <Content Include="asset\lib\moment.js\locale\te.min.js" />
    <Content Include="asset\lib\moment.js\locale\tet.js" />
    <Content Include="asset\lib\moment.js\locale\tet.min.js" />
    <Content Include="asset\lib\moment.js\locale\tg.js" />
    <Content Include="asset\lib\moment.js\locale\tg.min.js" />
    <Content Include="asset\lib\moment.js\locale\th.js" />
    <Content Include="asset\lib\moment.js\locale\th.min.js" />
    <Content Include="asset\lib\moment.js\locale\tk.js" />
    <Content Include="asset\lib\moment.js\locale\tk.min.js" />
    <Content Include="asset\lib\moment.js\locale\tl-ph.js" />
    <Content Include="asset\lib\moment.js\locale\tl-ph.min.js" />
    <Content Include="asset\lib\moment.js\locale\tlh.js" />
    <Content Include="asset\lib\moment.js\locale\tlh.min.js" />
    <Content Include="asset\lib\moment.js\locale\tr.js" />
    <Content Include="asset\lib\moment.js\locale\tr.min.js" />
    <Content Include="asset\lib\moment.js\locale\tzl.js" />
    <Content Include="asset\lib\moment.js\locale\tzl.min.js" />
    <Content Include="asset\lib\moment.js\locale\tzm-latn.js" />
    <Content Include="asset\lib\moment.js\locale\tzm-latn.min.js" />
    <Content Include="asset\lib\moment.js\locale\tzm.js" />
    <Content Include="asset\lib\moment.js\locale\tzm.min.js" />
    <Content Include="asset\lib\moment.js\locale\ug-cn.js" />
    <Content Include="asset\lib\moment.js\locale\ug-cn.min.js" />
    <Content Include="asset\lib\moment.js\locale\uk.js" />
    <Content Include="asset\lib\moment.js\locale\uk.min.js" />
    <Content Include="asset\lib\moment.js\locale\ur.js" />
    <Content Include="asset\lib\moment.js\locale\ur.min.js" />
    <Content Include="asset\lib\moment.js\locale\uz-latn.js" />
    <Content Include="asset\lib\moment.js\locale\uz-latn.min.js" />
    <Content Include="asset\lib\moment.js\locale\uz.js" />
    <Content Include="asset\lib\moment.js\locale\uz.min.js" />
    <Content Include="asset\lib\moment.js\locale\vi.js" />
    <Content Include="asset\lib\moment.js\locale\vi.min.js" />
    <Content Include="asset\lib\moment.js\locale\x-pseudo.js" />
    <Content Include="asset\lib\moment.js\locale\x-pseudo.min.js" />
    <Content Include="asset\lib\moment.js\locale\yo.js" />
    <Content Include="asset\lib\moment.js\locale\yo.min.js" />
    <Content Include="asset\lib\moment.js\locale\zh-cn.js" />
    <Content Include="asset\lib\moment.js\locale\zh-cn.min.js" />
    <Content Include="asset\lib\moment.js\locale\zh-hk.js" />
    <Content Include="asset\lib\moment.js\locale\zh-hk.min.js" />
    <Content Include="asset\lib\moment.js\locale\zh-mo.js" />
    <Content Include="asset\lib\moment.js\locale\zh-mo.min.js" />
    <Content Include="asset\lib\moment.js\locale\zh-tw.js" />
    <Content Include="asset\lib\moment.js\locale\zh-tw.min.js" />
    <Content Include="asset\lib\moment.js\moment-with-locales.js" />
    <Content Include="asset\lib\moment.js\moment-with-locales.min.js" />
    <Content Include="asset\lib\moment.js\moment.js" />
    <Content Include="asset\lib\moment.js\moment.min.js" />
    <Content Include="asset\lib\pnotify\brightTheme.min.css" />
    <Content Include="asset\lib\pnotify\pNotify.min.js" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\select2-bootstrap-5-theme.css" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\select2-bootstrap-5-theme.min.css" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\select2-bootstrap-5-theme.rtl.css" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\select2-bootstrap-5-theme.rtl.min.css" />
    <Content Include="asset\lib\select2\css\select2.css" />
    <Content Include="asset\lib\select2\css\select2.min.css" />
    <Content Include="asset\lib\select2\js\i18n\af.js" />
    <Content Include="asset\lib\select2\js\i18n\af.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ar.js" />
    <Content Include="asset\lib\select2\js\i18n\ar.min.js" />
    <Content Include="asset\lib\select2\js\i18n\az.js" />
    <Content Include="asset\lib\select2\js\i18n\az.min.js" />
    <Content Include="asset\lib\select2\js\i18n\bg.js" />
    <Content Include="asset\lib\select2\js\i18n\bg.min.js" />
    <Content Include="asset\lib\select2\js\i18n\bn.js" />
    <Content Include="asset\lib\select2\js\i18n\bn.min.js" />
    <Content Include="asset\lib\select2\js\i18n\bs.js" />
    <Content Include="asset\lib\select2\js\i18n\bs.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ca.js" />
    <Content Include="asset\lib\select2\js\i18n\ca.min.js" />
    <Content Include="asset\lib\select2\js\i18n\cs.js" />
    <Content Include="asset\lib\select2\js\i18n\cs.min.js" />
    <Content Include="asset\lib\select2\js\i18n\da.js" />
    <Content Include="asset\lib\select2\js\i18n\da.min.js" />
    <Content Include="asset\lib\select2\js\i18n\de.js" />
    <Content Include="asset\lib\select2\js\i18n\de.min.js" />
    <Content Include="asset\lib\select2\js\i18n\dsb.js" />
    <Content Include="asset\lib\select2\js\i18n\dsb.min.js" />
    <Content Include="asset\lib\select2\js\i18n\el.js" />
    <Content Include="asset\lib\select2\js\i18n\el.min.js" />
    <Content Include="asset\lib\select2\js\i18n\en.js" />
    <Content Include="asset\lib\select2\js\i18n\en.min.js" />
    <Content Include="asset\lib\select2\js\i18n\es.js" />
    <Content Include="asset\lib\select2\js\i18n\es.min.js" />
    <Content Include="asset\lib\select2\js\i18n\et.js" />
    <Content Include="asset\lib\select2\js\i18n\et.min.js" />
    <Content Include="asset\lib\select2\js\i18n\eu.js" />
    <Content Include="asset\lib\select2\js\i18n\eu.min.js" />
    <Content Include="asset\lib\select2\js\i18n\fa.js" />
    <Content Include="asset\lib\select2\js\i18n\fa.min.js" />
    <Content Include="asset\lib\select2\js\i18n\fi.js" />
    <Content Include="asset\lib\select2\js\i18n\fi.min.js" />
    <Content Include="asset\lib\select2\js\i18n\fr.js" />
    <Content Include="asset\lib\select2\js\i18n\fr.min.js" />
    <Content Include="asset\lib\select2\js\i18n\gl.js" />
    <Content Include="asset\lib\select2\js\i18n\gl.min.js" />
    <Content Include="asset\lib\select2\js\i18n\he.js" />
    <Content Include="asset\lib\select2\js\i18n\he.min.js" />
    <Content Include="asset\lib\select2\js\i18n\hi.js" />
    <Content Include="asset\lib\select2\js\i18n\hi.min.js" />
    <Content Include="asset\lib\select2\js\i18n\hr.js" />
    <Content Include="asset\lib\select2\js\i18n\hr.min.js" />
    <Content Include="asset\lib\select2\js\i18n\hsb.js" />
    <Content Include="asset\lib\select2\js\i18n\hsb.min.js" />
    <Content Include="asset\lib\select2\js\i18n\hu.js" />
    <Content Include="asset\lib\select2\js\i18n\hu.min.js" />
    <Content Include="asset\lib\select2\js\i18n\hy.js" />
    <Content Include="asset\lib\select2\js\i18n\hy.min.js" />
    <Content Include="asset\lib\select2\js\i18n\id.js" />
    <Content Include="asset\lib\select2\js\i18n\id.min.js" />
    <Content Include="asset\lib\select2\js\i18n\is.js" />
    <Content Include="asset\lib\select2\js\i18n\is.min.js" />
    <Content Include="asset\lib\select2\js\i18n\it.js" />
    <Content Include="asset\lib\select2\js\i18n\it.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ja.js" />
    <Content Include="asset\lib\select2\js\i18n\ja.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ka.js" />
    <Content Include="asset\lib\select2\js\i18n\ka.min.js" />
    <Content Include="asset\lib\select2\js\i18n\km.js" />
    <Content Include="asset\lib\select2\js\i18n\km.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ko.js" />
    <Content Include="asset\lib\select2\js\i18n\ko.min.js" />
    <Content Include="asset\lib\select2\js\i18n\lt.js" />
    <Content Include="asset\lib\select2\js\i18n\lt.min.js" />
    <Content Include="asset\lib\select2\js\i18n\lv.js" />
    <Content Include="asset\lib\select2\js\i18n\lv.min.js" />
    <Content Include="asset\lib\select2\js\i18n\mk.js" />
    <Content Include="asset\lib\select2\js\i18n\mk.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ms.js" />
    <Content Include="asset\lib\select2\js\i18n\ms.min.js" />
    <Content Include="asset\lib\select2\js\i18n\nb.js" />
    <Content Include="asset\lib\select2\js\i18n\nb.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ne.js" />
    <Content Include="asset\lib\select2\js\i18n\ne.min.js" />
    <Content Include="asset\lib\select2\js\i18n\nl.js" />
    <Content Include="asset\lib\select2\js\i18n\nl.min.js" />
    <Content Include="asset\lib\select2\js\i18n\pl.js" />
    <Content Include="asset\lib\select2\js\i18n\pl.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ps.js" />
    <Content Include="asset\lib\select2\js\i18n\ps.min.js" />
    <Content Include="asset\lib\select2\js\i18n\pt-BR.js" />
    <Content Include="asset\lib\select2\js\i18n\pt-BR.min.js" />
    <Content Include="asset\lib\select2\js\i18n\pt.js" />
    <Content Include="asset\lib\select2\js\i18n\pt.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ro.js" />
    <Content Include="asset\lib\select2\js\i18n\ro.min.js" />
    <Content Include="asset\lib\select2\js\i18n\ru.js" />
    <Content Include="asset\lib\select2\js\i18n\ru.min.js" />
    <Content Include="asset\lib\select2\js\i18n\sk.js" />
    <Content Include="asset\lib\select2\js\i18n\sk.min.js" />
    <Content Include="asset\lib\select2\js\i18n\sl.js" />
    <Content Include="asset\lib\select2\js\i18n\sl.min.js" />
    <Content Include="asset\lib\select2\js\i18n\sq.js" />
    <Content Include="asset\lib\select2\js\i18n\sq.min.js" />
    <Content Include="asset\lib\select2\js\i18n\sr-Cyrl.js" />
    <Content Include="asset\lib\select2\js\i18n\sr-Cyrl.min.js" />
    <Content Include="asset\lib\select2\js\i18n\sr.js" />
    <Content Include="asset\lib\select2\js\i18n\sr.min.js" />
    <Content Include="asset\lib\select2\js\i18n\sv.js" />
    <Content Include="asset\lib\select2\js\i18n\sv.min.js" />
    <Content Include="asset\lib\select2\js\i18n\th.js" />
    <Content Include="asset\lib\select2\js\i18n\th.min.js" />
    <Content Include="asset\lib\select2\js\i18n\tk.js" />
    <Content Include="asset\lib\select2\js\i18n\tk.min.js" />
    <Content Include="asset\lib\select2\js\i18n\tr.js" />
    <Content Include="asset\lib\select2\js\i18n\tr.min.js" />
    <Content Include="asset\lib\select2\js\i18n\uk.js" />
    <Content Include="asset\lib\select2\js\i18n\uk.min.js" />
    <Content Include="asset\lib\select2\js\i18n\vi.js" />
    <Content Include="asset\lib\select2\js\i18n\vi.min.js" />
    <Content Include="asset\lib\select2\js\i18n\zh-CN.js" />
    <Content Include="asset\lib\select2\js\i18n\zh-CN.min.js" />
    <Content Include="asset\lib\select2\js\i18n\zh-TW.js" />
    <Content Include="asset\lib\select2\js\i18n\zh-TW.min.js" />
    <Content Include="asset\lib\select2\js\select2.full.js" />
    <Content Include="asset\lib\select2\js\select2.full.min.js" />
    <Content Include="asset\lib\select2\js\select2.js" />
    <Content Include="asset\lib\select2\js\select2.min.js" />
    <Content Include="asset\lib\slim-scroll\slimscroll.js" />
    <Content Include="asset\lib\slim-scroll\slimscroll.min.js" />
    <Content Include="asset\lib\sweetalert2\sweetalert2.all.js" />
    <Content Include="asset\lib\sweetalert2\sweetalert2.all.min.js" />
    <Content Include="asset\lib\sweetalert2\sweetalert2.css" />
    <Content Include="asset\lib\sweetalert2\sweetalert2.js" />
    <Content Include="asset\lib\sweetalert2\sweetalert2.min.css" />
    <Content Include="asset\lib\sweetalert2\sweetalert2.min.js" />
    <Content Include="asset\lib\toastr.js\toastr.css" />
    <Content Include="asset\lib\toastr.js\toastr.min.css" />
    <Content Include="asset\lib\toastr.js\toastr.min.js" />
    <Content Include="bin\Common.NET.dll" />
    <Content Include="bin\DocumentFormat.OpenXml.dll" />
    <Content Include="bin\EPPlus.dll" />
    <Content Include="bin\Google.Apis.Auth.dll" />
    <Content Include="bin\Google.Apis.Auth.PlatformServices.dll" />
    <Content Include="bin\Google.Apis.Calendar.v3.dll" />
    <Content Include="bin\Google.Apis.Core.dll" />
    <Content Include="bin\Google.Apis.dll" />
    <Content Include="bin\Google.Apis.PlatformServices.dll" />
    <Content Include="bin\ICSharpCode.SharpZipLib.dll" />
    <Content Include="bin\Interop.WMPLib.dll" />
    <Content Include="bin\MediaDecoder.mp3.NET.dll" />
    <Content Include="bin\MediaDecoder.NET.dll" />
    <Content Include="bin\MediaExport.NET.dll" />
    <Content Include="bin\MediaParser.NET.dll" />
    <Content Include="bin\Microsoft.AspNet.SignalR.Client.dll" />
    <Content Include="bin\Microsoft.AspNet.SignalR.Core.dll" />
    <Content Include="bin\Microsoft.AspNet.SignalR.Owin.dll" />
    <Content Include="bin\Microsoft.AspNet.SignalR.SystemWeb.dll" />
    <Content Include="bin\Microsoft.Office.Interop.Word.dll" />
    <Content Include="bin\Microsoft.Owin.Cors.dll" />
    <Content Include="bin\Microsoft.Owin.dll" />
    <Content Include="bin\Microsoft.Owin.Host.HttpListener.dll" />
    <Content Include="bin\Microsoft.Owin.Host.SystemWeb.dll" />
    <Content Include="bin\Microsoft.Owin.Hosting.dll" />
    <Content Include="bin\Microsoft.Owin.Security.dll" />
    <Content Include="bin\Microsoft.Web.Infrastructure.dll" />
    <Content Include="bin\Newtonsoft.Json.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="bin\Owin.dll" />
    <Content Include="bin\PLUSManaged.dll" />
    <Content Include="bin\PLUSManaged.XmlSerializers.dll" />
    <Content Include="bin\RevCord.BusinessLogic.dll" />
    <Content Include="bin\RevCord.DataAccess.dll" />
    <Content Include="bin\RevCord.DataContracts.dll" />
    <Content Include="bin\RevCord.ServiceContracts.dll" />
    <Content Include="bin\RevCord.ServiceImplementation.dll" />
    <Content Include="bin\RevCord.Util.dll" />
    <Content Include="bin\System.Net.Http.Formatting.dll" />
    <Content Include="bin\System.Web.Cors.dll" />
    <Content Include="bin\System.Web.Http.dll" />
    <Content Include="bin\System.Web.Http.Owin.dll" />
    <Content Include="ClientBin\RevcordFilePlayer.xap" />
    <Content Include="ClientBin\RevcordLivePlayer.xap" />
    <Content Include="ClientBin\RevcordVideoPlayer.xap" />
    <Content Include="CustomerDatabase\IQ3AssetReportData.aspx" />
    <Content Include="CustomerDatabase\ViewIQ3Asset.aspx" />
    <Content Include="CustomerDatabase\Manage.aspx" />
    <Content Include="Dashboard\Default.aspx" />
    <Content Include="Default.aspx" />
    <Content Include="ErrorPage.aspx" />
    <Content Include="Evaluation\DispatcherEvaluation.aspx" />
    <Content Include="Evaluation\Default.aspx" />
    <Content Include="Evaluation\EvaluateCalls.aspx" />
    <Content Include="Evaluation\Feedback.aspx" />
    <Content Include="Evaluation\Search.aspx" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\app.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\bootstrap-responsive.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\bootstrap.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\bootstrap.datatables.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\bootstrap\bootstrap-responsive.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\bootstrap\bootstrap.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\bootstrap\bootstrap2.x.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\custom.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\datatables.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\fixHeader.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\font-awesome\css\font-awesome.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\font-awesome\css\font-awesome.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\1.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\10.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\11.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\12.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\1_revview.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\2.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\3.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\4.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\5.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\6.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\7.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\8.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\9.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\AttachFiles.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\balance_left.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\balance_left_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\balance_right.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\balance_right_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\begin.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\begin_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\blue_bg.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\bm-icon.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\bookmark-icon-16.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\bookmark.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\bookmark_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\callTypes.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\end.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\end_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\fast_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\fast_forward.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\fast_forward_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\header_bg.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\iconDelete.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\InternalNotes.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\IQ3Measurement.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\loop.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\loop_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\marker.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\max_volume.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\max_volume_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\min_volume.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\min_volume_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\notes.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\pause.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\pause_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\play.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\alarm.mp3" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\attach2.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\balance_left.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\balance_left_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\balance_right.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\balance_right_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\begin - Original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\begin.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\begin_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\begin_Ex.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\bookmark -Original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\bookmark.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\bookmark_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\browse3.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\browse4.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\button - Copy.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\button-1.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\button.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\dp2.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\end -Original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\end.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\end_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\export.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\export_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\fast_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\fast_forward -Original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\fast_forward.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\fast_forward_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\loop.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\loop_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\marker.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\max_volume.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\max_volume_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\min_volume.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\min_volume_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\pause -Original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\pause-2.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\pause.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\pause_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\pause_Test.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\play -Original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\play.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\play2.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\play3.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\player_bg.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\play_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\play_new.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\rewind -Original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\rewind.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\rewind_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\Save_Button.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\settings-original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\settings.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\settings_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\slow.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\slow_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\stop -Original.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\stop.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\stop_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\time_indicator.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\player\video.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\play_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\revcord.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\rewind.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\rewind_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\sharenotes.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\slow.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\slow_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\stop.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\stop_active.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\time_indicator.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\images\_ui-bg_gloss-wave_50_009E0F_500x100.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\img\glyphicons-halflings-white.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\img\glyphicons-halflings.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\jquery-ui-1.9.0.custom.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\jquery-ui-1.9.2.custom.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\jquery-ui-custom-mmsDialog.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\jquery.dataTables.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\lightgallery\lg-fb-comment-box.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\lightgallery\lg-transitions.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\lightgallery\lightgallery.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\panes.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\animate.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\chosen.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\colpick.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\datatables\bootstrap.datatables.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\datatables\datatables.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\daterangepicker-bs3.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\dropzone.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\fullcalendar.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\jquery.handsontable.full.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\jquery.pnotify.default.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\jquery.pnotify.default.icons.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\jquery.timepicker.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\plugins\jscrollpane.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\responsive.dataTables.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\css\jplayer.blue.monday.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\css\jplayer.blue.monday.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\image\jplayer.blue.monday.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\image\jplayer.blue.monday.seeking.gif" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\image\jplayer.blue.monday.video.play.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\image\revcord.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\mustache\jplayer.blue.monday.audio.playlist.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\mustache\jplayer.blue.monday.audio.single.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\mustache\jplayer.blue.monday.audio.stream.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\mustache\jplayer.blue.monday.video.playlist.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\blue.monday\mustache\jplayer.blue.monday.video.single.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\css\jplayer.pink.flag.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\css\jplayer.pink.flag.min.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\image\jplayer.pink.flag.jpg" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\image\jplayer.pink.flag.seeking.gif" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\image\jplayer.pink.flag.video.play.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\mustache\jplayer.pink.flag.audio.playlist.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\mustache\jplayer.pink.flag.audio.single.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\mustache\jplayer.pink.flag.audio.stream.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\mustache\jplayer.pink.flag.video.playlist.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\skin\pink.flag\mustache\jplayer.pink.flag.video.single.html" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\style.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\tableStyle.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\tablet.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\widget.css" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\font-awesome\fontawesome-webfont.svg" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\lg.svg" />
    <Content Include="ExportPlaylist\ListviewSL\html\images\1_revview.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\images\error-loading-image.png" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\application.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\bootstrap.datatables.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\bootstrap\bootstrap.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\bootstrap\bootstrap.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\bootstrap\bootstrap2.x.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\bootstrap\popover.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\bootstrap\tooltip.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\CommonConstants.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\data.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\dataTables.colReorderWithResize.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\datatables.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\dataTables.responsive.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\defaultPage.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\FilePlaybackItem3.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\FilePlayer.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\html5fileplayer.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\html5fileplayer3.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery-1.8.2.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery-1.9.1.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery-ui-1.9.0.custom.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery-ui-custom-mmsDialog.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery-ui-sliderAccess.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery.blockUI.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery.dataTables.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery.jplayer.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery.jplayer.swf" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery.mobile-events.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\lightgallery\lightgallery.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\lightgallery\modules\lg-autoplay.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\lightgallery\modules\lg-fullscreen.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\lightgallery\modules\lg-hash.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\lightgallery\modules\lg-thumbnail.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\lightgallery\modules\lg-zoom.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\MediaExportProcessor.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\moment.min.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\pdf.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\pdfobject.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\playlist.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\Sequencer.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\silverlight.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\TimeLineView\custom.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\TimeLineView\timeline.js" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\Watermarkdetails.js" />
    <Content Include="ExportPlaylist\ListviewSL\View Playlist.exe" />
    <Content Include="ExportPlaylist\ListviewSL\View Playlist.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\app.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\BookmarkFlagcolorPick.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\bootstrap-responsive.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\bootstrap.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\bootstrap\bootstrap-responsive.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\bootstrap\bootstrap.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\custom.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\fixHeader.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\font-awesome.min.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\10.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\11.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\12.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\1_revview.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\9.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\AttachFiles.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\balance_left.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\balance_left_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\balance_right.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\balance_right_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\begin.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\begin_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\blue_bg.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\bm-icon.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\bookmark.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\bookmark_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\callTypes.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\end.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\end_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\fast_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\fast_forward.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\fast_forward_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\GraphicMarker.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\header_bg.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\iconDelete.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\InternalNotes.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\loop.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\loop_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\marker.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\max_volume.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\max_volume_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\Metadata.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\min_volume.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\min_volume_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\notes.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\pause.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\pause_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\play.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\alarm.mp3" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\attach2.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\balance_left.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\balance_left_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\balance_right.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\balance_right_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\begin - Original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\begin.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\begin_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\begin_Ex.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\bookmark -Original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\bookmark.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\bookmark_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\browse3.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\browse4.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\button - Copy.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\button-1.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\button.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\dp2.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\end -Original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\end.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\end_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\export.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\export_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\fast_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\fast_forward -Original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\fast_forward.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\fast_forward_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\loop.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\loop_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\marker.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\max_volume.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\max_volume_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\min_volume.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\min_volume_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\pause -Original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\pause-2.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\pause.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\pause_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\pause_Test.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\play -Original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\play.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\play2.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\play3.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\player_bg.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\play_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\play_new.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\rewind -Original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\rewind.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\rewind_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\Save_Button.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\settings-original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\settings.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\settings_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\slow.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\slow_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\stop -Original.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\stop.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\stop_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\time_indicator.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\player\video.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\play_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\revcord.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\rewind.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\rewind_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\IQ3Measurement.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\sharenotes.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\slow.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\slow_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\stop.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\stop_active.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\time_indicator.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\images\ui-bg_inset-hard_100_fcfdfd_1x100.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\jquery-ui-1.9.0.custom.min.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\jquery-ui-1.9.2.custom.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\jquery-ui-custom-mmsDialog.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\lightgallery\lg-fb-comment-box.min.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\lightgallery\lg-transitions.min.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\lightgallery\lightgallery.min.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\panes.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\animate.min.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\chosen.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\colpick.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\datatables\bootstrap.datatables.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\datatables\datatables.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\daterangepicker-bs3.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\dropzone.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\fullcalendar.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\jquery.handsontable.full.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\jquery.pnotify.default.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\jquery.pnotify.default.icons.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\jquery.timepicker.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\plugins\jscrollpane.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\css\jplayer.blue.monday.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\css\jplayer.blue.monday.min.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\image\jplayer.blue.monday.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\image\jplayer.blue.monday.seeking.gif" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\image\jplayer.blue.monday.video.play.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\image\revcord.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\mustache\jplayer.blue.monday.audio.playlist.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\mustache\jplayer.blue.monday.audio.single.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\mustache\jplayer.blue.monday.audio.stream.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\mustache\jplayer.blue.monday.video.playlist.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\blue.monday\mustache\jplayer.blue.monday.video.single.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\css\jplayer.pink.flag.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\css\jplayer.pink.flag.min.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\image\jplayer.pink.flag.jpg" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\image\jplayer.pink.flag.seeking.gif" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\image\jplayer.pink.flag.video.play.png" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\mustache\jplayer.pink.flag.audio.playlist.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\mustache\jplayer.pink.flag.audio.single.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\mustache\jplayer.pink.flag.audio.stream.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\mustache\jplayer.pink.flag.video.playlist.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\skin\pink.flag\mustache\jplayer.pink.flag.video.single.html" />
    <Content Include="ExportPlaylist\TimelineView\html\css\style.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\tableStyle.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\tablet.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\TimelineView\custom.css" />
    <Content Include="ExportPlaylist\TimelineView\html\css\TimelineView\vis.css" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\font-awesome\fontawesome-webfont.svg" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\lg.svg" />
    <Content Include="ExportPlaylist\TimelineView\html\images\error-loading-image.png" />
    <Content Include="ExportPlaylist\TimelineView\html\js\application.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\BookmarkFlagcolorPick.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\bootstrap.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\collapse.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\dropdown.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\modal.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\popover.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\scrollspy.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\tab.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\tooltip.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\bootstrap\transition.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\CommonConstants.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\data.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\defaultPage.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\FilePlaybackItem3.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\FilePlayer.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\html5fileplayer.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\html5fileplayer3.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery-1.8.2.min.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery-1.9.1.min.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery-ui-1.9.0.custom.min.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery-ui-custom-mmsDialog.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery-ui-sliderAccess.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery.blockUI.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery.jplayer.min.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery.jplayer.swf" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery.min.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\jquery.mobile-events.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\lightgallery\lightgallery.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\lightgallery\modules\lg-autoplay.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\lightgallery\modules\lg-fullscreen.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\lightgallery\modules\lg-hash.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\lightgallery\modules\lg-thumbnail.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\lightgallery\modules\lg-zoom.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\moment.min.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\pdf.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\pdfobject.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\playlist.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\playlistTimeline.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\playlist_.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\Sequencer.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\silverlight.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\TimeLineView\custom.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\TimeLineView\custom_20191101.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\TimeLineView\timeline.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\TimeLineView\underscore-min.js" />
    <Content Include="ExportPlaylist\TimelineView\html\js\Watermarkdetails.js" />
    <Content Include="ExportPlaylist\TimelineView\View Playlist.exe" />
    <Content Include="ExportPlaylist\TimelineView\View Playlist.html" />
    <Content Include="ExportReport\StandardReport\html\css\bootstrap\bootstrap.css" />
    <Content Include="ExportReport\StandardReport\html\css\bootstrap\bootstrap2.x.css" />
    <Content Include="ExportReport\StandardReport\html\css\font-awesome\css\font-awesome.css" />
    <Content Include="ExportReport\StandardReport\html\css\font-awesome\css\font-awesome.min.css" />
    <Content Include="ExportReport\StandardReport\html\css\font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="ExportReport\StandardReport\html\css\images\loading-poster.png" />
    <Content Include="ExportReport\StandardReport\html\css\images\NoImageAvailable.jpg" />
    <Content Include="ExportReport\StandardReport\html\css\images\NoImageAvailable1.png" />
    <Content Include="ExportReport\StandardReport\html\css\images\play-poster.png" />
    <Content Include="ExportReport\StandardReport\html\css\images\revcord.png" />
    <Content Include="ExportReport\StandardReport\html\css\jquery-ui-1.9.2.custom.css" />
    <Content Include="ExportReport\StandardReport\html\css\jquery-ui-custom-mmsDialog.css" />
    <Content Include="ExportReport\StandardReport\html\css\std-rpt-design\images\close.png" />
    <Content Include="ExportReport\StandardReport\html\css\std-rpt-design\images\IQ3Reports.jpg" />
    <Content Include="ExportReport\StandardReport\html\css\std-rpt-design\images\loading.gif" />
    <Content Include="ExportReport\StandardReport\html\css\std-rpt-design\images\next.png" />
    <Content Include="ExportReport\StandardReport\html\css\std-rpt-design\images\prev.png" />
    <Content Include="ExportReport\StandardReport\html\css\std-rpt-design\lightbox.css" />
    <Content Include="ExportReport\StandardReport\html\css\std-rpt-design\lightbox.min.js" />
    <Content Include="ExportReport\StandardReport\html\css\std-rpt-design\style.css" />
    <Content Include="ExportReport\StandardReport\html\css\widget.css" />
    <Content Include="ExportReport\StandardReport\html\default.html" />
    <Content Include="ExportReport\StandardReport\html\js\bootstrap\bootstrap.min.js" />
    <Content Include="ExportReport\StandardReport\html\js\bootstrap\modal.js" />
    <Content Include="ExportReport\StandardReport\html\js\data.js" />
    <Content Include="ExportReport\StandardReport\html\js\jquery-1.9.1.min.js" />
    <Content Include="ExportReport\StandardReport\html\js\jquery-ui-custom-mmsDialog.js" />
    <Content Include="ExportReport\StandardReport\html\js\plugins\jquery.bootstrap.wizard.min.js" />
    <Content Include="ExportReport\StandardReport\startup.exe" />
    <Content Include="ExportReport\VesselReport\html\css\bootstrap\bootstrap.css" />
    <Content Include="ExportReport\VesselReport\html\css\bootstrap\bootstrap2.x.css" />
    <Content Include="ExportReport\VesselReport\html\css\font-awesome\css\font-awesome.css" />
    <Content Include="ExportReport\VesselReport\html\css\font-awesome\css\font-awesome.min.css" />
    <Content Include="ExportReport\VesselReport\html\css\font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="ExportReport\VesselReport\html\css\images\loading-poster.png" />
    <Content Include="ExportReport\VesselReport\html\css\images\NoImageAvailable.jpg" />
    <Content Include="ExportReport\VesselReport\html\css\images\play-poster.png" />
    <Content Include="ExportReport\VesselReport\html\css\images\revcord.png" />
    <Content Include="ExportReport\VesselReport\html\css\jquery-ui-1.9.2.custom.css" />
    <Content Include="ExportReport\VesselReport\html\css\jquery-ui-custom-mmsDialog.css" />
    <Content Include="ExportReport\VesselReport\html\css\widget.css" />
    <Content Include="ExportReport\VesselReport\html\default.html" />
    <Content Include="ExportReport\VesselReport\html\js\bootstrap\bootstrap.min.js" />
    <Content Include="ExportReport\VesselReport\html\js\bootstrap\modal.js" />
    <Content Include="ExportReport\VesselReport\html\js\data.js" />
    <Content Include="ExportReport\VesselReport\html\js\jquery-1.9.1.min.js" />
    <Content Include="ExportReport\VesselReport\html\js\jquery-ui-custom-mmsDialog.js" />
    <Content Include="ExportReport\VesselReport\html\js\plugins\jquery.bootstrap.wizard.min.js" />
    <Content Include="ExportReport\VesselReport\startup.exe" />
    <Content Include="ExportTemplates\package\assets\css\default.css" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\jplayer.blue.monday.css" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\jplayer.blue.monday.jpg" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\jplayer.blue.monday.seeking.gif" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\nextActive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\nextInactive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\pauseActive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\pauseInactive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\playActive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\playInactive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\previousActive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\previousInactive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\stopActive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\stopInactive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\timeline.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\volumeMaxActive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\volumeMaxInactive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\volumeMinActive.png" />
    <Content Include="ExportTemplates\package\assets\css\jpSkin\volumeMinInactive.png" />
    <Content Include="ExportTemplates\package\assets\css\table.css" />
    <Content Include="ExportTemplates\package\assets\data.js" />
    <Content Include="ExportTemplates\package\assets\images\1.png" />
    <Content Include="ExportTemplates\package\assets\images\header_bg.png" />
    <Content Include="ExportTemplates\package\assets\images\play_bg-blue.png" />
    <Content Include="ExportTemplates\package\assets\images\play_bg.png" />
    <Content Include="ExportTemplates\package\assets\images\revcord.png" />
    <Content Include="ExportTemplates\package\assets\images\revcordSmall.png" />
    <Content Include="ExportTemplates\package\assets\js\googleapis.js" />
    <Content Include="ExportTemplates\package\assets\js\jPlayer\jplayer.playlist.min.js" />
    <Content Include="ExportTemplates\package\assets\js\jPlayer\Jplayer.swf" />
    <Content Include="ExportTemplates\package\assets\js\jPlayer\jquery.jplayer.min.js" />
    <Content Include="ExportTemplates\package\assets\js\jquery-1.9.1.min.js" />
    <Content Include="ExportTemplates\package\assets\js\jquery.tmpl.min.js" />
    <Content Include="ExportTemplates\package\assets\playlist.js" />
    <Content Include="ExportTemplates\package\default.htm" />
    <Content Include="Global.asax" />
    <Content Include="Handlers\ReportHandlers\vesselModel.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Handlers\VoiceRecHandlers\IRHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\VoiceRecHandler.ashx" />
    <Content Include="Inquire\Invitation.aspx" />
    <Content Include="InstantRecall\InstantRecall.aspx" />
    <Content Include="InstantRecall\IR.aspx" />
    <Content Include="InstantRecall\IRSlimBlank.aspx" />
    <Content Include="InstantRecall\node_modules\eventemitter2\lib\eventemitter2.js" />
    <Content Include="InstantRecall\node_modules\konva\konva.js" />
    <Content Include="InstantRecall\node_modules\waveform-data\dist\waveform-data.js" />
    <Content Include="InstantRecall\node_modules\waveform-data\dist\waveform-data.min.js" />
    <Content Include="InstantRecall\src\main.js" />
    <Content Include="InstantRecall\src\main\markers\shapes\base.js" />
    <Content Include="InstantRecall\src\main\markers\shapes\rect.js" />
    <Content Include="InstantRecall\src\main\markers\shapes\wave.js" />
    <Content Include="InstantRecall\src\main\markers\waveform.points.js" />
    <Content Include="InstantRecall\src\main\markers\waveform.segments.js" />
    <Content Include="InstantRecall\src\main\player\player.js" />
    <Content Include="InstantRecall\src\main\player\player.keyboard.js" />
    <Content Include="InstantRecall\src\main\views\waveform.overview.js" />
    <Content Include="InstantRecall\src\main\views\waveform.zoomview.js" />
    <Content Include="InstantRecall\src\main\views\zooms\animated.js" />
    <Content Include="InstantRecall\src\main\views\zooms\static.js" />
    <Content Include="InstantRecall\src\main\waveform\waveform.axis.js" />
    <Content Include="InstantRecall\src\main\waveform\waveform.core.js" />
    <Content Include="InstantRecall\src\main\waveform\waveform.mixins.js" />
    <Content Include="Invite\EventGroup.html" />
    <Content Include="Invite\LoadPlaylistForm.aspx" />
    <Content Include="Invite\ViewEvent.aspx" />
    <Content Include="Invite\ViewVideo.aspx" />
    <Content Include="IQ3Inspection\CreateTemplate.aspx" />
    <Content Include="IQ3Inspection\Default.aspx" />
    <Content Include="IQ3Inspection\EditTemplate.aspx" />
    <Content Include="IQ3Inspection\GraphicMarker.aspx" />
    <Content Include="IQ3Inspection\CustomFields.aspx" />
    <Content Include="IQ3Inspection\RVIMessage.aspx" />
    <Content Include="IQ3Inspection\InspectionTitle.aspx" />

    <Content Include="Iwb\AddJob.ascx" />
    <Content Include="Iwb\AddLocation.ascx" />
    <Content Include="Iwb\AddOrganization.ascx" />
    <Content Include="assets\js\pages\iwb.js" />
    <Content Include="Iwb\AddTest.ascx" />
    <Content Include="Iwb\AddUser.ascx" />
    <Content Include="Iwb\AssignJob.ascx" />
    <Content Include="Iwb\Dashboard.aspx" />
    <Content Include="Iwb\ManageDocuments.aspx" />
    <Content Include="Iwb\ManageJobs.aspx" />
    <Content Include="Iwb\ManageLocations.aspx" />
    <Content Include="Iwb\ManageOrganization.ascx" />
    <Content Include="Iwb\ManageOrganizations.aspx" />
    <Content Include="Iwb\ManageTests.aspx" />
    <Content Include="Iwb\ManageWelders.aspx" />
    <Content Include="Iwb\ManageRepository.aspx" />
    <Content Include="Iwb\RegisterUser.aspx" />
    <Content Include="Iwb\Templates\wpq-certificate.html" />
    <Content Include="Iwb\MyWPQ.aspx" />
    <Content Include="Iwb\TestInvitation.ascx" />
    <Content Include="Iwb\UserJobs.ascx" />
    <Content Include="Iwb\ViewJobApplicants.ascx" />
    <Content Include="Iwb\ViewJobs.aspx" />
    <Content Include="Iwb\ViewLocations.ascx" />
    <Content Include="Iwb\ViewTestAttendees.ascx" />
    <Content Include="Iwb\ViewTests.aspx" />
    <Content Include="Iwb\WorkHistory.ascx" />
    <Content Include="Iwb\WorkHistory.aspx" />
    <Content Include="Iwb\WPQForm.aspx" />
    <Content Include="OTPLogin.aspx" />
    <Content Include="RemoteInspection\Default.aspx" />
    <Content Include="Monitor\Default.aspx" />
    <Content Include="Monitor\EventMonitor.aspx" />
    <Content Include="Invite\ViewPlaylist.aspx" />
    <Content Include="Playlist\Default.aspx" />
    <Content Include="Playlist\LoadPlaylistForm.aspx" />
    <Content Include="RemoteInspection\InspectionMonitor.aspx" />
    <Content Include="Reports\ActivityReport.aspx" />
    <Content Include="Reports\AdvanceReport.aspx" />
    <Content Include="Reports\CallAuditReport.aspx" />
    <Content Include="Reports\EvaluationReport.aspx" />
    <Content Include="Reports\ContractReport.aspx" />
    <Content Include="Reports\MMSReports.aspx" />
    <Content Include="Reports\ScheduleReport.aspx" />
    <Content Include="Reports\MapReport.aspx" />
    <Content Include="assets\js\jQuery\jquery.signalR-1.2.1.js" />
    <Content Include="assets\js\jQuery\jquery.signalR-1.2.1.min.js" />
    <Content Include="Handlers\InquireHandlers\InquireHandler.ashx" />
    <Content Include="Handlers\InquireGMHandlers\InquireGMHandler.ashx" />
    <Content Include="assets\js\jQuery\jquery-1.10.2.min.map" />
    <Content Include="MasterPages\Professional\AgentQA.Master" />
    <Content Include="Handlers\PlayerHandlers\FilePlayerHandler.ashx" />
    <Content Include="Handlers\PlayerHandlers\MonitorHandler.ashx" />
    <Content Include="Handlers\InquireHandlers\InquireRxHandler.ashx" />
    <Content Include="MasterPages\Professional\2ColumnsEvent.Master" />
    <Content Include="Properties\DataSources\RevCord.DataContracts.Messages.UMResponse.datasource" />
    <Content Include="Properties\DataSources\RevCord.DataContracts.Response.ReportResponse.datasource" />
    <Content Include="Properties\DataSources\RevCord.DataContracts.Response.SurveyResponse.datasource" />
    <Content Include="Properties\DataSources\RevCord.DataContracts.Response.UserManagementResponse.datasource" />
    <Content Include="Properties\DataSources\RevCord.DataContracts.SurveyEntities.Option.datasource" />
    <Content Include="Properties\DataSources\RevCord.DataContracts.SurveyEntities.Question.datasource" />
    <Content Include="Properties\DataSources\RevCord.DataContracts.SurveyEntities.Survey.datasource" />
    <Content Include="Properties\DataSources\RevCord.DataContracts.SurveyEntities.SurveySection.datasource" />
    <Content Include="Handlers\UserManagerHandlers\UserImageHandler.ashx" />
    <Content Include="Handlers\Captcha\Captcha.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\GuestHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\Text911FileHandler.ashx" />
    <Content Include="Handlers\InquireHandlers\GuestHandler.ashx" />
    <Content Include="Handlers\InquireHandlers\GoogleCalenderHandler.ashx" />
    <Content Include="Handlers\Common\LogHandler.ashx" />
    <Content Include="assets\lib\fullcalendar\bootstrap\package.json" />
    <Content Include="assets\lib\fullcalendar\bootstrap\README.md" />
    <Content Include="assets\lib\fullcalendar\core\package.json" />
    <Content Include="assets\lib\fullcalendar\core\README.md" />
    <Content Include="assets\lib\fullcalendar\daygrid\package.json" />
    <Content Include="assets\lib\fullcalendar\daygrid\README.md" />
    <Content Include="assets\lib\fullcalendar\google-calendar\package.json" />
    <Content Include="assets\lib\fullcalendar\google-calendar\README.md" />
    <Content Include="assets\lib\fullcalendar\interaction\package.json" />
    <Content Include="assets\lib\fullcalendar\interaction\README.md" />
    <Content Include="assets\lib\fullcalendar\list\package.json" />
    <Content Include="assets\lib\fullcalendar\list\README.md" />
    <Content Include="assets\lib\fullcalendar\luxon\package.json" />
    <Content Include="assets\lib\fullcalendar\luxon\README.md" />
    <Content Include="assets\lib\fullcalendar\moment-timezone\package.json" />
    <Content Include="assets\lib\fullcalendar\moment-timezone\README.md" />
    <Content Include="assets\lib\fullcalendar\moment\package.json" />
    <Content Include="assets\lib\fullcalendar\moment\README.md" />
    <Content Include="assets\lib\fullcalendar\rrule\package.json" />
    <Content Include="assets\lib\fullcalendar\rrule\README.md" />
    <Content Include="assets\lib\fullcalendar\timegrid\package.json" />
    <Content Include="assets\lib\fullcalendar\timegrid\README.md" />
    <Content Include="Handlers\VoiceRecHandlers\TreeViewHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\ExtraEmailConfigurationHandler.ashx" />
    <Content Include="Handlers\ReportHandlers\ReportHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\ExportPlaylistHandler.ashx" />
    <Content Include="NLog.config" />
    <Content Include="Handlers\RevcellHandler\RevcellHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\SearchMediaHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\AsyncSearchHandler.ashx" />
    <Content Include="Handlers\SettingsHandlers\CustomSettingsHandler.ashx" />
    <Content Include="Handlers\UserManagerHandlers\LoginHandler.ashx" />
    <Content Include="Handlers\ReportHandlers\SharedReportHandler.ashx" />
    <Content Include="Handlers\ReportHandlers\ScheduleReportHandler.ashx" />
    <Content Include="Handlers\ReportHandlers\ReportExportHandler.ashx" />
    <Content Include="Handlers\ReportHandlers\VesselReportHandler.ashx" />
    <Content Include="ExportReport\VesselReport\html\css\bootstrap\bootstrap.css.map" />
    <Content Include="ExportReport\VesselReport\html\css\font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="ExportReport\VesselReport\html\css\font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="ExportReport\VesselReport\html\css\font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="ExportReport\VesselReport\html\css\font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="ExportReport\VesselReport\html\css\font-awesome\fonts\FontAwesome.otf" />
    <Content Include="Handlers\MonitorHandlers\MonitorHandler.ashx" />
    <Content Include="ExportReport\StandardReport\html\css\bootstrap\bootstrap.css.map" />
    <Content Include="ExportReport\StandardReport\html\css\font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="ExportReport\StandardReport\html\css\font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="ExportReport\StandardReport\html\css\font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="ExportReport\StandardReport\html\css\font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="ExportReport\StandardReport\html\css\font-awesome\fonts\FontAwesome.otf" />
    <Content Include="Handlers\RoleHandlers\RoleHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\CallTaggingHandler.ashx" />
    <Content Include="ExportPlaylist\ListviewSL\html\ClientBin\RevcordFilePlayer.xap" />
    <Content Include="ExportPlaylist\ListviewSL\html\ClientBin\RevcordFilePlayer_.xap" />
    <Content Include="ExportPlaylist\ListviewSL\html\ClientBin\RevcordVideoPlayer.xap" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\bootstrap\bootstrap.css.map" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="ExportPlaylist\ListviewSL\html\css\font-awesome\fonts\FontAwesome.otf" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\font-awesome\fontawesome-webfont.eot" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\font-awesome\fontawesome-webfont.ttf" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\font-awesome\fontawesome-webfont.woff" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\font-awesome\FontAwesome.otf" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\lg.eot" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\lg.ttf" />
    <Content Include="ExportPlaylist\ListviewSL\html\fonts\lg.woff" />
    <Content Include="ExportPlaylist\ListviewSL\html\js\jquery.min.map" />
    <Content Include="ExportPlaylist\TimelineView\html\ClientBin\ClientBin.rar" />
    <Content Include="ExportPlaylist\TimelineView\html\ClientBin\RevcordFilePlayer.xap" />
    <Content Include="ExportPlaylist\TimelineView\html\ClientBin\RevcordFilePlayer_.xap" />
    <Content Include="ExportPlaylist\TimelineView\html\ClientBin\RevcordVideoPlayer.xap" />
    <Content Include="ExportPlaylist\TimelineView\html\css\css.rar" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\font-awesome\fontawesome-webfont.eot" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\font-awesome\fontawesome-webfont.ttf" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\font-awesome\fontawesome-webfont.woff" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\font-awesome\FontAwesome.otf" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\lg.eot" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\lg.ttf" />
    <Content Include="ExportPlaylist\TimelineView\html\fonts\lg.woff" />
    <Content Include="ExportPlaylist\TimelineView\html\js\js.rar" />
    <Content Include="Handlers\VoiceRecHandlers\ADGroupsSyncConfigurationHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\MMSHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\InspectionHandler.ashx" />
    <Content Include="Handlers\UserManagerHandlers\UploadPdfHandler.ashx" />
    <Content Include="Handlers\UserManagerHandlers\UploadMarkerImageHandler.ashx" />
    <Content Include="Handlers\InquireHandlers\GraphicMarkerHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\CustomerDatabaseHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\BookmarkFlagHandler.ashx" />
    <Content Include="Handlers\UserManagerHandlers\UploadLogoHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\MTRReportHandler.ashx" />
    <Content Include="Handlers\IwbHandlers\IwbHandler.ashx" />
    <Content Include="Handlers\IwbHandlers\DocumentUploadHandler.ashx" />
    <Content Include="libman.json" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-grid.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-grid.min.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-grid.rtl.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-grid.rtl.min.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-reboot.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-reboot.min.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-reboot.rtl.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-utilities.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-utilities.min.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-utilities.rtl.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap.min.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap.rtl.css.map" />
    <Content Include="asset\lib\bootstrap\css\bootstrap.rtl.min.css.map" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.bundle.js.map" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.bundle.min.js.map" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.esm.js.map" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.esm.min.js.map" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.js.map" />
    <Content Include="asset\lib\bootstrap\js\bootstrap.min.js.map" />
    <Content Include="asset\lib\bootstrap\scss\_accordion.scss" />
    <Content Include="asset\lib\bootstrap\scss\_alert.scss" />
    <Content Include="asset\lib\bootstrap\scss\_badge.scss" />
    <Content Include="asset\lib\bootstrap\scss\_breadcrumb.scss" />
    <Content Include="asset\lib\bootstrap\scss\_button-group.scss" />
    <Content Include="asset\lib\bootstrap\scss\_buttons.scss" />
    <Content Include="asset\lib\bootstrap\scss\_card.scss" />
    <Content Include="asset\lib\bootstrap\scss\_carousel.scss" />
    <Content Include="asset\lib\bootstrap\scss\_close.scss" />
    <Content Include="asset\lib\bootstrap\scss\_containers.scss" />
    <Content Include="asset\lib\bootstrap\scss\_dropdown.scss" />
    <Content Include="asset\lib\bootstrap\scss\_forms.scss" />
    <Content Include="asset\lib\bootstrap\scss\_functions.scss" />
    <Content Include="asset\lib\bootstrap\scss\_grid.scss" />
    <Content Include="asset\lib\bootstrap\scss\_helpers.scss" />
    <Content Include="asset\lib\bootstrap\scss\_images.scss" />
    <Content Include="asset\lib\bootstrap\scss\_list-group.scss" />
    <Content Include="asset\lib\bootstrap\scss\_maps.scss" />
    <Content Include="asset\lib\bootstrap\scss\_mixins.scss" />
    <Content Include="asset\lib\bootstrap\scss\_modal.scss" />
    <Content Include="asset\lib\bootstrap\scss\_nav.scss" />
    <Content Include="asset\lib\bootstrap\scss\_navbar.scss" />
    <Content Include="asset\lib\bootstrap\scss\_offcanvas.scss" />
    <Content Include="asset\lib\bootstrap\scss\_pagination.scss" />
    <Content Include="asset\lib\bootstrap\scss\_placeholders.scss" />
    <Content Include="asset\lib\bootstrap\scss\_popover.scss" />
    <Content Include="asset\lib\bootstrap\scss\_progress.scss" />
    <Content Include="asset\lib\bootstrap\scss\_reboot.scss" />
    <Content Include="asset\lib\bootstrap\scss\_root.scss" />
    <Content Include="asset\lib\bootstrap\scss\_spinners.scss" />
    <Content Include="asset\lib\bootstrap\scss\_tables.scss" />
    <Content Include="asset\lib\bootstrap\scss\_toasts.scss" />
    <Content Include="asset\lib\bootstrap\scss\_tooltip.scss" />
    <Content Include="asset\lib\bootstrap\scss\_transitions.scss" />
    <Content Include="asset\lib\bootstrap\scss\_type.scss" />
    <Content Include="asset\lib\bootstrap\scss\_utilities.scss" />
    <Content Include="asset\lib\bootstrap\scss\bootstrap-grid.scss" />
    <Content Include="asset\lib\bootstrap\scss\bootstrap-reboot.scss" />
    <Content Include="asset\lib\bootstrap\scss\bootstrap-utilities.scss" />
    <Content Include="asset\lib\bootstrap\scss\bootstrap.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_floating-labels.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_form-check.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_form-control.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_form-range.scss" />
    <Content Include="asset\lib\bootstrap\scss\_variables-dark.scss" />
    <Content Include="asset\lib\bootstrap\scss\_variables.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_form-select.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_form-text.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_input-group.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_labels.scss" />
    <Content Include="asset\lib\bootstrap\scss\forms\_validation.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_clearfix.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_color-bg.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_colored-links.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_focus-ring.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_icon-link.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_position.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_ratio.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_stacks.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_stretched-link.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_text-truncation.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_visually-hidden.scss" />
    <Content Include="asset\lib\bootstrap\scss\helpers\_vr.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_alert.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_backdrop.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_banner.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_border-radius.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_box-shadow.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_breakpoints.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_buttons.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_caret.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_clearfix.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_color-mode.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_color-scheme.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_container.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_deprecate.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_forms.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_gradients.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_grid.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_image.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_list-group.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_lists.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_pagination.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_reset-text.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_resize.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_table-variants.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_text-truncate.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_transition.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_utilities.scss" />
    <Content Include="asset\lib\bootstrap\scss\mixins\_visually-hidden.scss" />
    <Content Include="asset\lib\bootstrap\scss\utilities\_api.scss" />
    <Content Include="asset\lib\bootstrap\scss\vendor\_rfs.scss" />
    <Content Include="asset\lib\jquery\jquery.min.map" />
    <Content Include="asset\lib\jquery\jquery.slim.min.map" />
    <Content Include="asset\lib\font-awesome\webfonts\fa-brands-400.ttf" />
    <Content Include="asset\lib\font-awesome\webfonts\fa-brands-400.woff2" />
    <Content Include="asset\lib\font-awesome\webfonts\fa-regular-400.ttf" />
    <Content Include="asset\lib\font-awesome\webfonts\fa-regular-400.woff2" />
    <Content Include="asset\lib\font-awesome\webfonts\fa-solid-900.ttf" />
    <Content Include="asset\lib\font-awesome\webfonts\fa-solid-900.woff2" />
    <Content Include="asset\lib\font-awesome\webfonts\fa-v4compatibility.ttf" />
    <Content Include="asset\lib\font-awesome\webfonts\fa-v4compatibility.woff2" />
    <Content Include="asset\lib\toastr.js\toastr.js.map" />
    <Content Include="asset\lib\moment.js\moment-with-locales.min.js.map" />
    <Content Include="asset\lib\moment.js\moment.min.js.map" />
    <Content Include="MasterPages\2.0\2Columns.Master" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_disabled.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_dropdown.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_include-all.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_input-group.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_layout.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_multiple.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\select2-bootstrap-5-theme.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_single.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_sizing.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_validation.scss" />
    <Content Include="asset\lib\select2-bootstrap-5-theme\src\_variables.scss" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker.css.map" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker.standalone.css.map" />
    <Content Include="asset\lib\bootstrap-daterangepicker\daterangepicker.min.css.map" />
    <Content Include="asset\lib\bootstrap-daterangepicker\daterangepicker.min.js.map" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker3.css.map" />
    <Content Include="asset\lib\bootstrap-datepicker\css\bootstrap-datepicker3.standalone.css.map" />
    <Content Include="Handlers\VoiceRecHandlers\RevSignHandler.ashx" />
    <Content Include="Handlers\ConditionalLogicHandlers\ConditionalLogicHandler.ashx" />
    <Content Include="Handlers\IwbHandlers\MyWPQHandler.ashx" />
    <Content Include="Handlers\IwbHandlers\WorkerHistoryHandler.ashx" />
    <None Include="Properties\PublishProfiles\AG.pubxml" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Properties\PublishProfiles\FolderProfile1.pubxml" />
    <None Include="Properties\PublishProfiles\SM.pubxml" />
    <EmbeddedResource Include="Reports\RdlcDesigner\PBX911\PBX911BarChart.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\PBX911\PBX911PieChart.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Standard\CallNotAudit.rdlc" />
    <None Include="Properties\PublishProfiles\V12.2 Release.pubxml" />
    <None Include="Properties\PublishProfiles\V12.3.1-Revsign.pubxml" />
    <None Include="Properties\PublishProfiles\V121-Post-Playlist.pubxml" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Standard\WelderReport.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Standard\TestingReport.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Standard\InsuranceReport.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Standard\OwnerReport.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Standard\ContractorReport.rdlc" />
    <None Include="Scripts\jquery-1.6.4-vsdoc.js" />
    <Content Include="Reports\SoftwareAudit.aspx" />
    <Content Include="Reports\TenantIQ3UsageReport.aspx" />
    <Content Include="ScheduleEvent\Default.aspx" />
    <Content Include="Scripts\CustomScripts\esign-signed.js" />
    <Content Include="Scripts\CustomScripts\esign.js" />
    <Content Include="Scripts\jquery-1.6.4.js" />
    <Content Include="Scripts\jquery-1.6.4.min.js" />
    <Content Include="Scripts\jquery.signalR-1.2.1.js" />
    <Content Include="Scripts\jquery.signalR-1.2.1.min.js" />
    <Content Include="Scripts\jsnlog.js" />
    <Content Include="Scripts\jsnlog.min.js" />
    <Content Include="Search\bower_components\requirejs\require.js" />
    <Content Include="Search\LoadPlaylistForm.aspx" />
    <Content Include="Search\mitm.html" />
    <Content Include="Search\MultiChannelPlayback.aspx" />
    <Content Include="Search\node_modules\eventemitter2\lib\eventemitter2.js" />
    <Content Include="Search\node_modules\konva\konva.js" />
    <Content Include="Search\node_modules\waveform-data\dist\waveform-data.js" />
    <Content Include="Search\node_modules\waveform-data\dist\waveform-data.min.js" />
    <Content Include="Search\pdf.worker.js" />
    <Content Include="Search\SearchDefault.aspx" />
    <Content Include="Search\src\main.js" />
    <Content Include="Search\src\main\markers\shapes\base.js" />
    <Content Include="Search\src\main\markers\shapes\rect.js" />
    <Content Include="Search\src\main\markers\shapes\wave.js" />
    <Content Include="Search\src\main\markers\waveform.points.js" />
    <Content Include="Search\src\main\markers\waveform.segments.js" />
    <Content Include="Search\src\main\player\player.js" />
    <Content Include="Search\src\main\player\player.keyboard.js" />
    <Content Include="Search\src\main\views\waveform.overview.js" />
    <Content Include="Search\src\main\views\waveform.zoomview.js" />
    <Content Include="Search\src\main\views\zooms\animated.js" />
    <Content Include="Search\src\main\views\zooms\static.js" />
    <Content Include="Search\src\main\waveform\waveform.axis.js" />
    <Content Include="Search\src\main\waveform\waveform.core.js" />
    <Content Include="Search\src\main\waveform\waveform.mixins.js" />
    <Content Include="Search\sw.js" />
    <Content Include="SiteAdministration\ColumnSettings.aspx" />
    <Content Include="SiteAdministration\ADGroupsSyncConfiguration.aspx" />
    <Content Include="SiteAdministration\ExtraMailConfiguration.aspx" />
    <Content Include="SiteAdministration\IQ3UsageReporting.aspx" />
    <Content Include="SiteAdministration\ManageColumnSettings.aspx" />
    <Content Include="SiteAdministration\ManageSettings.aspx" />
    <Content Include="SiteAdministration\TeamsChannelConfiguration.aspx" />
    <Content Include="SiteAdministration\TenantIQ3UsageReport.aspx" />
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Surveys\CreateSurvey.aspx" />
    <Content Include="Surveys\EditSurvey.aspx" />
    <Content Include="Surveys\SurveyList.aspx" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\1.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\10.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\11.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\12.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\13.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\1_revview.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\2.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\3.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\4.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\5.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\6.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\7.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\7_virtual.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\8.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\8_virtual.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\DefaultIcons\9.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\1.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\10.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\11.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\12.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\13.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\1_revview.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\2.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\3.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\4.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\5.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\6.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\7.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\7_virtual.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\8.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\8_virtual.png" />
    <Content Include="SystemUploadedFiles\MediaIcons\Tenant_0\9.png" />
    <Content Include="Templates\Acceptance_Intimation.html" />
    <Content Include="Templates\BookmarkAlert_Email.htm" />
    <Content Include="Templates\Event_Invitation_Email.htm" />
    <Content Include="Templates\Invitation_Email.htm" />
    <Content Include="Templates\INQ_Invitation_Email.htm" />
    <Content Include="Templates\Invitation_Email_For_RevView.htm" />
    <Content Include="Templates\Invitation_Email_For_Event_Guest.htm" />
    <Content Include="Templates\Invitation_Email_For_New_Event_Guest.htm" />
    <Content Include="Templates\Invitation_Reminder_Email.htm" />
    <Content Include="Templates\IQ3StandardReport\images\close.png" />
    <Content Include="Templates\IQ3StandardReport\images\IQ3Reports.jpg" />
    <Content Include="Templates\IQ3StandardReport\images\IQ3_1694813902660_1694814053053.png" />
    <Content Include="Templates\IQ3StandardReport\images\loading.gif" />
    <Content Include="Templates\IQ3StandardReport\images\next.png" />
    <Content Include="Templates\IQ3StandardReport\images\prev.png" />
    <Content Include="Templates\IQ3StandardReport\lightbox.css" />
    <Content Include="Templates\IQ3StandardReport\lightbox.min.js" />
    <Content Include="Templates\IQ3StandardReport\modal.html" />
    <Content Include="Templates\IQ3StandardReport\style.css" />
    <Content Include="Templates\IQ3WelcomeLetter.html" />
    <Content Include="Templates\NewAccountIntimation.html" />
    <Content Include="Templates\OnDemandEvent\OnDemandEventParticipant.htm" />
    <Content Include="Templates\OnDemandEvent\OnDemandEventRemoteInspector.htm" />
    <Content Include="Templates\OnDemandEvent\VirtualEvent.htm" />
    <Content Include="Templates\OnDemandEvent\OnDemandEvent.htm" />
    <Content Include="Templates\OTPNotification.html" />
    <Content Include="Templates\PasswordReset2023.html" />
    <Content Include="Templates\Playlist\SharePlaylist.html" />
    <Content Include="Templates\Recover_Password.html" />
    <Content Include="Templates\Reports\StandardReport.htm" />
    <Content Include="Templates\Reports\PBX911Report.htm" />
    <Content Include="Templates\Reports\QAEvaluationReport.htm" />
    <Content Include="Templates\Reports\AdvancedReport.htm" />
    <Content Include="Templates\PasswordExpiryNotification.html" />
    <Content Include="Templates\PasswordReset.html" />
    <Content Include="Templates\Reset_Password.html" />
    <Content Include="Templates\RevSign\SignRequestCompleted.html" />
    <Content Include="Templates\RevSign\SignRequestDeleted.html" />
    <Content Include="Templates\RevSign\SignRequestReminder.html" />
    <Content Include="Templates\RevSign\SignRequestInitiated.html" />
    <Content Include="Templates\ScheduleEvent\DeleteInvitation.htm" />
    <Content Include="Templates\ScheduleEvent\IntimateEventOrganizer.htm" />
    <Content Include="Templates\ScheduleEvent\ReminderScheduleEvent.htm" />
    <Content Include="Templates\ScheduleEvent\ScheduleNewEvent.htm" />
    <Content Include="Templates\RevcellInviteWelcomeLetter.html" />
    <Content Include="Templates\SuccessfulPasswordReset2023.html" />
    <Content Include="Templates\WelcomeLetter.html" />
    <Content Include="Uploads\ReportImages\footer.png" />
    <Content Include="Uploads\ReportImages\header.png" />
    <Content Include="Uploads\UserImages\IQ3Reports.jpg" />
    <Content Include="Uploads\UserImages\user.jpg" />
    <Content Include="UserControls\CommonControls\ucCommonPageSideBarWrapper.ascx" />
    <Content Include="UserControls\CommonControls\ucCommonPageTopBarWrapper.ascx" />
    <Content Include="UserControls\CommonControls\ucCommonPageWrapper.ascx" />
    <Content Include="UserControls\CustomerDBControls\ucEventSummaryIQ3Asset.ascx" />
    <Content Include="UserControls\CustomerDBControls\ucAuditHistoryIQ3Asset.ascx" />
    <Content Include="UserControls\CustomerDBControls\ucViewIQ3Asset.ascx" />
    <Content Include="UserControls\EmailControls\EmailandPhoneControl.ascx" />
    <Content Include="UserControls\InquireControls\ucEventNotes.ascx" />
    <Content Include="UserControls\InquireControls\ucInspectionTemplateMarker.ascx" />
    <Content Include="UserControls\InquireControls\ucMultiLevelMarker.ascx" />
    <Content Include="UserControls\InquireControls\ucPictureEvent.ascx" />
    <Content Include="UserControls\InspectionControls\ucGraphicMarkerPdf.ascx" />
    <Content Include="UserControls\InspectionControls\ucInspectionControl.ascx" />
    <Content Include="UserControls\InspectionControls\ucInspectionSection.ascx" />
    <Content Include="UserControls\InspectionControls\ucInspectionTitle.ascx" />
    <Content Include="UserControls\InspectionControls\ucAutoReportRecipientControl.ascx" />
    <Content Include="UserControls\InspectionControls\ucTextFieldMarker.ascx" />
    <Content Include="UserControls\InspectionControls\ucSingleMultiSelectMarker.ascx" />
    <Content Include="UserControls\InspectionControls\ucGraphicMarker.ascx" />
    <Content Include="UserControls\InspectionControls\ucTextMarker.ascx" />
    <Content Include="UserControls\MonitorControls\ucLiveECG.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditAnnotationRecording.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditRecordSettings.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucMultipleCallsEdit.ascx" />
    <Content Include="UserControls\PlayerControls\LivePlayer\ucLiveMonitorSettings.ascx" />
    <Content Include="UserControls\ReportControls\EmailControls\ucEmailReport.ascx" />
    <Content Include="UserControls\ReportControls\ScheduleReportControls\ucAddScheduleReportButton.ascx" />
    <Content Include="UserControls\ReportControls\ScheduleReportControls\ucAddScheduleReportDialog.ascx" />
    <Content Include="UserControls\ReportControls\ScheduleReportControls\ucEditScheduleReportDialog.ascx" />
    <Content Include="UserControls\ReportControls\ScheduleReportControls\ucScheduleReportDialog.ascx" />
    <Content Include="UserControls\ReportControls\SharedReportControls\ucAddEditSharedReportDialog.ascx" />
    <Content Include="UserControls\ReportControls\SharedReportControls\ucEditSharedReportDialog.ascx" />
    <Content Include="UserControls\ReportControls\SharedReportControls\ucSharedReportButton.ascx" />
    <Content Include="UserControls\ReportControls\SharedReportControls\ucSharedReportDialog.ascx" />
    <Content Include="UserControls\ReportControls\ucInspectionReportDialog.ascx" />
    <Content Include="UserControls\ReportControls\ucReportPageSubSideBarWrapper.ascx" />
    <Content Include="UserControls\RevSignControls\ucSignOffControl.ascx" />
    <Content Include="UserControls\UMControls\ucAssignEnterpriseGroup.ascx" />
    <Content Include="UserControls\UMControls\ucRecorderTree.ascx" />
    <Content Include="UserControls\UMControls\ucChannel.ascx" />
    <Content Include="UserControls\UMControls\ucSiteChannel.ascx" />
    <Content Include="UserControls\UMControls\ucTreeIQ3.ascx" />
    <Content Include="UserControls\UMControls\ucTreeMD.ascx" />
    <Content Include="UserControls\UMControls\ucTreeReport.ascx" />
    <Content Include="UserControls\Uploads\UserImages\user.jpg" />
    <Content Include="UserControls\BaseControl.ascx" />
    <Content Include="UserControls\CommonControls\AutoCompleteControl.ascx" />
    <Content Include="UserControls\CommonControls\ucHighChart.ascx" />
    <Content Include="UserControls\CommonControls\ucPagerControl.ascx" />
    <Content Include="UserControls\InquireControls\ucAcceptInvitation.ascx" />
    <Content Include="UserControls\InquireControls\ucAssignUsers.ascx" />
    <Content Include="UserControls\InquireControls\ucCustomMarker.ascx" />
    <Content Include="UserControls\InquireControls\ucFileVideoAddBookmark.ascx" />
    <Content Include="UserControls\InquireControls\ucFileVideoEditBookmark.ascx" />
    <Content Include="UserControls\InquireControls\ucFileVideoNotes.ascx" />
    <Content Include="UserControls\InquireControls\ucNewGroup.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayer.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerAddUserFile.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerBookmark.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditCall.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEmail.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerSaveCalls.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerSettings.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerVideoPlayer.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFileVideoPlayer.ascx" />
    <Content Include="UserControls\PlayerControls\FilePlayer\ucFileVideoPlayer_Quad.ascx" />
    <Content Include="UserControls\PlayerControls\LivePlayer\ucLivePlayer.ascx" />
    <Content Include="UserControls\PlayerControls\LivePlayer\ucLivePlayerBookmark.ascx" />
    <Content Include="UserControls\PlayerControls\LivePlayer\ucLivePlayerSettings.ascx" />
    <Content Include="UserControls\QAEControls\ucEvaluateCall.ascx" />
    <Content Include="UserControls\QAEControls\ucSearchCalls.ascx" />
    <Content Include="UserControls\QAEControls\ucSearchQAEvaluations.ascx" />
    <Content Include="UserControls\SurveyControls\ucSingleMultiSelectQuestion.ascx" />
    <Content Include="UserControls\SurveyControls\ucSurveyControl.ascx" />
    <Content Include="UserControls\SurveyControls\ucSurveySection.ascx" />
    <Content Include="UserControls\SurveyControls\ucTextQuestion.ascx" />
    <Content Include="UserControls\UMControls\ucAcceptInvitation.ascx" />
    <Content Include="UserControls\UMControls\ucAssignChannels.ascx" />
    <Content Include="UserControls\UMControls\ucAssignGroup.ascx" />
    <Content Include="UserControls\UMControls\ucAssignPermissions.ascx" />
    <Content Include="UserControls\UMControls\ucInviteInqUser.ascx" />
    <Content Include="UserControls\UMControls\ucTree.ascx" />
    <Content Include="UserControls\UMControls\ucTreeAgent.ascx" />
    <Content Include="UserControls\UMControls\ucTreeAudio.ascx" />
    <Content Include="UserControls\UMControls\ucTreeAudioEC.ascx" />
    <Content Include="UserControls\UMControls\ucTreeInquire.ascx" />
    <Content Include="UserControls\UMControls\ucUserForm.ascx" />
    <Content Include="UserControls\VRControls\ucIRCalls.ascx" />
    <Content Include="UserControls\VRControls\ucMap.ascx" />
    <Content Include="UserControls\VRControls\ucMultiChannelPlayback.ascx" />
    <Content Include="UserControls\VRControls\ucPlayList.ascx" />
    <Content Include="UserControls\VRControls\ucSiteSelector.ascx" />
    <Content Include="UserManager\BookmarkFlag.aspx" />
    <Content Include="UserManager\CallTagging.aspx" />
    <Content Include="UserManager\AutoReportSettings.aspx" />
    <Content Include="UserManager\ManageSign.aspx" />
    <Content Include="UserManager\CustomSettings.aspx" />
    <Content Include="UserManager\ManageRoles.aspx" />
    <Content Include="UserManager\Default.aspx" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Content Include="UserManager\EditUseremailPassword.aspx" />
    <Content Include="UserManager\EnterpriseUser.aspx" />
    <Content Include="UserManager\InqGroupManager.aspx" />
    <Content Include="UserManager\Invitation.aspx" />
    <Content Include="UserManager\ManageGroups.aspx" />
    <Content Include="UserManager\RecoverPassword.aspx" />
    <Content Include="UserManager\ResetPassword.aspx" />
    <Content Include="ViewerJS\compatibility.js" />
    <Content Include="ViewerJS\example.local.css" />
    <Content Include="ViewerJS\images\kogmbh.png" />
    <Content Include="ViewerJS\images\nlnet.png" />
    <Content Include="ViewerJS\images\texture.png" />
    <Content Include="ViewerJS\images\toolbarButton-download.png" />
    <Content Include="ViewerJS\images\toolbarButton-fullscreen.png" />
    <Content Include="ViewerJS\images\toolbarButton-menuArrows.png" />
    <Content Include="ViewerJS\images\toolbarButton-pageDown.png" />
    <Content Include="ViewerJS\images\toolbarButton-pageUp.png" />
    <Content Include="ViewerJS\images\toolbarButton-presentation.png" />
    <Content Include="ViewerJS\images\toolbarButton-zoomIn.png" />
    <Content Include="ViewerJS\images\toolbarButton-zoomOut.png" />
    <Content Include="ViewerJS\index.html" />
    <Content Include="ViewerJS\pdf.js" />
    <Content Include="ViewerJS\pdf.worker.js" />
    <Content Include="ViewerJS\pdfjsversion.js" />
    <Content Include="ViewerJS\text_layer_builder.js" />
    <Content Include="ViewerJS\ui_utils.js" />
    <Content Include="ViewerJS\webodf.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Scripts\jsnlog.js.map" />
    <Content Include="SystemUploadedFiles\DemoText911\test1.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="SystemUploadedFiles\DemoText911\test2.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="SystemUploadedFiles\DemoText911\test3.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="SystemUploadedFiles\Manuals\RevCellManual.pdf" />
    <Content Include="SystemUploadedFiles\Manuals\IQ3-App-Quick-Start-Guide-11.3.2.pdf" />
    <Content Include="SystemUploadedFiles\Manuals\IQ3-App-Quick-Start-Guide-12.pdf" />
    <Content Include="Views\Sign\Index.cshtml" />
    <Content Include="Views\Sign\DocumentInfo.cshtml" />
    <Content Include="Uploads\Signatures\sample.pdf" />
    <Content Include="Views\Sign\Declined.cshtml" />
    <Content Include="Views\Sign\DeclinedPage.cshtml" />
    <Content Include="Views\Sign\DocumentExpired.cshtml" />
    <Content Include="Views\Sign\Error.cshtml" />
    <Content Include="Views\Sign\Final.cshtml" />
    <Content Include="Views\Sign\SignedDocument.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\JSNLogConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Classes\ActivityRepository.cs" />
    <Compile Include="Classes\AdditionalExportFile.cs" />
    <Compile Include="Classes\AppConstants.cs" />
    <Compile Include="Classes\CaseDetails.cs" />
    <Compile Include="Classes\ChannelRepository.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Classes\ColumnModel.cs" />
    <Compile Include="Classes\DemoData.cs" />
    <Compile Include="Classes\ECModuleAccess.cs" />
    <Compile Include="Classes\Encryption.cs" />
    <Compile Include="Classes\ExportPlaylistProcessor.cs" />
    <Compile Include="Classes\ExportReportProcessor.cs" />
    <Compile Include="Classes\GeneralVitals.cs" />
    <Compile Include="Classes\Interviews.cs" />
    <Compile Include="Classes\Markers.cs" />
    <Compile Include="Classes\MasterBase.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Classes\PatientDetails.cs" />
    <Compile Include="Classes\PlaylistInquireItemDetail.cs" />
    <Compile Include="Classes\ReportsConstants.cs" />
    <Compile Include="Classes\RevViewEvent.cs" />
    <Compile Include="Classes\State\CacheWrapper.cs" />
    <Compile Include="Classes\TenantConfiguration.cs" />
    <Compile Include="Classes\UIClasses.cs" />
    <Compile Include="Classes\UIUtils.cs" />
    <Compile Include="Classes\UserData.cs" />
    <Compile Include="Classes\ChannelMonitor.cs" />
    <Compile Include="Classes\CommonConstants.cs" />
    <Compile Include="Classes\Common\ApplicationConstant.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Classes\Common\JSONUtil.cs" />
    <Compile Include="Classes\Common\SearchCriteria.cs" />
    <Compile Include="Classes\PageBase.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Classes\SecureQuery.cs" />
    <Compile Include="Classes\SessionHandler.cs" />
    <Compile Include="Classes\Util\ActivityLogger.cs" />
    <Compile Include="Classes\Util\CacheHelper.cs" />
    <Compile Include="Classes\Util\CompletionCertificateWriter.cs" />
    <Compile Include="Classes\Util\CustomPasswordValidator.cs" />
    <Compile Include="Classes\Util\EncryptDecryptQueryString.cs" />
    <Compile Include="Classes\Util\ExportLogger.cs" />
    <Compile Include="Classes\Util\IPHelper.cs" />
    <Compile Include="Classes\Util\IQ3VirtualInspectionHelper.cs" />
    <Compile Include="Classes\Util\KeyValueConverter.cs" />
    <Compile Include="Classes\Util\MailHelper.cs" />
    <Compile Include="Classes\Util\PdfExportProcessor.cs" />
    <Compile Include="Classes\Util\RevSignUtil\RevSignHelper.cs" />
    <Compile Include="Classes\Util\RevSignUtil\StandardReportWriter.cs" />
    <Compile Include="Classes\Util\TreeviewHelper.cs" />
    <Compile Include="Classes\Util\URLShortenHelper.cs" />
    <Compile Include="Classes\Util\ValidationHelper.cs" />
    <Compile Include="Classes\Util\VesselReportWriter.cs" />
    <Compile Include="Classes\Util\WordToPdfConverter.cs" />
    <Compile Include="Classes\Util\ZohoSignTokenHelper.cs" />
    <Compile Include="Classes\VirtualInspectionNotification.cs" />
    <Compile Include="Controllers\SignController.cs" />
    <Compile Include="CustomerDatabase\IQ3AssetReportData.aspx.cs">
      <DependentUpon>IQ3AssetReportData.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CustomerDatabase\IQ3AssetReportData.aspx.designer.cs">
      <DependentUpon>IQ3AssetReportData.aspx</DependentUpon>
    </Compile>
    <Compile Include="CustomerDatabase\ViewIQ3Asset.aspx.cs">
      <DependentUpon>ViewIQ3Asset.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CustomerDatabase\ViewIQ3Asset.aspx.designer.cs">
      <DependentUpon>ViewIQ3Asset.aspx</DependentUpon>
    </Compile>
    <Compile Include="CustomerDatabase\Manage.aspx.cs">
      <DependentUpon>Manage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CustomerDatabase\Manage.aspx.designer.cs">
      <DependentUpon>Manage.aspx</DependentUpon>
    </Compile>
    <Compile Include="Dashboard\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Dashboard\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="ErrorPage.aspx.cs">
      <DependentUpon>ErrorPage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ErrorPage.aspx.designer.cs">
      <DependentUpon>ErrorPage.aspx</DependentUpon>
    </Compile>
    <Compile Include="Evaluation\DispatcherEvaluation.aspx.cs">
      <DependentUpon>DispatcherEvaluation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Evaluation\DispatcherEvaluation.aspx.designer.cs">
      <DependentUpon>DispatcherEvaluation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Evaluation\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Evaluation\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Evaluation\EvaluateCalls.aspx.cs">
      <DependentUpon>EvaluateCalls.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Evaluation\EvaluateCalls.aspx.designer.cs">
      <DependentUpon>EvaluateCalls.aspx</DependentUpon>
    </Compile>
    <Compile Include="Evaluation\Feedback.aspx.cs">
      <DependentUpon>Feedback.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Evaluation\Feedback.aspx.designer.cs">
      <DependentUpon>Feedback.aspx</DependentUpon>
    </Compile>
    <Compile Include="Evaluation\Search.aspx.cs">
      <DependentUpon>Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Evaluation\Search.aspx.designer.cs">
      <DependentUpon>Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Handlers\BaseHandler.ashx.cs">
      <DependentUpon>BaseHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\Captcha\Captcha.ashx.cs">
      <DependentUpon>Captcha.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\Common\AutoCompleteControl.ashx.cs">
      <DependentUpon>AutoCompleteControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\Common\CommonHandler.ashx.cs">
      <DependentUpon>CommonHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\Common\EmailHandler.ashx.cs">
      <DependentUpon>EmailHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\Common\LogHandler.ashx.cs">
      <DependentUpon>LogHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\Common\Suggesions.ashx.cs">
      <DependentUpon>Suggesions.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\Common\UtilityHandler.ashx.cs">
      <DependentUpon>UtilityHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\ConditionalLogicHandlers\ConditionalLogicHandler.ashx.cs">
      <DependentUpon>ConditionalLogicHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\EvaluationHandlers\EvaluationHandler.ashx.cs">
      <DependentUpon>EvaluationHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\HandlerBase.ashx.cs">
      <DependentUpon>HandlerBase.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\InquireGMHandlers\InquireGMHandler.ashx.cs">
      <DependentUpon>InquireGMHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\InquireHandlers\GoogleCalenderHandler.ashx.cs">
      <DependentUpon>GoogleCalenderHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\InquireHandlers\GraphicMarkerHandler.ashx.cs">
      <DependentUpon>GraphicMarkerHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\InquireHandlers\GuestHandler.ashx.cs">
      <DependentUpon>GuestHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\InquireHandlers\InquireHandler.ashx.cs">
      <DependentUpon>InquireHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\InquireHandlers\InquireRxHandler.ashx.cs">
      <DependentUpon>InquireRxHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\IwbHandlers\DocumentUploadHandler.ashx.cs">
      <DependentUpon>DocumentUploadHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\IwbHandlers\IwbHandler.ashx.cs">
      <DependentUpon>IwbHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\IwbHandlers\WorkerHistoryHandler.ashx.cs">
      <DependentUpon>WorkerHistoryHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\IwbHandlers\MyWPQHandler.ashx.cs">
      <DependentUpon>MyWPQHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\MonitorHandlers\MonitorHandler.ashx.cs">
      <DependentUpon>MonitorHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\PlayerHandlers\FilePlayerHandler.ashx.cs">
      <DependentUpon>FilePlayerHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\PlayerHandlers\MonitorHandler.ashx.cs">
      <DependentUpon>MonitorHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\PlayerHandlers\ReportsHandlers\ScheduleReportsHandler.cs" />
    <Compile Include="Handlers\ReportHandlers\ReportExportHandler.ashx.cs">
      <DependentUpon>ReportExportHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\ReportHandlers\ReportHandler.ashx.cs">
      <DependentUpon>ReportHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\ReportHandlers\ScheduleReportHandler.ashx.cs">
      <DependentUpon>ScheduleReportHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\ReportHandlers\SharedReportHandler.ashx.cs">
      <DependentUpon>SharedReportHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\ReportHandlers\VesselReportHandler.ashx.cs">
      <DependentUpon>VesselReportHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\RevcellHandler\RevcellHandler.ashx.cs">
      <DependentUpon>RevcellHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\RoleHandlers\RoleHandler.ashx.cs">
      <DependentUpon>RoleHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\SettingsHandlers\CustomSettingsHandler.ashx.cs">
      <DependentUpon>CustomSettingsHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\SurveyHandlers\SurveyHandler.ashx.cs">
      <DependentUpon>SurveyHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UserManagerHandlers\LoginHandler.ashx.cs">
      <DependentUpon>LoginHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UserManagerHandlers\UploadMarkerImageHandler.ashx.cs">
      <DependentUpon>UploadMarkerImageHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UserManagerHandlers\UploadPdfHandler.ashx.cs">
      <DependentUpon>UploadPdfHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UserManagerHandlers\UploadLogoHandler.ashx.cs">
      <DependentUpon>UploadLogoHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UserManagerHandlers\UploadUserImageHandler.ashx.cs">
      <DependentUpon>UploadUserImageHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UserManagerHandlers\UserImageHandler.ashx.cs">
      <DependentUpon>UserImageHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UserManagerHandlers\UserManagerHandler.ashx.cs">
      <DependentUpon>UserManagerHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UserManagerHandlers\UserManagerHandler1.ashx.cs">
      <DependentUpon>UserManagerHandler1.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\BookmarkFlagHandler.ashx.cs">
      <DependentUpon>BookmarkFlagHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\CallTaggingHandler.ashx.cs">
      <DependentUpon>CallTaggingHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\CustomerDatabaseHandler.ashx.cs">
      <DependentUpon>CustomerDatabaseHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\DownloadFile.ashx.cs">
      <DependentUpon>DownloadFile.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\ExcelExportHandler.ashx.cs">
      <DependentUpon>ExcelExportHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\ExportPlaylistHandler.ashx.cs">
      <DependentUpon>ExportPlaylistHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\ExportToExcelHandler.ashx.cs">
      <DependentUpon>ExportToExcelHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\ADGroupsSyncConfigurationHandler.ashx.cs">
      <DependentUpon>ADGroupsSyncConfigurationHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\ExtraEmailConfigurationHandler.ashx.cs">
      <DependentUpon>ExtraEmailConfigurationHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\GuestHandler.ashx.cs">
      <DependentUpon>GuestHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\IRHandler.ashx.cs">
      <DependentUpon>IRHandler.ashx</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\InspectionHandler.ashx.cs">
      <DependentUpon>InspectionHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\MTRReportHandler.ashx.cs">
      <DependentUpon>MTRReportHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\PlaylistHandler.ashx.cs">
      <DependentUpon>PlaylistHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\RevSignHandler.ashx.cs">
      <DependentUpon>RevSignHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\SearchMediaHandler.ashx.cs">
      <DependentUpon>SearchMediaHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\Text911FileHandler.ashx.cs">
      <DependentUpon>Text911FileHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\TranscriptionHandler.ashx.cs">
      <DependentUpon>TranscriptionHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\TreeViewHandler.ashx.cs">
      <DependentUpon>TreeViewHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\UploadFileHandler.ashx.cs">
      <DependentUpon>UploadFileHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\VoiceRecHandler.ashx.cs">
      <DependentUpon>VoiceRecHandler.ashx</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\MMSHandler.ashx.cs">
      <DependentUpon>MMSHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\VoiceRecHandlers\VRHandler.ashx.cs">
      <DependentUpon>VRHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Inquire\Invitation.aspx.cs">
      <DependentUpon>Invitation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Inquire\Invitation.aspx.designer.cs">
      <DependentUpon>Invitation.aspx</DependentUpon>
    </Compile>
    <Compile Include="InstantRecall\InstantRecall.aspx.cs">
      <DependentUpon>InstantRecall.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InstantRecall\InstantRecall.aspx.designer.cs">
      <DependentUpon>InstantRecall.aspx</DependentUpon>
    </Compile>
    <Compile Include="InstantRecall\IR.aspx.cs">
      <DependentUpon>IR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InstantRecall\IR.aspx.designer.cs">
      <DependentUpon>IR.aspx</DependentUpon>
    </Compile>
    <Compile Include="InstantRecall\IRSlimBlank.aspx.cs">
      <DependentUpon>IRSlimBlank.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InstantRecall\IRSlimBlank.aspx.designer.cs">
      <DependentUpon>IRSlimBlank.aspx</DependentUpon>
    </Compile>
    <Compile Include="Invite\LoadPlaylistForm.aspx.cs">
      <DependentUpon>LoadPlaylistForm.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Invite\LoadPlaylistForm.aspx.designer.cs">
      <DependentUpon>LoadPlaylistForm.aspx</DependentUpon>
    </Compile>
    <Compile Include="Invite\ViewEvent.aspx.cs">
      <DependentUpon>ViewEvent.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Invite\ViewEvent.aspx.designer.cs">
      <DependentUpon>ViewEvent.aspx</DependentUpon>
    </Compile>
    <Compile Include="Invite\ViewVideo.aspx.cs">
      <DependentUpon>ViewVideo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Invite\ViewVideo.aspx.designer.cs">
      <DependentUpon>ViewVideo.aspx</DependentUpon>
    </Compile>
    <Compile Include="IQ3Inspection\CreateTemplate.aspx.cs">
      <DependentUpon>CreateTemplate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IQ3Inspection\CreateTemplate.aspx.designer.cs">
      <DependentUpon>CreateTemplate.aspx</DependentUpon>
    </Compile>
    <Compile Include="IQ3Inspection\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IQ3Inspection\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="IQ3Inspection\EditTemplate.aspx.cs">
      <DependentUpon>EditTemplate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IQ3Inspection\EditTemplate.aspx.designer.cs">
      <DependentUpon>EditTemplate.aspx</DependentUpon>
    </Compile>
    <Compile Include="IQ3Inspection\GraphicMarker.aspx.cs">
      <DependentUpon>GraphicMarker.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IQ3Inspection\GraphicMarker.aspx.designer.cs">
      <DependentUpon>GraphicMarker.aspx</DependentUpon>
    </Compile>
    <Compile Include="IQ3Inspection\CustomFields.aspx.cs">
      <DependentUpon>CustomFields.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IQ3Inspection\CustomFields.aspx.designer.cs">
      <DependentUpon>CustomFields.aspx</DependentUpon>
    </Compile>
    <Compile Include="IQ3Inspection\RVIMessage.aspx.cs">
      <DependentUpon>RVIMessage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IQ3Inspection\RVIMessage.aspx.designer.cs">
      <DependentUpon>RVIMessage.aspx</DependentUpon>
    </Compile>
    <Compile Include="IQ3Inspection\InspectionTitle.aspx.cs">
      <DependentUpon>InspectionTitle.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IQ3Inspection\InspectionTitle.aspx.designer.cs">
      <DependentUpon>InspectionTitle.aspx</DependentUpon>
    </Compile>

    <Compile Include="Iwb\AddJob.ascx.cs">
      <DependentUpon>AddJob.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\AddJob.ascx.designer.cs">
      <DependentUpon>AddJob.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\AddLocation.ascx.cs">
      <DependentUpon>AddLocation.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\AddLocation.ascx.designer.cs">
      <DependentUpon>AddLocation.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\AddOrganization.ascx.cs">
      <DependentUpon>AddOrganization.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\AddOrganization.ascx.designer.cs">
      <DependentUpon>AddOrganization.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\AddTest.ascx.cs">
      <DependentUpon>AddTest.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\AddTest.ascx.designer.cs">
      <DependentUpon>AddTest.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\AddUser.ascx.cs">
      <DependentUpon>AddUser.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\AddUser.ascx.designer.cs">
      <DependentUpon>AddUser.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\AssignJob.ascx.cs">
      <DependentUpon>AssignJob.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\AssignJob.ascx.designer.cs">
      <DependentUpon>AssignJob.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ManageDocuments.aspx.cs">
      <DependentUpon>ManageDocuments.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ManageDocuments.aspx.designer.cs">
      <DependentUpon>ManageDocuments.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ManageJobs.aspx.cs">
      <DependentUpon>ManageJobs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ManageJobs.aspx.designer.cs">
      <DependentUpon>ManageJobs.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ManageLocations.aspx.cs">
      <DependentUpon>ManageLocations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ManageLocations.aspx.designer.cs">
      <DependentUpon>ManageLocations.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ManageOrganization.ascx.cs">
      <DependentUpon>ManageOrganization.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ManageOrganization.ascx.designer.cs">
      <DependentUpon>ManageOrganization.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ManageOrganizations.aspx.cs">
      <DependentUpon>ManageOrganizations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ManageOrganizations.aspx.designer.cs">
      <DependentUpon>ManageOrganizations.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ManageTests.aspx.cs">
      <DependentUpon>ManageTests.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ManageTests.aspx.designer.cs">
      <DependentUpon>ManageTests.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ManageWelders.aspx.cs">
      <DependentUpon>ManageWelders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ManageWelders.aspx.designer.cs">
      <DependentUpon>ManageWelders.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ManageRepository.aspx.cs">
      <DependentUpon>ManageRepository.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ManageRepository.aspx.designer.cs">
      <DependentUpon>ManageRepository.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\MyWPQ.aspx.cs">
      <DependentUpon>MyWPQ.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\MyWPQ.aspx.designer.cs">
      <DependentUpon>MyWPQ.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\RegisterUser.aspx.cs">
      <DependentUpon>RegisterUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\RegisterUser.aspx.designer.cs">
      <DependentUpon>RegisterUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\TestInvitation.ascx.cs">
      <DependentUpon>TestInvitation.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\TestInvitation.ascx.designer.cs">
      <DependentUpon>TestInvitation.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\UserJobs.ascx.cs">
      <DependentUpon>UserJobs.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\UserJobs.ascx.designer.cs">
      <DependentUpon>UserJobs.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ViewJobApplicants.ascx.cs">
      <DependentUpon>ViewJobApplicants.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ViewJobApplicants.ascx.designer.cs">
      <DependentUpon>ViewJobApplicants.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ViewJobs.aspx.cs">
      <DependentUpon>ViewJobs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ViewJobs.aspx.designer.cs">
      <DependentUpon>ViewJobs.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ViewLocations.ascx.cs">
      <DependentUpon>ViewLocations.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ViewLocations.ascx.designer.cs">
      <DependentUpon>ViewLocations.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ViewTestAttendees.ascx.cs">
      <DependentUpon>ViewTestAttendees.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ViewTestAttendees.ascx.designer.cs">
      <DependentUpon>ViewTestAttendees.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\ViewTests.aspx.cs">
      <DependentUpon>ViewTests.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\ViewTests.aspx.designer.cs">
      <DependentUpon>ViewTests.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\WorkHistory.ascx.cs">
      <DependentUpon>WorkHistory.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\WorkHistory.ascx.designer.cs">
      <DependentUpon>WorkHistory.ascx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\WorkHistory.aspx.cs">
      <DependentUpon>WorkHistory.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\WorkHistory.aspx.designer.cs">
      <DependentUpon>WorkHistory.aspx</DependentUpon>
    </Compile>
    <Compile Include="Iwb\WPQForm.aspx.cs">
      <DependentUpon>WPQForm.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Iwb\WPQForm.aspx.designer.cs">
      <DependentUpon>WPQForm.aspx</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\2.0\2Columns.Master.cs">
      <DependentUpon>2Columns.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\2.0\2Columns.Master.designer.cs">
      <DependentUpon>2Columns.Master</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\Professional\2Columns.Master.cs">
      <DependentUpon>2Columns.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\Professional\2Columns.Master.designer.cs">
      <DependentUpon>2Columns.Master</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\Professional\1Columns.Master.cs">
      <DependentUpon>1Columns.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\Professional\1Columns.Master.designer.cs">
      <DependentUpon>1Columns.Master</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\Professional\2ColumnsEvent.Master.cs">
      <DependentUpon>2ColumnsEvent.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\Professional\2ColumnsEvent.Master.designer.cs">
      <DependentUpon>2ColumnsEvent.Master</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\Professional\3Columns.Master.cs">
      <DependentUpon>3Columns.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\Professional\3Columns.Master.designer.cs">
      <DependentUpon>3Columns.Master</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\Professional\AgentQA.Master.cs">
      <DependentUpon>AgentQA.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\Professional\AgentQA.Master.designer.cs">
      <DependentUpon>AgentQA.Master</DependentUpon>
    </Compile>
    <Compile Include="MasterPages\Professional\NestedQA.master.cs">
      <DependentUpon>NestedQA.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPages\Professional\NestedQA.master.designer.cs">
      <DependentUpon>NestedQA.master</DependentUpon>
    </Compile>
    <Compile Include="OTPLogin.aspx.cs">
      <DependentUpon>OTPLogin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="OTPLogin.aspx.designer.cs">
      <DependentUpon>OTPLogin.aspx</DependentUpon>
    </Compile>
    <Compile Include="RemoteInspection\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RemoteInspection\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Monitor\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Monitor\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Monitor\EventMonitor.aspx.cs">
      <DependentUpon>EventMonitor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Monitor\EventMonitor.aspx.designer.cs">
      <DependentUpon>EventMonitor.aspx</DependentUpon>
    </Compile>
    <Compile Include="Invite\ViewPlaylist.aspx.cs">
      <DependentUpon>ViewPlaylist.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Invite\ViewPlaylist.aspx.designer.cs">
      <DependentUpon>ViewPlaylist.aspx</DependentUpon>
    </Compile>
    <Compile Include="Playlist\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Playlist\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Playlist\LoadPlaylistForm.aspx.cs">
      <DependentUpon>LoadPlaylistForm.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Playlist\LoadPlaylistForm.aspx.designer.cs">
      <DependentUpon>LoadPlaylistForm.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="RemoteInspection\InspectionMonitor.aspx.cs">
      <DependentUpon>InspectionMonitor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RemoteInspection\InspectionMonitor.aspx.designer.cs">
      <DependentUpon>InspectionMonitor.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ActivityReport.aspx.cs">
      <DependentUpon>ActivityReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ActivityReport.aspx.designer.cs">
      <DependentUpon>ActivityReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\AdvanceReport.aspx.cs">
      <DependentUpon>AdvanceReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\AdvanceReport.aspx.designer.cs">
      <DependentUpon>AdvanceReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CallAuditReport.aspx.cs">
      <DependentUpon>CallAuditReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CallAuditReport.aspx.designer.cs">
      <DependentUpon>CallAuditReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\EvaluationReport.aspx.cs">
      <DependentUpon>EvaluationReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\EvaluationReport.aspx.designer.cs">
      <DependentUpon>EvaluationReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ContractReport.aspx.cs">
      <DependentUpon>ContractReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ContractReport.aspx.designer.cs">
      <DependentUpon>ContractReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MMSReports.aspx.cs">
      <DependentUpon>MMSReports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MMSReports.aspx.designer.cs">
      <DependentUpon>MMSReports.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ScheduleReport.aspx.cs">
      <DependentUpon>ScheduleReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ScheduleReport.aspx.designer.cs">
      <DependentUpon>ScheduleReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MapReport.aspx.cs">
      <DependentUpon>MapReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MapReport.aspx.designer.cs">
      <DependentUpon>MapReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\SoftwareAudit.aspx.cs">
      <DependentUpon>SoftwareAudit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\SoftwareAudit.aspx.designer.cs">
      <DependentUpon>SoftwareAudit.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\TenantIQ3UsageReport.aspx.cs">
      <DependentUpon>TenantIQ3UsageReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\TenantIQ3UsageReport.aspx.designer.cs">
      <DependentUpon>TenantIQ3UsageReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="ScheduleEvent\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ScheduleEvent\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Search\LoadPlaylistForm.aspx.cs">
      <DependentUpon>LoadPlaylistForm.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Search\LoadPlaylistForm.aspx.designer.cs">
      <DependentUpon>LoadPlaylistForm.aspx</DependentUpon>
    </Compile>
    <Compile Include="Search\MultiChannelPlayback.aspx.cs">
      <DependentUpon>MultiChannelPlayback.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Search\MultiChannelPlayback.aspx.designer.cs">
      <DependentUpon>MultiChannelPlayback.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\ADGroupsSyncConfiguration.aspx.cs">
      <DependentUpon>ADGroupsSyncConfiguration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\ADGroupsSyncConfiguration.aspx.designer.cs">
      <DependentUpon>ADGroupsSyncConfiguration.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\ExtraMailConfiguration.aspx.cs">
      <DependentUpon>ExtraMailConfiguration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\ExtraMailConfiguration.aspx.designer.cs">
      <DependentUpon>ExtraMailConfiguration.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\IQ3UsageReporting.aspx.cs">
      <DependentUpon>IQ3UsageReporting.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\IQ3UsageReporting.aspx.designer.cs">
      <DependentUpon>IQ3UsageReporting.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\ManageColumnSettings.aspx.cs">
      <DependentUpon>ManageColumnSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\ManageColumnSettings.aspx.designer.cs">
      <DependentUpon>ManageColumnSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\ManageSettings.aspx.cs">
      <DependentUpon>ManageSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\ManageSettings.aspx.designer.cs">
      <DependentUpon>ManageSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Search\SearchDefault.aspx.cs">
      <DependentUpon>SearchDefault.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Search\SearchDefault.aspx.designer.cs">
      <DependentUpon>SearchDefault.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\SiteAdmin.Master.cs">
      <DependentUpon>SiteAdmin.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\SiteAdmin.Master.designer.cs">
      <DependentUpon>SiteAdmin.Master</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\ColumnSettings.aspx.cs">
      <DependentUpon>ColumnSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\ColumnSettings.aspx.designer.cs">
      <DependentUpon>ColumnSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\TeamsChannelConfiguration.aspx.cs">
      <DependentUpon>TeamsChannelConfiguration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\TeamsChannelConfiguration.aspx.designer.cs">
      <DependentUpon>TeamsChannelConfiguration.aspx</DependentUpon>
    </Compile>
    <Compile Include="SiteAdministration\TenantIQ3UsageReport.aspx.cs">
      <DependentUpon>TenantIQ3UsageReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SiteAdministration\TenantIQ3UsageReport.aspx.designer.cs">
      <DependentUpon>TenantIQ3UsageReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="Surveys\CreateSurvey.aspx.cs">
      <DependentUpon>CreateSurvey.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surveys\CreateSurvey.aspx.designer.cs">
      <DependentUpon>CreateSurvey.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surveys\EditSurvey.aspx.cs">
      <DependentUpon>EditSurvey.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surveys\EditSurvey.aspx.designer.cs">
      <DependentUpon>EditSurvey.aspx</DependentUpon>
    </Compile>
    <Compile Include="Surveys\SurveyList.aspx.cs">
      <DependentUpon>SurveyList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Surveys\SurveyList.aspx.designer.cs">
      <DependentUpon>SurveyList.aspx</DependentUpon>
    </Compile>
    <Compile Include="SystemHubs\ChannelStatusHub.cs" />
    <Compile Include="SystemHubs\InquireHub.cs" />
    <Compile Include="SystemHubs\IQ3VirtualInspectionHub.cs" />
    <Compile Include="SystemHubs\RevViewHub.cs" />
    <Compile Include="SystemHubs\Startup.cs" />
    <Compile Include="UserControls\BaseControl.ascx.cs">
      <DependentUpon>BaseControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\BaseControl.ascx.designer.cs">
      <DependentUpon>BaseControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CommonControls\AutoCompleteControl.ascx.cs">
      <DependentUpon>AutoCompleteControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CommonControls\AutoCompleteControl.ascx.designer.cs">
      <DependentUpon>AutoCompleteControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucCommonPageSideBarWrapper.ascx.cs">
      <DependentUpon>ucCommonPageSideBarWrapper.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucCommonPageSideBarWrapper.ascx.designer.cs">
      <DependentUpon>ucCommonPageSideBarWrapper.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucCommonPageTopBarWrapper.ascx.cs">
      <DependentUpon>ucCommonPageTopBarWrapper.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucCommonPageTopBarWrapper.ascx.designer.cs">
      <DependentUpon>ucCommonPageTopBarWrapper.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucCommonPageWrapper.ascx.cs">
      <DependentUpon>ucCommonPageWrapper.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucCommonPageWrapper.ascx.designer.cs">
      <DependentUpon>ucCommonPageWrapper.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucHighChart.ascx.cs">
      <DependentUpon>ucHighChart.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucHighChart.ascx.designer.cs">
      <DependentUpon>ucHighChart.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucPagerControl.ascx.cs">
      <DependentUpon>ucPagerControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CommonControls\ucPagerControl.ascx.designer.cs">
      <DependentUpon>ucPagerControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CustomerDBControls\ucEventSummaryIQ3Asset.ascx.cs">
      <DependentUpon>ucEventSummaryIQ3Asset.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CustomerDBControls\ucEventSummaryIQ3Asset.ascx.designer.cs">
      <DependentUpon>ucEventSummaryIQ3Asset.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CustomerDBControls\ucAuditHistoryIQ3Asset.ascx.cs">
      <DependentUpon>ucAuditHistoryIQ3Asset.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CustomerDBControls\ucAuditHistoryIQ3Asset.ascx.designer.cs">
      <DependentUpon>ucAuditHistoryIQ3Asset.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\CustomerDBControls\ucViewIQ3Asset.ascx.cs">
      <DependentUpon>ucViewIQ3Asset.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\CustomerDBControls\ucViewIQ3Asset.ascx.designer.cs">
      <DependentUpon>ucViewIQ3Asset.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\EmailControls\EmailandPhoneControl.ascx.cs">
      <DependentUpon>EmailandPhoneControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\EmailControls\EmailandPhoneControl.ascx.designer.cs">
      <DependentUpon>EmailandPhoneControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucAcceptInvitation.ascx.cs">
      <DependentUpon>ucAcceptInvitation.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucAcceptInvitation.ascx.designer.cs">
      <DependentUpon>ucAcceptInvitation.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucAssignUsers.ascx.cs">
      <DependentUpon>ucAssignUsers.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucAssignUsers.ascx.designer.cs">
      <DependentUpon>ucAssignUsers.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucCustomMarker.ascx.cs">
      <DependentUpon>ucCustomMarker.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucCustomMarker.ascx.designer.cs">
      <DependentUpon>ucCustomMarker.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucEventNotes.ascx.cs">
      <DependentUpon>ucEventNotes.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucEventNotes.ascx.designer.cs">
      <DependentUpon>ucEventNotes.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucFileVideoAddBookmark.ascx.cs">
      <DependentUpon>ucFileVideoAddBookmark.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucFileVideoAddBookmark.ascx.designer.cs">
      <DependentUpon>ucFileVideoAddBookmark.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucFileVideoEditBookmark.ascx.cs">
      <DependentUpon>ucFileVideoEditBookmark.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucFileVideoEditBookmark.ascx.designer.cs">
      <DependentUpon>ucFileVideoEditBookmark.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucFileVideoNotes.ascx.cs">
      <DependentUpon>ucFileVideoNotes.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucFileVideoNotes.ascx.designer.cs">
      <DependentUpon>ucFileVideoNotes.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucInspectionTemplateMarker.ascx.cs">
      <DependentUpon>ucInspectionTemplateMarker.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucInspectionTemplateMarker.ascx.designer.cs">
      <DependentUpon>ucInspectionTemplateMarker.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucMultiLevelMarker.ascx.cs">
      <DependentUpon>ucMultiLevelMarker.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucMultiLevelMarker.ascx.designer.cs">
      <DependentUpon>ucMultiLevelMarker.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucNewGroup.ascx.cs">
      <DependentUpon>ucNewGroup.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucNewGroup.ascx.designer.cs">
      <DependentUpon>ucNewGroup.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucPictureEvent.ascx.cs">
      <DependentUpon>ucPictureEvent.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InquireControls\ucPictureEvent.ascx.designer.cs">
      <DependentUpon>ucPictureEvent.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucGraphicMarkerPdf.ascx.cs">
      <DependentUpon>ucGraphicMarkerPdf.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucGraphicMarkerPdf.ascx.designer.cs">
      <DependentUpon>ucGraphicMarkerPdf.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucInspectionControl.ascx.cs">
      <DependentUpon>ucInspectionControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucInspectionControl.ascx.designer.cs">
      <DependentUpon>ucInspectionControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucInspectionSection.ascx.cs">
      <DependentUpon>ucInspectionSection.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucInspectionSection.ascx.designer.cs">
      <DependentUpon>ucInspectionSection.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucInspectionTitle.ascx.cs">
      <DependentUpon>ucInspectionTitle.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucInspectionTitle.ascx.designer.cs">
      <DependentUpon>ucInspectionTitle.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucAutoReportRecipientControl.ascx.cs">
      <DependentUpon>ucAutoReportRecipientControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucAutoReportRecipientControl.ascx.designer.cs">
      <DependentUpon>ucAutoReportRecipientControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucTextFieldMarker.ascx.cs">
      <DependentUpon>ucTextFieldMarker.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucTextFieldMarker.ascx.designer.cs">
      <DependentUpon>ucTextFieldMarker.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucSingleMultiSelectMarker.ascx.cs">
      <DependentUpon>ucSingleMultiSelectMarker.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucSingleMultiSelectMarker.ascx.designer.cs">
      <DependentUpon>ucSingleMultiSelectMarker.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucGraphicMarker.ascx.cs">
      <DependentUpon>ucGraphicMarker.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucGraphicMarker.ascx.designer.cs">
      <DependentUpon>ucGraphicMarker.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucTextMarker.ascx.cs">
      <DependentUpon>ucTextMarker.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\InspectionControls\ucTextMarker.ascx.designer.cs">
      <DependentUpon>ucTextMarker.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\MonitorControls\ucLiveECG.ascx.cs">
      <DependentUpon>ucLiveECG.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\MonitorControls\ucLiveECG.ascx.designer.cs">
      <DependentUpon>ucLiveECG.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayer.ascx.cs">
      <DependentUpon>ucFilePlayer.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayer.ascx.designer.cs">
      <DependentUpon>ucFilePlayer.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerAddUserFile.ascx.cs">
      <DependentUpon>ucFilePlayerAddUserFile.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerAddUserFile.ascx.designer.cs">
      <DependentUpon>ucFilePlayerAddUserFile.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerBookmark.ascx.cs">
      <DependentUpon>ucFilePlayerBookmark.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerBookmark.ascx.designer.cs">
      <DependentUpon>ucFilePlayerBookmark.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditAnnotationRecording.ascx.cs">
      <DependentUpon>ucFilePlayerEditAnnotationRecording.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditAnnotationRecording.ascx.designer.cs">
      <DependentUpon>ucFilePlayerEditAnnotationRecording.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditCall.ascx.cs">
      <DependentUpon>ucFilePlayerEditCall.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditCall.ascx.designer.cs">
      <DependentUpon>ucFilePlayerEditCall.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditRecordSettings.ascx.cs">
      <DependentUpon>ucFilePlayerEditRecordSettings.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEditRecordSettings.ascx.designer.cs">
      <DependentUpon>ucFilePlayerEditRecordSettings.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEmail.ascx.cs">
      <DependentUpon>ucFilePlayerEmail.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerEmail.ascx.designer.cs">
      <DependentUpon>ucFilePlayerEmail.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerSaveCalls.ascx.cs">
      <DependentUpon>ucFilePlayerSaveCalls.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerSaveCalls.ascx.designer.cs">
      <DependentUpon>ucFilePlayerSaveCalls.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerSettings.ascx.cs">
      <DependentUpon>ucFilePlayerSettings.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerSettings.ascx.designer.cs">
      <DependentUpon>ucFilePlayerSettings.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerVideoPlayer.ascx.cs">
      <DependentUpon>ucFilePlayerVideoPlayer.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFilePlayerVideoPlayer.ascx.designer.cs">
      <DependentUpon>ucFilePlayerVideoPlayer.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFileVideoPlayer.ascx.cs">
      <DependentUpon>ucFileVideoPlayer.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFileVideoPlayer.ascx.designer.cs">
      <DependentUpon>ucFileVideoPlayer.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFileVideoPlayer_Quad.ascx.cs">
      <DependentUpon>ucFileVideoPlayer_Quad.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucFileVideoPlayer_Quad.ascx.designer.cs">
      <DependentUpon>ucFileVideoPlayer_Quad.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucMultipleCallsEdit.ascx.cs">
      <DependentUpon>ucMultipleCallsEdit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\FilePlayer\ucMultipleCallsEdit.ascx.designer.cs">
      <DependentUpon>ucMultipleCallsEdit.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\LivePlayer\ucLivePlayer.ascx.cs">
      <DependentUpon>ucLivePlayer.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\LivePlayer\ucLivePlayer.ascx.designer.cs">
      <DependentUpon>ucLivePlayer.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\LivePlayer\ucLivePlayerBookmark.ascx.cs">
      <DependentUpon>ucLivePlayerBookmark.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\LivePlayer\ucLivePlayerBookmark.ascx.designer.cs">
      <DependentUpon>ucLivePlayerBookmark.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\LivePlayer\ucLiveMonitorSettings.ascx.cs">
      <DependentUpon>ucLiveMonitorSettings.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\LivePlayer\ucLiveMonitorSettings.ascx.designer.cs">
      <DependentUpon>ucLiveMonitorSettings.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\PlayerControls\LivePlayer\ucLivePlayerSettings.ascx.cs">
      <DependentUpon>ucLivePlayerSettings.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\PlayerControls\LivePlayer\ucLivePlayerSettings.ascx.designer.cs">
      <DependentUpon>ucLivePlayerSettings.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\QAEControls\ucEvaluateCall.ascx.cs">
      <DependentUpon>ucEvaluateCall.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\QAEControls\ucEvaluateCall.ascx.designer.cs">
      <DependentUpon>ucEvaluateCall.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\QAEControls\ucSearchCalls.ascx.cs">
      <DependentUpon>ucSearchCalls.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\QAEControls\ucSearchCalls.ascx.designer.cs">
      <DependentUpon>ucSearchCalls.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\QAEControls\ucSearchQAEvaluations.ascx.cs">
      <DependentUpon>ucSearchQAEvaluations.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\QAEControls\ucSearchQAEvaluations.ascx.designer.cs">
      <DependentUpon>ucSearchQAEvaluations.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\EmailControls\ucEmailReport.ascx.cs">
      <DependentUpon>ucEmailReport.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\EmailControls\ucEmailReport.ascx.designer.cs">
      <DependentUpon>ucEmailReport.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\ScheduleReportControls\ucAddScheduleReportButton.ascx.cs">
      <DependentUpon>ucAddScheduleReportButton.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\ScheduleReportControls\ucAddScheduleReportButton.ascx.designer.cs">
      <DependentUpon>ucAddScheduleReportButton.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\ScheduleReportControls\ucAddScheduleReportDialog.ascx.cs">
      <DependentUpon>ucAddScheduleReportDialog.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\ScheduleReportControls\ucAddScheduleReportDialog.ascx.designer.cs">
      <DependentUpon>ucAddScheduleReportDialog.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\ScheduleReportControls\ucEditScheduleReportDialog.ascx.cs">
      <DependentUpon>ucEditScheduleReportDialog.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\ScheduleReportControls\ucEditScheduleReportDialog.ascx.designer.cs">
      <DependentUpon>ucEditScheduleReportDialog.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\ScheduleReportControls\ucScheduleReportDialog.ascx.cs">
      <DependentUpon>ucScheduleReportDialog.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\ScheduleReportControls\ucScheduleReportDialog.ascx.designer.cs">
      <DependentUpon>ucScheduleReportDialog.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\SharedReportControls\ucAddEditSharedReportDialog.ascx.cs">
      <DependentUpon>ucAddEditSharedReportDialog.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\SharedReportControls\ucAddEditSharedReportDialog.ascx.designer.cs">
      <DependentUpon>ucAddEditSharedReportDialog.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\SharedReportControls\ucEditSharedReportDialog.ascx.cs">
      <DependentUpon>ucEditSharedReportDialog.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\SharedReportControls\ucEditSharedReportDialog.ascx.designer.cs">
      <DependentUpon>ucEditSharedReportDialog.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\SharedReportControls\ucSharedReportButton.ascx.cs">
      <DependentUpon>ucSharedReportButton.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\SharedReportControls\ucSharedReportButton.ascx.designer.cs">
      <DependentUpon>ucSharedReportButton.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\SharedReportControls\ucSharedReportDialog.ascx.cs">
      <DependentUpon>ucSharedReportDialog.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\SharedReportControls\ucSharedReportDialog.ascx.designer.cs">
      <DependentUpon>ucSharedReportDialog.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\ucInspectionReportDialog.ascx.cs">
      <DependentUpon>ucInspectionReportDialog.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\ucInspectionReportDialog.ascx.designer.cs">
      <DependentUpon>ucInspectionReportDialog.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\ReportControls\ucReportPageSubSideBarWrapper.ascx.cs">
      <DependentUpon>ucReportPageSubSideBarWrapper.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\ReportControls\ucReportPageSubSideBarWrapper.ascx.designer.cs">
      <DependentUpon>ucReportPageSubSideBarWrapper.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\RevSignControls\ucSignOffControl.ascx.cs">
      <DependentUpon>ucSignOffControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\RevSignControls\ucSignOffControl.ascx.designer.cs">
      <DependentUpon>ucSignOffControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\SurveyControls\ucSingleMultiSelectQuestion.ascx.cs">
      <DependentUpon>ucSingleMultiSelectQuestion.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\SurveyControls\ucSingleMultiSelectQuestion.ascx.designer.cs">
      <DependentUpon>ucSingleMultiSelectQuestion.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\SurveyControls\ucSurveyControl.ascx.cs">
      <DependentUpon>ucSurveyControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\SurveyControls\ucSurveyControl.ascx.designer.cs">
      <DependentUpon>ucSurveyControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\SurveyControls\ucSurveySection.ascx.cs">
      <DependentUpon>ucSurveySection.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\SurveyControls\ucSurveySection.ascx.designer.cs">
      <DependentUpon>ucSurveySection.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\SurveyControls\ucTextQuestion.ascx.cs">
      <DependentUpon>ucTextQuestion.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\SurveyControls\ucTextQuestion.ascx.designer.cs">
      <DependentUpon>ucTextQuestion.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAcceptInvitation.ascx.cs">
      <DependentUpon>ucAcceptInvitation.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAcceptInvitation.ascx.designer.cs">
      <DependentUpon>ucAcceptInvitation.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAssignChannels.ascx.cs">
      <DependentUpon>ucAssignChannels.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAssignChannels.ascx.designer.cs">
      <DependentUpon>ucAssignChannels.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAssignEnterpriseGroup.ascx.cs">
      <DependentUpon>ucAssignEnterpriseGroup.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAssignEnterpriseGroup.ascx.designer.cs">
      <DependentUpon>ucAssignEnterpriseGroup.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAssignGroup.ascx.cs">
      <DependentUpon>ucAssignGroup.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAssignGroup.ascx.designer.cs">
      <DependentUpon>ucAssignGroup.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAssignPermissions.ascx.cs">
      <DependentUpon>ucAssignPermissions.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucAssignPermissions.ascx.designer.cs">
      <DependentUpon>ucAssignPermissions.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucRecorderTree.ascx.cs">
      <DependentUpon>ucRecorderTree.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucRecorderTree.ascx.designer.cs">
      <DependentUpon>ucRecorderTree.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucChannel.ascx.cs">
      <DependentUpon>ucChannel.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucChannel.ascx.designer.cs">
      <DependentUpon>ucChannel.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucSiteChannel.ascx.cs">
      <DependentUpon>ucSiteChannel.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucSiteChannel.ascx.designer.cs">
      <DependentUpon>ucSiteChannel.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeAgent.ascx.cs">
      <DependentUpon>ucTreeAgent.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeAgent.ascx.designer.cs">
      <DependentUpon>ucTreeAgent.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucInviteInqUser.ascx.cs">
      <DependentUpon>ucInviteInqUser.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucInviteInqUser.ascx.designer.cs">
      <DependentUpon>ucInviteInqUser.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTree.ascx.cs">
      <DependentUpon>ucTree.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTree.ascx.designer.cs">
      <DependentUpon>ucTree.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeAudio.ascx.cs">
      <DependentUpon>ucTreeAudio.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeAudio.ascx.designer.cs">
      <DependentUpon>ucTreeAudio.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeAudioEC.ascx.cs">
      <DependentUpon>ucTreeAudioEC.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeAudioEC.ascx.designer.cs">
      <DependentUpon>ucTreeAudioEC.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeIQ3.ascx.cs">
      <DependentUpon>ucTreeIQ3.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeIQ3.ascx.designer.cs">
      <DependentUpon>ucTreeIQ3.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeMD.ascx.cs">
      <DependentUpon>ucTreeMD.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeMD.ascx.designer.cs">
      <DependentUpon>ucTreeMD.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeInquire.ascx.cs">
      <DependentUpon>ucTreeInquire.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeInquire.ascx.designer.cs">
      <DependentUpon>ucTreeInquire.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeReport.ascx.cs">
      <DependentUpon>ucTreeReport.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucTreeReport.ascx.designer.cs">
      <DependentUpon>ucTreeReport.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\UMControls\ucUserForm.ascx.cs">
      <DependentUpon>ucUserForm.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\UMControls\ucUserForm.ascx.designer.cs">
      <DependentUpon>ucUserForm.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\VRControls\ucIRCalls.ascx.cs">
      <DependentUpon>ucIRCalls.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\VRControls\ucIRCalls.ascx.designer.cs">
      <DependentUpon>ucIRCalls.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\VRControls\ucMap.ascx.cs">
      <DependentUpon>ucMap.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\VRControls\ucMap.ascx.designer.cs">
      <DependentUpon>ucMap.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\VRControls\ucMultiChannelPlayback.ascx.cs">
      <DependentUpon>ucMultiChannelPlayback.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\VRControls\ucMultiChannelPlayback.ascx.designer.cs">
      <DependentUpon>ucMultiChannelPlayback.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\VRControls\ucPlayList.ascx.cs">
      <DependentUpon>ucPlayList.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\VRControls\ucPlayList.ascx.designer.cs">
      <DependentUpon>ucPlayList.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\VRControls\ucSiteSelector.ascx.cs">
      <DependentUpon>ucSiteSelector.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\VRControls\ucSiteSelector.ascx.designer.cs">
      <DependentUpon>ucSiteSelector.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\BookmarkFlag.aspx.cs">
      <DependentUpon>BookmarkFlag.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\BookmarkFlag.aspx.designer.cs">
      <DependentUpon>BookmarkFlag.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\CallTagging.aspx.cs">
      <DependentUpon>CallTagging.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\CallTagging.aspx.designer.cs">
      <DependentUpon>CallTagging.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\AutoReportSettings.aspx.cs">
      <DependentUpon>AutoReportSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\AutoReportSettings.aspx.designer.cs">
      <DependentUpon>AutoReportSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\ManageSign.aspx.cs">
      <DependentUpon>ManageSign.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\ManageSign.aspx.designer.cs">
      <DependentUpon>ManageSign.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\CustomSettings.aspx.cs">
      <DependentUpon>CustomSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\CustomSettings.aspx.designer.cs">
      <DependentUpon>CustomSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\ManageRoles.aspx.cs">
      <DependentUpon>ManageRoles.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\ManageRoles.aspx.designer.cs">
      <DependentUpon>ManageRoles.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\EditUseremailPassword.aspx.cs">
      <DependentUpon>EditUseremailPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\EditUseremailPassword.aspx.designer.cs">
      <DependentUpon>EditUseremailPassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\EnterpriseUser.aspx.cs">
      <DependentUpon>EnterpriseUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\EnterpriseUser.aspx.designer.cs">
      <DependentUpon>EnterpriseUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\Invitation.aspx.cs">
      <DependentUpon>Invitation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\Invitation.aspx.designer.cs">
      <DependentUpon>Invitation.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\InqGroupManager.aspx.cs">
      <DependentUpon>InqGroupManager.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\InqGroupManager.aspx.designer.cs">
      <DependentUpon>InqGroupManager.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\ManageGroups.aspx.cs">
      <DependentUpon>ManageGroups.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\ManageGroups.aspx.designer.cs">
      <DependentUpon>ManageGroups.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\RecoverPassword.aspx.cs">
      <DependentUpon>RecoverPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\RecoverPassword.aspx.designer.cs">
      <DependentUpon>RecoverPassword.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserManager\ResetPassword.aspx.cs">
      <DependentUpon>ResetPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserManager\ResetPassword.aspx.designer.cs">
      <DependentUpon>ResetPassword.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="asset\js\" />
    <Folder Include="Classes\Base\" />
    <Folder Include="ExportPlaylist\ListviewSL\html\mediaFiles\" />
    <Folder Include="ExportPlaylist\TimelineView\html\bin\" />
    <Folder Include="ExportPlaylist\TimelineView\html\mediaFiles\" />
    <Folder Include="ExportReport\StandardReport\html\MediaFiles\" />
    <Folder Include="ExportReport\VesselReport\html\MediaFiles\" />
    <Folder Include="ExportTemplates\package\assets\media\" />
    <Folder Include="ExportTemplates\package\paperclip\" />
    <Folder Include="MasterPages\Classic\" />
    <Folder Include="PaperClips\Tenant_1\1000\169\" />
    <Folder Include="SystemUploadedFiles\InspectionReports\" />
    <Folder Include="SystemUploadedFiles\InspectionTemplate\" />
    <Folder Include="SystemUploadedFiles\Iwb\" />
    <Folder Include="SystemUploadedFiles\Playlist\" />
    <Folder Include="SystemUploadedFiles\SavedCalls\" />
    <Folder Include="SystemUploadedFiles\SurveyForms\" />
    <Folder Include="SystemUploadedFiles\UserImages\" />
    <Folder Include="SystemUploadedFiles\VesselReports\" />
    <Folder Include="Uploads\TempReportLogo\" />
    <Folder Include="Videos\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="MasterPages\Professional\2Columns.Master" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="MasterPages\Professional\1Columns.Master" />
    <Content Include="Handlers\Common\AutoCompleteControl.ashx" />
    <Content Include="Handlers\Common\Suggesions.ashx" />
    <Content Include="Handlers\EvaluationHandlers\EvaluationHandler.ashx" />
    <Content Include="Handlers\HandlerBase.ashx" />
    <Content Include="Handlers\SurveyHandlers\SurveyHandler.ashx" />
    <Content Include="Handlers\UserManagerHandlers\UserManagerHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\ExportToExcelHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\UploadFileHandler.ashx" />
    <Content Include="MasterPages\Professional\3Columns.Master" />
    <Content Include="MasterPages\Professional\NestedQA.master" />
    <None Include="assets\js\uploadify\uploadify.fla" />
    <Content Include="Handlers\Common\UtilityHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\DownloadFile.ashx" />
    <Content Include="Handlers\UserManagerHandlers\UploadUserImageHandler.ashx" />
    <Content Include="SiteAdministration\SiteAdmin.Master" />
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Handlers\VoiceRecHandlers\PlaylistHandler.ashx" />
    <Content Include="Handlers\UserManagerHandlers\UserManagerHandler1.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\VRHandler.ashx" />
    <Content Include="Handlers\Common\CommonHandler.ashx" />
    <Content Include="Handlers\BaseHandler.ashx" />
    <Content Include="Handlers\VoiceRecHandlers\ExcelExportHandler.ashx" />
    <Content Include="Site.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Handlers\VoiceRecHandlers\TranscriptionHandler.ashx" />
    <Content Include="Handlers\Common\EmailHandler.ashx" />
    <Content Include="MasterPages\Professional\AgentQA.Master" />
    <Content Include="Handlers\InquireHandlers\InquireHandler.ashx" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByMonthAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByMonthCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByMonthTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByWeekDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByWeekDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\BarChartByWeekDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\CallDetail.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Column\CallDetailByDay.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Columner\ColChartByAgent.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Columner\ColChartByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Columner\ColChartByScorer.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Excel\ExcelByAgent.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Excel\ExcelByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Excel\ExcelByAgentMonthlyScore.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Excel\ExcelByAgentScore.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Excel\ExcelByEvaluatorScoring.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Excel\ExcelByEvaluatorScoringDetails.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Excel\ExcelByScorer.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Pie\PieByAgent.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Tabular\BarChartByAgent.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Tabular\BarChartByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Evaluation\Tabular\BarChartByScorer.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\CallDetail.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByDay.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByHour.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByHourAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByHourCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByHourTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByMonth.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByMonthAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByMonthCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByMonthTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByMonthWeekDay.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByMonthWeekDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByMonthWeekDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByMonthWeekDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByWeekDay.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByWeekDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByWeekDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Excel\ExcelByWeekDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\CallDetailByDay.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByMonthAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByMonthCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByMonthTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByWeekDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByWeekDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\AdvanceReports\Pie\PieChartByWeekDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Columner\ColChartByAgent.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Columner\ColChartByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Columner\ColChartByScorer.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Excel\ExcelByAgent.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Excel\ExcelByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Excel\ExcelByAgentMonthlyScore.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Excel\ExcelByAgentScore.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Excel\ExcelByEvaluatorScoring.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Excel\ExcelByEvaluatorScoringDetails.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Excel\ExcelByScorer.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Pie\PieByAgent.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Tabular\BarChartByAgent.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Tabular\BarChartByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\EvaluationReports\Tabular\BarChartByScorer.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByMonthAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByMonthCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByMonthTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByWeekDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByWeekDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\BarChartByWeekDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\CallDetail.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Column\CallDetailByDay.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\CallDetail.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByDay.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByHour.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByHourAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByHourCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByHourTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByMonth.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByMonthAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByMonthCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByMonthTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByMonthWeekDay.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByMonthWeekDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByMonthWeekDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByMonthWeekDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByWeekDay.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByWeekDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByWeekDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Excel\ExcelByWeekDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\CallDetailByDay.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByMonthAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByMonthCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByMonthTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByWeekDayAvgCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByWeekDayCallDuration.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Advance\Pie\PieChartByWeekDayTotalCall.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Columner\ColChartByAgent.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Columner\ColChartByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Columner\ColChartByScorer.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Excel\ExcelByAgent.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Excel\ExcelByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Excel\ExcelByAgentMonthlyScore.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Excel\ExcelByAgentScore.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Excel\ExcelByEvaluatorScoring.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Excel\ExcelByEvaluatorScoringDetails.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Excel\ExcelByScorer.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Pie\PieByAgent.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Tabular\BarChartByAgent.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Tabular\BarChartByAgentGroup.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Evaluation\Tabular\BarChartByScorer.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\PBX911\PBX911ByChannel.rdlc" />
    <EmbeddedResource Include="Reports\RdlcDesigner\Standard\CallAudit.rdlc" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="Shell32">
      <Guid>{50A7E9B0-70EF-11D1-B75A-00A0C90564FE}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="WMPLib">
      <Guid>{6BF52A50-394A-11D3-B153-00C04F79FAA6}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <Page Include="ExportPlaylist\ListviewSL\html\css\images\player\Styles.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ExportPlaylist\TimelineView\html\css\images\player\Styles.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RevCord.BusinessLogic\RevCord.BusinessLogic.csproj">
      <Project>{df816774-fb1d-428d-96de-8f6b704bedef}</Project>
      <Name>RevCord.BusinessLogic</Name>
    </ProjectReference>
    <ProjectReference Include="..\RevCord.DataAccess\RevCord.DataAccess.csproj">
      <Project>{1aabeaa7-6e73-417e-b181-1825d833b2d9}</Project>
      <Name>RevCord.DataAccess</Name>
    </ProjectReference>
    <ProjectReference Include="..\RevCord.DataContracts\RevCord.DataContracts.csproj">
      <Project>{84c8cf04-1c65-4c93-8391-df8d74bb0b96}</Project>
      <Name>RevCord.DataContracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\RevCord.ServiceContracts\RevCord.ServiceContracts.csproj">
      <Project>{42aa6686-6def-4eb6-be68-a2302ec493cb}</Project>
      <Name>RevCord.ServiceContracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\RevCord.ServiceImplementation\RevCord.ServiceImplementation.csproj">
      <Project>{8af97872-4bf7-4434-9fa6-df365ee4294f}</Project>
      <Name>RevCord.ServiceImplementation</Name>
    </ProjectReference>
    <ProjectReference Include="..\RevCord.Util\RevCord.Util.csproj">
      <Project>{465eca0c-68e4-413d-9462-9377ad7c4d53}</Project>
      <Name>RevCord.Util</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>2703</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:2705</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureBclBuildImported" BeforeTargets="BeforeBuild" Condition="'$(BclBuildImported)' == ''">
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="The build restored NuGet packages. Build the project again to include these packages in the build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>