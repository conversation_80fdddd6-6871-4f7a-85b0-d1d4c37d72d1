﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace RevCord.DataContracts.ScheduleReportEntities
{
    public class Recurrence
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public RecurrenceType objRecurrenceType { get; set; }
        public RecurrenceData objRecurrenceData { get; set; }

        public DateTime? GetNextReportOn(DateTime? LastReportOn = null, int SendCounter = 0)
        {
            DateTime? NextReportOn = null;

            switch(objRecurrenceType)
            {
                case RecurrenceType.None:
                    if (LastReportOn == null)
                    {
                        NextReportOn = DateTime.Now;
                    }
                    break;

                case RecurrenceType.Daily:
                    if (LastReportOn == null)
                    {
                        LastReportOn = DateTime.Now;
                    }

                    NextReportOn = Convert.ToDateTime(LastReportOn).AddDays(1);

                    break;

                case RecurrenceType.WeeklyOnFriday:
                    if (LastReportOn == null)
                    {
                        LastReportOn = DateTime.Now.AddDays((DayOfWeek.Friday - DateTime.Now.DayOfWeek - 7) % 7);
                    }

                    NextReportOn = Convert.ToDateTime(LastReportOn).AddDays(7);

                    break;

                case RecurrenceType.MonthlyOnFourthFriday:
                    DateTime tempDate = LastReportOn.GetValueOrDefault(DateTime.Now);
                    if (tempDate.Day > 21)
                    {
                        tempDate = tempDate.AddMonths(1);
                    }

                    tempDate = new DateTime(tempDate.Year, tempDate.Month, 1);

                    //Calculating First Friday Date
                    NextReportOn = tempDate.AddDays((DayOfWeek.Friday - tempDate.DayOfWeek + 7) % 7);
                    
                    NextReportOn = Convert.ToDateTime(NextReportOn).AddDays(3 * 7);

                    break;

                case RecurrenceType.EveryWeekday:
                    if (LastReportOn == null)
                    {
                        LastReportOn = DateTime.Now;
                    }

                    switch (DateTime.Now.DayOfWeek)
                    {
                        case DayOfWeek.Sunday:
                        case DayOfWeek.Monday:
                        case DayOfWeek.Tuesday:
                        case DayOfWeek.Wednesday:
                        case DayOfWeek.Thursday:
                            NextReportOn = Convert.ToDateTime(LastReportOn).AddDays(1);
                            break;

                        case DayOfWeek.Friday:
                            NextReportOn = Convert.ToDateTime(LastReportOn).AddDays(3);
                            break;

                        case DayOfWeek.Saturday:
                            NextReportOn = Convert.ToDateTime(LastReportOn).AddDays(2);
                            break;
                    }

                    break;

                case RecurrenceType.AnnuallyOn:
                case RecurrenceType.Custom:
                    NextReportOn = objRecurrenceData.GetNextReportOn(LastReportOn, SendCounter);
                    break;
            }

            return NextReportOn;
        }
    }

    public enum RecurrenceType
    {
        [DescriptionAttribute("Does Not Repeat")]
        None,
        [DescriptionAttribute("Daily")]
        Daily,
        [DescriptionAttribute("Weekly on Friday")]
        WeeklyOnFriday,
        [DescriptionAttribute("Monthly on Fourth Friday")]
        MonthlyOnFourthFriday,
        [DescriptionAttribute("Annually On")]
        AnnuallyOn,
        [DescriptionAttribute("Every Weekday")]
        EveryWeekday,
        [DescriptionAttribute("Custom")]
        Custom,
    }

    public class RecurrenceData
    {
        public string ReportOnDate { get; set; }
        public CustomRecurrence objCustomRecurrence { get; set; }

        public RecurrenceData()
        {
            this.ReportOnDate = null;
            this.objCustomRecurrence = null;
        }
        public RecurrenceData(DateTime AnnuallyOn)
        {
            this.ReportOnDate = ReportOnDate;
        }
        public RecurrenceData(CustomRecurrence objCustomRecurrence)
        {
            this.objCustomRecurrence = objCustomRecurrence;
        }
        public RecurrenceData(string strRecurrenceData)
        {
            RecurrenceData objRecurrenceData = new JavaScriptSerializer().Deserialize<RecurrenceData>(strRecurrenceData);

            this.ReportOnDate = objRecurrenceData.ReportOnDate;
            this.objCustomRecurrence = objRecurrenceData.objCustomRecurrence;
        }

        public RecurrenceData(RecurrenceData objRecurrenceData)
        {
            this.ReportOnDate = objRecurrenceData.ReportOnDate;
            this.objCustomRecurrence = objRecurrenceData.objCustomRecurrence;
        }
        public override string ToString()
        {
            return new JavaScriptSerializer().Serialize(new
                    {
                        ReportOnDate = this.ReportOnDate,
                        objCustomRecurrence = this.objCustomRecurrence
                    });
        }

        public DateTime? GetNextReportOn(DateTime? LastReportOn = null, int SendCounter = 0)
        {
            DateTime? NextReportOn = null;

            if (!string.IsNullOrEmpty(this.ReportOnDate))
            {
                if (LastReportOn == null)
                {
                    NextReportOn = Convert.ToDateTime(this.ReportOnDate);
                }
                else {
                    NextReportOn = Convert.ToDateTime(LastReportOn).AddYears(1);
                }
            }
            else
            {
                NextReportOn = objCustomRecurrence.GetNextReportOn(LastReportOn, SendCounter);
            }

            return NextReportOn;
        }
    }
}
