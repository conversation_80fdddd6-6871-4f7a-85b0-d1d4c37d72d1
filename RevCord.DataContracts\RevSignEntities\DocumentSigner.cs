﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RevSignEntities
{
    public class DocumentSigner
    {
        public int Id { get; set; }
        public int DocumentId { get; set; }
        public int SignerId { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public int SignerTypeId { get; set; }
        public bool IsSystemUser { get; set; }
        public SignStatus SignStatus { get; set; }
        public string SignatureFileName { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime? ViewDate { get; set; }
        public DateTime? TermsAgreeDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public bool IsDeleted { get; set; }
    }
}
