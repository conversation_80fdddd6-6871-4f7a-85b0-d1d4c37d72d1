﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RevcellEntities
{
    [JsonObject("data")]
    public class PhoneNumber
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("number")]
        public string Number { get; set; }

        [JsonProperty("capabilities")]
        public List<string> Capabilities { get; set; }

        [JsonProperty("number_type")]
        public string NumberType { get; set; }

        [JsonProperty("e911_address_id")]
        public List<string> E911AddressId { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [JsonProperty("next_billed_at")]
        public DateTime NextBilledAt { get; set; }


        [JsonProperty("call_handler")]
        public string CallHandler { get; set; }

        [JsonProperty("call_receive_mode")]
        public string CallReceiveMode { get; set; }

        [JsonProperty("call_request_url")]
        public string CallRequestURL { get; set; }

        [JsonProperty("call_request_method")]
        public string CallRequestMethod { get; set; }

        [JsonProperty("call_fallback_url")]
        public string CallFallbackURL { get; set; }

        [JsonProperty("call_fallback_method")]
        public string CallFallbackMethod { get; set; }

        [JsonProperty("call_status_callback_url")]
        public string CallStatusCallbackURL { get; set; }

        [JsonProperty("call_status_callback_method")]
        public string CallStatusCallbackMethod { get; set; }

        [JsonProperty("call_laml_application_id")]
        public string CallLamlApplicationId { get; set; }

        [JsonProperty("call_dialogflow_agent_id")]
        public string CallDialogflowAgentId { get; set; }

        [JsonProperty("call_relay_context")]
        public string CallRelayContext { get; set; }

        [JsonProperty("call_relay_connector_id")]
        public string CallRelayConnectorId { get; set; }

        [JsonProperty("call_sip_endpoint_id")]
        public string CallSipEndpointId { get; set; }

        [JsonProperty("call_verto_resource")]
        public string CallVertoResource { get; set; }

        [JsonProperty("message_handler")]
        public string MessageHandler { get; set; }

        [JsonProperty("message_request_url")]
        public string MessageRequestURL { get; set; }

        [JsonProperty("message_request_method")]
        public string MessageRequestMethod { get; set; }

        [JsonProperty("message_fallback_url")]
        public string MessageFallbackURL { get; set; }

        [JsonProperty("message_fallback_method")]
        public string MessageFallbackMethod { get; set; }

        [JsonProperty("message_laml_application_id")]
        public string MessageLamlApplicationId { get; set; }

        [JsonProperty("message_relay_context")]
        public string MessageRelayContext { get; set; }
    }
}
