﻿<%@ Page Title="MMS::Work Log" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="WorkHistory.aspx.cs" Inherits="RevCord.VoiceRec.WebUIClient.Iwb.WorkHistory1" %>

<%@ Import Namespace="RevCord.Util" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .underline-tabs .nav-tabs {
            border-bottom: 2px solid #e9ecef;
        }

        .underline-tabs .nav-link {
            border: none;
            border-bottom: 2px solid transparent;
            padding: 1rem 1.5rem;
            margin-bottom: -2px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
        }

            .underline-tabs .nav-link:hover {
                border-bottom-color: #0d6efd;
                color: #0d6efd;
            }

            .underline-tabs .nav-link.active {
                border-bottom-color: #0053a3;
                color: #0053a3;
                font-weight: bold;
            }

        /* Content Animation */
        .tab-pane.fade {
            transition: all 0.2s ease-out;
        }

            .tab-pane.fade.show {
                animation: fadeIn 0.5s ease-out;
            }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .bold-first-column td:first-child {
            font-weight: bold;
        }
          .table-container {
           overflow-x: unset;
        }
    </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
    <div class="row search-row g-2 align-items-center mb-4 mt-2">
        <!-- Left Side -->
        <div class="col-md-7 d-flex align-items-center">
            <div class="me-3 fw-bold fs-5">
                Contractor's Work Log
            </div>
        </div>
    </div>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">
    <div class="underline-tabs mb-5">
        <ul class="nav nav-tabs nav-fill" id="underlineTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="tab1" data-bs-toggle="tab" data-bs-target="#workHistory" type="button" role="tab">
                    Work History
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tab2" data-bs-toggle="tab" data-bs-target="#activeWelders" type="button" role="tab" onclick="getWelderHistory(1);">
                    Active Welders
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tab3" data-bs-toggle="tab" data-bs-target="#previousWelders" type="button" role="tab" onclick="getWelderHistory(0);">
                    Previous Welders
                </button>
            </li>
        </ul>
        <div class="tab-content" id="underlineTabsContent">
            <div class="tab-pane fade mt-2 show active" id="workHistory" role="tabpanel">
                <h4>Work History </h4>
                <div class="table-container">
                    <table id="tblWelders" class="table-bordered text-center" style="width: 100%;">
                        <thead class="header-row">
                            <tr>
                                <th>#</th>
                                <th>Job Title</th>
                                <th>Owner/Contractor Name</th>
                                <th>From Date</th>
                                <th>To Date</th>
                                <th>Location</th>
                                <th>Rating</th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                         </tbody>
                    </table>
                </div>
            </div>

            <div class="tab-pane fade mt-2" id="activeWelders" role="tabpanel">
                <h4>List of Active Welders</h4>
                <div class="table-container">
                    <table id="tblActiveWelders" class="table-bordered text-center" style="width: 100%;">
                        <thead class="header-row">
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>SSN</th>
                                <th>Stencil Number</th>
                                <th>City</th>
                                <th>Job Title</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                         </tbody>
                    </table>
                </div>
            </div>

            <div class="tab-pane fade mt-2" id="previousWelders" role="tabpanel">
                <h4>List of Previous Welders</h4>
                <div class="table-container">
                    <table id="tblPreviousWelders" class="table-bordered text-center" style="width: 100%;">
                        <thead class="header-row">
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>SSN</th>
                                <th>Stencil Number</th>
                                <th>City</th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                         </tbody>
                    </table>
                </div>
            </div>

        </div>

        <div id="welderInfoModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Welder Info</h5>
                    </div>
                    <div class="modal-body">
                        <div id="welderInfoContainer"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="btnClosePopup" class="btn btn-secondary" onclick="updateWelderInfoModal(false);">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/common.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/workerhistory.js") %>" type="text/javascript"></script>
    <script src="https://ajax.microsoft.com/ajax/jquery.templates/beta1/jquery.tmpl.min.js"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/datatables.net-bs5/dataTables.bootstrap5.min.js") %>" type="text/javascript"></script>
    <script>
        const siteBaseUrl = "<%= SiteConfig.WebURL %>";
        getHistory();
    </script>

    <script id="welderInfoTemplate" type="text/x-jquery-tmpl">
        <table style="width: 100%;" class="bold-first-column">
            <tr>
                <td style="width: 30%;">Name:</td>
                <td style="width: 70%;">${UserName}</td>
                <td rowspan="3">
                    <img id="imgUser" class="rounded-circle mb-3" src="${UserPic}" style="width: 100px; height: 100px;" alt="Profile Picture">
                </td>
            </tr>
            <tr>
                <td>Email:</td>
                <td>${UserEmail}</td>
            </tr>
            <tr>
                <td>Phone:</td>
                <td>${UserPhone}</td>
            </tr>
            <tr>
                <td>Stencil Number:</td>
                <td colspan="2">${WelderStencilNumber}</td>
            </tr>
            <tr>
                <td>State:</td>
                <td colspan="2">${State}</td>
            </tr>
            <tr>
                <td>City:</td>
                <td colspan="2">${City}</td>
            </tr>
            <tr>
                <td>SSN:</td>
                <td colspan="2">${SocialSecurityNumber}</td>
            </tr>
            <tr>
                <td>DOB:</td>
                <td colspan="2">${formatDate(DOB)}</td>
            </tr>
        </table>
    </script>
</asp:Content>
