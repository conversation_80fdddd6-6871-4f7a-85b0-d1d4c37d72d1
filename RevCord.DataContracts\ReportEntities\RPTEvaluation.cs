﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.ReportEntities
{
    public class RPTEvaluation
    {
        public long Id { get; set; }
        public string CallId { get; set; }
        public int SurveyId { get; set; }
        public string SurveyName { get; set; }
        public float EvaluatedScore { get; set; }
        public float MaxScore { get; set; }

        public long QId { get; set; }
        public string QTitle { get; set; }
        public string QAnswer { get; set; } //
        public float QScore { get; set; }
        public float TotalScore { get; set; }

        public string SupervisorComments { get; set; }
        public bool IsShared { get; set; }
        public short StatusId { get; set; }
        public EvaluationStatus Status
        {
            get { return (EvaluationStatus)StatusId; }
            set { StatusId = (byte)value; }
        }

        public DateTime CompletedDate { get; set; }

        public int GroupNum { get; set; }
        public string GroupName { get; set; }
        public int AgentId { get; set; }
        public string AgentName { get; set; }
        public int SupervisorId { get; set; }
        public string SupervisorName { get; set; }
        public int SectionId { get; set; }
        public string SectionTitle { get; set; }


    }
}
