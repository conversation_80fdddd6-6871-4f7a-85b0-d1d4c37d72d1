﻿<div class="">
    <!-- Profile Information -->
    <div class="col-lg-12 col-sm-12 col-12">
        <div id="" class="text-danger validation-summary"></div>
    </div>
    <input type="hidden" id="hdnOrgId" />
    <div class="row col-12">
        <div class="col-6">
            <!-- Email Address -->
            <div class="mb-2">
                <label for="txtEmailAddress" class="form-label">Email Address <span class="text-danger fw-bold">*</span> :</label>
                <input id="txtEmailAddress" type="email" class="form-control" placeholder="Enter email address" />
            </div>

            <!-- Full Name -->
            <div class="mb-2">
                <label for="txtFullName" class="form-label">Full Name <span class="text-danger fw-bold">*</span> :</label>
                <input id="txtFullName" type="text" class="form-control" placeholder="Enter full name" />
            </div>
            <!-- Password -->
            <div class="mb-2">
                <label for="txtUserPassword" class="form-label">Password <span class="text-danger fw-bold">*</span> :</label>
                <input id="txtUserPassword" type="password" class="form-control" placeholder="Enter password" />
            </div>
            <!-- Phone -->
            <div class="mb-2">
                <label for="txtPhone" class="form-label">Phone</label>
                <!--<input id="txtPhone" type="text" class="form-control" placeholder="Enter phone" />-->
                <input id="txtPhone" class="form-control" type="text" name="phoneNumber" placeholder="Phone Number" minlength="10" maxlength="10" />
            </div>

        </div>

        <div class="col-6">
            <div class="text-center mb-3">
                <div class="col-12">
                    <img id="imgUser" class="rounded-circle mb-3" src="../Uploads/UserImages/user.jpg"
                        style="width: 100px; height: 100px;" alt="Profile Picture" />
                </div>
                <div class="col-12">
                    <div class="row">
                        <div class="col-4 mt-2">
                            <a id="lnkRemoveProfileImg" href="javascript:void(0);" class="d-none float-end">
                                <i class="fa-solid fa-trash text-danger"></i>
                            </a>
                        </div>
                        <div class="col-8">
                            <div id="dZUploadProfilePic" class="dropzone dZUploadProfilePic">
                                <input type="button" name="UploadImage" value="Upload Image" class="ml fileinput-button-btnUserImage btn btn-primary float-start" lkey="btnUploadImage" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="row col-12">
        <div class="col-6">
            <!-- SSN -->
            <div class="mb-2">
                <label for="txtSSN" class="form-label">SSN</label>
                <input id="txtSSN" type="text" class="form-control" placeholder="Enter ssn" />
            </div>
        </div>
        <div class="col-6">
            <!-- DOB -->
            <div class="mb-2">
                <label for="txtDOB" class="form-label">DOB</label>
                <div class="input-group">
                    <input id="txtDOB" type="text" class="form-control datepicker birth-date" placeholder="Enter dob" readonly />
                    <span class="input-group-text" id="dobCalendarIcon">
                        <i class="fa fa-calendar"></i>
                    </span>
                </div>
            </div>
        </div>

    </div>

    <div class="row col-12">
        <div class="col-6">
            <!-- State -->
            <div class="mb-2">
                <label for="ddlState" class="form-label">State</label>
                <select id="ddlState" style="" class="form-control ml">
                    <option value="0">Choose</option>
                    <option value="TX">Texas</option>
                    <option value="AL">Alabama</option>
                    <option value="AK">Alaska</option>
                    <option value="AZ">Arizona</option>
                    <option value="AR">Arkansas</option>
                    <option value="CA">California</option>
                    <option value="CO">Colorado</option>
                    <option value="CT">Connecticut</option>
                    <option value="DE">Delaware</option>
                    <option value="DC">District Of Columbia</option>
                    <option value="FL">Florida</option>
                    <option value="GA">Georgia</option>
                    <option value="HI">Hawaii</option>
                    <option value="ID">Idaho</option>
                    <option value="IL">Illinois</option>
                    <option value="IN">Indiana</option>
                    <option value="IA">Iowa</option>
                    <option value="KS">Kansas</option>
                    <option value="KY">Kentucky</option>
                    <option value="LA">Louisiana</option>
                    <option value="ME">Maine</option>
                    <option value="MD">Maryland</option>
                    <option value="MA">Massachusetts</option>
                    <option value="MI">Michigan</option>
                    <option value="MN">Minnesota</option>
                    <option value="MS">Mississippi</option>
                    <option value="MO">Missouri</option>
                    <option value="MT">Montana</option>
                    <option value="NE">Nebraska</option>
                    <option value="NV">Nevada</option>
                    <option value="NH">New Hampshire</option>
                    <option value="NJ">New Jersey</option>
                    <option value="NM">New Mexico</option>
                    <option value="NY">New York</option>
                    <option value="NC">North Carolina</option>
                    <option value="ND">North Dakota</option>
                    <option value="OH">Ohio</option>
                    <option value="OK">Oklahoma</option>
                    <option value="OR">Oregon</option>
                    <option value="PA">Pennsylvania</option>
                    <option value="RI">Rhode Island</option>
                    <option value="SC">South Carolina</option>
                    <option value="SD">South Dakota</option>
                    <option value="TN">Tennessee</option>
                    <option value="UT">Utah</option>
                    <option value="VT">Vermont</option>
                    <option value="VA">Virginia</option>
                    <option value="WA">Washington</option>
                    <option value="WV">West Virginia</option>
                    <option value="WI">Wisconsin</option>
                    <option value="WY">Wyoming</option>
                </select>
            </div>

        </div>
        <div class="col-6">
            <!-- City -->
            <div class="mb-2">
                <label for="ddlCity" class="form-label">City</label>
                <select id="ddlCity" class="form-control ml"></select>
            </div>
        </div>
    </div>

</div>
<script type="text/javascript">
    $(document).ready(function () {
        /*$('#txtDOB').datepicker({
            changeMonth: true,
            changeYear: true,
            yearRange: "-70:+0", // Allow selecting years from 70 years ago up to the current year
            dateFormat: 'dd/mm/yy'
        });*/
    });

    $('#txtDOB').datepicker({
        dateFormat: 'dd-MM-yy',
        changeMonth: true,
        changeYear: true,
        yearRange: "-100:+0", // Limits DOB from 100 years ago up to today
        maxDate: 0 // Prevent future dates
    });

    // Show datepicker when clicking the icon
    $('#dobCalendarIcon').click(function () {
        $('#txtDOB').datepicker('show');
    });


    var imgDropzone;
    $(document).ready(function () {
        Dropzone.autoDiscover = false; // Disabling autoDiscover, otherwise Dropzone will try to attach twice.
        imgDropzone = new Dropzone('#dZUploadProfilePic', {
            url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=SaveWelder',
            uploadMultiple: false,
            clickable: false,
            clickable: ".fileinput-button-btnUserImage",
            //clickable: ".fileinput-button-btnUserImage",
            previewsContainer: false,
            createImageThumbnails: false,
            maxFilesize: 4, // 4MB
            acceptedFiles: '.jpeg, .jpg, .gif, .bmp, .png',
            //addRemoveLinks: false,
            autoProcessQueue: false,
            init: function () {
                dzClosure = this; // Makes sure that 'this' is understood inside the functions below.

                //send all the form data along with the files:
                this.on("sending", function (file, xhr, formData) {  // Passing Parameters
                    //debugger
                    var userData = getUserData();
                    debugger;
                    formData.append('UserData', JSON.stringify(userData));
                    $.blockUI({ baseZ: 10000, message: '<h2><i class="fa fa-spinner fa-spin fa-5x fa-fw"></i><span class="sr-only">' + langKeys[currentLanguage]['Processing'] + '</h2>' });

                });

                this.on('addedfile', function (file) {
                    $('#lnkRemoveProfileImg').removeClass('d-none');
                });
                this.on('removedfile', function (file) {
                    $('#lnkRemoveProfileImg').addClass('d-none');
                });
            },
            success: function (file, response) { //The file has been uploaded successfully.
                //debugger
                //$.growlUI(null, langKeys[currentLanguage]['UserProfileImageSuccess']);
                //$.pnotify({ title: 'Document', text: langKeys[currentLanguage]['UserProfileImageSuccess'], opacity: 1, type: "info", delay: pageNotificationTimeout });
                $('#divAddDialogContents').mmsDialog('close');
                //getWelders();
                $.unblockUI();
            },
            error: function (file, response) { //An error occured.
                showErrorDialog(response);
            },
            complete: function (event, queueID, fileOBj, response, data) { //Called when the upload was either successful or erroneous.
            }

        });
    });

    function removeUserImg() {
        var usernum = 0;
        var imageName = $('#imgUser').attr('src').split('/').pop(-1);
        var revsyncUserId = 0;
        debugger

        if (imageName != 'user.jpg') {
            $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
            $.ajax({
                url: handlersBaseURL + '/UserManagerHandlers/UserImageHandler.ashx',
                type: 'POST',
                data: { method: 'RemoveUserImage', userId: usernum, fileName: imageName, revsyncUserId: revsyncUserId },
                success: function (response) {
                    $.unblockUI();
                    $('#imgUser').attr('src', siteBaseUrl + '/Uploads/UserImages/user.jpg');
                    //$('.profile-pic').attr('src', '<%=ResolveClientUrl("~/assets/icons/um/loginUserIcon.png")%>');
                    toastr.success('Image removed successfully.');

                    $('#lnkRemoveProfileImg').addClass('d-none');
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $.unblockUI();
                    console.log('Error occurred in removeUserImg() <br/>' + jqXHR.responseText.substring(jqXHR.responseText.indexOf('<body')));
                }
            });
        }
    }
</script>
