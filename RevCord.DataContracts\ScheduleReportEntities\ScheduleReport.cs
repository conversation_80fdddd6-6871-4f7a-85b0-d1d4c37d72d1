﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.ScheduleReportEntities
{
    [Serializable]
    public class ScheduleReport
    {
        public int Id { get; set; }
        public int SaveReportId { get; set; }
        public int UserId { get; set; }
        public string ReportName { get; set; }
        public ReportExportType ExportType { get; set; }
        public List<string> Recipients { get; set; }
        public Recurrence objRecurrence { get; set; }
        public DateTime? LastReportOn { get; private set; }
        public DateTime? NextReportOn { get; private set; }
        public int SendCounter { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime UpdatedOn { get; set; }

        public DateTime? GetNextReportOn(DateTime? LastReportOn = null, int SendCounter = 0)
        {
            return this.objRecurrence.GetNextReportOn(LastReportOn, SendCounter);
        }

        public void SetLastReportOn(DateTime? LastReportOn)
        {
            this.LastReportOn = LastReportOn;
        }

        public void SetNextReportOn(DateTime? NextReportOn)
        {
            this.NextReportOn = NextReportOn;
        }
    }

    public enum ReportExportType
    {
        [DescriptionAttribute("pdf")]
        PDF = 0,
        [DescriptionAttribute("xlsx")]
        Excel = 1,
        [DescriptionAttribute("doc")]
        Word = 2,
    }
}
