﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace RevCord.DataContracts.ScheduleReportEntities
{
    public class Repeat
    {
        public int RepeatEvery { get; set; }
        public DayOfWeek? WeekDay { get; set; }
        public int? MonthDay { get; set; }
        public int? Month { get; set; }
        public DateTime? NextReportOn { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public RepeatType ReportRepeatType { get; set; }
        public DateTime? GetNextReportOn(DateTime? LastReportOn = null, int SendCounter = 0)
        {
            return CalculateNextReportDate(LastReportOn);
        }

        public override string ToString()
        {
            return new JavaScriptSerializer().Serialize(this);
        }

        public DateTime? CalculateNextReportDate(DateTime? LastReportOn = null)
        {
            DateTime NextReportOn = DateTime.Now;

            if (LastReportOn != null)
            {
                NextReportOn = Convert.ToDateTime(LastReportOn);
            }

            switch (this.ReportRepeatType)
            {
                case RepeatType.Weekly:
                    if (LastReportOn == null)
                    {
                        NextReportOn = DateTime.Now.AddDays((this.WeekDay.GetValueOrDefault(DayOfWeek.Friday) - DateTime.Now.DayOfWeek -7) % 7);
                    }

                    NextReportOn = NextReportOn.AddDays(RepeatEvery * 7);

                    break;

                case RepeatType.Monthly:
                    if (LastReportOn == null)
                    {
                        NextReportOn = new DateTime(DateTime.Now.Year, DateTime.Now.Month, MonthDay.GetValueOrDefault(DateTime.Now.Day));
                    }

                    NextReportOn = NextReportOn.AddMonths(RepeatEvery * 1);

                    break;

                case RepeatType.Quarterly:
                    if (LastReportOn == null)
                    {
                        NextReportOn = new DateTime(DateTime.Now.Year, Month.GetValueOrDefault(DateTime.Now.Month), MonthDay.GetValueOrDefault(DateTime.Now.Day));
                    }

                    NextReportOn = NextReportOn.AddMonths(RepeatEvery * 3);

                    break;

                case RepeatType.HalfYearly:
                    if (LastReportOn == null)
                    {
                        NextReportOn = new DateTime(DateTime.Now.Year, Month.GetValueOrDefault(DateTime.Now.Month), MonthDay.GetValueOrDefault(DateTime.Now.Day));
                    }

                    NextReportOn = NextReportOn.AddMonths(RepeatEvery * 6);

                    break;

                case RepeatType.Yearly:
                    if (LastReportOn == null)
                    {
                        NextReportOn = new DateTime(DateTime.Now.Year, Month.GetValueOrDefault(DateTime.Now.Month), MonthDay.GetValueOrDefault(DateTime.Now.Day));
                    }

                    NextReportOn = NextReportOn.AddYears(RepeatEvery * 1);

                    break;
            }

            return NextReportOn;
        }

    }

    public enum RepeatType
    {
        [DescriptionAttribute("Repeat Weekly")]
        Weekly,
        [DescriptionAttribute("Repeat Monthly")]
        Monthly,
        [DescriptionAttribute("Repeat Quartely")]
        Quarterly,
        [DescriptionAttribute("Repeat Half Yearly")]
        HalfYearly,
        [DescriptionAttribute("Repeat Yearly")]
        Yearly
    }
}
