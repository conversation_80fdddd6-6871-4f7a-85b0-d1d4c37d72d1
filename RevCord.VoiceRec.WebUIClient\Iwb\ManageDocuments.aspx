﻿<%@ Page Title="MMS::Manage Documents" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="ManageDocuments.aspx.cs" Inherits="RevCord.VoiceRec.WebUIClient.Iwb.ManageDocuments" %>

<%@ Register TagName="ucSignOffDialog" TagPrefix="uc8" Src="~/UserControls/RevSignControls/ucSignOffControl.ascx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/asset/lib/dropzone/basic.min.css")%>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/asset/lib/dropzone/dropzone.min.css")%>" />
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/asset/lib/datatables.net-bs5/dataTables.bootstrap5.css")%>" />

    <style>
    @media screen and (max-width: 1366px) {

        #btnUploadDocument,
        #btnTryAI,
        #txtFileName {
            font-size: 0.85rem !important;
            max-width: 261px !important;
            padding: 8px 8px !important;
        }

            #btnUploadDocument::after,
            #btnTryAI::after {
                content: '';
            }
    }

    /* Fix for 1280x600 resolution */

    @media screen and (max-width: 1280px) and (max-height: 600px) {

        #btnUploadDocument,
        #btnTryAI,
        #txtFileName {
            font-size: 0.8rem !important;
            max-width: 246px !important;
            padding: 8px 8px !important;
        }

            #btnUploadDocument::after,
            #btnTryAI::after {
                content: '';
            }

        #lnkRemoveDocument {
            right: 25px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            font-size: 1rem !important;
        }

        /* Optional: Ensure input and icon container does not overflow */

        .position-relative {
            overflow: hidden;
        }
    }
</style>

    <style type="text/css">
        th.string, th.object, th.array {
            border: 1px;
            border-style: solid !important;
        }
        td.string, td.object, td.array {
            border: 1px;
            border-style: solid !important;
        }
        /*.item-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
            background-color: #e5efff;
            border-radius: 10px;
            height: 103px;
        }
            .item-group div {
                text-align: center;
                flex: 1;
            }*/

        .ui-new-dialog {
            max-width: unset;
        }
            .ui-new-dialog .ui-resizable-se {
                width: 13px;
                height: 13px;
            }

        div.dt-scroll-body thead {
            display: none;
        }

        .select2-container--bootstrap-5{
            display: inline-block;
        }
    </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
    <!-- Filter Row -->
    <div class="row filter-row g-2 align-items-center d-none">
        <!-- Root Filter -->
        <div class="col-xl-2 col-lg-3 col-md-3 col-sm-3">
            <label for="txtFilterDocName" class="form-label">Document Name</label>
            <div class="input-group">
                <input id="txtFilterDocName" type="text" class="form-control" placeholder="Document Name">
            </div>
        </div>

        <div class="col-3">
            <label class="form-label">Document Type</label>
            <div class="input-group">
                <div class="row col-12 pt-2">
                    <div class="col-sm-3 mt-1">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" value="0" name="rbFilterDocType" id="rbFilterDocType0" checked>
                            <label class="form-check-label" for="rbFilterDocType0">
                                ALL
                            </label>
                        </div>
                    </div>
                    <div class="col-sm-3 mt-1">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" value="1" name="rbFilterDocType" id="rbFilterDocType1">
                            <label class="form-check-label" for="rbFilterDocType1">
                                MTR
                            </label>
                        </div>
                    </div>
                    <!--<div class="col-sm-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" value="3" name="rbFilterDocType" id="rbFilterDocType3">
                            <label class="form-check-label" for="rbFilterDocType3">
                                WPS
                            </label>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" value="4" name="rbFilterDocType" id="rbFilterDocType4">
                            <label class="form-check-label" for="rbFilterDocType4">
                                PQR
                            </label>
                        </div>
                    </div>-->
                </div>

            </div>
        </div>

        <div class="col-xl-2 col-lg-3 col-md-3 col-sm-3">
            <label for="txtFilterAlias" class="form-label">Alias</label>
            <div class="input-group">
                <input id="txtFilterAlias" type="text" class="form-control" placeholder="Alias">
            </div>
        </div>


        <!-- Action Buttons -->
        <div class="col-xl-4 col-lg-6 col-md-4 d-flex gap-2" style="margin-top: 40px;">
            <button id="btnSearchDoc" class="btn btn-primary flex-grow-1"><i class="fa-solid fa-magnifying-glass"></i>&nbsp;Search</button>
            <button id="btnResetDoc" class="btn flex-grow-1" style="border: 1px solid red;"><i class="fas fa-undo"></i><span>&nbsp;Reset Filter</span></button>
        </div>
    </div>

    <!-- Second Row with Three Search Fields and Buttons -->
    <div class="row title-row g-2 align-items-center mb-1 mt-2">
        <!-- Left Side -->
        <div class="col-md-7 d-flex align-items-center">
            <div class="me-3 fw-bold fs-5">
                Document Records
            </div>
        </div>

        <!-- Right Side -->
        <div class="col-md-5 d-flex justify-content-end">
            <a id="btnAddNewDoc" class="add-doc btn btn-primary d-none"><i class="fa fa-plus-circle"></i>
                <span lkey="" class="ml">Add New Document</span>
            </a>
        </div>
    </div>
    <!-- Add the dialog code here -->
    <div id="divSignOffRequestDialog" style="display: none;">
        <uc8:ucSignOffDialog ID="ucSignOffRequestDialog" runat="server" />
    </div>

    <!-- Edit Document Modal -->
<div id="editDocumentModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="editDocumentLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div id="edit-validation-summary" class="validation-summary text-danger mb-2"></div>
                <!-- Hidden field to store document ID -->
                <input type="hidden" id="hdnDocumentId" />

                <!-- Document Fields -->
                <div class="container-fluid">
                    <div class="row">
                        <!-- Upload Document -->
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="control-label">Upload Document <span class="text-danger fw-bold">*</span> :</label>
                     
                          <div id="dZUploadDocument" class="dZUploadDocument p-2">
                            <div class="row align-items-center g-2">
                                <!-- Left: Input with Trash Icon -->
                                <div class="col-12 col-md-6 position-relative">
                                    <input id="edittxtFileName" class="form-control doc-name pe-5" placeholder="Upload Document *" readonly />
                                    <a id="lnkRemoveDocument" href="javascript:void(0);"
                                        class="position-absolute top-50 translate-middle-y end-0 me-3">
                                        <i class="fa-solid fa-trash text-danger fs-5"></i>
                                    </a>
                                </div>
                                <!-- Right: Buttons -->
                                <div class="col-12 col-md-6 align-items-center justify-content-end gap-2">
                                    <input type="button"
                                        name="UploadDocument"
                                        value="Upload Document"
                                        class="btn btn-primary fileinput-button-btnUpload flex-shrink-0"
                                        id="btnUploadDocument"
                                        lkey="btnUploadDocument" />
                                    <button type="button"
                                        class="btn btn-primary flex-shrink-0"
                                        id="btnTryAI">
                                        Run MTR AI Validation
                                    </button>
                                </div>
                            </div>
                        </div>
                                <span style="color:red; font-size: 11pt;">Note: Please ensure the document you are uploading is clear and legible</span>
                            </div>
                        </div>

                        <!-- Document Name -->
                        <div class="col-md-12 mt-3">
                            <div class="form-group">
                                <label class="control-label">Name <span class="text-danger fw-bold">*</span> :</label>
                                <input id="edittxtName" type="text" placeholder="Document Name" controltitle="Document Name" class="form-control" />
                            </div>
                        </div>

                        <!-- Document Type -->
                        <div class="col-md-12 mt-3">
                            <div class="form-group">
                                <label class="control-label">Document Type <span class="text-danger fw-bold">*</span> :</label>
                                <div class="col-sm-12">
                                    <div class="row">
                                        <div class="col-sm-6 add-doc-mtr">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                                                <label class="form-check-label" for="rbDocType1">MTR</label>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 add-doc-wps d-none">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" value="3" name="rbDoc" id="rbDocType3">
                                                <label class="form-check-label" for="rbDocType3">WPS</label>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 add-doc-pqr d-none">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" value="4" name="rbDoc" id="rbDocType4">
                                                <label class="form-check-label" for="rbDocType4">PQR</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-md-12 mt-3">
                            <div class="form-group">
                                <label class="control-label">Description</label>
                                <textarea id="edittxtDescription" class="form-control" placeholder="Description"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="saveDocumentEdit()">Update</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">
    <div class="table-container">
        <table id="tblDocuments" class="table-bordered text-center" style="width: 100%;">
            <thead class="header-row">
                <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Alias</th>
                    <th>Upload Date</th>
                    <th>Uploaded By</th>
                    <th>Action(s)</th>
                </tr>
            </thead>
            <tbody class="table-body">
            </tbody>
        </table>
    </div>

    <div id="divPlayListDialog" style="display: none;">
        <select class="form-control" id="cbxLoadPlayList" lkey="cbxLoadPlayList">
        </select>
    </div>
    <div id="divAddDialogContents" style="display: none;">
    </div>

    <!-- Add SignOff Popup -->
    <div id="addSignOffPopup" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Document Sign-Off</h5>                
                </div>
                <div class="modal-body">
                    <p>Do you want to add your signature to this document?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnAddSignOff" class="btn btn-primary">Add SignOff</button>
                    <button type="button" id="btnClosePopup" class="btn btn-secondary" onclick="closeSelfSignRequestModal()">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add SignOff Popup -->
    <div id="ocrResponseModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">AI MTR Verification Complete</h5>                
                </div>
                <div class="modal-body">
                    <div id="ocrStatus"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnViewOCROutput" class="btn btn-primary" onclick="showOutputPdfFile(this);">View Output</button>
                    <button type="button" class="btn btn-primary" onclick="saveOCResponse();">Save</button>
                    <button type="button" class="btn btn-secondary" onclick="ocrResultModalStatus(false)">Close</button>
                </div>
            </div>
        </div>
    </div>

    <%--View sign-off button popup--%>
    <div id="viewSignOffPopup" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Sign-Off Users</h5>                
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table id="tblSignOffRequestUsers" class="table-bordered text-center" style="width: 100%;">
                            <thead class="header-row">
                                <tr>
                                    <th><input type="checkbox" id="selectAllCheckbox" /></th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Sign Status</th>
                                </tr>
                            </thead>
                            <tbody class="table-body">
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="btnExportSignOff" class="btn btn-primary" type="button" onclick="exportSelectedSignOffUser();">
                        <i class="fas fa-file-export"></i> Export
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="updateViewSignOffPopup(false,0)">Close</button>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/dropzone/dropzone.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/datatables.net-bs5/dataTables.bootstrap5.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/iwb.js") %>" type="text/javascript"></script>

<script type="text/javascript">
    var docDropzone;
    Dropzone.autoDiscover = false; // Disabling autoDiscover, otherwise Dropzone will try to attach twice.
    $(document).ready(function () {        

        if (Dropzone != undefined) {
            if (Dropzone.instances.length > 0) {
                Dropzone.instances.forEach(dz => dz.destroy()); // Destroy existing instances
            }
        }

        docDropzone = new Dropzone('#dZUploadDocument', {
            //url: handlersBaseURL + '/IwbHandlers/DocumentUploadHandler.ashx',
            url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=SaveDocument',
            uploadMultiple: false,
            clickable: false,
            clickable: ".fileinput-button-btnUpload",
            previewsContainer: false,
            createImageThumbnails: false,
            maxFiles: 1,
            maxFilesize: 4, // 4MB
            acceptedFiles: '.pdf',
            //addRemoveLinks: false,
            autoProcessQueue: false,
            init: function () {
                dzClosure = this; // Makes sure that 'this' is understood inside the functions below.               

                //send all the form data along with the files:
                this.on("sending", function (file, xhr, formData) {  // Passing Parameters
                    //debugger
                    var document = {
                        Alias: $('.doc-name').val(),
                        Name: $('#txtName').val(),
                        Type: $('input[name=rbDoc]:checked').val(),
                        Description: $('#txtDescription').val(),
                    };

                    formData.append('Name', $('#txtName').val());
                    formData.append('Type', $('input[name=rbDoc]:checked').val());
                    formData.append('Description', $('#txtDescription').val());
                    formData.append('doc', JSON.stringify(document));
                    $.blockUI({ baseZ: 10000, message: '<h2><i class="fa fa-spinner fa-spin fa-5x fa-fw"></i><span class="sr-only">' + langKeys[currentLanguage]['Processing'] + '</h2>' });

                });

                this.on('addedfile', function (file) {
                    if (this.files.length > 1) {
                        //this.removeFile(this.files[0]);
                        //toastr.error("You can't add more files. Kindly, remove the previously selected file.");
                    }
                    else {
                        $('#lnkRemoveDocument').removeClass('d-none');
                        $('.doc-name').val(file.name);
                    }
                });
                this.on('removedfile', function (file) {
                    $('.doc-name').val('');
                    $('#lnkRemoveDocument').addClass('d-none');
                });
            },
            success: function (file, response) { //The file has been uploaded successfully.
                $.unblockUI();
                $('#divAddDialogContents').attr("displayalert", "no");
                var resultData = JSON.parse(response);
                //debugger

                if (resultData.data.fileType == 'MTR') {
                    ocrResultModalStatus(true, resultData);
                    //showSelfSignRequestDialog(resultData);
                }
                else {
                    getDocuments();
                }
            },
            error: function (file, response) {
                $.unblockUI();
                toastr.error(response + " Kindly, remove the previously selected file.");
            },
            complete: function (event, queueID, fileOBj, response, data) { //Called when the upload was either successful or erroneous.
                //$('#divAddDialogContents').mmsDialog('close');
            }

        });

        $(function () {
            getAllPlayListData();
        });

        function showSelfSignRequestDialog(resultData) {
            $('#divAddDialogContents').attr("displayalert", "");
            $(".modal-backdrop").remove();
            if (addDocumentPopup != null) {
                $(addDocumentPopup).mmsDialog('close');
            }
            $('#addSignOffPopup').attr("pdffilename", resultData.data.pdfFileName);
            $('#addSignOffPopup').attr("iwbDocumentid", resultData.data.iwbDocumentid);
            $('#addSignOffPopup').modal('show'); // Show popup after document save
        }

    });        
</script>
</asp:Content>

