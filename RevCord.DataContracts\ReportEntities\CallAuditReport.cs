﻿using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.ReportEntities
{
    public class CallAuditReport
    {
        public int RowNo { get; set; }
        public int Id { get; set; }
        public int NoOfAudits { get; set; }
        public string CallId { get; set; }
        public string FileName { get; set; }
        public int UserNum { get; set; }
        public string AuditedBy { get; set; }
        public DateTime AuditDateTime { get; set; }
        public string IPAddress { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsSaved { get; set; }
        public bool IsTagged { get; set; }
        public bool IsDeleted { get; set; }

        public string StartTime { get; set; }
        public int Ext { get; set; }
        public string ExtName { get; set; }
        public int CallType { get; set; }
        public string CallTypeString { get { return (this.CallType == 1) ? "Audio" : (this.CallType == 3) ? "Text911" : (this.CallType == 6) ? "Screen" : (this.CallType == 7 || this.CallType == 8) ? "Inquire" : this.CallType == 11 ? "MD" : "Unknown"; } }
        public int DurationInMilliSeconds { get; set; }
        public double DurationInSeconds { get { return Convert.ToDouble(TimeSpan.FromMilliseconds(this.DurationInMilliSeconds).TotalSeconds); } }
        public string Duration { get { return this.DurationInMilliSeconds.MillisecondsToString(); } }
    }
}