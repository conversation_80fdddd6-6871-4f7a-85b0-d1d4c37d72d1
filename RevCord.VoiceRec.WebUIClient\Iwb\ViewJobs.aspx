﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="ViewJobs.aspx.cs" Inherits="RevCord.VoiceRec.WebUIClient.Iwb.ViewJobs" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style> 
        .table-container {
           overflow-x: unset;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
    <!-- Filter Row -->
    <div class="row filter-row g-2 align-items-center">
        <!-- Root Filter -->
        <div class="col-xl-2 col-lg-3 col-md-3 col-sm-3">
            <label for="dateTimeRange" class="form-label">Job Title</label>
            <div class="input-group ">
                <input id="txtFilterJobName" type="text" class="form-control" id="searchInput" placeholder="Job Title">
            </div>
        </div>

        <!-- Other Filters -->
        <div class="col-xl-2 col-lg-3 col-md-3 col-sm-3">
            <label for="dateTimeRange" class="form-label">WPS</label>
            <div class="input-group">
                <input id="txtFilterWpsName" type="text" class="form-control" placeholder="WPS">
            </div>
        </div>

        <div class="col-xl-2 col-lg-3 col-md-3 col-sm-3">
            <label for="dateTimeRange" class="form-label">Location</label>
            <div class="input-group">
                <input id="txtFilterJobLocation" type="text" class="form-control" placeholder="Location">
            </div>
        </div>


        <!-- Action Buttons -->
        <div class="col-xl-4 col-lg-6 col-md-4 d-flex gap-2" style="margin-top: 40px;">
            <button id="btnSearchJob" class="btn btn-primary flex-grow-1"> <i class="fa-solid fa-magnifying-glass"></i> &nbsp;Search</button>
            <button id="btnResetJob" class="btn flex-grow-1" style="border: 1px solid red;"><i class="fas fa-undo"></i><span> &nbsp;Reset Filter</span></button>
        </div>

    </div>

    <!-- Second Row with Three Search Fields and Buttons -->
    <div class="row search-row g-2 align-items-center mb-2 mt-2">
        <!-- Left Side -->
        <div class="col-md-7 d-flex align-items-center">
            <div class="me-3 fw-bold fs-5">
                Job Records
            </div>
        </div>
    </div>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">
    <div class="table-container">
        <table id="tblViewJobs" class="table-bordered text-center" style="width: 100%;">
            <thead class="header-row">
                <tr>
                    <th>#</th>
                    <th>Title</th>
                    <th>Start Date</th>
                    <th>End Data</th>
                    <!--<th>No. Of Positions</th>-->
                    <th>WPS</th>
                    <th>Status</th>
                    <th>Action(s)</th>
                </tr>
            </thead>
            <tbody class="table-body">
            </tbody>
        </table>
    </div>

</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/datatables.net-bs5/dataTables.bootstrap5.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/iwb.js") %>" type="text/javascript"></script>
</asp:Content>
