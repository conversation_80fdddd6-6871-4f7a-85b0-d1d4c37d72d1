﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.SurveyEntities
{
    /// <summary>
    /// Data Contract Class - Survey
    /// It is used to manage Survey/Campaign in the application.
    /// </summary>
    [Serializable]
    public class Survey
    {

        #region Properties

        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool HasSections { get; set; }
        public Nullable<DateTime> StartDate { get; set; }
        public Nullable<DateTime> EndDate { get; set; }
        public DateTime PublishedDate { get; set; }
        public bool ActiveAfterEndDate { get; set; }
        public bool IsPublished { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public int ModifiedBy { get; set; }
        public bool IsDeleted { get; set; }
        
        public int NoOfQuestions { get; set; }
        public int NoOfSections { get; set; }

        public float Score { get; set; }

        public int RecId { get; set; }
        public string RecName { get; set; }
        public int RevSyncSurveyId { get; set; }
        public int IsSyncedFromClient { get; set; }

        #endregion

        #region Associations

        public List<Question> Questions { get; set; }

        public List<SurveySection> Sections { get; set; }

        #endregion

        public object Clone()
        {
            return ObjectUtil.DeepClone(this);
        }

    }
}
