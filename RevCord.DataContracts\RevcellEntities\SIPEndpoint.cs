﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RevcellEntities
{
    [JsonObject("data")]
    public class SipEndpoint
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [<PERSON>son<PERSON>roperty("username")]
        public string UserName { get; set; }

        [Json<PERSON>roperty("password")]
        public string Password { get; set; }

        [JsonProperty("caller_id")]
        public string CallerId { get; set; }

        [JsonProperty("send_as")]
        public string SendAs { get; set; }

        [JsonProperty("ciphers")]
        public List<string> Ciphers { get; set; }

        [JsonProperty("codecs")]
        public List<string> Codecs { get; set; }

        [JsonProperty("encryption")]
        public string Encryption { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }
    }
}
