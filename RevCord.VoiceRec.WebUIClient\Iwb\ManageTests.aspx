﻿<%@ Page Title="MMS::Manage Test" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="ManageTests.aspx.cs" Inherits="RevCord.VoiceRec.WebUIClient.Iwb.ManageTests" %>

<%@ Import Namespace="RevCord.Util" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" type="text/css" href="<%= Page.ResolveClientUrl("~/asset/lib/jquery-datetimepicker/jquery.datetimepicker.min.css")%>" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
    <style>
        .table-container {
            overflow-x: unset;
        }
    </style>
    <!-- Filter Row -->
    <div class="row filter-row g-2 align-items-center mb-5">
        <!-- Root Filter -->
        <div class="col-2">
            <label class="form-label">Test Name</label>
            <div class="input-group">
                <input id="txtFilterScheduleName" type="text" class="form-control name-filter" placeholder="Schedule Name">
            </div>
        </div>

        <div class="col-2">
            <label class="form-label">Test Code</label>
            <div class="input-group">
                <input id="txtFilterTestCode" type="text" class="form-control name-filter" placeholder="Test Code">
            </div>
        </div>
        <%--<div class="col-3">
            <label class="form-label">Test Schedule Status</label>
            <div class="input-group">
                <select id="ddlFilterTestStatus" required class="form-control select2 col-md-8"></select>
            </div>
        </div>--%>
        <div class="col-3">
            <label class="form-label">Test Schedule Status</label>
            <div class="input-group">
                <asp:DropDownList ID="ddlFilterTestStatus" runat="server" CssClass="form-control select2 col-md-8" ClientIDMode="Static" AppendDataBoundItems="true">
                    <asp:ListItem Text="Select Status" Value="" />
                </asp:DropDownList>
            </div>
        </div>
        <!-- Action Buttons -->
        <div class="col-xl-4 col-lg-6 col-md-4 d-flex gap-2" style="margin-top: 40px;">
            <button id="btnSearchTest" class="btn btn-primary flex-grow-1"><i class="fa-solid fa-magnifying-glass"></i>&nbsp;Search</button>
            <button id="btnResetTest" class="btn flex-grow-1" style="border: 1px solid red;"><i class="fas fa-undo"></i><span>&nbsp;Reset Filter</span></button>
        </div>

    </div>

    <!-- View Welder Ratings Popup -->
    <div id="viewWelderRatingsPopup" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Welder Ratings</h5>
                </div>
                <div class="modal-body">
                    <div class="ratings-container px-3">
                        <!-- Ratings will be dynamically loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Submit Rating Modal -->
    <div id="submitRatingPopup" style="display:none">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">                    
                    <div class="modal-body">
                        <div class="welder-rating-validation-summary text-danger mb-3"></div>
                        <div id="submitRatingForm">
                            <div class="container-fluid">
                                <div class="row mb-3">
                                 <div class="col-md-6">
                                    <label for="performanceRating" class="form-label">
                                        Performance Rating (1-5)<span class="text-danger fw-bold">*</span> :
                                    </label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="performanceRating" 
                                           name="performanceRating" 
                                           min="1" 
                                           max="5" 
                                           step="1" 
                                           required 
                                           oninput="validity.valid||(value='');"
                                           placeholder="Enter a number between 1 and 5" />
                                </div>

                                    <div class="col-md-6">
                                        <label for="performanceComment" class="form-label">Performance Comments</label>
                                        <textarea type="text"  id="performanceComment" class="form-control" name="performanceComment" rows="2"></textarea>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                   <div class="col-md-6">
                                    <label class="form-label">Weld Count <span class="text-danger fw-bold">*</span> :</label>
                                    <input type="number" class="form-control" id="weldCount" name="weldCount" min="0" oninput="validity.valid||(value='');" />
                                </div>
                                    <div class="col-md-6">
                                        <label for="weldCountComment" class="form-label">Weld Count Comments</label>
                                        <textarea class="form-control" id="weldCountComment" name="weldCountComment" rows="2"></textarea>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                 <div class="col-md-6">
                                    <label for="repairs" class="form-label">Repairs<span class="text-danger fw-bold">*</span> :</label>
                                    <input type="number" class="form-control" id="repairs" name="repairs" min="0" oninput="validity.valid||(value='');" />
                                </div>
                                    <div class="col-md-6">
                                        <label for="repairsComment" class="form-label">Repairs Comments</label>
                                        <textarea class="form-control" id="repairsComment" name="repairsComment" rows="2"></textarea>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="workEthicRating" class="form-label">Work Ethic Rating (1-5)<span class="text-danger fw-bold">*</span> :</label>
                                        <input type="number" class="form-control" id="workEthicRating" name="performanceRating" 
                                           min="1" max="5" step="1" required oninput="validity.valid||(value='');" placeholder="Enter a number between 1 and 5" />
                                    </div>
                                    <div class="col-md-6">
                                        <label for="workEthicComment" class="form-label">Work Ethic Comments</label>
                                        <textarea class="form-control" id="workEthicComment" name="workEthicComment" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>                    
                </div>
            </div>
        </div>

    <!-- Second Row with Three Search Fields and Buttons -->
    <div class="row search-row g-2 align-items-center mb-4">
        <!-- Left Side -->
        <div class="col-md-7 d-flex align-items-center">
            <div class="me-3 fw-bold fs-5">
                Test Schedule Records
            </div>
        </div>

        <!-- Right Side -->
        <div class="col-md-5 d-flex justify-content-end">
            <a id="btnAddNewTest" class="add-test btn btn-primary"><i class="fa fa-plus-circle"></i>
                <span lkey="" class="ml">Add New Test</span>
            </a>
        </div>
    </div>

</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">
    <div class="table-container">
        <table id="tblTests" class="table-bordered text-center" style="width: 100%;">
            <thead class="header-row">
                <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Start Date</th>
                    <th>Test Code</th>
                    <th>No. Of Positions</th>
                    <th>Deadline</th>
                    <th>No. Of Applicants</th>
                    <th>Status</th>
                    <th>Action(s)</th>
                </tr>
            </thead>
            <tbody class="table-body">
            </tbody>
        </table>
    </div>

</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/datatables.net-bs5/dataTables.bootstrap5.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/asset/lib/jquery-datetimepicker/jquery.datetimepicker.full.min.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/iwb.js") %>" type="text/javascript"></script>

    <script>
        const siteBaseUrl = "<%= SiteConfig.WebURL %>";
    </script>
</asp:Content>
