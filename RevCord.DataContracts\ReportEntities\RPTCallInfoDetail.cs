﻿//***********************************************************************
// AssemblyName   : 
// Author               :   <PERSON><PERSON>
// Created ON           :   27-March-2012
//
// Last Modified By     :   
// Last Modified On     :   
// Description          :   
//***********************************************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Globalization;

namespace RevCord.DataContracts.ReportEntities
{
    public class RPTCallInfoDetail
    {
        #region Properties

        public string Key { get; set; }
        public string CallID { get; set; }
        public string UserName { get; set; }
        public string GroupName { get; set; }
        public string ChannelName { get; set; }
        public string Extension { get; set; }

        public DateTime CallDate { get; set; }
        public long Duration { get; set; }

        public DayOfWeek DayOfWeek { get; set; }
        public string DayOfWeekString
        {
            get { return this.DayOfWeek.ToString(); }
        }

        public int NoOfRings { get; set; }
        public long RingTime { get; set; }
        public long TalkTime { get; set; }
        public long HoldTime { get; set; }
        public CallStatus CallStatus { get; set; }

        public int RecorderId { get; set; }
        public string RecoderName { get; set; }

        #endregion


        public RPTCallInfoDetail() { }

        public RPTCallInfoDetail(string key, string callID, string userName, string groupName, string channelName, string extension, string callDateTime, long duration)
        {
            this.Key = key;
            this.CallID = callID;
            this.UserName = userName;
            this.GroupName = groupName;
            this.ChannelName = channelName;
            this.Extension = extension;



            this.CallDate = DateTime.ParseExact(callDateTime, "yyyyMMddHHmmss", CultureInfo.InvariantCulture);
            this.Duration = duration;
        }

        public RPTCallInfoDetail(string key, string callID, string userName, string groupName, string channelName, string extension, string callDateTime, long duration, DayOfWeek dayOfWeek)
        {
            this.Key = key;
            this.CallID = callID;
            this.UserName = userName;
            this.GroupName = groupName;
            this.ChannelName = channelName;
            this.Extension = extension;

            this.CallDate = DateTime.ParseExact(callDateTime, "yyyyMMddHHmmss", CultureInfo.InvariantCulture);
            this.Duration = duration;

            this.DayOfWeek = dayOfWeek;
        }
        public RPTCallInfoDetail(string key, string callID, string userName, string groupName, string channelName, string extension, string callDateTime, long duration, DayOfWeek dayOfWeek, int recId, string recName)
        {
            this.Key = key;
            this.CallID = callID;
            this.UserName = userName;
            this.GroupName = groupName;
            this.ChannelName = channelName;
            this.Extension = extension;

            this.CallDate = DateTime.ParseExact(callDateTime, "yyyyMMddHHmmss", CultureInfo.InvariantCulture);
            this.Duration = duration;

            this.DayOfWeek = dayOfWeek;

            this.RecorderId = recId;
            this.RecoderName = recName;
        }

        public RPTCallInfoDetail(string key, string callID, string userName, string groupName, string channelName, string extension, string callDateTime, long duration, int recId, string recName)
        {
            this.Key = key;
            this.CallID = callID;
            this.UserName = userName;
            this.GroupName = groupName;
            this.ChannelName = channelName;
            this.Extension = extension;

            this.CallDate = DateTime.ParseExact(callDateTime, "yyyyMMddHHmmss", CultureInfo.InvariantCulture);
            this.Duration = duration;

            this.RecorderId = recId;
            this.RecoderName = recName;
        }


        /*public RPTCallInfoDetail(string key, string callID, string userName, string groupName, string channelName, string extension, string callDateTime, long duration, string dayOfWeekString, int recId, string recName)
        {
            this.Key = key;
            this.CallID = callID;
            this.UserName = userName;
            this.GroupName = groupName;
            this.ChannelName = channelName;
            this.Extension = extension;

            this.CallDate = DateTime.ParseExact(callDateTime, "yyyyMMddHHmmss", CultureInfo.InvariantCulture);
            this.Duration = duration;

            this.DayOfWeekString = dayOfWeekString;

            this.RecorderId = recId;
            this.RecoderName = recName;
        }*/

    }
}