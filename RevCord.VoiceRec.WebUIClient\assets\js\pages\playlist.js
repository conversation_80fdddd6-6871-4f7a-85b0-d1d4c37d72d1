﻿var ProcessingWait = { 'en': 'Processing, please wait ..', 'es': 'Procesando, favor espere ...' };
var rowData = [];
var currentPlaylistId = 0;
var currentPlaylistName = '';
var currentPlaylistNoOfCalls = 0;
var userBrowser = '';

var iconCss, iconCss1, iconCss2;
var tooltip, ht, isUserItemIcon;

var plOption = [];
var imgColPL = 3;
var addPlDuration = 1000;
var progressDuration = 3000;
var zipDownloadDur = 5000;
var startPlayback = false;
var editCommentsLength = 100;
var pListId = 0;
var zipFileName = '';
var noOfMaxCallsToPlay = 500;//100;
var noOfPlayingCalls = 0;

var playlistTable = null;
var playlistTableSize = 0;
var tblPlaylistData = null;
var applicationName = "Inquire";
var SNNotation = "¶SN¶";

var bIsZeroItem = true; //arivu
var bIsAddUserFileDialog = false;//arivu
var userAddedFile = "";
var bIsHasFile = false;
var bIsTLLoadedInAUF = false;
var bIsDestroyTLInAUF = false;
var oldplaylistid = 0;
var bNotSavedAUF = false;
var g_IQ3AdvancedReportCriteria = '';
var checkeventBuffering = true;
var currentSelectedPlaylistId = 0;

var mainPlaylistTable = null;
var allUsers = null;

var bRightClickSingleCallSave = false;
var minimumTimelineVisualizationHeight = 200;
var currentPlaylistItemsCount = 0;
var searchareapanel = "";

var callTagData = [];
var AvailablecallTagColors = [];
var localPlaylistExportPercent = 0;
var isPlayersHidden = false;
var isPlayersAutoHidden = true;
var fullScreenCaseNotes = '';
var g_Inspection = null;
var step = 25;
var scrolling = false;
var SelectedMultiplePlaylist = [];
var obj_PlaylistInspectionTemplates = '';
var obj_PlaylistDetails = '';

var g_HasPhotoMarker = false;
var g_SelectedPhotoFileName = '';
var g_EventsDataJSON = [];

var isMTRplaylist = false;

$(document).ready(function () {

    $(window).trigger('resize');

    $("#hdnActiveTab").val("Playlist");

    if (isSharedPage) {

        if (isViewPlaylist) {

            showConfirmationDlg("This is a one time link. Please click Yes to access the playlist.",
                $("#hdnShareId").val(),
                function (param) {
                    $.ajax({
                        type: 'POST',
                        url: playlistHandlerURL,
                        data: { plShareId: param, tenantId: tenantId, method: 'SetSharedPlaylistViewStatus', isSharedPage: isSharedPage },
                        success: function (response) {
                            switch (response.success) {
                                case true:
                                    fetchAndDisplayAllPlaylists();
                                    break;

                                default:
                                    $.unblockUI();
                                    showErrorDialog(response.message);
                                    break;
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            $.unblockUI();
                            showExceptionDialog('Some error has occurred. Please refresh.');
                        }
                    });
                },
                function (param) {
                    window.close();
                },
                function (param) {
                    showErrorDialog("Closing or skipping CONFIRMATION not allowed.");
                }
            );

            //Blocking Adding New Items
            $("#btnAddManualFile").attr("disabled", "disabled");
            $("#btnAddInternalNote").attr("disabled", "disabled");
            $("#btnAddSharedNote").attr("disabled", "disabled");
        }

    }
    else {
        fetchAndDisplayAllPlaylists();
    }

    playlistDialog();
    exportDialog();
    saveTracksByPLId();
    getAllUsers();
    resetAdvancedReportOptions();

    $(document).on("click", 'a.DeletePlaylist', function () {
        var playlistId = parseInt($(this).closest('tr').attr('playlistId'), 10);
        var playlistName = $(this).closest('tr').attr('playlistName');
        var RevSyncServerID = $(this).closest('tr').attr('revSyncServerID');
        var playlistInfo = {
            playlistId: playlistId,
            playlistName: playlistName,
            revSyncServerID: RevSyncServerID
        };
        showConfirmationDialog(langKeys[currentLanguage]['cmsgPlDel'] + "<br> " + langKeys[currentLanguage]['thColPlaylistName'] + " : <b>" + playlistInfo.playlistName + "</b>", playlistInfo, deletePlaylist);
    });

    $(document).on("click", 'button.EditPlaylist', function () {
        var playlistId = parseInt($(this).closest('tr').attr('playlistId'), 10);
        var playlistExistingName = $(this).closest('tr').attr('playlistName');
        var visibilityType = parseInt($(this).closest('tr').attr('visibilityType'), 10);
        var comments = $(this).closest('tr').attr('comments');
        console.log('playlistId = ' + playlistId + ' playlistExistingName = ' + playlistExistingName + ' visibilityType = ' + visibilityType + ' comments = ' + comments);
        $("#divEditPlaylist").mmsDialog({
            autoOpen: false,
            modal: true,
            resizable: true,
            closeText: langKeys[currentLanguage]['ttClose'],
            open: function () {
                $(this).mmsDialog("option", "title", $(this).attr('plID') == 0 ? langKeys[currentLanguage]['msgDlgTitleCreatePl'] : langKeys[currentLanguage]['msgDlgTitleEditPl']);
                $('#lblPlErrorMsg').html('');
                $('#txtPLComments').val(comments);
                $("#txtEditPlaylistName").val(playlistExistingName);
                if(visibilityType == 0)
                    $('#radPrivateVisibility').prop('checked', true);
                else if(visibilityType == 1)
                    $('#radPublicVisibility').prop('checked', true);
            },
            close: function () {
                $('#lblPlErrorMsg').html('');
                $('#txtPLComments').val('');
                $("#txtEditPlaylistName").val('');
            },
            show: { effect: 'fade', complete: function () { $(this).find("#txtEditPlaylistName").focus(); } },
            buttons: [
                {
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        //if ($.trim($('#txtEditPlaylistName').val()) != "") {
                        //    if (!IsPlaylistNameAlreadyExists($.trim($('#txtEditPlaylistName').val()))) {
                        //        $.blockUI();
                        //        updatePlaylistAttributes(playlistId, $.trim($('#txtEditPlaylistName').val()), $("input[name='visibilityType']:checked").val(), $('#txtPLComments').val());
                        //    }
                        //    else {
                        //        showErrorDialog(langKeys[currentLanguage]['emsgSameNamePl']);
                        //        $.unblockUI();
                        //        return;
                        //    }
                        //}
                        //else
                        //    $('#lblPlaylistErrorMsg').html(langKeys[currentLanguage]['emsgReqFieldsMissing']).show();
                        if (validatePlaylistAttributes()) {
                            updatePlaylistAttributes(playlistId, $.trim($('#txtEditPlaylistName').val()), $("input[name='visibilityType']:checked").val(), $('#txtPLComments').val());
                        }
                    }
                }, {
                    text: langKeys[currentLanguage]['msgBtnCancel'],
                    click: function () {
                        $('#lblPlaylistErrorMsg').html('');
                        $('#txtEditPlaylistName').val('');
                        $("#divEditPlaylist").mmsDialog("close");
                    }
                }
            ]
        });
        $("#divEditPlaylist").mmsDialog("open");
    });

    $(document).on("click", '#btnAddNewRepository', function () {
        $("#divPlaylist").mmsDialog("open");
    });

    $(document).on("click", 'button.OpenPlaylist', function () {
        $('#btnAddNewRepository').hide();
        isPlayersAutoHidden = true;

        var playlistId = parseInt($(this).closest('tr').attr('playlistId'), 10);
        var playlistName = $(this).closest('tr').attr('playlistName');
        var playlistType = $(this).closest('tr').attr('PlaylistType');
        if (playlistType = 2) {
            isMTRplaylist = true;
        }
        else {
            isMTRplaylist = false;
        }

        currentPlaylistId = playlistId;
        currentPlaylistName = playlistName;
        resetPlaylistDetailsDialog();
        changeLayout('listMode');
        //hfVal = "Timeline";
        $('#hdnActiveTab').val("Timeline");
        $("#divPlaylistTabs").css('display', 'none');
        //$("#playlistindex").hide();
        //$("#playlisttool").show();

        getplaylistDetail(playlistId, playlistName);
    });

    $(document).on("click", 'button.SharePlaylist', function () {
        var playlistId = parseInt($(this).closest('tr').attr('playlistid'), 10);
        var playlistName = $(this).closest('tr').attr('playlistname');
        var playlistComments = $(this).closest('tr').attr('comments');
        var noOfItems = $(this).closest('tr').attr('noofitems');

        $("#dlgPlaylistShare").mmsDialog({
            modal: true,
            resizable: false,
            closeText: langKeys[currentLanguage]['ttClose'],
            title: langKeys[currentLanguage]['plSharePlaylist'],
            width: '800',
            minHeight: '400',
            height: '500',
            show: 'fade',
            open: function (event, ui) {
                $('#lblPlaylistShareError').html('');
                var pattern = /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i
                var serialNumber = 0;
                removeAllEmailRecipients();
                if (allUsers.length && $('#tblSysUsersForEmail > tbody > tr').length == 0) {
                    $.each(allUsers, function (i, user) {
                        if (pattern.test(user.UserID)) {
                            serialNumber = serialNumber + 1;
                            $('#tblSysUsersForEmail > tbody').append("<tr UserEmail='" + user.UserID + "' usernum='" + user.UserNum + "' IsSystemUser='true'><td><input type='checkbox'/></td><td class='col-serial'>" + serialNumber + "</td><td>" + user.UserID + "</td></tr>");
                        }
                    });
                }
            },
            close: function (event, ui) {
                $('#txtAddEmailRecipient').val('');
                searchSystemUserByEmail('');
            },
            buttons: [{
                text: langKeys[currentLanguage]['plSharePlaylist'],
                click: function () {
                    var rowCount = $('#tblRecipientsForEmail > tbody > tr').length;
                    $('#tblRecipientsForEmail > tbody > tr')

                    if (rowCount > 0) {

                        var userEmails = $("#tblRecipientsForEmail tbody td:nth-child(3)").clone().map(function () { return $(this).text() }).get().join(',');
                        console.log(' userEmails = ' + userEmails + ' playlistId = ' + playlistId + ' playlistName = ' + playlistName + ' playlistComments = ' + playlistComments + ' noOfItems = ' + noOfItems);
                        sendPlaylistShareLink(userEmails, playlistId, playlistName, playlistComments, noOfItems);
                    }
                    else {
                        showErrorDialog(langKeys[currentLanguage]['emsgReportAddRecipients']);
                    }
                }
            },
            {
                text: langKeys[currentLanguage]['msgBtnClose'],
                click: function () {
                    $(this).mmsDialog("close");
                }
            }]
        });
        $("#dlgPlaylistShare").mmsDialog("open");
    });

    $('#ancrClosePlaylistTool').on("click", function (e) {
        $('#hdnActiveTab').val("Playlist");
        changeLayout('listMode');
        e.preventDefault();

        if (!boolIEBrowser) {
            nState = FilePlayerState.Stop;
            LoopingFalse();
        }

        var grid = $("#tPlaylist tbody tr");
        $("input[id='SelectAll']:checkbox").prop('checked', false);
        if (grid.length > 0) for (i = 0; i < grid.length; i++) if (grid[i].cells[1].childNodes[0].type == "checkbox") grid[i].cells[1].childNodes[0].checked = false;

        $('.lnk-Open-All-playlist').addClass('hide');

        displaylink();
        // Refresh the page after everything else
        location.reload();
    });

    $(document).on("click", '#btnAddInternalNote', function () {
        if (isViewPlaylist) {
            showErrorDialog(langKeys[currentLanguage]['lPlSharedEditingNotAllowed']);
            return;
        }

        if ($('#txtInternalNote').val() == "")
            showErrorDialog(langKeys[currentLanguage]['plEmptyNoteIsNotAllowed']);
        else {
            var internalNoteObject = getPlaylistNoteObject(0, currentPlaylistId, 1, $('#txtInternalNote').val(), new Date(), new Date(), false);
            $.blockUI();
            insertPlaylistNote(internalNoteObject);
        }
    });
    $(document).on("click", '#btnAddSharedNote', function () {
        if (isViewPlaylist) {
            showErrorDialog(langKeys[currentLanguage]['lPlSharedEditingNotAllowed']);
            return;
        }

        if ($('#txtSharedNote').val() == "")
            showErrorDialog(langKeys[currentLanguage]['plEmptyNoteIsNotAllowed']);
        else {
            var sharedNoteObject = getPlaylistNoteObject(0, currentPlaylistId, 2, $('#txtSharedNote').val(), new Date(), new Date(), false);
            $.blockUI();
            insertPlaylistNote(sharedNoteObject);
        }
    });
    $(document).on("click", 'button.EditNote', function () {
        $('#txtEditedNote').val('');
        var noteId = parseInt($(this).closest('li').attr('noteId'), 10);
        var noteType = parseInt($(this).closest('li').attr('noteType'), 10);
        var playlistId = parseInt(currentPlaylistId, 10);
        var noteText = $(this).closest('li').find('p').text();

        $("#divEditPlaylistNote").mmsDialog({
            autoOpen: true,
            modal: true,
            resizable: false,
            closeText: langKeys[currentLanguage]['ttClose'],
            title: langKeys[currentLanguage]['msgDlgTitleEditPlNote'],
            open: function () {
                $(this).find("#txtEditedNote").val(noteText);
                $('#lblPlErrorMsg').html('');
            },
            show: { effect: 'fade', complete: function () { $(this).find("#txtEditedNote").focus(); } },
            buttons: [
                {
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        if ($.trim($('#txtEditedNote').val()) != "") {
                            $.blockUI();
                            var playlistNoteObject = getPlaylistNoteObject(noteId, playlistId, noteType, $.trim($('#txtEditedNote').val()), new Date(), new Date(), false);
                            updatePlaylistNote(playlistNoteObject);
                        }
                        else
                            $('#lblEditPlaylistNoteErrorMsg').html(langKeys[currentLanguage]['emsgReqFieldsMissing']).show();
                    }
                }, {
                    text: langKeys[currentLanguage]['msgBtnCancel'],
                    click: function () {
                        $('#lblEditPlaylistNoteErrorMsg').html('');
                        $('#txtEditedNote').val('');
                        $("#divEditPlaylistNote").mmsDialog("close");
                    }
                }
            ]
        });
    });
    $(document).on("click", 'a.DeleteNote', function () {
        var noteId = parseInt($(this).closest('li').attr('noteId'), 10);
        var noteType = parseInt($(this).closest('li').attr('noteType'), 10);
        deletePlaylistNote(currentPlaylistId, noteId, noteType);
    });
    $(document).on("click", 'a.DeletePaperClip', function () {
        if (isViewPlaylist) {
            showErrorDialog(langKeys[currentLanguage]['lPlSharedEditingNotAllowed']);
            return;
        }
        var fileName = $(this).attr('fileName');
        var directoryName = $(this).attr('directoryName');
        //deletePaperClip(directoryName, fileName);
        var obj = { "DirName": directoryName, "FileName": fileName };
        showConfirmationDialog(langKeys[currentLanguage]['cmsgPldDelAttachedFile'], obj, deletePaperClip);
    });
    $('#txtEditedNote').focus(function () {
        $("#lblEditPlaylistNoteErrorMsg").css("display", "none").fadeOut(2000);
    });
    $('#btnAddManualFile').live('click', function () {
        if (isViewPlaylist) {
            showErrorDialog(langKeys[currentLanguage]['lPlSharedEditingNotAllowed']);
            return;
        }

        showExportDialog();
    });
    let inputField = $("#tPlaylist_filter label input");

    // Remove any default "Search" text from DataTables
    $("#tPlaylist_filter label").contents().filter(function () {
        return this.nodeType === 3; // Removes text nodes
    }).remove();

    // Ensure input is inside a search-container and has an icon
    if (!inputField.parent().hasClass("search-container")) {
        inputField.wrap('<div class="search-container"></div>');
        inputField.before('<i class="fa fa-search search-icon" aria-hidden="true"></i>'); // Add icon before input
    }

    // Set placeholder text
    inputField.attr("placeholder", "Search");

    // Ensure input is cleared on page load
    inputField.val('');

    // Keyup event for search functionality
    inputField.off().on("keyup", function () {
        filterPlaylist($(this).val());
    });

    // Force parent container to align everything to the left
    $("#tPlaylist_filter").css({
        "text-align": "left",
        "display": "flex",
        "justify-content": "flex-start",
        "align-items": "center",
        "width": "100%"
    });

    Dropzone.autoDiscover = false;
    $("#dZUploadpaperClip").dropzone({
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        uploadMultiple: false, // Restrict sending multiple files in one request.
        clickable: false, // Disable upload functionality on drop
        clickable: ".fileinput-button-paperclip", // For make functionality working on button click
        maxFilesize: 256, // 4MB
        addRemoveLinks: false,
        createImageThumbnails: false,
        params: {
            uName: loginUserName,
            methodName: 'upload',
            uId: loginUserId
        },
        init: function () {
            this.on("sending", function (file, xhr, formData) {  // Passing Parameters
                formData.append("plId", currentPlaylistId);
                formData.append("plName", currentPlaylistName);
                //formData.append("uId", loginUserId);

                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }
            });
        },
        success: function (file, response) { //The file has been uploaded successfully.
            //var obj = JSON.parse(response);
            var obj = response;
            switch (obj.success) {
                case true:
                    obj.data.forEach(function (paperClip, i) {
                        populatePaperClip(paperClip);
                    });
                    removeAlldZUploadpaperClipPreviews();
                    break;
                case false:
                    $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    removeAlldZUploadUAIUploadPreviews();
                    break;
                default:
                    showErrorDialog(obj);
                    removeAlldZUploadpaperClipPreviews();
                    break;
            }
        },
        error: function (file, response) { //An error occured.
            showErrorDialog(response);
            removeAlldZUploadpaperClipPreviews();
        },
        complete: function (event, queueID, fileOBj, response, data) { //Called when the upload was either successful or erroneous.
        }
    });
    $("#btnMapPlaylist").live('click', function () {
        var plId = currentPlaylistId;
        loadPlMapCtrl(plId);
    });

    $('#btnSaveCalls').live('click', function () {
        saveTracksByPLId();
        saveTracks();
    });

    $('#cbxLoadPlayList').on('change', function () {

        resetPlaylistDetailsDialog();
        isPlayersAutoHidden = true;
        currentPlaylistId = $(this).val();
        currentPlaylistName = $(this).text();
        getplaylistDetail(currentPlaylistId, currentPlaylistName);

        ////resetPlaylistDetailsDialog();
        //checkeventBuffering = false;
        ////currentSelectedPlaylistId = $(this).val();
        //currentPlaylistId = $(this).val();
        ////removeVideoFilePlayerSrc();

        ////if (!boolIEBrowser)
        ////    deleteallmedia();
        //var element = $(this).find('option:selected');
        ////debugger
        //if ($(this).val() != 0) {
        //    var plid = $(this).val();
        //    //var planchor = $("#ulPlaylists > li > span[id='" + plid + "']").parent();
        //    //$('#hdnActiveTab').val('Timeline');
        //    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
        //    getplaylistDetail(plid);
        //    $.unblockUI();
        //}
    });

    $(document).on('keyup', '#txtSearchBookmarks', function () {
        var regex = new RegExp($('#txtSearchBookmarks').val(), "i");
        var rows = $(".bm-contents table tr");
        rows.each(function (index) {
            $row = $(this);
            var bm = $row.find("td:eq(2)").text();
            if (bm.search(regex) != -1) {
                $(this).show();
            } else {
                $row.hide();
            }
        });
    });

    (function ($) {
        $.fn.hasScrollBar = function () {
            return this.get(0).scrollHeight > this.height();
        }
    })(jQuery);
    makeDialogs();

    $(".videoPanel").on('dblclick', function () {

        var videoPlayer = $(this)[0];
        var videoPlayerContainer = $(this).closest('.widget').length > 0 ? $(this).closest('.widget')[0] : null;

        var screenState = isFullScreen();
        var screenEvent = screenState ? 'FullscreenOn' : 'FullscreenOff';

        if (videoPlayerContainer == void 0 || videoPlayer.childElementCount == 0 || screenEvent == "FullscreenOn") {
            //Skipping if Container is null or already fullscreen
            if (screenState) {
                exitFullScreen();
            }
            if (currentloadedscreen == "ListView") {
                //$("#videofileplayer").css('position', 'absolute');
            }
            return;
        }
        else {
            setTimeout(function () {
                let fullScreenHeight = $(window).height() - 40;
                $(videoPlayer).height(fullScreenHeight);

                //Because Timeline
                if (currentloadedscreen == "ListView") {
                    $("#videofileplayer").css('position', '');
                    if ($(videoPlayer).children('video').length) {
                        //$(videoPlayer).children('video').css({ 'height': fullScreenHeight, 'max-height': fullScreenHeight });
                        $(videoPlayer).children('video').css({ 'width': '100%', 'height': fullScreenHeight, 'max-height': fullScreenHeight });
                    }
                    if ($(videoPlayer).children('img').length)
                        $(videoPlayer).children('img').css({ 'height': fullScreenHeight, 'max-height': fullScreenHeight });
                }
                else {
                    showMetadataFullScreen(videoPlayerContainer);
                }
            }, 100);

            if (videoPlayerContainer.webkitRequestFullscreen) {
                videoPlayerContainer.webkitRequestFullscreen();
            } else if (videoPlayerContainer.mozRequestFullScreen) {
                videoPlayerContainer.mozRequestFullScreen();
            } else if (videoPlayerContainer.msRequestFullscreen) {
                videoPlayerContainer.msRequestFullscreen();
            } else if (videoPlayerContainer.requestFullscreen) {
                videoPlayerContainer.requestFullscreen();
            }
        }
    });

    //Hide/View Players
    $('.widget-control-show-players').on("click", function () {
        $elem = $(this);
        if (isPlayersHidden || isPlayersAutoHidden) {
            if (slHaveVideo()) {
                $(this).attr('data-original-title', 'Hide Players');
                $elem.children('.fa').removeClass('fa-eye');
                $elem.children('.fa').addClass('fa-eye-slash');

                //Show Players
                isPlayersHidden = false;
                showHideTimelineVideos(true);
            }
        } else {
            $(this).attr('data-original-title', 'Show Players');
            $elem.children('.fa').removeClass('fa-eye-slash');
            $elem.children('.fa').addClass('fa-eye');

            //Hide Players
            isPlayersHidden = true;
            showHideTimelineVideos(false);
        }
        //return false;
    });

    $('#lnk-Open-All-playlist').on("click", function (e) {

        SelectedMultiplePlaylist = [];
        var html = ""; var isfirst = true; var firstplaylistid = 0; var firstplaylistname = 0;
        $("#divPlaylistTabs").css('display', 'block');
        $('#tPlaylist tbody tr').each(function (index) {
            if ($(this).closest('tr')[0].cells[1].children[0].checked) {
                var playlistId = parseInt($(this).closest('tr').attr('playlistId'), 10);
                var playlistName = $(this).closest('tr').attr('playlistName').trim();

                html += '<a class="nav-item nav-link active" data-toggle="tab" role="tab" aria-controls="public" aria-expanded="true" id="tab' + playlistId + '" onclick="openplaylist(' + playlistId + ')">' + playlistName
                    + '<i class="fa fa-times-circle" data-toggle="tooltip" style="margin: -2px 0 0 10px;" data-placement="top" onclick="this.parentNode.remove(); closetab(' + playlistId + ');" data-original-title="Close Tab"></i></a>';

                if (isfirst) { firstplaylistid = playlistId; firstplaylistname = playlistName; isfirst = false; }
                SelectedMultiplePlaylist.push(playlistId);
            }
        });

        if (document.getElementById("tPlaylist").tBodies[0].rows.length != SelectedMultiplePlaylist.length)
            html += '<a class="nav-item nav-link" data-toggle="tab" role="tab" aria-controls="public" onclick="opentab();" aria-expanded="true" id="tab" title="Add New Tab"><i class="fa fa-plus-circle" data-toggle="tooltip" data-placement="top" data-original-title="Add New Tab"></i></a>';
        $('#myTab').html(html);

        $("#cbxLoadPlayList").css('display', 'none'); isPlayersAutoHidden = true; currentPlaylistId = firstplaylistid; currentPlaylistName = firstplaylistname;
        resetPlaylistDetailsDialog(); changeLayout('listMode'); $('#hdnActiveTab').val("Timeline");

        for (var i = 0; i < SelectedMultiplePlaylist.length; i++) document.getElementById('tab' + SelectedMultiplePlaylist[i]).style.backgroundColor = '#2980b9'
        document.getElementById('tab' + currentPlaylistId).style.backgroundColor = '#777'

        getplaylistDetail(currentPlaylistId, currentPlaylistName);


        setTimeout(function () {
            if (document.getElementById("myTab").clientWidth < document.getElementById("myTab").scrollWidth) {
                $('#leftArr').css('display', 'block');
                $('#rightArr').css('display', 'block');
                var topPos = ((document.getElementById('myTab').getBoundingClientRect().height / 2) - (document.getElementById("leftArr").getBoundingClientRect().height / 2)) + 'px';
                $('#leftArr').css('top', topPos);
                $('#rightArr').css('top', topPos);
            }
            else {
                $('#leftArr').css('display', 'none');
                $('#rightArr').css('display', 'none');
            }
        }, 1000);
    });
});

function openplaylist(playlistId) {
    var playlistName = "";
    $('#tPlaylist tbody tr').each(function (index) {
        if (parseInt($(this).closest('tr').attr('playlistId'), 10) == playlistId)
            playlistName = $(this).closest('tr').attr('playlistName').trim();
    });

    isPlayersAutoHidden = true; currentPlaylistId = playlistId; currentPlaylistName = playlistName; resetPlaylistDetailsDialog(); $('#hdnActiveTab').val("Timeline");

    try{
        for (var i = 0; i < SelectedMultiplePlaylist.length; i++) document.getElementById('tab' + SelectedMultiplePlaylist[i]).style.backgroundColor = '#2980b9'
        document.getElementById('tab' + currentPlaylistId).style.backgroundColor = '#777'
    } catch (e) {

    }

    getplaylistDetail(currentPlaylistId, currentPlaylistName);
}

function closetab(playlistId) {
    if (currentPlaylistId == playlistId) {
        var currentplaylistnodeid = 0;
        for (var i = 0; i < SelectedMultiplePlaylist.length; i++)
            if (SelectedMultiplePlaylist[i] == currentPlaylistId) {
                currentplaylistnodeid = i;
                SelectedMultiplePlaylist.splice(i, 1);
                i--;
            }

        if (currentplaylistnodeid > SelectedMultiplePlaylist.length)
            currentplaylistnodeid = currentplaylistnodeid - 1;

        playlistId = SelectedMultiplePlaylist[currentplaylistnodeid];
        var playlistName = "";
        $('#tPlaylist tbody tr').each(function (index) { if (parseInt($(this).closest('tr').attr('playlistId'), 10) == playlistId) playlistName = $(this).closest('tr').attr('playlistName').trim(); });

        isPlayersAutoHidden = true; currentPlaylistId = playlistId; currentPlaylistName = playlistName; resetPlaylistDetailsDialog(); $('#hdnActiveTab').val("Timeline");

        for (var i = 0; i < SelectedMultiplePlaylist.length; i++) document.getElementById('tab' + SelectedMultiplePlaylist[i]).style.backgroundColor = '#2980b9'
        document.getElementById('tab' + currentPlaylistId).style.backgroundColor = '#777'

        getplaylistDetail(currentPlaylistId, currentPlaylistName);
    } else {
        for (var i = 0; i < SelectedMultiplePlaylist.length; i++) if (SelectedMultiplePlaylist[i] == currentPlaylistId) { SelectedMultiplePlaylist.splice(i, 1); i--; }
    }

    var html = "";
    for (var i = 0; i < SelectedMultiplePlaylist.length; i++) {
        var playlistId = SelectedMultiplePlaylist[i]; var playlistName = "";

        $('#tPlaylist tbody tr').each(function (index) {
            if (parseInt($(this).closest('tr').attr('playlistId'), 10) == playlistId)
                playlistName = $(this).closest('tr').attr('playlistName').trim();
        });

        html += '<a class="nav-item nav-link active" data-toggle="tab" role="tab" aria-controls="public" aria-expanded="true" id="tab' + playlistId + '" onclick="openplaylist(' + playlistId + ')">' + playlistName
            + '<i class="fa fa-times-circle" data-toggle="tooltip" style="margin: -2px 0 0 10px;" data-placement="top" onclick="this.parentNode.remove(); closetab(' + playlistId + ');" data-original-title="Close Tab"></i></a>';
    }

    if (document.getElementById("tPlaylist").tBodies[0].rows.length != SelectedMultiplePlaylist.length)
        html += '<a class="nav-item nav-link" data-toggle="tab" href="#tab1" role="tab" aria-controls="public" aria-expanded="true" onclick="opentab();" id="tab" title="Add New Tab"><i class="fa fa-plus-circle" data-toggle="tooltip" data-placement="top" data-original-title="Add New Tab"></i></a>';
    $('#myTab').html(html);

    for (var i = 0; i < SelectedMultiplePlaylist.length; i++) document.getElementById('tab' + SelectedMultiplePlaylist[i]).style.backgroundColor = '#2980b9'
    document.getElementById('tab' + currentPlaylistId).style.backgroundColor = '#777'

    setTimeout(function () {
        if (document.getElementById("myTab").clientWidth < document.getElementById("myTab").scrollWidth) {
            $('#leftArr').css('display', 'block');
            $('#rightArr').css('display', 'block');
            var topPos = ((document.getElementById('myTab').getBoundingClientRect().height / 2) - (document.getElementById("leftArr").getBoundingClientRect().height / 2)) + 'px';
            $('#leftArr').css('top', topPos);
            $('#rightArr').css('top', topPos);
        }
        else {
            $('#leftArr').css('display', 'none');
            $('#rightArr').css('display', 'none');
        }
    }, 200);

    if (SelectedMultiplePlaylist.length == 0) {
        $('#hdnActiveTab').val("Playlist");
        changeLayout('listMode');
        e.preventDefault();

        if (!boolIEBrowser) {
            nState = FilePlayerState.Stop;
            LoopingFalse();
        }
        displaylink();

        var grid = $("#tPlaylist tbody tr");
        $("input[id='SelectAll']:checkbox").prop('checked', false);
        if (grid.length > 0) for (i = 0; i < grid.length; i++) if (grid[i].cells[1].childNodes[0].type == "checkbox") grid[i].cells[1].childNodes[0].checked = false;
        $('.lnk-Open-All-playlist').addClass('hide');
    }
}

function opentab() {
    var val = 0;
    var optionString = '<div>';
    //'<div id=\"divErrMsg\" class=\"hide\">' +
    //'<span id=\"lblPlAddItemErrorMsg\" style=\"font-weight:bold; color:#b94a48;\" ></span>' +
    //'</div>';

    $('#tPlaylist tbody tr').each(function (index) {
        if (!SelectedMultiplePlaylist.includes(parseInt($(this).closest('tr').attr('playlistId'), 10))) {
            var playlistName = $(this).closest('tr').attr('playlistName').trim(); val += 1;
            if (val == 1) optionString += '<label style=\"margin:3px;\"><input type=\"radio\" name=\"rbAddNewTab\" value=\"' + playlistName + '\" id=\"' + parseInt($(this).closest('tr').attr('playlistId'), 10) + '\" checked=\"checked\" /> ' + playlistName + '</label></br>';
            else optionString += '<label style=\"margin:3px;\"><input type=\"radio\" name=\"rbAddNewTab\" value=\"' + playlistName + '\" id=\"' + parseInt($(this).closest('tr').attr('playlistId'), 10) + '\" /> ' + playlistName + '</label></br>';
        }
    });

    optionString += '</div>';

    if (nState == FilePlayerState.Play || nState == FilePlayerState.PendingPlay)
        btnPause_Click();

    var $dialog = $(optionString).mmsDialog({
        autoOpen: false,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['cdSelectPlaylist'],
        modal: true,
        width: 300,
        height: 400,
        close: function () {
            $(this).mmsDialog('destroy').remove();
        },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnOK'],
                click: function () {
                    debugger
                    var playlistName = document.querySelector("input[type='radio'][name=rbAddNewTab]:checked").value;
                    var playlistId = parseInt($('input:radio[name="rbAddNewTab"]:checked')[0].id, 10);
                    var html = "";
                    SelectedMultiplePlaylist.push(playlistId);

                    for (var i = 0; i < SelectedMultiplePlaylist.length; i++) {
                        var playlistId = SelectedMultiplePlaylist[i]; var playlistName = "";

                        $('#tPlaylist tbody tr').each(function (index) {
                            if (parseInt($(this).closest('tr').attr('playlistId'), 10) == playlistId)
                                playlistName = $(this).closest('tr').attr('playlistName').trim();
                        });

                        html += '<a class="nav-item nav-link active" data-toggle="tab" role="tab" aria-controls="public" aria-expanded="true" id="tab' + playlistId + '" onclick="openplaylist(' + playlistId + ')">' + playlistName
                            + '<i class="fa fa-times-circle" data-toggle="tooltip" style="margin: -2px 0 0 10px;" data-placement="top" onclick="this.parentNode.remove(); closetab(' + playlistId + ');" data-original-title="Close Tab"></i></a>';
                    }

                    if (document.getElementById("tPlaylist").tBodies[0].rows.length != SelectedMultiplePlaylist.length)
                        html += '<a class="nav-item nav-link" data-toggle="tab" href="#tab1" role="tab" aria-controls="public" aria-expanded="true" onclick="opentab();" id="tab" title="Add New Tab"><i class="fa fa-plus-circle" data-toggle="tooltip" data-placement="top" data-original-title="Add New Tab"></i></a>';
                    $('#myTab').html(html);

                    for (var i = 0; i < SelectedMultiplePlaylist.length; i++) document.getElementById('tab' + SelectedMultiplePlaylist[i]).style.backgroundColor = '#2980b9'
                    document.getElementById('tab' + currentPlaylistId).style.backgroundColor = '#777'
                    $(this).mmsDialog('close');

                    setTimeout(function () {
                        if (document.getElementById("myTab").clientWidth < document.getElementById("myTab").scrollWidth) {
                            $('#leftArr').css('display', 'block');
                            $('#rightArr').css('display', 'block');
                            var topPos = ((document.getElementById('myTab').getBoundingClientRect().height / 2) - (document.getElementById("leftArr").getBoundingClientRect().height / 2)) + 'px';
                            $('#leftArr').css('top', topPos);
                            $('#rightArr').css('top', topPos);
                        }
                        else {
                            $('#leftArr').css('display', 'none');
                            $('#rightArr').css('display', 'none');
                        }
                    }, 200);
                }
            }
        ]
    });
    $dialog.mmsDialog('open');
}

//#region Playlist Core Functionality
function buildHTML(playlist) {
    currentPlaylistNoOfCalls = playlist.NoOfCalls;
    updateNewNoItemsInPlaylist(currentPlaylistNoOfCalls);

    var playlistNotes = playlist.PlaylistNotes;
    var playlistPaperClips = playlist.PlaylistPaperClips;
    try {
        if (playlistTable == null) {
            initializePlayListTableUsingDataTable();
        }

        buildPlaylistRows(playlist.PlaylistDetails);

        if (playlistNotes != null) {

            var internalNotes = playlistNotes.filter(function (playlistNote) {
                return playlistNote.NoteType == 1;
            });
            internalNotes.forEach(function (internalNote, i) {
                populatePlaylistNote(internalNote);
            });
            var sharedNotes = playlistNotes.filter(function (playlistNote) {
                return playlistNote.NoteType == 2;
            });
            sharedNotes.forEach(function (sharedNote, i) {
                populatePlaylistNote(sharedNote);
            });
        }
        if (playlistPaperClips != null && playlistPaperClips.length != 0) {
            playlistPaperClips.forEach(function (paperClip, i) {
                populatePaperClip(paperClip);
            });
        }
    }
    catch (e) {
        JL(frontendLogger).fatalException("Exception inside buildHTML:", e);
    }
}

function isFullScreen() {
    return document.webkitIsFullScreen || document.mozFullScreen || document.fullScreen;
}

function buildPlaylistRows(playlistData) {
    if (playlistTable != null)
        playlistTable.clear();

    $.each(playlistData, function (i, callData) {
        try {

            let channelId = (callData.ChannelId == void 0) ? "" : callData.ChannelId;
            //let channelName = (callData.ChannelName == null || callData.ChannelName == undefined) ? "" : callData.channelName;
            let channelName = (callData.ChannelName == void 0) ? "" : callData.ChannelName;
            let startTimeString = (callData.StartTimeString == void 0) ? "" : callData.StartTimeString;
            let groupName = (callData.GroupName == void 0) ? "" : callData.GroupName;
            let tag3 = (callData.Tag3 == void 0) ? "" : callData.Tag3;
            let tag4 = (callData.Tag4 == void 0) ? "" : callData.Tag4;
            let ani_ph = (callData.ANI_PH == void 0) ? "" : callData.ANI_PH;
            let isPictureEvent = callData.IsPictureEvent;
            let isVirtualInspection = (callData.IsVirtualInspection == void 0) ? false : callData.IsVirtualInspection;
            let isUserAddedItem = (callData.IsUserAddedItem == void 0) ? false : callData.IsUserAddedItem;
            let callerId = (callData.ANI_PH == null || callData.ANI_PH == undefined) ? 'N/A' : callData.ANI_PH;
            let isRevview = (callData.IsRevView == void 0) ? false : callData.IsRevView;
            let revview_fname = (callData.RevViewFileName == void 0) ? "" : callData.RevViewFileName;
            let revview_sttime = (callData.RevViewStartTime == void 0) ? "" : callData.RevViewStartTime;
            let revview_phonenumber = (callData.RevViewPhoneNumber == void 0) ? "" : callData.RevViewPhoneNumber;
            let revview_agentname = (callData.RevViewAgentName == void 0) ? "" : callData.RevViewAgentName;
            let vesselId = (callData.VesselId == void 0) ? "" : callData.VesselId;

            let flName = (callData.FileName == void 0) ? "" : callData.FileName;
            let fileExt = flName == "" ? "" : flName.substr(flName.lastIndexOf('.') + 1); //flName.split('.')[1];
            //var fileTypeIcon = '7';
            let fileTypeIcon = callData.FileType;

            if (callData.IsRevcell) fileTypeIcon = '1';
            if (callData.IsRevView) fileTypeIcon = '1_revview';

            if (callData.FileType == 7) {
                switch (fileExt.toLowerCase()) {
                    case "mp4":
                    case "mov":
                        fileTypeIcon = '8';
                        if (callData.IsVirtualInspection) {
                            fileTypeIcon = '8_virtual';
                        }
                        break;

                    case "zip":
                        fileTypeIcon = '12';
                        break;

                    default:
                        fileTypeIcon = '7';
                        if (callData.IsVirtualInspection) {
                            fileTypeIcon = '7_virtual';
                        }
                        break;
                }
            }

            //var imgSrc = fileTypeIcon + '.png';
            var imgPrefix = '../SystemUploadedFiles/MediaIcons/Tenant_' + tenantId + '/';
            var imgSrc = fileTypeIcon + '.png';

            let cType = callData.FileType;
            let callId = callData.CallId;
            let startTime = (callData.StartTimeString == void 0) ? "" : callData.StartTimeString;
            let durInSec = callData.DurationInSec; //callData.DurationInMs
            let chNum = callData.ChannelId;
            let chName = (callData.ChannelName == void 0) ? "" : callData.ChannelName;
            var msgBody = (callData.MessageBody == void 0) ? "" : callData.MessageBody;
            var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);
            //debugger            
            switch (cType) {
                case 3:
                    if (!isUserAddedItem) {
                        tooltip = "";
                        tooltip = '<div>';
                        tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName.replace(/\+/g, ' ')) : "") + '<br/>';
                        tooltip += '<b>Time :</b> ' + datea + '<br/>';
                        tooltip += '<b>Phone Number :</b> ' + ani_ph + '<br/>';
                        tooltip += '<b>Text :</b> ' + tag3 + '';
                        tooltip += '</div>';
                    }
                    else {
                        tooltip = "";
                        tooltip = '<div>';
                        tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                        tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                        tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                        tooltip += '<b>Message :</b> ' + msgBody + ''
                        tooltip += '</div>';
                    }
                    break;
                case 4:
                case 5:
                    tooltip = "";
                    tooltip = '<div>';
                    tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                    tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                    tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                    tooltip += '<b>Message :</b> ' + msgBody + '';
                    tooltip += '</div>';
                    break;
                default:
                    debugger    
                    tooltip = "";
                    tooltip = '<div>';
                    tooltip += isOnlyIQ3ModeEnabled ? '<b>Inspector :</b> ' + chName + '<br/>' : '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                    tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                    tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                    tooltip += '<b>Duration :</b> ' + timeFormate(durInSec) + '<br/>';
                    if (!isOnlyIQ3ModeEnabled)
                        tooltip += '<b>Caller ID :</b> ' + ((callerId == null || callerId == undefined || callerId == 'undefined') ? '' : callerId) + '<br/>';
                    if (cType == 1)
                        tooltip += '<b>Call Tag :</b> ' + callData.TagName + '<br/>';
                    tooltip += '</div>';
                    break;
            }
            ht = $($.parseHTML(tooltip));


            var $trHtml = $('<tr id="' + callData.Id + '" callid="' + callId.trim() + '"'
                + ' ctype="' + cType + '"'
                + ' fname="' + flName + '"'
                + ' sttime="' + startTimeString + '"'
                + ' cduration="' + callData.DurationInSec + '"'
                + ' cdduration="' + callData.DurationInMs + '"'
                + ' chnum="' + channelId + '"'
                + ' chname="' + channelName + '"'
                + ' recid="' + callData.RecorderId + '"'
                + ' sname="' + callData.ScreenRecFile + '"'
                + ' isuseritem="' + callData.IsUserAddedItem + '"'
                + ' pages="' + callData.NoOfPages + '"'
                + ' ani_ph="' + ani_ph + '"'
                + ' isPictureEvent="' + isPictureEvent + '"'
                + ' isVirtualInspection="' + isVirtualInspection + '"'
                + ' tag3="' + tag3 + '"'
                + ' tag4="' + tag4 + '"'
                + ' interview_gps="' + callData.interview_gps + '"'
                + ' interview_notes="' + callData.interview_Notes + '"'
                + ' interview_datetime="' + callData.interview_DateTime + '"'
                + ' interview_interviewer="' + callData.interview_Interviewer + '"'
                + ' interview_interviewee="' + callData.interview_Interviewee + '"'
                + ' interview_interviewid="' + callData.interview_InterviewId + '"'
                + ' msgbody="' + msgBody + '"'
                + ' isrevcell="' + callData.IsRevcell + '"'
                + ' isRevview="' + callData.IsRevView + '"'
                + ' revview_fname="' + revview_fname + '"'
                + ' revview_sttime="' + revview_sttime + '"'
                + ' revview_phonenumber="' + revview_phonenumber + '"'
                + ' revview_agentname="' + revview_agentname + '"'
                + ' vesselId="' + vesselId + '"'
                + ' TagName="' + callData.TagName + '"'
                + '>');
            $trHtml.append($('<td>').html('<input type="hidden" id="hdnBookmarkXml" value="' + callData.BookmarkXML + '" /><span>' + i + '</span>'));
            $trHtml.append($('<td class="delItem">').html($('<a id="lnkDelete" class="deleteIcon ml" lkey="imgDelete" title="Delete" href="javascript:void(0);" style="float: left;"></a>')));
            //$trHtml.append($('<td style="text-align: center;">').html('<img src="../assets/icons/search/' + fileTypeIcon + '.png" id="imgMsg" runat="server" clientidmode="Static" />'));
            $trHtml.append($("<td style='text-align: center;'>").html(
                "<img style='cursor:pointer; width:16px; height:16px;' " +
                "data-bs-toggle='popover' " +
                "data-bs-html='true' " +
                "data-bs-trigger='hover focus' " +
                "data-bs-placement='right' " +
                "title='Metadata' " +
                "data-bs-content=\"" + ht.html().replace(/"/g, "&quot;") + "\" " +
                "src='" + imgPrefix + imgSrc + "' class='imgMsg' />"
            ));
            $trHtml.append($('<td>').html('' + channelId));
            $trHtml.append($('<td>').html('' + channelName));
            $trHtml.append($('<td>').html('' + groupName));
            $trHtml.append($('<td abbr="colStartTime">').html(ConvertStartTimeToDateTime(startTimeString)));
            $trHtml.append($('<td>').html((callData.FileType == '3' ? '' : callData.Duration)));
            $trHtml.append($('<td>').html((callData.ANI_PH == null || callData.ANI_PH == undefined || callData.ANI_PH == '') ? 'N/A' : callData.ANI_PH));


            let isChecked = callData.RetainValue == true ? "checked" : "";
            let isDisabled = ((cType == 1 || cType == 3 || cType == 6 || cType == 11) && !isUserAddedItem) ? "" : "disabled";
            $trHtml.append($('<td style="text-align: center;">').html('<input id="chkPlRetain" name="chkPlRetain" type="checkbox" class="gridrowChk" onclick="javascript:plRetainCall(this)" value="' + callData.CallId + '" ' + isChecked + ' ' + isDisabled + '/>'));


            var TagColorID = callData.TagColorID;
            var TagName = callData.TagName;
            if (cType == 1) {
                $trHtml.append($('<td abbr="colCallTag">').html('<div class="picker" data-id="' + callId.trim() + '" id="picker' + callId.trim() + '" Title="' + TagName + '"></div>'));
                setTimeout(function () { $("#picker" + callId.trim()).colorPick({ 'initialColor': '#' + TagColorID, 'allowRecent': false, 'palette': AvailablecallTagColors, 'paletteLabel': '', 'onColorSelected': function () { this.element.css({ 'backgroundColor': this.color, 'color': this.color }); } }); }, 50);
            } else {
                $trHtml.append($('<td abbr="colCallTag">').html(''));
            }
            //$trHtml.append($('<td style="text-align: center;">').html('<input id="chkPlRetain" name="chkPlRetain" type="checkbox" class="gridrowChk" onclick="javascript:plRetainCall(this)" value="' + callData.CallId + '" ' + isChecked + ' ' + isDisabled + '/>'));

            let bookmarkContent = '';
            if (callData.BookmarkCSV != void 0 && callData.BookmarkCSV != '') {
                bookmarkContent = callData.BookmarkCSV.length <= 20 ? callData.BookmarkCSV : callData.BookmarkCSV.substring(0, 20);
                bookmarkContent = bookmarkContent + '<a id="lnkBm" noOfBm="21" href="javascript:void(0);" style="padding-left: 3px; text-align: right;" class="bm-details-Listview"><img height="16" alt="" src="../assets/icons/search/bookmark-icon-16.png"></a>';
            }
            $trHtml.append($('<td class="bookmark">').html(bookmarkContent));

            var commentsClass = ((cType == 1 || cType == 3 || cType == 6 || cType == 7) && isUserAddedItem) ? "editPL" : "";

            var isAdminUser = false;
            isAdminUser = (userType == "1" || userType == 1);

            if (cType == 10) {
                debugger
                console.log('callData--', callData);
                var lnkStdReport = ' <a title="View signed file" style="cursor: pointer;" data-link="' + callData.DocumentInfo.SignLink +'" onclick="viewSignedDocument(this);"><i class="fa fa-file-pdf-o"></i></a> ';
                lnkStdReport += ' <a title="Sign-off history" class="mtrSignType1_revview" style="cursor: pointer;" callid="' + callData.DocumentInfo.SignDocumentId + '" href="javascript:void(0);" onclick="viewSignoffHistory(this);" > <i class="fa fa-eye"></i></a > ';
                $trHtml.append($('<td abbr="colReport" >').html(lnkStdReport));
            }            
            else {
                $trHtml.append($('<td abbr="colReport" >').html(''));
            }

            if (callData.Comments != null && callData.Comments.length != 0) {
                var initialText = callData.Comments.substring(0, 16);
                var commentCell = initialText;
                commentCell += '<a class="read-full-comments" style="padding-left: 3px; text-align: right;" href="javascript:void(0);"><img height="16" src="../assets/icons/search/comments-16.png" alt=""></a>';
                commentCell += '<p class="full-comments hide">' + callData.Comments + '</p>';

                callData.Comments = commentCell;

                $trHtml.append($('<td abbr="colComment" >').html(commentCell));
            }
            else {
                $trHtml.append($('<td abbr="colComment" >').html(''));
            }

            $trHtml.append($('<td style="display: none;">').html(tag3));

            playlistTable.rows.add($trHtml);
        }
        catch (e) {
            console.log("Exception => buildPlaylistRows ",e.message);
        }
    });

    playlistTable.draw();

    $(document).on('mouseenter', '.imgMsg', function () {
        let $el = $(this); $el.popover('show'); // Auto-hide on mouseleave
        $('.popover').on('mouseleave', function () { $el.popover('hide'); });
    }); $(document).on('mouseleave', '.imgMsg', function () { let $el = $(this); setTimeout(function () { if (!$('.popover:hover').length) { $el.popover('hide'); } }, 100); });
    //$('[data-bs-toggle="popover"]').popover();

}

function fetchAndDisplayAllPlaylists() {
    isViewPlaylist = false;
    var playlistBaseHandlerURL = '../Handlers/VoiceRecHandlers/PlaylistHandler.ashx?method=FetchAllPlaylists';
    mainPlaylistTable = null;
    mainPlaylistTable = $("#tPlaylist").DataTable({
        "processing": false,
        "serverSide": false,
        "bFilter": false,
        "info": false,
        "bInfo": false,
        "stateSave": false,
        "bPaginate": false,
        "bLengthChange": false,
        "bFilter": true,
        "bInfo": false,
        "bAutoWidth": false,
        "pagging": false,
        "language": {
            infoEmpty: langKeys[currentLanguage]['sdNoResultsFound'],
            processing: ProcessingWait[currentLanguage]
        },
        "pageLength": -1,
        "dataFilter": function (data) {
            var json = jQuery.parseJSON(data);
            json.recordsTotal = json.total;
            json.recordsFiltered = json.total;
            json.data = json.list;
            rowData = data.rows;
            return JSON.stringify(json); // return JSON string
        },
        "scrollY": $(window).height() - $('#logo').height() - 145, //$(window).height() - $('#divPlaylists').height() - $('#logo').height() - 50,
        "scrollX": true,
        "destroy": true,
        "responsive": false,
        "ajax": {
            "url": playlistBaseHandlerURL,
            "type": "GET",
            "data": { isSharedPage: isSharedPage },
            "error": function (jqXHR, exception) {
                //showExceptionDialog(jqXHR.responseText);
                JL(frontendLogger).info('fetchAndDisplayAllPlaylists');
                JL(frontendLogger).info(jqXHR);
                JL(frontendLogger).info(exception);
            }
        },
        "drawCallback": function (settings) {
            if (typeof (switchUserViewMode) == "function") {
                switchUserViewMode();
            }
        },
        "createdRow": function (row, data, dataIndex) {
            $(row).attr('playlistId', data["Id"]);
            $(row).attr('playlistName', data["Name"]);
            $(row).attr('revSyncServerID', data["RevSyncServerID"]);
            $(row).attr('isSyncedFromClient', data["IsSyncedFromClient"]);
            $(row).attr('noofitems', data["NoOfCalls"]);
            $(row).attr('visibilityType', data["VisibilityType"]);
            $(row).attr('comments', data["Comments"]);
            $(row).attr('isOwnerPlaylist', data["IsOwnerPlaylist"]);
            $(row).attr('playlistType', data["PlaylistType"]);
            $('#cbxLoadPlayList').append('<option value="' + data["Id"] + '">' + data["Name"] + '</option>');
            if (data["VisibilityType"] == 0)
                $($(row)[0]).find('.SharePlaylist').hide();
            else
                $($(row)[0]).find('.SharePlaylist').show();

            if (data["IsOwnerPlaylist"] == "false" || data["IsOwnerPlaylist"] == false) {
                $($(row)[0]).find('.SharePlaylist').hide();
                $($(row)[0]).find('.EditPlaylist').hide();
                $($(row)[0]).find('.DeletePlaylist').hide();
            }
        },
        "columnDefs": [
            {
                "searchable": false,
                "orderable": true,
                "sortable": true,
                "targets": 1,
                "width": "50",
                render: function (data, type, row, meta) {
                    //return data;
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            {
                "width": "20", "targets": 0, //"class": "ml",
                "orderable": false,
                "sortable": false,
                "className": "dt-left",
                "visible": isViewPlaylist ? false : true,
                render: function (data, type, row, meta) {
                    //return "<input type='checkbox' class='media-row-chk'>";
                    return "<input type='checkbox' class='row-checkbox media-row-chk' value='" + (row.Id || '') + "'>";
                }
            },
            {
                "width": "300", "targets": 2, //"class": "ml",
                "orderable": true,
                "sortable": true,
                "className": "dt-left",
            },
            {
                "width": "100", "targets": 3,
                //"class": "pl-call-count",
                "orderable": true,
                "sortable": true,
                "className": 'dt-left pl-call-count'
            },
            {
                "width": "100", "targets": 4, //"class": "ml",
                "orderable": true,
                "sortable": true,
                "className": 'dt-left'
            },
            {
                "width": "100", "targets": 5, //"class": "ml",
                "orderable": true,
                "sortable": true,
                "className": 'dt-left'
            },
            {
                "width": "200",
                "targets": 6,
                "data": null,
                "className": 'dt-center',
                //"class": "ml",
                "defaultContent": "<td class=''>"
                    + "<button type='button' class='btn btn-iconed btn-info btn-xs OpenPlaylist'><i class='fa fa-caret-square-o-right'></i><span class='ml' lkey='plOpen'>" + langKeys[currentLanguage]['plOpen'] + "</span></button>"
                    + (!isViewPlaylist ? ("<button type='button' class='btn btn-iconed btn-primary btn-xs EditPlaylist' style='margin-left:3px;'><i class='fas fa-edit'></i><span class='ml' lkey='plEdit'>" + langKeys[currentLanguage]['plEdit'] + "</span></button>"
                    + "<button type='button' class='btn btn-iconed btn-success btn-xs SharePlaylist' style='margin-left:3px;  color: white'><i class='fa fa-share'></i><span class='ml' lkey='plShare'>" + langKeys[currentLanguage]['plShare'] + "</span></button>"
                        + "<a href='javascript:void(0);' class='btn btn-danger btn-xs remove-tr DeletePlaylist' style='margin-left:3px;'><i class='fa fa-times' style='padding-left: 3px;'></i></a>")
                        : "")
                    + "</td>"
            }
        ],
        "order": [[4, 'desc']],
        "columns": [
            { "data": null, "orderable": true, "sortable": true, class: 'text-center' },
            { "data": null, "orderable": false, "sortable": false, class: 'text-center' },
            { "data": "Name", "orderable": true, "sortable": true },
            { "data": "NoOfCalls", "orderable": true, "sortable": true },
            { "data": "CreatedDate", "orderable": true, "sortable": true },
            { "data": "VisibilityType", "orderable": true, "sortable": true },
            { "data": null, "orderable": false, "sortable": false }
        ],
        "fnRowCallback": function (nRow, aData, iDisplayIndex) {
            if (!isViewPlaylist) {
                $("td:nth-child(4)", nRow).html(aData["NoOfCalls"], true, true);
                $("td:nth-child(5)", nRow).html(parseDate(aData["CreatedDate"], true, true));
                var visibilityType = aData["VisibilityType"] == "0" ? "Private" : (aData["IsOwnerPlaylist"] == "false" || aData["IsOwnerPlaylist"] == false ? "Shared" : "Public");
                $("td:nth-child(6)", nRow).html(visibilityType, true, true);
            }
            else {
                $("td:nth-child(3)", nRow).html(aData["NoOfCalls"], true, true);
                $("td:nth-child(4)", nRow).html(parseDate(aData["CreatedDate"], true, true));
                var visibilityType = aData["VisibilityType"] == "0" ? "Private" : (aData["IsOwnerPlaylist"] == "false" || aData["IsOwnerPlaylist"] == false ? "Shared" : "Public");
                $("td:nth-child(5)", nRow).html(visibilityType, true, true);
            }
            return nRow;
        }
    });

    $('td[abbr="colComment"]').live('click dblclick', function (e) {
        var callType = isECEnabled ? $(this).closest('tr').attr('ctype') : $(this).closest('tr').data('ctype');
        if (callType == 2 || callType == 4 || callType == 5)
            return;

        var $fc = $(this).find('.full-comments');

        if ($(this).find('input').length == 0) {
            var oldTextFull = $fc.text();//$(this).text();
            var oldTextReadable = this.childNodes.length ? this.childNodes[0].nodeValue : '';
            $(this).text('');

            $('<input type="text" class="form-control input-sm comment-input maxlength="120" "/>').appendTo($(this)).val(oldTextFull).select().blur(function () {
                if (oldTextReadable.length) {
                    var cellHtml = oldTextReadable;
                    cellHtml += '<a class="read-full-comments" style="padding-left: 3px; text-align: right;" href="javascript:void(0);"><img height="16" src="../assets/icons/search/comments-16.png" alt=""></a>';
                    cellHtml += '<p class="full-comments hide">' + oldTextFull + '</p>';
                    $(this).parent().html(cellHtml).find('input:text').remove();
                }
                else
                    $(this).parent().text(oldTextReadable).find('input:text').remove();
            });
        }

        e.stopPropagation();
    });

    $('td[abbr="colComment"]').live("keydown", function (e) {

        if (e.which == 13) {
            e.preventDefault();
            var dataEntered = $(e.target).val();
            //debugger;

            var callId = $(this).closest('tr').attr('callid');
            var colClass = 'Comment';

            if (dataEntered.indexOf('\'') != -1) {
                showErrorDialog('Single quotes is not allowed in Comments.');
                return;
            }

            if (dataEntered.length > 127) {
                showErrorDialog('Input exceeds the maximum word count of 128. Please shorten your text and try again.');
                return;
            }

            if (isECEnabled) {
                var recCallids = [];
                recCallids.push({ 'RecId': $(this).closest('tr').attr('recid'), 'CallId': $(this).closest('tr').attr('callid') });
                updateColCommentsContentsEC(recCallids, dataEntered, colClass);
            }
            else {
                updateColCommentsContents("'" + callId + "'", dataEntered, colClass);
            }

            $(this).find('input:text').remove();
        }
    });

    //if (mainPlaylistTable != null) {
    //    mainPlaylistTable.on('search.dt order.dt', function () {
    //        mainPlaylistTable.column(0, { search: 'applied', order: 'applied' }).nodes().each(function (cell, i) {
    //            cell.innerHTML = (i + 1).toString();
    //        });
    //    });
    //}
}

function selectAll(e, id) {
    console.log('selectAll');
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    setTimeout(function () {
        try {
            var grid = $("#tPlaylist tbody tr");
            $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });

            if (grid.length > 0)
                for (i = 0; i < grid.length; i++)
                    if (grid[i].cells[1].childNodes[0].type == "checkbox")
                        grid[i].cells[1].childNodes[0].checked = ($(e).is(':checked'));

            checkUncheckAll();
            $.unblockUI();
        } catch (e) {
            $.unblockUI();
            JL(frontendLogger).info('Error in selectAll : ' + e.toString());
        }
    }, 500);

}

function checkUncheckAll() {
    var totalCheckboxes = $("#tPlaylist input:checkbox.media-row-chk").size();
    var checkedCheckboxes = $("#tPlaylist input.media-row-chk:checkbox:checked").size();
    if (totalCheckboxes == checkedCheckboxes) $("input[id='SelectAll']:checkbox").prop('checked', true);
    else $("input[id='SelectAll']:checkbox").prop('checked', false);

    if (checkedCheckboxes > 1) $('.lnk-Open-All-playlist').removeClass('hide');
    else $('.lnk-Open-All-playlist').addClass('hide');
}

$('.media-row-chk').live('change', function (e) {
    checkUncheckAll();
});

function resizeMainPlaylistTable() {
    if (mainPlaylistTable) {
        let logoHeight = $('#logo').outerHeight(true) || 0;
        let windowWidth = $(window).width();

        // Increase table width dynamically
        let mainPlaylistTableHeight = $(window).height() - logoHeight - 145;
        let mainPlaylistTableWidth = Math.min(windowWidth * 0.9, 1800); // Max width 1100px

        JL(frontendLogger).info('New playlistTableSize ', mainPlaylistTableHeight, ' New Width: ', mainPlaylistTableWidth);

        // Ensure a minimum width to prevent shrinking
        mainPlaylistTableWidth = Math.max(mainPlaylistTableWidth, 1200); // Increased min width

        // Apply width to table wrapper
        $('#tPlaylist_wrapper').css({
            'width': mainPlaylistTableWidth + 'px'
        });

        $('#tPlaylist').css({
            'width': mainPlaylistTableWidth + 'px',
            'table-layout': 'fixed'
        });

        // Ensure thead and tbody match the table width
        $('#tPlaylist thead, #tPlaylist tbody').css({
            'width': '100%'
        });

        // Ensure column widths are equal
        $('#tPlaylist thead th, #tPlaylist tbody td').css({
            'min-width': '50px', // Adjust width for readability
            'max-width': 'auto',
            'white-space': 'nowrap' // Prevent text wrapping
        });

        // Force DataTables to recalculate column widths
        setTimeout(function () {
            if (mainPlaylistTable) {
                mainPlaylistTable.columns.adjust().draw();
            }
        }, 500);
    }
}

function deletePlaylist(plInfo) {
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: playlistHandlerURL,
        data: { id: plInfo.playlistId, RevSyncServerID: plInfo.revSyncServerID, name: plInfo.playlistName, method: 'DeletePlaylist', operation: 'Delete', isSharedPage: isSharedPage },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI(null, langKeys[currentLanguage]['sPlDelete']);
                    $.unblockUI();
                    $("#tPlaylist tbody tr[playlistId='" + plInfo.playlistId + "']").remove();
                    updateSerialNumber();
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred deletePlaylist(): ' + jqXHR.responseText);
        }
    });
}
function getplaylistDetail(playlistId, playlistName) {
    var plName = playlistId > 0 ? $("#tPlaylist > tbody > tr[playlistid='" + playlistId + "']").find('td:eq(1)').text() : '';

    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: playlistHandlerURL,
        data: { playlistId: playlistId, method: 'GetPlaylist', playlistName: plName, isSharedPage: isSharedPage },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    //JL(frontendLogger).info(response.data);
                    buildHTML(response.data);
                    tblPlaylistData = response.data.PlaylistDetails;
                    obj_PlaylistInspectionTemplates = response.data.InspectionTemplates;
                    obj_PlaylistDetails = response.data.PlaylistDetails;
                    changeLayout('detailMode');
                    $("select[id='cbxLoadPlayList'] > option[value='" + playlistId + "']").attr("selected", "selected");
                    displaylink();
                    //$("select[id='cbxLoadPlayList'] > option[value='" + playlistId + "']").attr("selected", "selected");
                    //var msgData = loginUserName + '¶' + playlistName;
                    //var params = '{Id:' + playlistId + ',Name:' + playlistName + '}';
                    //logActivity(loginUserId, 50, msgData, params);
                    if (isSharedPage)
                        $('#cbxLoadPlayList').prop('disabled', true);
                    if (isOnlyIQ3ModeEnabled || isIQ3View == true || isIQ3View == "True" || isIQ3View == "true") {
                        setTimeout(function () {
                            $('#btnSearchSaveCalls').hide();
                        }, 500);
                    }

                    $('#hdnActiveTab').val('Listview'); hideMetadata(); displaylink();
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred getplaylistDetail(): ' + jqXHR.responseText);
        }
    });
}

function updatePlaylistAttributes(playlistId, playlistUpdatedName, visibilityType, playlistComments) {
    var oldPlName = playlistId > 0 ? $("#tPlaylist > tbody > tr[playlistid='" + playlistId + "']").find('td:eq(1)').text() : '';

    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: {
            method: 'UpdatePlaylistAttributes',
            playlistId: playlistId, playlistName: playlistUpdatedName, oldPlName: oldPlName, VisibilityType: visibilityType == 0 ? "0" : "1",
            Comments: playlistComments, isSharedPage: isSharedPage
        },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $("#tPlaylist tbody tr[playlistId='" + playlistId + "'] td:nth-child(3)").text(playlistUpdatedName);
                    $("#tPlaylist tbody tr[playlistId='" + playlistId + "'").attr('playlistname', playlistUpdatedName);

                    var visibilityTypeString = visibilityType == "0" ? "Private" : "Public";
                    $("#tPlaylist tbody tr[playlistId='" + playlistId + "'] td:nth-child(6)").text(visibilityTypeString);
                    $("#tPlaylist tbody tr[playlistId='" + playlistId + "'").attr('visibilityType', visibilityType);
                    $("#tPlaylist tbody tr[playlistId='" + playlistId + "'").attr('comments', playlistComments);

                    if (visibilityType == "0")
                        $("#tPlaylist tbody tr[playlistId='" + playlistId + "'").find('.SharePlaylist').hide();
                    else if (visibilityType == "1")
                        $("#tPlaylist tbody tr[playlistId='" + playlistId + "'").find('.SharePlaylist').show();

                    $("#cbxLoadPlayList option[value=" + playlistId + "]").html(playlistUpdatedName);
                    $('#txtEditPlaylistName').val('');
                    $('#txtPLComments').val('');
                    $('#lblPlaylistErrorMsg').html('');
                    $("#divEditPlaylist").mmsDialog("close");
                    break;
                default:
                    $.unblockUI();
                    $('#txtEditPlaylistName').val('');
                    $('#lblPlaylistErrorMsg').html('');
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            $('#txtEditPlaylistName').val('');
            $('#lblPlaylistErrorMsg').html('');
            showExceptionDialog('An error has occurred updatePlaylistName(): ' + jqXHR.responseText);
        }
    });
}
function addAlternateFileName(playlistId, callId, alternateFileName) {
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { method: 'AddAlternateFileName', playlistId: playlistId, callId: callId, alternateFileName: alternateFileName, isSharedPage: isSharedPage },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    JL(frontendLogger).info(response.data);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred addAlternateFileName(): ' + jqXHR.responseText);
        }
    });
}
//#endregion

//#region Playlist Note
function buildNoteli(playlistNote) {
    var noteli = '<li noteId=' + playlistNote.Id + ' noteType=' + playlistNote.NoteType + '>' +
        '<div class="row" >' +
        '<div class="col-xs-8"><p><i class="fa fa-bell activity-image"></i>' + playlistNote.Note + '</p></div>' +
        '<div class="col-xs-4 text-right">' +
        (!isViewPlaylist ? ('<button type="button" class="btn btn-iconed btn-primary btn-xs EditNote"><i class="fa fa-pencil"></i></button>' +
            '<a href="javascript:void(0)" class="btn btn-danger btn-xs remove-tr DeleteNote"><i class="fa fa-times"></i></a>') : "") +
        '</div>' +
        '</div>' +
        '</li>';
    return noteli;
}
function populatePlaylistNote(playlistNote) {
    if (playlistNote.Id != "") {
        if (playlistNote.NoteType == 1) {
            $('#txtInternalNote').val('');
            $("#ulInternalNote").prepend(buildNoteli(playlistNote));
        }
        else {
            $('#txtSharedNote').val('');
            $("#ulSharedNote").prepend(buildNoteli(playlistNote));
        }
    }
    else {
        JL(frontendLogger).fatalException('NoteId is empty');
    }
}
function getPlaylistNoteObject(id, playlistId, noteType, note, createdDate, modifiedDate, isDeleted) {
    var playlistNote = {
        Id: id,
        PlaylistId: playlistId,
        NoteType: noteType,
        Note: note,
        CreatedDate: createdDate,
        ModifiedDate: modifiedDate,
        IsDeleted: isDeleted,
    };
    return playlistNote;
}
function insertPlaylistNote(playlistNote) {
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { method: 'InsertPlaylistNote', PlaylistNote: JSON.stringify(playlistNote), isSharedPage: isSharedPage },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    var playlistNote = response.data;
                    //JL(frontendLogger).info(playlistNote);
                    populatePlaylistNote(playlistNote);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred insertPlaylistNote(): ' + jqXHR.responseText);
        }
    });
}
function updatePlaylistNote(playlistNoteObject) {
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { method: 'UpdatePlaylistNote', PlaylistNote: JSON.stringify(playlistNoteObject), isSharedPage: isSharedPage },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    JL(frontendLogger).info(response.data);
                    if (playlistNoteObject.NoteType == 1)
                        $("#ulInternalNote li[noteId='" + playlistNoteObject.Id + "']").find('p').html('<i class="fa fa-bell activity-image"></i>' + playlistNoteObject.Note);
                    else
                        $("#ulSharedNote li[noteId='" + playlistNoteObject.Id + "']").find('p').html('<i class="fa fa-bell activity-image"></i>' + playlistNoteObject.Note);
                    $("#divEditPlaylistNote").mmsDialog("close");
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred updatePlaylistNote(): ' + jqXHR.responseText);
        }
    });
}
function deletePlaylistNote(playlistId, noteId, noteType) {
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { method: 'DeletePlaylistNote', playlistId: playlistId, noteId: noteId, isSharedPage: isSharedPage },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    JL(frontendLogger).info(response.data);
                    if (noteType == 1)
                        $("#ulInternalNote li[noteId='" + noteId + "']").remove();
                    else
                        $("#ulSharedNote li[noteId='" + noteId + "']").remove();
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred updatePlaylistNote(): ' + jqXHR.responseText);
        }
    });
}

function removePlaylistItem(pldId, RevSyncServerPlaylistID) {
    //JL(frontendLogger).info(pldId); return;
    var plName = currentPlaylistName;
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { id: pldId, RevSyncServerPlaylistID: RevSyncServerPlaylistID, plName: currentPlaylistName, method: 'RemovePlaylistItem', operation: 'Delete', isSharedPage: isSharedPage },
        success: function (response) {
            switch (response.success) {
                case true:
                    var callId = $("#tblPlaylist tbody tr[id='" + pldId + "']").attr('callId');
                    if (obj_PlaylistDetails) {
                        // Remove deleted playlist item from obj_PlaylistDetails object.
                        obj_PlaylistDetails = $.grep(obj_PlaylistDetails, function (e) {
                            return e.Id != pldId;
                        });
                    }
                    //obj_PlaylistInspectionTemplates       //obj_PlaylistDetails
                    var temp_PlaylistInspectionTemplates = obj_PlaylistInspectionTemplates;
                    $.each(temp_PlaylistInspectionTemplates, function (index, obj_PlaylistInspectionTemplate) {
                        var hasAnyPlaylistItem = false;
                        $.each(obj_PlaylistDetails, function (index, obj_PlaylistDetail) {
                            if (obj_PlaylistDetail.InspectionTemplateId == obj_PlaylistInspectionTemplate.Id)
                                hasAnyPlaylistItem = true;
                        });
                        if (hasAnyPlaylistItem == false) {
                            obj_PlaylistInspectionTemplates = $.grep(obj_PlaylistInspectionTemplates, function (e) {
                                return e.Id != obj_PlaylistInspectionTemplate.Id;
                            });
                        }
                    });
                    if (boolIEBrowser)
                        slDeleteMedia_Silverlight(callId);
                    else
                        slDeleteMedia(callId);
                    //$("#tblPlaylist tbody tr[id='" + pldId + "']").remove();
                    JL(frontendLogger).info('Playlist Delete - ', callId);
                    //playlistTable.row("[callid='" + callId + "']").remove().draw();
                    updateTableAfterRemovingItems(callId);

                    $.growlUI(null, langKeys[currentLanguage]['sPldItemDel']);

                    var existingItems = parseInt($("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "']").attr('noofitems'), 10);
                    var newItems = existingItems - 1;
                    updateNewNoItemsInPlaylist(newItems);
                    //$("#ulPlaylists > li.active").attr('noofitems', newItems);

                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred removePlaylistItem(): ' + jqXHR.responseText);
        }
    });
}

function removePlaylistUserItem(callId) {
    //JL(frontendLogger).info(pldId); return;
    var plName = currentPlaylistName; //$('#cbxLoadPlayList :selected').text()// $("#ulPlaylists > li.active").find('a').html();
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { callId: callId, plName: plName, method: 'RemoveUserPlaylistItem', isSharedPage: isSharedPage },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI(null, langKeys[currentLanguage]['sPldItemDel']);

                    JL(frontendLogger).info('Playlist Delete - ', callId);

                    updateTableAfterRemovingItems(callId);

                    if (boolIEBrowser)
                        slDeleteMedia_Silverlight(callId);
                    else
                        slDeleteMedia(callId);

                    var existingItems = parseInt($("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "']").attr('noofitems'), 10);
                    var deletedItems = response.data;
                    var newItems = existingItems - deletedItems;
                    updateNewNoItemsInPlaylist(newItems);

                    $("#divLvVideoPlayer").html('');

                    if (newItems == 0) {
                        playlistTable.clear();
                        redrawPlaylistTable();
                        displaylink();
                        configureListViewHeight();
                    }

                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred removePlaylistUserItem(): ' + jqXHR.responseText);
        }
    });
}

function removePlaylistItems(Ids) {
    //JL(frontendLogger).info(plId); return;
    var plId = Ids.split('#')[0];
    var RevSyncServerPlaylistID = Ids.split('#')[0];

    var plName = currentPlaylistName;
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { plId: plId, RevSyncServerPlaylistID: RevSyncServerPlaylistID, plName: plName, method: 'RemovePlaylistItems', operation: 'Delete', isSharedPage: isSharedPage },
        success: function (response) {
            switch (response.success) {
                case true:

                    JL(frontendLogger).info('Playlist Delete All Items - ', Ids);
                    playlistTable.clear();
                    redrawPlaylistTable();

                    // $('#plList').html('No data available.'); ==> KM on 20180409
                    displaylink();
                    if (boolIEBrowser) {
                        slReset_Silverlight();
                    }
                    else
                        slReset();
                    $.growlUI(null, langKeys[currentLanguage]['sPldItemsDel']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred removePlaylistItems(): ' + jqXHR.responseText);
        }
    });
};

function updateNewNoItemsInPlaylist(newItems) {
    currentPlaylistNoOfCalls = newItems;
    $("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "']").attr('noofitems', newItems);
    $("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "'] td.pl-call-count").html(newItems);
}

function updateTableAfterRemovingItems(callId) {
    playlistTable.row("[callid='" + callId + "']").remove().draw();
    $("#ulPlaylistUserAddedFiles div[callid='" + callId + "']").parent('li').remove();
}

function playlist_addfiles() {

    if (!isFlashEnabled())
        showErrorDialog(langKeys[currentLanguage]['emsgFlashNotFound'] + "<a href='http://get.adobe.com/flashplayer/' target='_blank'>" + langKeys[currentLanguage]['emsgClickHereToInstall'] + "</a>");
    else {
        $.ajax({
            type: 'POST',
            url: playlistHandlerURL,
            data: { method: 'CountPlaylistItems', PlaylistId: currentPlaylistId, isSharedPage: isSharedPage },
            cache: false,
            success: function (response) {
                $.unblockUI();
                switch (response.success) {
                    case true:
                        currentPlaylistItemsCount = response.data;
                        ShowAddUserFileForm();//FilePlayer.js function
                        var playlistId = currentPlaylistId;
                        var userId = $('#header-login-name').attr('uid');
                        readDirectory_addfiles(playlistId, userId);
                        break;
                    case false:
                        $.unblockUI();
                        showErrorDialog(response.message);
                        break;
                    default:
                        $.unblockUI();
                        showErrorDialog(response.message);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred in CountPlaylistItems: ' + jqXHR.responseText);
            }
        });
    }
}

$(function () {
    $('#btnPlaylistAddFiles').live('click', function () {
        if (!isFlashEnabled())
            showErrorDialog(langKeys[currentLanguage]['emsgFlashNotFound'] + "<a href='http://get.adobe.com/flashplayer/' target='_blank'>" + langKeys[currentLanguage]['emsgClickHereToInstall'] + "</a>");
        else {
            bindUploadify_addfiles();
            $("#divPlaylistAddFiles").mmsDialog("open");
            var playlistId = currentPlaylistId;
            var userId = $('#header-login-name').attr('uid');
            readDirectory_addfiles(playlistId, userId);
        }
    });
});

function clearSelectedFileinAUF() {

    $("#ulPlaylistAddFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
}

//  bindUploadify_addfiles();

function bindUploadify_addfiles() {
    $("#fluTLAddUserFile").mmsDialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['msgAddUserFile'],
        width: '500',
        maxHeight: '400',
        height: '250',
        buttons: [{
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).mmsDialog("close");
            }
        }],
        open: function () {
            videoPlayerIssue('open');
        },
        close: function () {
            videoPlayerIssue('close');
        }
    });
}

function readDirectory_addfiles(plId, userId) {
    $('#ulUploadedFiles li').remove();
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        //data: { methodName: 'getFiles_playlistaddfiles', plId: plId, uId: userId, isSharedPage: isSharedPage },
        data: { methodName: 'getFiles_playlistaddfiles', plId: plId, uId: userNum, isSharedPage: isSharedPage },
        cache: false,
        success: function (response) {

            $.unblockUI();
            switch (response.success) {
                case true:
                    if (response.data.length == 0)//langKeys[currentLanguage]['imsgNoFileUpload']
                        $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    else {
                        buildUploadedFilesList_addfiles(response.data);
                        $('#ulUploadedFiles > #liNoFileUploaded').remove();
                    }
                    break;
                case false:
                    $.unblockUI();
                    $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in readDirectory(): ' + jqXHR.responseText);
        }
    });

}

function deleteFile_addfiles(plId, uId, dirName, fileName) {
    //$.blockUI();
    var plName = currentPlaylistName;
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        data: {
            methodName: 'delete_playlistaddfiles', plId: plId, plName: plName, uId: uId,
            uName: $.trim($('#header-login-name').text()), dirName: dirName,
            filename: fileName, isSharedPage: isSharedPage
        },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $("#ulUploadedFiles > li[id='" + fileName + "']").remove();
                    if ($('#ulUploadedFiles > li').length == 0)
                        $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    $.growlUI(null, langKeys[currentLanguage]['sPlUplFileDel']/*response.message*/);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in deleteFile(): ' + jqXHR.responseText);
        }
    });

}

function buildUploadedFilesList_addfiles(data) {
    //var $ul = $('<ul></ul>');
    $.each(data, function (key, val) {
        var counter = key + 1;
        //JL(frontendLogger).info(counter);
        $("#ulUploadedFiles").append("<li id='" + val.split(',')[1] + "'>" + counter + ' - ' + val.split(',')[1] + "<img dname='" + val.split(',')[0] + "' fname='" + val.split(',')[1] + "' width='20' height='20' src='../assets/icons/general/trash_24X24.png' style='cursor:pointer;'></li>");
        //var row = ('<li>' + val + '</li>');
        //$("#files").append(row);
    });
}
//#endregion

//#region Playlist Dropdown

function removeVideoFilePlayerSrc() {
    $("#videofileplayer").attr("src", '');
    $("#videofileplayer1").attr("src", '');
}
//#endregion

//region Paper Clip
function buildPaperClipli(paperClip) {
    //text = text + "<li id='" + paperClip.FileName + "'>" + i + ' - ' + paperClip.FileName + "<img dname='" + paperClip.DirectoryName + "' fname='" + paperClip.FileName + "' width='20' height='20' src='../assets/icons/general/trash_24X24.png' style='cursor:pointer;'></li>";
    //var iconClass = '';
    //switch (paperClip.FileType) {
    //    case ".doc":
    //        iconClass = 'fa-file-word-o';
    //        break;
    //    case ".docx":
    //        iconClass = 'fa-file-word-o';
    //        break;
    //    case ".pdf":
    //        iconClass = 'fa-file-pdf-o';
    //        break;
    //    case ".xls":
    //        iconClass = 'fa-file-excel-o';
    //        break;
    //    case ".xlsx":
    //        iconClass = 'fa-file-excel-o';
    //        break;
    //    case ".ppt":
    //        iconClass = 'fa-file-powerpoint-o';
    //        break;
    //    case ".pptx":
    //        iconClass = 'fa-file-powerpoint-o';
    //        break;
    //    case ".png":
    //        iconClass = 'fa-file-picture-o';
    //        break;
    //    case ".PNG":
    //        iconClass = 'fa-file-picture-o';
    //        break;
    //    case ".jpg":
    //        iconClass = 'fa-file-picture-o';
    //        break;
    //    case ".bmp":
    //        iconClass = 'fa-file-picture-o';
    //        break;
    //    case ".txt":
    //        iconClass = 'fa-file-text';
    //        break;
    //    case ".log":
    //        iconClass = 'fa-file-text';
    //        break;
    //    case ".mp4":
    //        iconClass = 'fa-file-video';
    //        break;
    //    case ".avi":
    //        iconClass = 'fa-file-movie';
    //        break;
    //    case ".mpg":
    //        iconClass = 'fa-file-movie';
    //        break;
    //    case ".zip":
    //        iconClass = 'fa-file-zip-o';
    //        break;
    //    case ".rar":
    //        iconClass = 'fa-file-zip-o';
    //        break;
    //    default:
    //        iconClass = 'fa-file';
    //        break;

    //}
    var paperClipli = '<li fileName="' + paperClip.FileName + '">' +
        '<div class="row" flName="' + paperClip.FileName + '" style="margin-left: 0px; margin-right: 0px;">' +
        '<div class="col-xs-10" onclick="showAttachedPaperClipItem(this);" style="padding-left: 0px; padding-right: 0px;">' +
        '<div style="display: flex; width: 100%; line-height: 1.8em;"><i class="fa ' + getAttachFilesIcon(paperClip.FileType) + '" style="font-size: 1.8em; color: #2f94d8;">&nbsp;</i><span title="' + paperClip.FileName + '" style="font-weight: 600; overflow: hidden; text-overflow: ellipsis;">' + paperClip.FileName + '</div>' +
        '</div>' +

        '<div class="col-xs-2 text-right" style="padding-right: 0px;">' +
        '<a href="javascript:void(0);" fileName="' + paperClip.FileName + '" directoryName="' + paperClip.DirectoryName + '" class="btn btn-danger btn-xs remove-tr DeletePaperClip"><i class="fa fa-times" style="margin: 0"></i></a>' +
        '</div>' +
        '</div>' +
        '</li>';

    return paperClipli;
}

function populatePaperClip(paperClip) {
    if (paperClip.SrNo != "") {
        $("#ulPaperClip").prepend(buildPaperClipli(paperClip));
    }
    else {
        JL(frontendLogger).fatalException('Paper Clip Sr. No. is empty');
    }
}

function getPlaylistPaperClips(playlistId) {
    $('#ulUploadedFiles li').remove();
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { method: 'getPlaylistPaperClip', playlistId: playlistId, uId: loginUserId, isSharedPage: isSharedPage },
        cache: false,
        success: function (response) {
            $.unblockUI();
            switch (response.success) {
                case true:
                    JL(frontendLogger).info(response.data);
                    //if (response.data.length == 0)//langKeys[currentLanguage]['imsgNoFileUpload']
                    //    $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    //else {
                    //    buildUploadedFilesList(response.data);
                    //    $('#ulUploadedFiles > #liNoFileUploaded').remove();
                    //}
                    break;
                case false:
                    $.unblockUI();
                    $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in getPlaylistPaperClips(): ' + jqXHR.responseText);
        }
    });
}
function deletePaperClip(obj) {//(dirName, fileName) {

    var requestData = {
        methodName: 'delete',
        plId: currentPlaylistId,
        plName: currentPlaylistName,
        //uId: loginUserId,
        uId: userNum,
        uName: loginUserName,
        dirName: obj.DirName,
        filename: obj.FileName,
        isSharedPage: isSharedPage
    };
    console.log('Input data for deletePaperClip:', requestData);
 
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        data: requestData,
        cache: false,
        success: function (response) {
            console.log('Response from deletePaperClip:', response);
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, langKeys[currentLanguage]['sPlUplFileDel']);
                    $("#ulPaperClip li[filename='" + obj.FileName + "']").remove();
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (xhr, status, error) {
            console.error('AJAX error:', status, error);
            $.unblockUI();
        }
    });

}
//#endregion


//#region Add Files
function showExportDialog() {
    var optionString = '<div>' +
        '<div id=\"divErrMsg\" class=\"hide\">' +
        '<span id=\"lblPlAddItemErrorMsg\" style=\"font-weight:bold; color:#b94a48;\" ></span>' +
        '</div>' +
        '<label style=\"margin:3px;\"><input type=\"radio\" name=\"rbAddPlaylistItem\" value=\"paperClip\" checked=\"checked\" /> ' + langKeys[currentLanguage]['cdAttachInFolder'] + '</label>' +
        '</br>' +
        '<label style=\"margin:3px;\"><input type=\"radio\" name=\"rbAddPlaylistItem\" value=\"manualFile\" /> ' + langKeys[currentLanguage]['cdInsertIntoPlaylist'] + '</label>' +
        '</div>';

    if (nState == FilePlayerState.Play || nState == FilePlayerState.PendingPlay)
        btnPause_Click();

    var $dialog = $(optionString).mmsDialog({
        autoOpen: false,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['cdSelectOption'],
        modal: true,
        width: 300,
        close: function () {
            $(this).mmsDialog('destroy').remove();
        },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnNext'],
                click: function () {
                    var radioGrp = $('input:radio[name="rbAddPlaylistItem"]:checked');
                    if (radioGrp.length == 0) {
                        $(this).find('#divErrMsg').removeClass('hide');
                        $('#lblPlAddItemErrorMsg').html(langKeys[currentLanguage]['emsgSelectOption']);
                        return;
                    }
                    var optionType = radioGrp.val();
                    JL(frontendLogger).info(optionType);
                    switch (optionType) {
                        case 'paperClip':
                            playlistPaperclip();
                            break;
                        case 'manualFile':
                            if (boolIEBrowser)
                                slAddUserFile();
                            else
                                playlist_addfiles();
                            break;
                        default:
                            throw "Invalid ModelType '" + modelType + "'";
                    }
                    $(this).mmsDialog('close');
                }
            }
        ]
    });
    $dialog.mmsDialog('open');
}
function playlistPaperclip() {
    bindUploadify();
    $("#divPaperClip").mmsDialog("open");
    var playlistId = currentPlaylistId; //$("#ulPlaylists > li.active").attr("id");
    var userId = $('#header-login-name').attr('uid');
}

//#region Uploadify function

function isFlashEnabled() {
    //var hasFlash = false;
    //try {
    //    var fo = new ActiveXObject('ShockwaveFlash.ShockwaveFlash');
    //    if (fo) hasFlash = true;
    //}
    //catch (e) {
    //    if (navigator.mimeTypes["application/x-shockwave-flash"] != undefined) hasFlash = true;
    //}
    //return hasFlash;
    return true;
}

function bindUploadify() {
    $("#divPaperClip").mmsDialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['msgDlgTitlePaperClip'],
        width: '500',
        maxHeight: '400',
        height: 'auto',
        buttons: [{
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).mmsDialog("close");
            }
        }],
        open: function () {
            videoPlayerIssue('open');
        },
        close: function () {
            videoPlayerIssue('close');
        }
    });
}

function deleteAddUserFile(plId, uId, dirName, fileName, lastItem, bIsCurrentUserAddedFile) {//arivu
    //$.blockUI();
    var plName = currentPlaylistName;
    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/VoiceRecHandlers/UploadFileHandler.ashx',
        data: {
            methodName: 'delete_playlistaddfiles', plId: plId, plName: plName,
            uId: uId, uName: $.trim($('#header-login-name').text()), dirName: dirName,
            Filename: fileName, AddUserFile: bIsCurrentUserAddedFile,
            isSharedPage: isSharedPage
        },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:

                    $.unblockUI();
                    //$("#ulPaperClip > li[id='" + fileName + "']").remove();
                    $("#ulPaperClip > li[filename='" + fileName + "']").remove();
                    //if ($('#ulUploadedFiles > li').length == 0)
                    //    $("#ulUploadedFiles").append('<li id="liNoFileUploaded">' + langKeys[currentLanguage]['imsgNoFileUpload'] + '</li>');
                    $.growlUI(null, langKeys[currentLanguage]['sPlUplFileDel']/*response.message*/);

                    //if (lastItem) {
                    //    bIsZeroItem = true;
                    //}
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in deleteFile(): ' + jqXHR.responseText);
        }
    });

}

function bindUploadify_addfiles() {
    $("#fluTLAddUserFile").mmsDialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['msgAddUserFile'],
        width: '500',
        maxHeight: '400',
        height: '250',
        buttons: [{
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).mmsDialog("close");
            }
        }],
        open: function () {
            videoPlayerIssue('open');
        },
        close: function () {
            videoPlayerIssue('close');
        }
    });
}

//#endregion

function videoPlayerIssue(dialogStatus) {
    switch (dialogStatus) {
        case 'open':
            $('#jqDialogStatus').attr('jqDialogStatus', 'open');
            //JL(frontendLogger).info($('#divLvVideoPlayer').css('display'));
            if (userBrowser == 'InternetExplorer') {
                if ($('#hdnActiveTab').val() == "Timeline")
                    $('#divTimeline').css('display', 'none'); //$('#divTimeline').hide();
                if ($('#hdnActiveTab').val() == "Listview")
                    $('#divLvVideoPlayerContainer').css('display', 'none'); //$('#divLvVideoPlayer').css('display', 'none');
            }
            break;
        case 'close':
            //JL(frontendLogger).info($('#divLvVideoPlayer').css('display'));
            $('#jqDialogStatus').attr('jqDialogStatus', 'close');
            if (userBrowser == 'InternetExplorer') {
                if ($('#hdnActiveTab').val() == "Timeline" && slHaveVideo())
                    $('#divTimeline').css('display', 'block'); //$('#divTimeline').show();
                var plCallType = $('#tblPlaylist tbody tr.selected').attr('ctype');
                //JL(frontendLogger).info(plCallType);
                if (plCallType == undefined) {
                    $('#divLvVideoPlayer').css('display', 'block');
                    $('#divLvVideoPlayerContainer').css('display', 'block');
                }
                if ($('#hdnActiveTab').val() == "Listview" && (plCallType == 2 || plCallType == 6 || plCallType == 9 || plCallType == 10)) {
                    //$('#divLvVideoPlayer').attr('jqDialogStatus') == 'close')
                    //$('#divLvVideoPlayer').css('display') == 'none')
                    //$('#divLvVideoPlayer').is(":visible");

                    $('#divLvVideoPlayerContainer').css('display', 'none');
                    $('#divLvVideoPlayer').css('display', 'none');

                    //$('#divLvVideoPlayer').slideUp(3000);
                }
            }
            break;
        default:
            break;
    }
}

function removeAlldZUploadpaperClipPreviews() {
    $("#dZUploadpaperClip .dz-preview").remove();
}

//#endregion

//#region

function searchPlListviewTable(inputVal) {
    var table = $('#tblPlaylist');
    table.find('tr').each(function (index, row) {
        var allCells = $(row).find('td');
        //if (allCells.length > 0) {
        if (allCells.length) {
            var found = false;
            allCells.each(function (index, td) {
                var regExp = new RegExp(inputVal, 'i');
                if (regExp.test($(td).text())) {
                    found = true;
                    return false;
                }
            });
            if (found == true)
                $(row).show();
            else
                $(row).hide();
        }
    });
}

//#endregion

//#region Map Playlist
function loadPlMapCtrl(plId) {
    plURL = 'LoadPlaylistForm.aspx';
    $.ajax({
        type: "POST",
        async: false,
        url: plURL,
        success: function (html) {
            videoPlayerIssue('open');
            $("#divMapPlaylist").html(html);
            makePlMapDialog();
            $("#divMapPlaylist").mmsDialog("open");
        },
        error: function (msg) {
            showErrorDialog('There was a problem while loading playlist map control.' + msg);
        }
    });
}
function makePlMapDialog() {
    $("#divMapPlaylist").mmsDialog({
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['msgDlgTitleMapPl'],
        width: '99.5%',
        height: $(window).height() - 100,
        modal: true,
        resizable: false,
        draggable: false
        , beforeClose: function (e, ui) {
            ////alert($(e.currentTarget).parent().attr('id'));
        }
        , open: function () {
            $.unblockUI();
            videoPlayerIssue('open');
        }
        , close: function (e, ui) {
            $('#lblMapPlErrorMsg').hide();
            videoPlayerIssue('close');
        }
    });
}
//#endregion

//#region Save Playlist
function saveTracks() {
    $("#divExportPlaylist").mmsDialog("open");
    return false;
}
function saveTracksByPLId() {
    var plurl = handlersBaseURL + '/VoiceRecHandlers/ExportPlaylistHandler.ashx';
    localPlaylistExportPercent = 0;

    $("#divExportPlaylist").mmsDialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['msgDlgTitleExportPl'],
        width: '500px',
        dialogClass: 'plSave',
        open: function (event, ui) {
            $(this).parent().parent().find(".ui-new-dialog-titlebar-close").hide();
            $('#chkProtectFromChange').attr('checked', false);

            if (nState == FilePlayerState.Play || nState == FilePlayerState.PendingPlay)
                btnPause_Click();

            videoPlayerIssue('open');
        },
        close: function () {
            videoPlayerIssue('close');
        },
        buttons: [
            {
                id: "button-PlSave",
                text: langKeys[currentLanguage]['msgBtnExport'],
                click: function () {
                    var radioExportType = $('input:radio[name="rbExportPlaylist"]:checked');
                    var protectChange = $('#chkProtectFromChange').prop('checked');
                    if (radioExportType.length == 0) {
                        $('#lblExportPlaylistMsg').html(langKeys[currentLanguage]['emsgPlSelectExportType'] + " </br>").show();
                        return;
                    }
                    if (currentPlaylistNoOfCalls == 0) {
                        $('#lblExportPlaylistMsg').html(langKeys[currentLanguage]['emsgPlNoRecordInPL'] + " </br>").show();
                        return;
                    }
                    if (currentPlaylistNoOfCalls > 0) {
                        var plId = currentPlaylistId;
                        var userId = loginUserId;
                        if (plId > 0) {
                            $('#lblExportPlaylistMsg').html('').hide();
                            // Disable the buttons
                            $('#button-PlSave').attr('disabled', true);
                            $('#button-PlClose').attr('disabled', true);

                            $.ajax({
                                type: 'POST',
                                url: plurl,
                                data:
                                {
                                    MethodName: 'StartExportProcess'
                                    , uniqueId: uniqueId
                                    , playlistId: plId
                                    , userId: userId
                                    , exportMode: $('#hdnActiveTab').val()
                                    , exportType: radioExportType.val()
                                    , protectFromChange: protectChange
                                    , lvExportType: 'slPlayer'//'htmlPlayer'//
                                    , isSharedPage: isSharedPage
                                },
                                success: function (response) {
                                    zipFileName = response;
                                    getStatus();
                                },
                                error: function (response) {
                                    //alert(response);
                                    JL(frontendLogger).info(response);
                                    $('#button-PlSave').removeAttr('disabled');
                                    $('#button-PlClose').removeAttr('disabled');
                                    if (response.responseText)
                                        showExceptionDialog("Error occured while saveTracksByPLId(): " + response.responseText);
                                }
                            });
                        }
                    }
                    else {
                        $('#lblExportPlaylistMsg').html(langKeys[currentLanguage]['emsgPlNoExportableMedia'] + " </br>").show();
                    }
                    $(".ui-new-dialog").addClass("plSave");
                }
            },
            {
                id: "button-PlClose",
                text: langKeys[currentLanguage]['msgBtnClose'],
                click: function () {
                    $('#lblExportPlaylistMsg').html('').hide();
                    //$('#divExportPlaylist').find('input:radio:checked').removeAttr('checked');
                    updateProgressBar(0, $('#divExportprogressBar'));
                    $(this).mmsDialog("close");
                }
            }
        ]
    });
}
function updateProgressBar(percent, $element) {
    if (percent != "") {
        if (percent > 20) {
            localPlaylistExportPercent = percent;
        }
        else {
            if (localPlaylistExportPercent < 20) {
                localPlaylistExportPercent += 1;
            }
        }
        var progressBarWidth = localPlaylistExportPercent * $element.width() / 100;
        $element.find('div').animate({ width: progressBarWidth }, 500).html(localPlaylistExportPercent + " %"); // + " %&nbsp;");
    }
    else {
        $element.find('div').animate({ width: 0 }, 500).html("");
        localPlaylistExportPercent = 0;
    }
}
function getStatus() {
    var plurl = handlersBaseURL + '/VoiceRecHandlers/ExportPlaylistHandler.ashx';
    $.ajax({
        type: "GET",
        url: plurl,
        data: { 'MethodName': 'GetCurrentProgress', 'uniqueId': uniqueId, isSharedPage: isSharedPage },
        success: function (response) {
            switch (response.success) {
                case true:
                    updateProgressBar(response.data, $('#divExportprogressBar'));
                    if (response.data != "100") {
                        window.setTimeout("getStatus()", progressDuration);
                    }
                    else {
                        $.growlUI(null, langKeys[currentLanguage]['sPlExported']);
                        window.setTimeout(function () {
                            downloadFile(zipFileName);
                            zipFileName = '';
                            setTimeout(function () {
                                $('#button-PlSave').removeAttr('disabled');
                                $('#button-PlClose').removeAttr('disabled');
                            }, 2000);
                        }, zipDownloadDur);
                    };
                    break;
                default:
                    updateProgressBar('', $('#divExportprogressBar'));
                    $.growlUI(null, langKeys[currentLanguage]['setupProbProcessing']);
                    $('#button-PlSave').removeAttr('disabled');
                    $('#button-PlClose').removeAttr('disabled');
                    showErrorDialog(langKeys[currentLanguage]['setupProbProcessing']);
                    break;
            }
        }
    });
}
function downloadFile(flName) {
    window.location = handlersBaseURL + '/VoiceRecHandlers/DownloadFile.ashx' + "?filename=" + flName;
    //logging
    var plId = currentPlaylistId;
    var plName = currentPlaylistName;
    var userId = loginUserId;
    var radioExportType = $('input:radio[name="rbExportPlaylist"]:checked');
    var protectChange = $('#chkProtectFromChange').prop('checked');

    var msgData = loginUserName + '¶' + plName + '¶' + radioExportType.val();
    var params = { MethodName: 'StartExportProcess', flName: flName + '.zip', playlistId: plId, userId: userId, exportMode: $('#hdnActiveTab').val(), exportType: radioExportType.val(), protectFromChange: protectChange, lvExportType: 'slPlayer' };
    if (protectChange) {
        logActivity(loginUserId, 60, msgData, JSON.stringify(params));
    }
    else {
        logActivity(loginUserId, 61, msgData, JSON.stringify(params));
    }

}
//#endregion

//#region utility function
function parseDate(jsonDate, bOnlyDateRequired, bAmericamFormat) {
    //JL(frontendLogger).info('Inside parseDate function.');
    var date = new Date(jsonDate.match(/\d+/)[0] * 1);
    if (bOnlyDateRequired) {
        var dd = date.getDate();
        var mm = date.getMonth() + 1;
        var hh = date.getHours();
        var min = date.getMinutes();
        var sec = date.getSeconds();
        var yyyy = date.getFullYear();
        if (dd < 10)
            dd = '0' + dd;
        if (mm < 10)
            mm = '0' + mm;
        if (hh < 10)
            hh = '0' + hh;
        if (min < 10)
            min = '0' + min;
        if (sec < 10)
            sec = '0' + sec;
        if (bAmericamFormat)
            return mm + '/' + dd + '/' + yyyy;
        else
            return yyyy + '-' + mm + '-' + dd;
    }
    else
        return date;
}
function checkMSIEBrowser() {
    var ua = window.navigator.userAgent;
    var msie = ua.indexOf("MSIE ");

    if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) {
        return true;
    }
    else {
        return false;
    }
    return false;
}
function updateSerialNumber() {
    $('#tPlaylist tbody tr').each(function (index) {
        $(this).find('td').eq(1).text(index + 1);
    });
}
function resetPlaylistDetailsDialog() {
    $("#ulPaperClip").html('');
    $("#ulInternalNote").html('');
    $("#ulSharedNote").html('');
}
//#endregion

//#region Metadata Functions

function showMetadata(eventId) {
    cleanPlMetadata();
    $('#divPlMetadata').removeClass('hide');
    $(".md-case-info").hide();
    $(".revview-case-info").hide();
    $(".inquire-case-info").show();

    // Maximize if minimized
    $("#pl-event-details .widget-content").css({ 'display': 'block' });
    $("#pl-event-details a.widget-control-minimize i.fa-plus-circle").parent().trigger('click');

    $("#pl-event-details .widget-content").css({ 'max-height': $(window).height() * 0.4 });
    var plEventDetailsHeight = $("#pl-event-details .widget-content").height();

    if (plEventDetailsHeight < 200)
        plEventDetailsHeight = 200;
    $("#pl-event-bm .widget-content").css({ 'height': plEventDetailsHeight, 'max-height': plEventDetailsHeight });
    $("#pl-event-chat .widget-content").css({ 'height': plEventDetailsHeight, 'max-height': plEventDetailsHeight });
    getPlaylistEventDetail(eventId);
}

function showRevviewMetadata(eventId, revviewEvent) {
    cleanPlMetadata();
    $('#divPlMetadata').removeClass('hide');
    $(".md-case-info").hide();
    $(".inquire-case-info").hide();
    $(".revview-case-info").show();

    // Maximize if minimized
    $("#pl-revview-event-details a.widget-control-minimize i.fa-plus-circle").parent().trigger('click');

    $("#pl-revview-event-details .widget-content").css({ 'max-height': $(window).height() * 0.4 });
    var plEventDetailsHeight = $("#pl-revview-event-details .widget-content").height();

    if (plEventDetailsHeight < 200)
        plEventDetailsHeight = 200;
    $("#pl-revview-event-bm .widget-content").css({ 'height': plEventDetailsHeight, 'max-height': plEventDetailsHeight });
    $("#pl-revview-event-chat .widget-content").css({ 'height': plEventDetailsHeight, 'max-height': plEventDetailsHeight });
    getPlaylistEventDetail(eventId, revviewEvent);
}

function showMDMetadata(eventId) {
    cleanPlMetadata();
    $('#divPlMetadata').removeClass('hide');
    $(".inquire-case-info").hide();
    $(".md-case-info").show();
    $(".revview-case-info").hide();
    $("#pl-md-event-details .widget-content").css({ 'max-height': $(window).height() * 0.4 });
    var plEventDetailsHeight = $("#pl-md-event-details .widget-content").height();
    if (plEventDetailsHeight < 200)
        plEventDetailsHeight = 200;
    $("#pl-md-event-details .widget-content").css({ 'height': plEventDetailsHeight });
    $("#pl-patient-details .widget-content").css({ 'height': plEventDetailsHeight, 'max-height': plEventDetailsHeight });
    $("#pl-case-general-vitals .widget-content").css({ 'height': plEventDetailsHeight, 'max-height': plEventDetailsHeight });
    $("#pl-event-bm .widget-content").css({ 'height': plEventDetailsHeight, 'max-height': plEventDetailsHeight });
    $("#pl-event-chat .widget-content").css({ 'height': plEventDetailsHeight, 'max-height': plEventDetailsHeight });
    getPlaylistEventDetail(eventId);
}

function hideMetadata() {
    //if (mdPanel)
    //    mdPanel.close();
    cleanPlMetadata();
    $('#divPlMetadata').addClass('hide');
}

function cleanPlMetadata() {
    $('.pl-case-no').html('');
    $('.pl-case-location').html('');
    $('.pl-case-date').html('');
    $('.pl-case-Interviewer').html('');
    $('.pl-case-Interviewee').html('');
    $('.pl-case-Type').html('');
    $('.pl-case-Notes').html('');
    $('.pl-case-qb-dialogid').html('');

    // MD Specific
    $('.pl-case-patient-name').text('');
    $('.pl-case-flight-no').text('');

    $('.pl-case-patient-height').text('');
    $('.pl-case-patient-weight').text('');
    $('.pl-case-patient-dob').text('');
    $('.pl-case-patient-gender').text('');
    $('.pl-case-complaint').text('');

    $('.pl-case-vitals-info table tbody').html('');

    $("#tblPlBookmarkContent tbody").html('');
    $("#pl-messages-list").html('');

    $("#divBookmarks").empty();

    //RevView
    $('.ID-CallID').text('');
    $('.ID-CallerID').text('');
    $('.ID-StartTime').text('');
    $('.ID-Ext').text('');
    $('.ID-ExtName').text('');
    $('.ID-AgentName').text('');

    $('.revview-case-info #pl-messages-list').html('');
    $("#divRevViewBookmarks").empty();
}

//#endregion

function makeDialogs() {

}

$(function () {
    $('#Metadataa').click(function () {
        //makedialogs();

        $("#divmetadataa").mmsDialog({
            autoOpen: true,
            maxwidth: 'auto',
            width: 'auto',
            height: 'auto',
            resizable: false,
            //title: 'confirmation message',
            closeText: langKeys[currentLanguage]['ttClose'],
            title: 'MetaData',
            modal: true
        });

    });
    $('#Sharednotess').click(function () {
        // makedialogs();
        if (nState == FilePlayerState.Play || nState == FilePlayerState.PendingPlay)
            btnPause_Click();

        $("#divSharednotess").mmsDialog({
            autoOpen: true,
            maxwidth: 'auto',
            width: '35%',
            height: 'auto',
            resizable: false,
            //title: 'confirmation message',
            closeText: langKeys[currentLanguage]['ttClose'],
            title: langKeys[currentLanguage]['cdSharedNotes'],
            modal: true
        });

    });
    $('#Internalnotess').click(function () {
        //  makeDialog();
        if (nState == FilePlayerState.Play || nState == FilePlayerState.PendingPlay)
            btnPause_Click();

        $("#divinternalnotess").mmsDialog({
            autoOpen: true,
            maxWidth: 'auto',
            width: '35%',
            height: 'auto',
            resizable: false,
            //title: 'Confirmation Message',
            closeText: langKeys[currentLanguage]['ttClose'],
            title: langKeys[currentLanguage]['cdInternalNotes'],
            modal: true
        });
    });

    $('#Attachedfiless').click(function () {
        //  makeDialog();
        if (nState == FilePlayerState.Play || nState == FilePlayerState.PendingPlay)
            btnPause_Click();

        $("#divattachedfiless").mmsDialog({
            autoOpen: true,
            maxwidth: 'auto',
            width: '300px',
            height: 'auto',
            resizable: false,
            //title: 'confirmation message',
            closeText: langKeys[currentLanguage]['ttClose'],
            close: function () {
                if (nState == FilePlayerState.Pause)
                    btnPause_Click();
                $(this).mmsDialog('destroy');
            },
            title: 'Attached Files',
            modal: true
        });

    });

    $(document).on('click', "#lnkTimeline", function () {
        ///Added by ifuturz on 20161229
        if ($('#hdnActiveTab').val() != 'Timeline') {
            $('#hdnActiveTab').val('Timeline');
            displaylink();
        }
        //logging
        //var msgData = loginUserName;
        //var params = '{Id:' + $('#cbxLoadPlayList :selected').val() + ',Name:' + $('#cbxLoadPlayList').find(":selected").text() + '}';
        //logActivity(loginUserId, 52, msgData, params);
    });

    $('.vis-item-content span.call-type-pl').live('click', function (e) {
        //alert('vis-item-content -> Click');
        //var classes = $(this).attr('class').split(' ');
        //alert($(this).prop('className').split(' '));
        //$('#metadataModal').modal('show');
        var eventId = $.trim($(e.target).data('evt-id'));
        //getPlaylistEventDetail(eventId);
        if ($(e.target)[0].classList.contains('callType7') || $(e.target)[0].classList.contains('callType8') || $(e.target)[0].classList.contains('callType8_virtual')) {
            showMetadata(eventId);
        }
        else if ($(e.target)[0].classList.contains('callType11')) {
            showMDMetadata(eventId);
        }
        else if ($(e.target)[0].classList.contains('callType1_revview')) {
            var revviewEvent = tblPlaylistData.find(t => t.CallId == eventId);
            showRevviewMetadata(eventId, revviewEvent);
        }
        else if ($(e.target)[0].classList.contains('mtrSignType1_revview')) {
            var revviewEvent = tblPlaylistData.find(t => t.CallId == eventId);
            showRevviewMTRSign(eventId, revviewEvent);
        }
        else {
            hideMetadata();
        }
    });

    $('img.pl-item-lv-inq').live('click', function (e) {
        e.preventDefault();
        var eventId = $.trim($(e.target).closest('tr').attr('callid'));
        var callType = $(e.target).closest('tr').attr('ctype');
        if (callType == '7') {
            //getPlaylistEventDetail(eventId);
            showMetadata(eventId);
        } else if (callType == '8') {
            showMDMetadata(eventId);
        }
    });

    $('.close-pl-metadata').click(function (e) {
        hideMetadata();
    });

    //$('#case-container').accordion({
    $("div.accordian").accordion({

        autoHeight: true,
        collapsible: true,
        heightStyle: 'content',//'content', //'auto',//'fill',//'panel',//
        activate: function (event, ui) {
            JL(frontendLogger).info('activate');
            event.preventDefault();
        },
        beforeActivate: function (event, ui) {
            if (ui.newHeader[0]) {
                var currHeader = ui.newHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            } else {
                var currHeader = ui.oldHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            }
            var isPanelSelected = currHeader.attr('aria-selected') == 'true';
            currHeader.toggleClass('ui-corner-all', isPanelSelected).toggleClass('accordion-header-active ui-state-active ui-corner-top', !isPanelSelected).attr('aria-selected', ((!isPanelSelected).toString()));
            currHeader.children('.ui-icon').toggleClass('ui-icon-triangle-1-e', isPanelSelected).toggleClass('ui-icon-triangle-1-s', !isPanelSelected);
            currContent.toggleClass('accordion-content-active', !isPanelSelected)
            if (isPanelSelected) { currContent.slideUp(); } else { currContent.slideDown(); }

            return false;
        }
    });

    $("div.pl-accordian").accordion({
        autoHeight: true,
        collapsible: true,
        //heightStyle: 'auto',//'fill',//'panel',//'content',
        heightStyle: 'content',//'content', //'auto',//'fill',//'panel',// /*ADDED BY KM IFUTURZ*/
        activate: function (event, ui) {
            JL(frontendLogger).info('activate');
            event.preventDefault();
        },
        beforeActivate: function (event, ui) {
            if (ui.newHeader[0]) {
                var currHeader = ui.newHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            } else {
                var currHeader = ui.oldHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            }
            var isPanelSelected = currHeader.attr('aria-selected') == 'true';
            currHeader.toggleClass('ui-corner-all', isPanelSelected).toggleClass('accordion-header-active ui-state-active ui-corner-top', !isPanelSelected).attr('aria-selected', ((!isPanelSelected).toString()));
            currHeader.children('.ui-icon').toggleClass('ui-icon-triangle-1-e', isPanelSelected).toggleClass('ui-icon-triangle-1-s', !isPanelSelected);
            currContent.toggleClass('accordion-content-active', !isPanelSelected)
            if (isPanelSelected) { currContent.slideUp(); } else { currContent.slideDown(); }

            return false;
        }
    });

    //$('#btnSaveCalls').live('click', function () {
    //    saveTracks();
    //});

    $("#lnkVideoClose").click(function () {
        reOrderzIndex('searchNonVideoView');
    });

    $("#btnExportCalls").click(function () {
        $("#divExportData").mmsDialog("open");
        //$("#divExportData").parent().appendTo($("form:first"));
        return false;
    });

    $('#lnkDeleteAllPL').live('click', function () {
        //deletePlaylistItem(1, 'DeletePlaylistDetails');
        if ($('#tblPlaylist > tbody > tr').length) {
            var playlistId = currentPlaylistId; //$("#ulPlaylists > li.active").attr("id");
            var RevSyncServerID = $("#tPlaylist > tbody > tr[playlistid='" + playlistId + "']").attr('RevSyncServerID'); // $(this).attr('id');
            showConfirmationDialog(langKeys[currentLanguage]['cmsgPldDelAll'], playlistId + '#' + RevSyncServerID, removePlaylistItems);
        }
    });

    $("#tblPlaylist tbody tr .delItem").live('click', function () {
        if (isViewPlaylist) {
            showErrorDialog(langKeys[currentLanguage]['lPlSharedEditingNotAllowed']);
            return;
        }

        var rowId = $(this).parent('tr').attr('id');
        var callId = $(this).parent('tr').attr('callid');
        if ($(this).parent('tr').attr('isuseritem') == 'true')
            showConfirmationDialog(langKeys[currentLanguage]['cmsgPldDel'], callId, removePlaylistUserItem);
        else
            showConfirmationDialog(langKeys[currentLanguage]['cmsgPldDel'], rowId, removePlaylistItem);
    });

    $('#tblPlaylist tbody tr').live("dblclick", function () {
        if ($(this).attr('ctype') == '1' || $(this).attr('ctype') == '2' || $(this).attr('ctype') == '6' || $(this).attr('ctype') == '7' || $(this).attr('ctype') == '9' || $(this).attr('ctype') == '10' || $(this).attr('ctype') == '11')
            if (boolIEBrowser)
                slPlayMedia_Silverlight($(this).attr('callId'));
            else {
                slPlayMedia($(this).attr('callId'));
                $('#btnStop').trigger('click');
                $('#btnPlay').trigger('click');
            }
        else if ($(this).attr('ctype') == '3')
            getText911Conversation($(this).attr('callid'), $(this).attr('sttime'), $(this).attr('ctype'), $(this).attr('chnum'), $(this).attr('chname'), $(this).attr('fname'));
    });

    var selectedPlObj;
    $('td.editPL').live("click dblclick", function () {
        //JL(frontendLogger).info($(this).next().find('div').html());
        if ($('#editboxPlComm').length == 0) {
            selectedPlObj = $(this);
            var currentValue = $(this).text();
            var newValue = currentValue;

            $(this).html("<input id='editboxPlComm' type='text' value='" + currentValue + "' style=\"width: 100%\" size='" + editCommentsLength + "' />");
            $('#editboxPlComm').focus();
        }
        e.stopPropagation();
    });
    $('td.editPL').live("keydown", function (event) {
        //JL(frontendLogger).info(arr[1]); event.preventDefault(); return;
        if (event.which == 13) {
            event.preventDefault(); //event.stopPropragation();
            var callId = $(this).parent().attr('callid');
            var txtval = $('#editboxPlComm').val();
            if (txtval != '' && callId.length) {
                if (isECEnabled) {
                    var recCallIds = [];
                    recCallIds.push({ 'RecId': $(this).parent().attr('recid'), 'CallId': $(this).parent().attr('callid') });
                    updateCallCustomFields(recCallIds, txtval, 'colCallComments');
                }
                else
                    updatePlRowContents("'" + callId + "'", txtval);

                var newValue = $('#editboxPlComm').val();
                selectedPlObj.html('');
                selectedPlObj.text(newValue);
            }
        }
    });
    $('#editboxPlComm').live('blur', function () {
        //JL(frontendLogger).info($(this).val() + $(this).text()); return;
        //JL(frontendLogger).info(selectedPlObj.html() + selectedPlObj.val() + selectedPlObj.text());
        var newValue = $('#editboxPlComm').val();
        selectedPlObj.html('');
        selectedPlObj.text(newValue);
    });

    $(".bm-details").live("click", function (e) {
        //JL(frontendLogger).info($(this).closest('tr').attr('id'));
        var $tr = $(this).closest('tr');
        showBookmarkDialog($tr);
    });

    //ADDED BY KM - IFUTURZ FOR BOOKMARK DIALOG IN LISTVIEW - START
    $(".bm-details-Listview").live("click", function (e) {
        var $tr = $(this).closest('tr');
        //showBookmarkDialogListView($tr);
        fetchAndShowInspectionBookmarkDialog($tr);
    });
    //ADDED BY KM - IFUTURZ FOR BOOKMARK DIALOG IN LISTVIEW - END

    $(".read-full-comments").live("click", function (e) {
        var $tr = $(this).closest('tr');
        showCommentsDialog($tr);
    });

    $('#txtPlSearch').keyup(function () {
        searchPlListviewTable($(this).val());
    });

    $('#ulPlaylistUserAddedFiles').on('click', '#lnkRemovePlAttachedFile', function (e) {
        if (isViewPlaylist) {
            showErrorDialog(langKeys[currentLanguage]['lPlSharedEditingNotAllowed']);
            return;
        }
        e.stopPropagation();
        e.preventDefault();
        var callId = $(this).closest("div[callid]").attr('callid');
        showConfirmationDialog(langKeys[currentLanguage]['cmsgPldDelAttachedFile'], callId, removePlaylistUserItem);
    });

    $(document).on("click", "#lnkVitalMedications", function (e) {
        var medNames = $(this).attr('med-names');
        var medImages = $(this).attr('med-images');

        var arrMedNames = $(this).attr('med-names').split(',');
        var arrMedImages = $(this).attr('med-images').split(',');
        var date = $(this).attr('date');

        var medImgPath = inquireURL + date + '/';

        var medlist = '';
        if (medNames.length) {
            medlist = '<ul class="drug-list"><li class="ui-menu-item">' + arrMedNames.join('</li><li>') + '</li></ul>';
        }

        var $html = '<div style="margin-top: 10px;">' +
            '<div class="med-items"><p>' + medlist + '</p></div>' +
            '<div id="divMedImgNames" class="med-images"></div>' +
            '</div>';
        showInfoDialog('List of Medications', $html);

        var $div = $('#divMedImgNames');
        $.each(arrMedImages, function (i, val) {
            if (val != "")
                $('<img />').attr({ 'src': medImgPath + val, 'alt': val, 'title': val, 'width': 150, 'height': 100, 'class': 'app-Img' }).appendTo($div);
        });
    });

    $(document).on("click", "#lnkVitalDrugs", function (e) {
        var drugsImgPath = rxUploadsRootPath + 'Drugs/';
        var names = $(this).attr('drug-names');
        var images = $(this).attr('drug-images');
        var arrNames = $(this).attr('drug-names').split(',');
        var arrImages = $(this).attr('drug-images').split(',');

        lst = '';
        if (names.length) {
            lst = '<ul class="drug-list"><li class="ui-menu-item">' + arrNames.join('</li><li>') + '</li></ul>';
        }
        var $html = '<div style="margin-top: 10px;">' +
            '<div class="med-items">' + lst + '</div>' +
            '<div id="divDrugImgNames" class="drug-images"></div>' +
            '</div>';
        showInfoDialog('Drug Details', $html);
        if (images.length) {
            var $div = $('#divDrugImgNames');
            $.each(arrImages, function (i, v) {
                $('<img />').attr({ 'src': drugsImgPath + v, 'alt': v, 'title': v, 'width': 150, 'height': 100, 'class': 'app-Img' }).appendTo($div);
            });
        }
    });

    $(document).on("click", "#lnkVitalEEG", function (e) {
        var names = $(this).attr('eeg-names');
        var images = $(this).attr('eeg-images');
        var arrNames = $(this).attr('eeg-names').split(',');
        var arrImages = $(this).attr('eeg-images').split(',');
        var date = $(this).attr('date');

        var eegImgPath = inquireURL + date + '/';

        lst = '';
        if (names.length) {
            lst = '<ul class="eeg-list"><li class="ui-menu-item">' + arrNames.join('</li><li>') + '</li></ul>';
        }
        var $html = '<div style="margin-top: 10px;">' +
            '<div class="med-items">' + lst + '</div>' +
            '<div id="divEEGImgNames" class="eeg-images"></div>' +
            '</div>';
        showInfoDialog('EEG', $html);
        if (images.length) {
            var $div = $('#divEEGImgNames');
            $.each(arrImages, function (i, v) {
                $('<img />').attr({ 'src': eegImgPath + v, 'alt': v, 'title': v, 'width': 150, 'height': 100, 'class': 'app-Img' }).appendTo($div);
            });
        }
    });
});

//#region Bookmark

function showBookmarkDialogListView($tr) {
    //debugger
    var callId = $tr.attr('id');
    var startTime = $tr.find('td[abbr="colStartTime"]').html();
    var bmXmlData = $tr.find('input[id=hdnBookmarkXml]').val();

    var xml = bmXmlData,
        xmlDoc = $.parseXML(xml.replace('&', '&amp;')),
        $xml = $(xmlDoc),
        $bmRecord = $xml.find("Record");

    var eTable = "<div><table class='table table-hover table-striped table-bordered table-condensed table-details' border='0'><thead><tr><th colspan='4'><small>Available bookmarks against StartTime: </small>" + startTime + "</th></tr><tr><th style='width:10%;'>#</th><th style='width:20%;'>Location</th><th>Text</th><th></th><th></th></tr></thead><tbody>";
    $.each($bmRecord, function (i, item) {
        var id = $(this).find('Id').text();
        var callIndex = $(this).find('CallIndex').text();
        var bmPosition = $(this).find('Pos').text();
        var bmText = $(this).find('Text').text();
        var bmnotes = $(this).find('Notes').text();
        var isPicture = $(this).find('IsPicture').text();
        var pictureFileName = $(this).find('PictureFileName').text();
        var eventDateTime = $(this).find('EventDateTime').text();
        var eventDate = eventDateTime.substring(0, 8);

        eTable += "<tr>";
        eTable += "<td style='vertical-align: middle;'>" + ++i + "</td>";
        eTable += "<td style='vertical-align: middle;'>" + toTimeString(bmPosition) + "</td>";

        if (bmnotes.length > 0)
            eTable += "<td style='vertical-align: middle;'>" + bmText + "<img style='cursor:pointer; vertical-align: middle; text-align: center; width: 20px;' class='bmicon' src='../assets/icons/um/notes.jpg' bmnotes='" + bmnotes + "' title='" + langKeys[currentLanguage]['InqBookmarkNotes'] + "' onclick='viewBookmarkNotes(this);'/> </td>";
        else
            eTable += "<td style='vertical-align: middle;'>" + bmText + "</td>";

        if (isPicture)
            eTable += "<td style='cursor:pointer; vertical-align: middle; text-align: center; width: 60px; height:60px;'><img class='bmicon' src='" + inquireURL + (tenantId > 0 ? '/' + tenantId + '/' : '/') + eventDate + '/' + pictureFileName + "' fileName='" + pictureFileName + "' eventDate='" + eventDate + "' title='" + langKeys[currentLanguage]['lmViewPicture'] + "' onclick='viewBookmarkPicture(this);'/></td>";
        eTable += "</tr>";
    });
    eTable += "</tbody></table></div></div>";
    var $dialog = $(eTable).mmsDialog({
        autoOpen: false,
        resizable: false,
        modal: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: 'Bookmarks',
        maxWidth: 'auto',
        width: 500,
        height: 400,
        close: function () {
            $(this).mmsDialog('destroy').remove();
        },
        buttons: {
            "Close": function () {
                $(this).mmsDialog('close');
            }
        },
    });
    $dialog.mmsDialog('open');
}

//#endregion

//#region Sort Function

var items_playlist_count = 0;
// Add Milliseconds to StartTime..

function getEndDate(start, ms) {
    //d = new Date(start);

    //if (window.navigator.userAgent.indexOf("Edge") > -1 || window.navigator.userAgent.indexOf("Safari") > -1)
    //    //d = new Date(start.replace(' ', 'T')); // ADDED BY KRUNAL IFUTURZ TO RESOLVE INVALID DATE IN EDGE
    //    d = new Date(start.replace(/-/g, "/")); // ADDED BY KRUNAL IFUTURZ TO RESOLVE INVALID DATE IN EDGE
    //else
    //    d = new Date(start);

    d = new Date(start.replace(/-/g, "/")); // ADDED BY KRUNAL IFUTURZ TO RESOLVE INVALID DATE IN EDGE

    end_date = new Date(d.setMilliseconds(ms));
    return vis.moment(end_date).format('YYYY-MM-DD HH:mm:ss');

    //return new Date(d.setMilliseconds(ms));
}

function sortByDate(a, b) {
    if (a.startTime < b.startTime)
        return -1;
    if (a.startTime > b.startTime)
        return 1;
    return 0;
}

//#endregion


//#region Display Mode - Timeline, ListView, Playlist (Playlist Table)

function displaylink() {
    try {
        if (boolIEBrowser) {

            var hfVal = $('#hdnActiveTab').val();
            var playlistId = currentPlaylistId;
            var userId = $('#header-login-name').attr('uid');
            var playlistIsSyncedFromClient = $("#tPlaylist > tbody > tr[playlistid='" + playlistId + "']").attr("IsSyncedFromClient");

            if (hfVal == "Playlist") {
                swapVideoPlayerContents('Search');

                //$('.body.row').css('z-index', '2'); // swap z-index

                //$('#divTimeline').css('display', 'none');
                //$('#divListview').css('display', 'none');

                changeLayout('listMode');
            }

            if (hfVal == "Listview") {
                swapVideoPlayerContents('Listview');
                //disableQuadView();

                if (playlistIsSyncedFromClient == 1)
                    document.getElementById('btnAddManualFile').style.visibility = 'hidden';
                else
                    document.getElementById('btnAddManualFile').style.visibility = 'visible';

                $('#divLvVideoPlayer').show();
                //$('#divLvVideoPlayerContainer').show();

                $('#divTimeline').css('display', 'none');
                $('#divListview').css({ 'display': 'block', 'z-index': 2 });
                $('#divListview').css({ 'position': 'relative' });
                $("#divListview").removeClass('hide');

                //$("lnkListview").removeClass("inActiveplLV").addClass("activeplLV");

                //slReset_Silverlight();
                //slDisableTimeline();
                //slCtl.Content.JS2SL.SetPlaylistParameters(helperServiceIp, userId, playlistId, false, nDiffMinutes);
                $('.body.row').css('z-index', '2'); // swap z-index
                var recid = '';
                var cType = '';
                var callId = '';
                var flName = '';
                var startTime = '';
                var fileExt = '';
                var durInSec = '';
                var durInMilliSec = '';
                var cType = '';
                var chNum = '';
                var chName = '';
                var screenName = '';
                var bookmark = '';
                var vod = '';
                var vid = '';
                IsContainText911 = 0;

                //initializePlayListTableUsingDataTable();
                if (playlistTable.rows().count() != 0) {
                    $("#tblPlaylist > tbody > tr").each(function () {

                        recid = $(this).attr('recid');
                        cType = $(this).attr('ctype');
                        callId = $(this).attr('callId');
                        flName = $(this).attr('fName');
                        startTime = $(this).attr('stTime');
                        fileExt = flName.substr(flName.lastIndexOf('.') + 1); // flName.split('.')[1];
                        durInSec = $(this).attr('cDuration'); // convertTimeToSeconds($(this).attr('cDuration'));
                        // durInMilliSec = $(this).attr('cdDuration');
                        //  durInSec = durInMilliSec / 1000;
                        chNum = $(this).attr('chNum');
                        chName = $(this).attr('chName');
                        screenName = $(this).attr('sName').split(',')[0];
                        bookmark = $(this).find('input[id=hdnBookmarkXml]').val();
                        var pages = $(this).attr('pages');
                        var isUserItem = $(this).attr('isuseritem');
                        var IsRevcell = $(this).attr('IsRevcell');
                        //JL(frontendLogger).info(fileExt);
                        if (cType == "1" || cType == "13") {
                            //JL(frontendLogger).info(callId + '-' + vod);

                            var fileExt = flName.split('.')[1];

                            // For revcell calls for which Revcell channel disabled
                            if ((IsRevcell == false || IsRevcell == "false") && fileExt.toLowerCase() != "dsf" && !isUserItem)
                                IsRevcell = true;

                            if (IsRevcell == true) {
                                vod = createRevcellURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                slAddMedia_Silverlight(callId, vod);
                            } else {
                                switch (fileExt.toLowerCase()) {
                                    case "dsf":
                                        vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                        if (vod)
                                            slAddMedia_Silverlight(callId, vod);
                                        break;
                                    case "mp3":
                                        if (isUserItem)
                                            vod = createDocumentURI(5, startTime, flName, durInSec);
                                        else
                                            vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);
                                        slAddMedia_Silverlight(callId, vod);
                                        break;
                                    case "wav":
                                        vod = createDocumentURI(1, startTime, flName, durInSec, pages);
                                        slAddMedia_Silverlight(callId, vod);
                                        break;
                                }
                            }

                        }
                        else if (cType == "2") {
                            //vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                            if (isUserItem == 'true')
                                vid = createDocumentURI(3, startTime, flName, durInSec, 0);
                            else
                                vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                            slAddMedia_Silverlight(callId, vid);
                            //setTimeout(function () { slAddMedia(callId, vid); }, 1000);
                        }
                        else if (cType == "3") {
                            let msgURI = createMessageURI(6, startTime, flName, msgBody, chNum, chName, durInSec);
                            slAddMedia_Silverlight(callId, msgURI);
                            IsContainText911 += 1;
                        }
                        else if (cType == "6") {
                            //setTimeout(function () { slAddMedia(callId, vid); }, 1000);
                            //JL(frontendLogger).info(screenName);
                            if (flName)
                                vid = createScreenURI(recid, 0, startTime, flName, durInSec, screenName, chNum, chName);
                            else
                                vid = createScreenURIWithoutDSF(recid, 3, startTime, durInSec, screenName, chNum, chName);
                            //JL(frontendLogger).info(vid);
                            slAddMedia_Silverlight(callId, vid);
                        }
                        else if (cType == "7" || cType == "8") {
                            //setTimeout(function () { slAddMedia(callId, vid); }, 1000);
                            //JL(frontendLogger).info(screenName);

                            var fileExt = flName.split('.');
                            var ftype = "";
                            //var vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName); // 
                            vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName, recid);
                            //JL(frontendLogger).info(vid);
                            var bookmark_inquire = "<inquire></inquire>";

                            // slAddMedia(callId, vid);
                            if (fileExt[1] == "mp3" || fileExt[1] == "m4a" || fileExt[1] == "wav") {
                                ftype = "audio";
                            }
                            else
                                ftype = "video";

                            slAddInquireMedia(callId, vid, bookmark_inquire, null, ftype);
                        }
                        else if (cType == "9") { //User Added Image
                            vod = createDocumentURI(9, startTime, flName, durInSec, pages);
                            //JL(frontendLogger).info(vod);
                            slAddMedia_Silverlight(callId, vod);
                        }
                        else if (cType == "10") { //User Added Document
                            //$("#divMainPlayerContainer").css('height', $('#divMainPlayerContainer').height() - $('#divTimeline').height() + 45);
                            vod = createDocumentURI(10, startTime, flName, durInSec, pages);
                            //JL(frontendLogger).info(vod);
                            slAddMedia_Silverlight(callId, vod);
                        }
                    });
                }

                $('#tblPlaylist > tbody > tr').show(); // $('#tblPlaylist > tbody > tr').attr("style", "display: inline;");
                //ShowHideText911Message(IsContainText911);
                $('#txtPlSearch').val('').show();

                //To nullify the effect of Timelineview
                $('#slvrObject').css({ 'height': '100%', 'top': '' });

                window.setTimeout(function () {
                    playlistTable.columns.adjust();
                    configureListViewHeight();
                }, 500);
            }
        }
        else {
            if (!boolIEBrowser)
                ClearBookmarksInPlayer(); // ADDED BY KM IFUTURZ

            SetFilePlayerTitlesLocalization(); // ADDED BY KM IFUTURZ

            var hfVal = $('#hdnActiveTab').val();
            // var hfAddUserFile = $('hdnActiveForm').val();
            var playlistId = currentPlaylistId;
            var playlistIsSyncedFromClient = $("#tPlaylist > tbody > tr[playlistid='" + currentPlaylistId + "']").attr("IsSyncedFromClient");
            var userId = $('#header-login-name').attr('uid');
            ////alert(hfVal);

            if (hfVal == "Playlist") {

                secondTimerDisplaySettings(1); // ADDED BY KM IFUTURZ

                $('input[id=chkAllCalls]').attr('checked', false); // ADDED BY KM IFUTURZ TO UNCHECK CHECKBOX FOR ALL CALLS AFTER NEVIGATE

                // KM for HTML5
                currentloadedscreen = 'search';
                deleteallmedia();
                containsvideo = false;
                Reset();
                //vplayer = document.getElementById("videofileplayer");

                $('#idvideolist_listview').html('');
                swapVideoPlayerContents('Search');
                SetBuffering(false);
                current_screencallid = "";
                screenvideo_obj = null;
                is_multiblobplay = false;

                //$('#videofileplayer1').css({ 'width': '0px', 'height': '0px' });

                //$('.body.row').css('z-index', '2'); // swap z-index

                //$('#divTimeline').css('display', 'none');
                //$('#divListview').css('display', 'none');

                changeLayout('listMode');
                //     
                //$('#divtimelineviewdesign').hide();
                $("#divListview").show();
                if (!bIsZeroItem) {
                    try {

                        if (pl != null) {
                            if (pl.timeline) {
                                //this code will destroy timline, later we can recreate a new timeline
                                pl.timeline.destroy();
                                pl = null
                            }
                        }

                        if (plAuserAdded.timeline) {
                            plAuserAdded.timeline.destroy();
                            plAuserAdded = null
                        }
                    } catch (e) {

                    }

                    //if (plA.timeline) {
                    //    plA.timeline.destroy();
                    //    plA = null
                    //}
                }

                bIsAddUserFileDialog = false;
                // $('#hdnActiveForm').val('');
                slReset();
                slDisableTimeline();
                ///Added by ifuturz on 20161223
                pbw = ProgressEx.width();
                //slUpperSlider.css('left', pbw);
                ///End by ifuturz on 20161223
            }

            if (hfVal == "Timeline") {
                if (isOnlyIQ3ModeEnabled || isIQ3View == true || isIQ3View == "True" || isIQ3View == "true") {
                    $(document).find('[lkey="lCallsTimeLine"]').text(langKeys[currentLanguage]['lEventTimeLine']);
                    $(document).find('[lkey="lCallsTimeLine"]').attr('lkey', 'lEventTimeLine');
                    $(document).find('[lkey="spanPlaylistCalls"]').text(langKeys[currentLanguage]['spanPlaylistEvents']);
                    $(document).find('[lkey="spanPlaylistCalls"]').attr('lkey', 'spanPlaylistEvents');
                }
                else {
                    $(document).find('[lkey="lEventTimeLine"]').text(langKeys[currentLanguage]['lCallsTimeLine']);
                    $(document).find('[lkey="lEventTimeLine"]').attr('lkey', 'lCallsTimeLine');
                    $(document).find('[lkey="spanPlaylistEvents"]').text(langKeys[currentLanguage]['spanPlaylistCalls']);
                    $(document).find('[lkey="spanPlaylistEvents"]').attr('lkey', 'spanPlaylistCalls');
                }


                if (playlistIsSyncedFromClient == 1)
                    document.getElementById('btnAddManualFile').style.visibility = 'hidden';
                else
                    document.getElementById('btnAddManualFile').style.visibility = 'visible';

                secondTimerDisplaySettings(0); // ADDED BY KM IFUTURZ
                sequencer.SetPosition(0); // ADDED BY KM IFUTURZ TO RESOLVE PLAYBACK AND POSITION AFTER SWITCHING FROM LISTVIEW TO TIMELINE VIEW.
                $("#divuseraddeditem").html(""); // ADDED BY KM IFUTURZ TO RESOLVE UAI TIMELINE VIEW NEW PLAYLIST ISSUE [SOCIAL ITEMS UI].
                removeContainersElementsTL(); // ADDED BY KM IFUTURZ TO REMOVE ALL VIDEO CONTAINERS CONTAINS 
                $('#divtimelineviewdesign').val('');
                //$('#divtimelineviewdesign').show();
                $("#divListview").show();
                //$("#ulPlaylistUserAddedFiles").html('');
                containsvideo = false;

                if (bIsTLLoadedInAUF) {

                    try {
                        if (plA != null) { // Added by ifuturz pageSize === "undefined"
                            if (plA.timeline) {
                                plA.timeline.destroy();
                                plA = null
                            }
                        }
                        if (pl != null) {
                            if (pl.timeline) {
                                //this code will destroy timline, later we can recreate a new timeline
                                pl.timeline.destroy();
                                pl = null
                            }
                        }

                        if (plAuserAdded != null) {
                            if (plAuserAdded.timeline) {
                                plAuserAdded.timeline.destroy();
                                plAuserAdded = null
                            }
                        }
                    } catch (e) {

                    }

                    ///Added by ifututrz to solve on switch between playlist from dropdown
                    sequencer.setLoaded(false);
                    ///End by ifututrz to solve on switch between playlist from dropdown
                    bIsDestroyTLInAUF = false;
                }
                else if (!bIsZeroItem) {
                    if (currentloadedscreen == 'Timeline') {

                        try {
                            if (pl != null) { // Added by ifuturz
                                if (pl.timeline) {
                                    //this code will destroy timline, later we can recreate a new timeline
                                    pl.timeline.destroy();
                                    pl = null;
                                }
                            }
                            if (plA != null) { // Added by ifuturz
                                if (plA.timeline) {
                                    plA.timeline.destroy();
                                    plA = null;
                                }
                            }
                            if (plAuserAdded != null) {
                                if (plAuserAdded.timeline) {
                                    plAuserAdded.timeline.destroy();
                                    plAuserAdded = null
                                }
                            }
                        } catch (e) {

                        }

                    }
                    bIsDestroyTLInAUF = false;
                }
                else {
                    if (bIsDestroyTLInAUF) {
                        try {
                            if (plA != null) { // Added by ifuturz
                                if (plA.timeline) {
                                    plA.timeline.destroy();
                                    plA = null;
                                }
                            }
                        } catch (e) {

                        }

                        bIsDestroyTLInAUF = false;
                    }
                }

                totalTimelineDuration = 0;

                //swapVideoPlayerContents('Listview');


                hideMetadata();
                //$('#divTimeline').css('display', 'none'); //PL Timeline View
                $('#divListview').css('display', 'none'); //PL Listview

                if (!$('#divTimeline').is(":visible") && !$('#divtimelineviewdesign').is(":visible")) {
                    //$('#divTimeline').css('display', 'block'); //PL Timeline View
                    //$('#divListview').css('display', 'none'); //PL Listview
                }

                if (!boolIEBrowser && currentloadedscreen == "ListView")
                    btnStop_Click();
                currentloadedscreen = 'Timeline';
                //vplayer = document.getElementById("videofileplayer");
                deleteallmedia();
                SetBuffering(false);
                Reset();
                slReset();
                isPlayersAutoHidden = true;
                //$('#videofileplayer1').css({ 'width': '0px', 'height': '0px' });

                current_screencallid = "";
                screenvideo_obj = null;
                is_multiblobplay = false;
                //slEnableTimeline();

                //slCtl.Content.JS2SL.SetPlaylistParameters(helperServiceIp, userId, playlistId, true);
                // KM on 20160705
                //slCtl.Content.JS2SL.SetPlaylistParameters(helperServiceIp, userId, playlistId, true, nDiffMinutes);

                $('.body.row').css('z-index', '1'); // swap z-index

                var items = []; // timeline
                var groups = []; // timeline
                var temp = [];  // used to sort array based on starttime
                var sortedPlaylist = []; // store sorted array.
                var msgURI = '';
                var items_playlist_count = 0;
                var firstFile = true;
                ///Added by ifuturz for userAdded item
                var items1 = []; // timeline
                var groups1 = []; // timeline

                //initializePlayListTableUsingDataTable();
                if (playlistTable.rows().count() != 0) {
                    $("#tblPlaylist > tbody > tr").each(function () {
                        bIsZeroItem = false;
                        msgURI = '';

                        var recid1 = $(this).attr('recid');
                        var callId1 = $(this).attr('callId');
                        var flName1 = $(this).attr('fName');
                        var startTime1 = $(this).attr('stTime');
                        var fileExt1 = (flName1 == void 0) ? "" : flName1.substr(flName1.lastIndexOf('.') + 1); //flName.split('.')[1];
                        var durInSec1 = $(this).attr('cDuration'); //convertTimeToSeconds($(this).attr('cDuration'));
                        // var durInMilliSec = $(this).attr('cdDuration');
                        //var durInSec = durInMilliSec / 1000;
                        var cType1 = $(this).attr('ctype');
                        var chNum1 = $(this).attr('chNum');
                        var chName1 = $(this).attr('chName');
                        //var screenName1 = $(this).attr('sName').split(',')[0]; /// Commeted by ifuturz
                        var screenName1 = $(this).attr('sName').split(',')[0]; ///Added by ifututrz on 20161230 to get videos from string after split
                        var pages1 = $(this).attr('pages');
                        var isUserItem1 = $(this).attr('isuseritem');
                        isUserItemIcon = $(this).attr('isuseritem');
                        var msgBody1 = $(this).attr('msgBody');  //// Added by ifuturz for Message,Email and Social
                        var IsRevcell = ($(this).attr('IsRevCell') != void 0) ? ($(this).attr('IsRevCell').toLowerCase() == 'true') : false;
                        var tag3 = $(this).attr('tag3');
                        var ani_ph = $(this).attr('ani_ph');
                        var isPictureEvent = $(this).attr('isPictureEvent');
                        var isVirtualInspection = $(this).attr('isVirtualInspection');
                        var isRevview = $(this).attr('isRevview');
                        var revview_fname = $(this).attr('revview_fname');
                        var revview_sttime = $(this).attr('revview_sttime');
                        var revview_phonenumber = $(this).attr('revview_phonenumber');
                        var revview_agentname = $(this).attr('revview_agentname');
                        var TagName = $(this).attr('TagName');

                        if (firstFile) {
                            if ($('#txtDateAddUserFile').val().length == 0) // ADDED BY KM IFUTURZ TO RESOLVE EDITED DATE TIME CHNAGE ON FILE SELECT IN TIMELINE UAI VIEW
                                sendStartTime(startTime1);
                        }
                        firstFile = false;
                        ////Added by ifuturz on 20161230 for Video file on timeline
                        //if (!screenName1 == '') {
                        //    flName1 = screenName1;
                        //}
                        if (flName1 == "") {
                            flName1 = screenName1;
                        }
                        if (isUserItem1 && flName1 == "") {
                            flName1 = "User Added Item";
                            fileExt1 = "uai";
                        }
                        ////End by ifuturz on 20161230 for Video file on timeline
                        temp.push({
                            'recid': recid1,
                            'CallId': callId1,
                            'flName': flName1,
                            'startTime': startTime1,
                            'fileExt': fileExt1,
                            'durInSec': durInSec1,
                            'cType': cType1,
                            'chNum': chNum1,
                            'chName': chName1,
                            'screenName': screenName1,
                            'pages': pages1,
                            'isUserItem': isUserItem1,
                            'msgBody': msgBody1,          //// Added by ifuturz for Message,Email and Social
                            'IsRevcell': IsRevcell,
                            'tag3': tag3,                  //Text911 Message Content
                            'ani_ph': ani_ph,
                            'isPictureEvent': isPictureEvent,
                            'isVirtualInspection': isVirtualInspection,
                            'isRevview': isRevview,
                            'revview_fname': revview_fname,
                            'revview_sttime': revview_sttime,
                            'revview_phonenumber': revview_phonenumber,
                            'revview_agentname': revview_agentname,
                            'TagName': TagName,
                        });
                        items_playlist_count++;
                    });
                }

                items_playlist_count = 0;

                sortedPlaylist = temp.sort(sortByDate);
                if (sortedPlaylist.length == 0) {
                    bIsZeroItem = true;
                    //var starttime = "";
                    //sendStartTime(null);
                }
                var bIsCurrentUserAddedFile = bIsHasFile;
                if (bIsAddUserFileDialog) {
                    if (bIsCurrentUserAddedFile) {

                        var dt = new Date();
                        var callId = "";
                        var flName = userAddedFile;
                        var extInfo = flName.split('.');
                        var startTime = vis.moment(dt).format('YYYY-MM-DD HH:mm:ss');//dt.getFullYear() + "0" + dt.getMonth() + "0" + dt.getDay() + dt.getHours() + dt.getMinutes() + dt.getSeconds();
                        var fileExt = extInfo[extInfo.length - 1].toLowerCase();
                        var durInSec = 60;

                        var datea = startTime;//startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                        var datea_Stoptime = getEndDate(datea, durInSec * 1000);

                        //iconCss1 = setTimelineIcon(cType, fileExt);
                        if (fileExt == "jpg" || fileExt == "png" || fileExt == "jpeg" || fileExt == "gif") {
                            iconCss1 = setTimelineIcon('9', fileExt);
                        } else {
                            iconCss1 = setTimelineIcon('10', fileExt);
                        }

                        // items.push({ x: items_playlist_count, id: items_playlist_count, content: '<img class="item_image" src="https://cdn2.iconfinder.com/data/icons/circle-icons-1/64/speaker-16.png"> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName });//Commented by Arivu
                        items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName, callId: callId, flName: flName });
                        ////Commented by ifuturz
                        //groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='https://cdn3.iconfinder.com/data/icons/70-basic-icons/100/close-32.png' /></a>" });
                        ////Commented by ifuturz
                        //Added by ifuturz
                        if (playlistIsSyncedFromClient == 0)
                            groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                        else
                            groups.push({ id: items_playlist_count, content: "<a href='#' data-id=" + items_playlist_count + "></a>" });
                        //Added by ifututrz
                        bNotSavedAUF = true;
                        items_playlist_count++;

                        if (bIsZeroItem) { // check this condition. what is zeroitem here.

                            plA = new PlayerList();
                            plA.seteventua(false);
                            plA.loadItems(items, groups, true);
                            plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth
                            bIsTLLoadedInAUF = true;
                            bIsAddUserFileDialog = true;
                            bNotSavedAUF = true;
                        }
                    }

                    for (i = 0; i < sortedPlaylist.length; i++) {

                        msgURI = '';

                        var recid = sortedPlaylist[i].recid;
                        var callId = sortedPlaylist[i].CallId;
                        var flName = sortedPlaylist[i].flName;
                        var startTime = sortedPlaylist[i].startTime;
                        var fileExt = sortedPlaylist[i].fileExt;
                        var durInSec = sortedPlaylist[i].durInSec;
                        var cType = sortedPlaylist[i].cType;
                        var chNum = sortedPlaylist[i].chNum;
                        var chName = sortedPlaylist[i].chName;
                        var screenName = sortedPlaylist[i].screenName;
                        var pages = sortedPlaylist[i].pages;
                        var isUserItem = sortedPlaylist[i].isUserItem;
                        isUserItemIcon = sortedPlaylist[i].isUserItem;
                        var msgBody = sortedPlaylist[i].msgBody;        //// Added by ifuturz for Message,Email and Social
                        var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);


                        var isRevView = sortedPlaylist[i].isRevView;
                        var isVirtualInspection = sortedPlaylist[i].isVirtualInspection;

                        var datea_Stoptime = getEndDate(datea, durInSec * 1000);


                        ////Added by ifuturz on 20161228
                        if (isUserItem && flName == "User Added Item") {
                            flName = "User Added Item";
                            fileExt = "uai";
                        }
                        //tooltip = "";
                        //tooltip = '<div>';
                        //tooltip += 'Channel : ' + flName.split('.')[0] + '\n';
                        //tooltip += 'Date : ' + datea + '\n';
                        //tooltip += 'Time : ' + datea.split(' ')[1] + '\n';
                        //tooltip += 'Duration : ' + timeFormate(durInSec) + '\n';
                        //tooltip += '</div>';

                        tooltip = "";
                        tooltip = '<div>';
                        tooltip += isOnlyIQ3ModeEnabled ? 'Inspector : ' + flName.split('.')[0] + '<br/>' : 'Channel : ' + flName.split('.')[0] + '<br/>';
                        tooltip += 'Date : ' + datea + '<br/>';
                        tooltip += 'Time : ' + datea.split(' ')[1] + '<br/>';
                        tooltip += 'Duration : ' + timeFormate(durInSec) + '<br/>';
                        tooltip += '</div>';
                        ht = $($.parseHTML(tooltip));

                        iconCss1 = setTimelineIcon(cType, fileExt, isRevview, isVirtualInspection);
                        //items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-evt-id="' + callId + '" class="' + iconCss1 + '" ></span> ' + '<span style="margin-left: 28px;">' + flName.split('.')[0] + ' | ' + datea + '</span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: ht.html(), callId: callId, flName: flName });
                        items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-toggle="popover" data-custom-title="Metadata" data-content="' + ht.html() + '"><span data-evt-id="' + callId + '" class="' + iconCss1 + '"></span> ' + '<span style="margin-left: 3px;">' + chName + ' ( ' + chNum + ' )'+ ' | ' + datea + '</span></span>', start: datea, end: datea_Stoptime, group: items_playlist_count, callId: callId, flName: flName });
                        ///End by ifuturz                    

                        ////Commented by ifuturz
                        // items.push({ x: items_playlist_count, id: items_playlist_count, content: '<img class="item_image" src="https://cdn2.iconfinder.com/data/icons/circle-icons-1/64/speaker-16.png"> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName });//Commented by Arivu
                        //items.push({ x: items_playlist_count, id: items_playlist_count, content: '<img class="item_image" src="https://cdn2.iconfinder.com/data/icons/circle-icons-1/64/speaker-16.png"> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName, callId: callId });
                        //groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='https://cdn3.iconfinder.com/data/icons/70-basic-icons/100/close-32.png' /></a>" });
                        ////Commented by ifuturz

                        //Added by ifuturz
                        if (playlistIsSyncedFromClient == 0)
                            groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                        else
                            groups.push({ id: items_playlist_count, content: "<a href='#' data-id=" + items_playlist_count + "></a>" });
                        //Added by ifututrz
                        items_playlist_count++;

                    }
                }
                else {

                    $("#ulPlaylistUserAddedFiles").html('');
                    $("#ulPlaylistExportReportFiles").html("");

                    for (i = 0; i < sortedPlaylist.length; i++) {
                        msgURI = '';
                        ;
                        var recid = sortedPlaylist[i].recid;
                        var callId = sortedPlaylist[i].CallId;
                        var flName = sortedPlaylist[i].flName;
                        var startTime = sortedPlaylist[i].startTime;
                        var fileExt = sortedPlaylist[i].fileExt;
                        var durInSec = sortedPlaylist[i].durInSec;
                        var cType = sortedPlaylist[i].cType;
                        var tag3 = sortedPlaylist[i].tag3;
                        var ani_ph = sortedPlaylist[i].ani_ph;
                        var chNum = sortedPlaylist[i].chNum;
                        var chName = sortedPlaylist[i].chName;
                        var screenName = sortedPlaylist[i].screenName;
                        var pages = sortedPlaylist[i].pages;
                        var isUserItem = sortedPlaylist[i].isUserItem;
                        isUserItemIcon = sortedPlaylist[i].isUserItem;
                        var msgBody = sortedPlaylist[i].msgBody;        //// Added by ifuturz for Message,Email and Social
                        var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);
                        var IsRevcell = sortedPlaylist[i].IsRevcell;
                        var datea_Stoptime = getEndDate(datea, durInSec * 1000);
                        var isPictureEvent = sortedPlaylist[i].isPictureEvent;
                        var isVirtualInspection = sortedPlaylist[i].isVirtualInspection;
                        var callerId = (ani_ph == null || ani_ph == undefined || ani_ph == '') ? 'N/A' : ani_ph;
                        var isRevview = sortedPlaylist[i].isRevview;
                        var revview_fname = sortedPlaylist[i].revview_fname;
                        var revview_sttime = sortedPlaylist[i].revview_sttime;
                        var TagName = sortedPlaylist[i].TagName;

                        // this image url might nort available that is why this  issue occurs. you can use come images from the assets directory..

                        // items.push({ x: items_playlist_count, id: items_playlist_count, content: '<img class="item_image" src="https://cdn2.iconfinder.com/data/icons/circle-icons-1/64/speaker-16.png"> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName });//Commented by Arivu
                        ////Added by ifuturz on 20161228

                        if (isUserItem == "true" || isUserItem == true) {

                            let ext = fileExt.toLowerCase();
                            JL(frontendLogger).info('preventing')
                            if (ext == "doc" || ext == "docx" || ext == "pdf" || ext == "ppt" || ext == "pptx" || ext == "xls" || ext == "xlsx"
                                || ext == "jpg" || ext == "png" || ext == "jpeg" || ext == "gif") {
                                addItemInAttachedFiles(sortedPlaylist[i]);
                                //continue;
                            }
                        }

                        //if (callId.startsWith("IQ3")) {
                        //    var $tr = $("#tblPlaylist tbody tr[callid='" + callId + "']");
                        //    addItemInExportReportFiles($tr);
                        //}

                        if (isUserItem && flName == "User Added Item") {
                            flName = "User Added Item";
                            fileExt = "uai";
                        }
                        //if (cType != "3") {
                        //    tooltip = "";
                        //    tooltip = '<div>';
                        //    tooltip += 'Channel : ' + chNum + (chName != "" ? (" - " + chName) : "") + '\n';
                        //    tooltip += 'Date : ' + datea.split(' ')[0] + '\n';
                        //    tooltip += 'Time : ' + datea.split(' ')[1] + '\n';
                        //    tooltip += 'Duration : ' + timeFormate(durInSec) + '\n';
                        //    tooltip += '</div>';
                        //}
                        //else {

                        //    tooltip = "";
                        //    tooltip = '<div>';
                        //    tooltip += 'Channel : ' + chNum + (chName != "" ? (" - " + chName.replace(/\+/g, ' ')) : "") + '\n';
                        //    tooltip += 'Time : ' + datea + '\n';
                        //    tooltip += 'Phone Number : ' + ani_ph + '\n';
                        //    tooltip += 'Text : ' + tag3 + '\n';
                        //    tooltip += '</div>';
                        //}

                        switch (cType) {
                            case "3":
                                if (isUserItem == "false" || isUserItem == false) {
                                    tooltip = "";
                                    tooltip = '<div>';
                                    tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName.replace(/\+/g, ' ')) : "") + '<br/>';
                                    tooltip += '<b>Time :</b> ' + datea + '<br/>';
                                    tooltip += '<b>Phone Number :</b> ' + ani_ph + '<br/>';
                                    tooltip += '<b>Text :</b> ' + tag3 + '';
                                    tooltip += '</div>';
                                }
                                else {
                                    tooltip = "";
                                    tooltip = '<div>';
                                    tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                    tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                    tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                    tooltip += '<b>Message :</b> ' + msgBody + ''
                                    tooltip += '</div>';
                                }
                                break;
                            case "4":
                            case "5":
                                tooltip = "";
                                tooltip = '<div>';
                                tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                tooltip += '<b>Message :</b> ' + msgBody + '';
                                tooltip += '</div>';
                                break;
                            default:
                                tooltip = "";
                                tooltip = '<div>';
                                tooltip += isOnlyIQ3ModeEnabled ? '<b>Inspector :</b> ' + chName + '<br/>' : '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                tooltip += '<b>Duration :</b> ' + timeFormate(durInSec) + '<br/>';
                                //tooltip += '<b>Caller ID :</b>  ' + callerId;
                                if (!isOnlyIQ3ModeEnabled)
                                    tooltip += '<b>Caller ID :</b> ' + ((callerId == null || callerId == undefined || callerId == 'undefined') ? '' : callerId);
                                tooltip += '</div>';
                                break;
                        }

                        ht = $($.parseHTML(tooltip));

                        iconCss1 = setTimelineIcon(cType, fileExt, isRevview, isVirtualInspection);
                        if (isMTRplaylist) {
                            iconCss2 = "callTypeSprite mtrSignType1_revview call-type-pl pull-left";
                        }

                        //items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-evt-id="' + callId + '" class="' + iconCss1 + '" ></span> ' + '<span style="margin-left: 3px;">' + flName.split('.')[0] + ' | ' + datea + '<span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: ht.html(), callId: callId, flName: flName });
                        //items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-evt-id="' + callId + '" class="' + iconCss1 + '" data-toggle="popover" data-original-title="Call Metadata" data-content="' + ht.html() + '" ></span> ' + '<span style="margin-left: 3px;">' + chNum + ' | ' + datea + '</span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: ht.html(), callId: callId, flName: flName });
                        items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-toggle="popover" data-custom-title="Metadata" data-content="' + ht.html() + '"><span data-evt-id="' + callId + '" class="' + iconCss1 + '"></span> ' + '<span data-evt-id="' + callId + '" class="' + iconCss2 + '"></span> ' + '<span style="margin-left: 3px;">' + chName + ' ( ' + chNum + ' )' + ' | ' + datea + '</span></span>', start: datea, end: datea_Stoptime, group: items_playlist_count, callId: callId, flName: flName, isUserItem: isUserItem });

                        ////Commented by ifuturz
                        //groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='http://cdn3.iconfinder.com/data/icons/70-basic-icons/100/close-32.png' /></a>" });
                        ////Commented by ifuturz

                        //Added by ifuturz
                        //if(playlistIsSyncedFromClient == 0)
                        if (playlistIsSyncedFromClient == 0)
                            groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                        else
                            groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                        //End by ifututrz
                        /////Commented by ifuturz
                        //items_playlist_count++;
                        /////End by ifuturz

                        switch (cType) {
                            case "1": //Audio
                            case "13": //Teams
                                var vod = '';
                                var vid = '';
                                //JL(frontendLogger).info(callId + '-' + vod);

                                // For revcell calls for which Revcell channel disabled
                                if ((IsRevcell == false || IsRevcell == "false") && fileExt.toLowerCase() != "dsf" && !isUserItem)
                                    IsRevcell = true;

                                if (IsRevcell == true) {
                                    vod = createRevcellURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                    slAddMedia(callId, vod);
                                } else {
                                    switch (fileExt.toLowerCase()) {
                                        case "dsf":
                                            if (isRevview == true || isRevview == "true") {
                                                vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);

                                                var vid = getRevviewURI(revview_fname, 3, "" + revview_sttime, durInSec, chNum, chName, recid);
                                                var revviewEvent = {
                                                    videoURI: vid,
                                                    isRevview: true,
                                                    playOffset: getVideoOffsetForRevview(startTime, revview_sttime),
                                                    startTime: moment(startTime + "", "YYYYMMDDHHmmss"),
                                                };

                                                slAddMedia(callId, vod, false, revviewEvent);
                                                containsvideo = true;
                                            }
                                            else {
                                                vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                                if (vod)
                                                    slAddMedia(callId, vod);
                                            }
                                            break;
                                        case "mp3":
                                            //COMMENTED BY KM IFUTURZ TO RESOLVE ISSUE WHILE UPLOAD .dsf.mp3 FILE - START
                                            //if (isUserItem)
                                            //    vod = createDocumentURI(5, startTime, flName, durInSec);
                                            //else {
                                            //    iconCss = "callTypeSprite callType1";
                                            //    vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);
                                            //    slAddMedia(callId, vod);
                                            //}
                                            //break;
                                            //COMMENTED BY KM IFUTURZ TO RESOLVE ISSUE WHILE UPLOAD .dsf.mp3 FILE - END

                                            //ADDED BY KM IFUTURZ TO RESOLVE ISSUE WHILE UPLOAD .dsf.mp3 FILE - START
                                            if (isUserItem)
                                                vod = createDocumentURI(5, startTime, flName, durInSec);
                                            else
                                                vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);

                                            iconCss = "callTypeSprite callType1";
                                            slAddMedia(callId, vod);
                                            break;
                                        //ADDED BY KM IFUTURZ TO RESOLVE ISSUE WHILE UPLOAD .dsf.mp3 FILE - STOP
                                        case "wav":
                                            vod = createDocumentURI(1, startTime, flName, durInSec, pages);
                                            slAddMedia(callId, vod);
                                            break;
                                    }
                                }
                                break;
                            case "2": //Video
                                if (isUserItem == true || isUserItem == 'true') {
                                    vid = createDocumentURI(3, startTime, flName, durInSec, 0);
                                }
                                else {
                                    vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                                }
                                //JL(frontendLogger).info(callId + vid);
                                slAddMedia(callId, vid);
                                containsvideo = true;
                                break;
                            case "3": //SMS
                                ///Commented by ifututrz for SMS
                                //msgURI = createMessageURI(6, startTime, $(this).find('td:nth-child(3) img').attr('msgBody'), chNum, chName);
                                ///Added by ifututrz for SMS
                                msgURI = createMessageURI(6, startTime, flName, msgBody, chNum, chName, durInSec);
                                ///End by ifututrz for SMS
                                //JL(frontendLogger).info('msgURI-SMS:-' + callId + msgURI);
                                slAddSocialMedia(callId, msgURI);
                                //items1.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></spna> ' + '<span><span style="margin-left: 28px;" id=' + datea + '>' + datea + '</span>  <span style="margin-left: 130px;">' + msgBody + '</span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: '', callId: callId, flName: msgBody });
                                //groups1.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                                break;
                            case "4": //Social
                                //msgURI = createMessageURI(8, startTime, $(this).find('td:nth-child(3) img').attr('msgBody'), chNum, chName);
                                msgURI = createMessageURI(8, startTime, flName, msgBody, chNum, chName, durInSec);
                                //JL(frontendLogger).info(callId + msgURI);
                                slAddSocialMedia(callId, msgURI);
                                //items1.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + '<span><span style="margin-left: 3px;" id=' + datea + '>' + datea + '</span>  <span style="margin-left: 130px;">' + msgBody + '</span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: '', callId: callId, flName: msgBody });
                                //groups1.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                                break;
                            case "5": //Email
                                //msgURI = createMessageURI(7, startTime, $(this).find('td:nth-child(3) img').attr('msgBody'), chNum, chName);
                                msgURI = createMessageURI(7, startTime, flName, msgBody, chNum, chName, durInSec);
                                //JL(frontendLogger).info(callId + msgURI);
                                slAddSocialMedia(callId, msgURI);
                                //items1.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + '<span><span style="margin-left: 3px;" id=' + datea + '>' + datea + '</span>  <span style="margin-left: 130px;">' + msgBody + '</span>', start: datea, end: datea_Stoptime, group: items_playlist_count, title: '', callId: callId, flName: msgBody });
                                //groups1.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                                break;
                            case "6": //Screens
                                //if (flName)
                                //    vid = createScreenURI(recid, 0, startTime, flName, durInSec, screenName, chNum, chName);
                                //else
                                //    vid = createScreenURIWithoutDSF(recid, 3, startTime, durInSec, screenName, chNum, chName);
                                ////JL(frontendLogger).info(vid);
                                //slAddMedia(callId, vid);

                                var audioUri = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                var screensUri = createQuadScreenURI(recid, 3, startTime, durInSec, screenName, flName);
                                if (boolIEBrowser)
                                    screensUri = createQuadScreenURI_Silverlight(recid, 3, startTime, durInSec, screenName, flName);

                                var fnameSplited = flName.split('.');
                                var ext = fnameSplited[1];

                                if (ext == 'dsf' || ext == 'DSF')
                                    audioUri += "&screen=withaudio";
                                else
                                    audioUri += "&screen=withoutaudio";

                                if (ext == 'dsf' || ext == 'DSF')
                                    AddQuadMedia(callId, audioUri, screensUri);
                                else
                                    slAddMedia(callId, screensUri[0]);

                                containsvideo = true;
                                break;
                            case "7": //Inquire IQ3
                            case "11": //Inquire MD

                                if (isPictureEvent == "true" && cType == "7") {
                                    vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName, recid);
                                    slAddMedia(callId, vid, isPictureEvent);

                                    containsvideo = true;
                                }
                                else {

                                    var gps = $(this).attr('interview_GPS');
                                    var interview_DateTime = $(this).attr('interview_DateTime');
                                    var interview_Interviewer = $(this).attr('interview_Interviewer');
                                    var interview_Interviewee = $(this).attr('interview_Interviewee');
                                    var interview_InterviewId = $(this).attr('interview_InterviewId');
                                    var interview_Notes = $(this).attr('interview_Notes');

                                    var interview_details = interview_InterviewId + ";" + gps + ";" + interview_DateTime + ";" + interview_Interviewer + ";" + interview_Interviewee + ";" + interview_Notes;
                                    var bm = $(this).find("input[id=hdnCallBookmarks]").val();
                                    var bookmark_inquire = "<inquire>" + bm + "</inquire>";

                                    vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName, recid);
                                    // vid = createInquireURI(recid, 0, startTime, flName, durInSec, screenName, chNum, chName);
                                    //JL(frontendLogger).info(vid);

                                    slAddMedia(callId, vid);
                                    var fileExt = flName.split('.');
                                    var ftype = "";
                                    if (fileExt[1] == "mp3" || fileExt[1] == "m4a" || fileExt[1] == "wav") {
                                        ftype = "audio";
                                    }
                                    else
                                        ftype = "video";
                                    // slAddInquireMedia(callId, vid, bookmark_inquire, interview_details);
                                    containsvideo = true;
                                }
                                break;
                            case "9": //User Added Image
                                // durInSec = 60;
                                vod = createDocumentURI(9, startTime, flName, durInSec, pages);
                                JL(frontendLogger).info('createDocumentURI for Image  :: ' + vod);
                                //JL(frontendLogger).info(vod);
                                slAddMedia(callId, vod); // this is interface for imeage.
                                containsvideo = true;
                                break;
                            case "10": //User Added Document
                                //$("#divMainPlayerContainer").css('height', $('#divMainPlayerContainer').height() - $('#divTimeline').height() + 45);
                                // durInSec = 60;
                                vod = createDocumentURI(10, startTime, flName, durInSec, pages);
                                JL(frontendLogger).info('createDocumentURI for User Added Document  :: ' + vod);
                                //JL(frontendLogger).info(vod);
                                slAddMedia(callId, vod); // this is for document..
                                containsvideo = true;
                                break;
                            default:
                                $.unblockUI();
                                showErrorDialog("Invalid Call Type: " + cType);
                                break;
                        }
                        ///Added by ifuturz
                        items_playlist_count++;
                        ///End by ifuturz
                    }
                }

                if (slHaveVideo()) {
                    if (!$('#divTimeline').is(":visible"))
                        $('#divTimeline').slideDown(1000);
                    setTimeout(configureTimelineVisualizationHeight, 1000);
                }
                else {
                    if ($('#divTimeline').is(":visible"))
                        $('#divTimeline').slideUp(1000);
                    setTimeout(configureTimelineVisualizationHeight, 1000);
                    ///Commented by ifuturz on 20170103
                    //$('#divLvVideoPlayer').slideDown(3000);
                    //$('#divListViewTable').stop().animate({ top: 282 }, 3000);
                    ///End by ifuturz on 20170103
                }

                $('#txtPlSearch').val('').hide();

                //$('#divtimelineviewdesign').show();
                $("#divListview").show();

                if (sortedPlaylist.length > 0)//arivu
                {
                    //; //ifuturz
                    //PlayerList for TL 
                    if (bIsAddUserFileDialog) {
                        plA = new PlayerList();
                        plA.seteventua(false);
                        plA.loadItems(items, groups, true);
                        plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth
                        //bIsAddUserFileDialog = false;
                        bIsCurrentUserAddedFile = true;
                        //bIsTLLoadedInAUF = true;
                    }
                    else {
                        JL(frontendLogger).info('search.js-->pl = new PlayerList();');
                        pl = new PlayerList();
                        //pl.loadItems(items, groups, false);

                        // Added by IFuturz to catch the error
                        try {
                            pl.loadItems(items, groups, false); // ERROR_PL_LOAD_ITEMS
                            ////alert("pl.loadItems-->try");
                        } catch (e) {
                            var error = e.message;
                            var err = e.toString();
                            ////alert("pl.loadItems-->catch-->error-->" + error);
                        }
                        // Added by IFuturz to catch the error

                        // pl.init(items, groups);
                        //if (!slHaveVideo())
                        pl.setfullscreen(true);
                        pl.init(items, groups, 'visualization');
                        configureTimelineVisualizationHeight();

                        plA = new PlayerList();
                        plA.seteventua(false);
                        if (!slHaveVideo())
                            plA.setfullscreen(true);
                        plA.loadItems(items, groups, false);
                        plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth

                        ///Added by ifuturz for user added item
                        plAuserAdded = new PlayerList();

                        if (items1.length > 0) {
                            plAuserAdded.loadItemsUserAdded(items1, groups1, false);
                            plAuserAdded.initUserAdded(items1, groups1, 'divuseraddeditem');
                        }
                        ///End by ifuturz for user added item

                        // plA.syncScroll();
                        bIsTLLoadedInAUF = true;
                        bIsCurrentUserAddedFile = false;

                        // pl.syncScroll();
                        //pl.start();  // when loading the timeline page.. i just call stop method.. this will start and pause.. so, either i can remove this call. or
                        // you can 
                        //pl.start();
                        // pl.play();
                        //pl.pause();
                    }
                }
                $('.remove_item').on('click', function () {
                    try {

                        if (isViewPlaylist) {
                            showErrorDialog(langKeys[currentLanguage]['lPlSharedEditingNotAllowed']);
                            return;
                        }

                        for (i = 0; i < items.length; i++) {
                            if ($(this).data('id') == items[i].id) {

                                var callid = items[i].callId;
                                var fileName = items[i].flName;
                                var lastItem = false;

                                if (!isMTRplaylist) {
                                    if (items[i].isUserItem == "true" || items[i].isUserItem == true) {
                                        if (!bNotSavedAUF)
                                            removePlaylistUserItem(callid);
                                        $("#tblPlaylist tbody tr[callid='" + callid + "']").remove(); // ADDED BY KM IFUTURZ TO REMOVE ROW TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                                        //var fileName = items[i].title;
                                        if (items.length == 1) {
                                            lastItem = true;

                                        }
                                        bIsTLLoadedInAUF = false;
                                        bIsZeroItem = false;
                                        if (fileName != 'null' && fileName != 'User Added Item')
                                            deleteAddUserFile(playlistId, userId, null, fileName, lastItem, bIsCurrentUserAddedFile);
                                    }
                                    else {
                                        var RevSyncServerId = $("#tblPlaylist tbody tr[callid='" + callid + "']").attr('RevSyncServerID');
                                        var id = $("#tblPlaylist tbody tr[callid='" + callid + "']").attr('id');

                                        removePlaylistItem(id, RevSyncServerId);

                                        updateTableAfterRemovingItems(callid);

                                        if (items.length == 1) {
                                            lastItem = true;

                                        }
                                    }
                                }
                                else {
                                    alert("You are not able delete the MTR file");
                                }
                                //getplaylistDetail(playlistId);
                            }
                        }
                        plA.removeItem([$(this).data('id')]);
                        getFileDetails(null);
                        pl.removeItem([$(this).data('id')]);
                        //Added by ifutrz to delete on User Added item list delete
                        plAuserAdded.removeItem([$(this).data('id')]);
                        //End by ifutrz to delete on User Added item list delete

                        if (items.length == 1) {
                            //bIsZeroItem = true;
                        }

                        displaylink(); // ADDED BY KM IFUTURZ TO RELOAD TIMELINE VIEW AND CREATE NEW OBJECTS OF sequencer.js ARRAYS TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                    } catch (e) {
                        //alert(e.message);
                    }
                });

                ////Added by ifuturz for UserAdded Item on click and mouse hover
                $('#divuseraddeditem .vis-group').on('click', function () {
                    $('#divuseraddeditem .vis-group').removeClass('mouseClickClass');
                    $(this).addClass('mouseClickClass');
                });

                $('#divuseraddeditem .vis-group').hover(function () {
                    if (!$(this).hasClass('mouseClickClass')) {
                        $(this).addClass('mouseHoverClass');
                    }
                }, function () {
                    if ($(this).hasClass('mouseHoverClass')) {
                        $(this).removeClass('mouseHoverClass');
                    }
                });
                ////End by ifuturz for UserAdded Item on click and mouse hover

                // bIsCurrentUserAddedFile = false;
                ///Added by ifuturz on 20161223
                pbw = ProgressEx.width();
                //slUpperSlider.css('left', pbw); // COMMENTED BY KM IFUTURZ FOR RESOLVE SCRUBBER ISSUE
                //slLowerSlider.css('left', 0); // COMMENTED BY KM IFUTURZ FOR RESOLVE SCRUBBER ISSUE
                //slUpperSlider.css('left', pbw - 1); // ADDEDE BY KM IFUTURZ TO RESOLVE SCRUBBER ISSUE
                //slLowerSlider.css('left', '5px'); // ADDEDE BY KM IFUTURZ TO RESOLVE SCRUBBER ISSUE

                $('#divLvVideoPlayer').css('display', 'none');
                //$('#divLvVideoPlayerContainer').css('display', 'none');

                JL(frontendLogger).info('divLvVideoPlayer-->display->none');
                ///End by ifuturz on 20161223

                totalDurationTimeline(); // ADDED BY KM IFUTURZ FOR TOTAL DURATION TIMELINE
                $('#hdnspnTotalDuration').val('0'); // ADDED BY KM IFUTURZ FOR RESOLVE TOOLTIP BUG

                //ADDED BY KM IFUTURZ TO RESOLVE TIMELINE ZOOM IN ZOOM OUT TIMELINE TIME ISSUE - START
                try {
                    $('#visualization > .vis-timeline > .vis-bottom').css('left', $('#visualization > .vis-timeline > .vis-vertical')[0].style.left);
                } catch (ex) {
                    //alert(ex.message);
                }
                //ADDED BY KM IFUTURZ TO RESOLVE TIMELINE ZOOM IN ZOOM OUT TIMELINE TIME ISSUE - END
            }
            
            if (hfVal == "Listview") {
                // KM for HTML5
                if (isOnlyIQ3ModeEnabled || isIQ3View == true || isIQ3View == "True" || isIQ3View == "true") {
                    //$(document).find('[lkey="cplCallComments"]').text(langKeys[currentLanguage]['cplEventComments']);
                    //$(document).find('[lkey="cplCallComments"]').attr('lkey', 'cplEventComments');
                    $(document).find('[lkey="cplCallComments"]').text("Report");
                    $(document).find('[lkey="cplCallComments"]').attr('lkey', 'customReportLabel');
                    //hideColumnsByHeaderNames('#tblPlaylist', ["Inspector"]);
                    playlistTable.column(3).visible(false);
                    playlistTable.column(8).visible(false);
                    playlistTable.column(9).visible(false);
                    playlistTable.column(10).visible(false);
                    playlistTable.column(13).visible(false);
                   /* $('#plList table th').find('[lkey="cplChannelName"]').text(langKeys[currentLanguage]['cInspector'])
                    $('#plList table th').find('[lkey="cplChannelName"]').attr('lkey', 'cInspector');*/
                }
                else {
                    $(document).find('[lkey="cplEventComments"]').text(langKeys[currentLanguage]['cplCallComments']);
                    $(document).find('[lkey="cplEventComments"]').attr('lkey', 'cplCallComments');
                    playlistTable.column(3).visible(true);
                    playlistTable.column(8).visible(true);
                    playlistTable.column(9).visible(true);
                    playlistTable.column(10).visible(true);
                    playlistTable.column(13).visible(true);
                    /*$('#plList table th').find('[lkey="cInspector"]').text(langKeys[currentLanguage]['cplChannelName'])
                    $('#plList table th').find('[lkey="cInspector"]').attr('lkey', 'cplChannelName');*/
                }
                secondTimerDisplaySettings(0); // ADDED BY KM IFUTURZ

                $('#divLvVideoPlayer').show();
                //$('#divLvVideoPlayerContainer').show();
                $("#ulPlaylistUserAddedFiles").html('');
                calculateAndAdjustVPPosition();

                if (playlistIsSyncedFromClient == 1)
                    document.getElementById('btnAddManualFile').style.visibility = 'hidden';
                else
                    document.getElementById('btnAddManualFile').style.visibility = 'visible';

                try {
                    if (!bIsZeroItem) {
                        if (pl.timeline) {
                            //this code will destroy timline, later we can recreate a new timeline
                            pl.timeline.destroy();
                            pl = null
                        }

                        if (plA.timeline) {
                            plA.timeline.destroy();
                            plA = null
                        }
                    }
                } catch (e) {

                }

                //KM IFUTURZ - START
                try {
                    if (bIsTLLoadedInAUF) {
                        if (plA != null) { // Added by ifuturz
                            if (plA.timeline) {
                                plA.timeline.destroy();
                                plA = null
                            }
                        }
                        if (pl != null) {
                            if (pl.timeline) {
                                //this code will destroy timline, later we can recreate a new timeline
                                pl.timeline.destroy();
                                pl = null
                            }
                        }

                        if (plAuserAdded != null) {
                            if (plAuserAdded.timeline) {
                                plAuserAdded.timeline.destroy();
                                plAuserAdded = null
                            }
                        }

                        ///Added by ifututrz to solve on switch between playlist from dropdown
                        sequencer.setLoaded(false);
                        ///End by ifututrz to solve on switch between playlist from dropdown
                        bIsDestroyTLInAUF = false;
                    }
                    else if (!bIsZeroItem) {
                        if (pl != null) { // Added by ifuturz
                            if (pl.timeline) {
                                //this code will destroy timline, later we can recreate a new timeline
                                pl.timeline.destroy();
                                pl = null;
                            }
                        }
                        if (plA != null) { // Added by ifuturz
                            if (plA.timeline) {
                                plA.timeline.destroy();
                                plA = null;
                            }
                        }
                        if (plAuserAdded != null) {
                            if (plAuserAdded.timeline) {
                                plAuserAdded.timeline.destroy();
                                plAuserAdded = null
                            }
                        }
                        bIsDestroyTLInAUF = false;
                    }
                    else {
                        if (bIsDestroyTLInAUF) {
                            if (plA != null) { // Added by ifuturz
                                if (plA.timeline) {
                                    plA.timeline.destroy();
                                    plA = null;
                                }
                            }
                            bIsDestroyTLInAUF = false;
                        }
                    }
                    if (isIQ3View == false || isIQ3View == "False" || isIQ3View == "false") {
                        console.log("IF isOnlyIQ3ModeEnabled", isOnlyIQ3ModeEnabled);   
                        console.log("IF isIQ3View", isIQ3View);
                        playlistTable.column(3).visible(false);
                        playlistTable.column(8).visible(false);
                        playlistTable.column(9).visible(false);
                        playlistTable.column(10).visible(false);
                        playlistTable.column(13).visible(false);
                        /*$('#plList table th').find('[lkey="cplChannelName"]').text(langKeys[currentLanguage]['cInspector'])
                        $('#plList table th').find('[lkey="cplChannelName"]').attr('lkey', 'cInspector');*/
                    }
                    /*if (isOnlyIQ3ModeEnabled == true || isOnlyIQ3ModeEnabled == "True" || isOnlyIQ3ModeEnabled == "true"  || (isIQ3View == true || isIQ3View == "True" || isIQ3View == "true") ) {
                        console.log("IF isOnlyIQ3ModeEnabled", isOnlyIQ3ModeEnabled);
                        console.log("IF isIQ3View", isIQ3View);
                        playlistTable.column(3).visible(false);
                        playlistTable.column(8).visible(false);
                        playlistTable.column(9).visible(false);
                        playlistTable.column(10).visible(false);
                        playlistTable.column(13).visible(false);
                        $('#plList table th').find('[lkey="cplChannelName"]').text(langKeys[currentLanguage]['cInspector'])
                        $('#plList table th').find('[lkey="cplChannelName"]').attr('lkey', 'cInspector');
                    }
                    else {
                        console.log("ELSE isOnlyIQ3ModeEnabled", isOnlyIQ3ModeEnabled);
                        console.log("ELSE  isIQ3View", isIQ3View);
                        playlistTable.column(3).visible(true);
                        playlistTable.column(8).visible(true);
                        playlistTable.column(9).visible(true);
                        playlistTable.column(10).visible(true);
                        playlistTable.column(13).visible(true);
                        $('#plList table th').find('[lkey="cInspector"]').text(langKeys[currentLanguage]['cplChannelName'])
                        $('#plList table th').find('[lkey="cInspector"]').attr('lkey', 'cplChannelName');
                    }*/

                    var items = []; // timeline
                    var groups = []; // timeline
                    var temp = [];  // used to sort array based on starttime
                    var sortedPlaylist = []; // store sorted array.
                    var msgURI = '';
                    var items_playlist_count = 0;
                    var firstFile = true;
                    ///Added by ifuturz for userAdded item
                    var items1 = []; // timeline
                    var groups1 = []; // timeline

                    //initializePlayListTableUsingDataTable();
                    if (playlistTable.rows().count() != 0) {
                        $("#tblPlaylist > tbody > tr").each(function () {
                            bIsZeroItem = false;
                            msgURI = '';

                            var recid1 = $(this).attr('recid');
                            var callId1 = $(this).attr('callId');
                            var flName1 = $(this).attr('fName');
                            var startTime1 = $(this).attr('stTime');
                            var fileExt1 = (flName1 == void 0) ? "" : flName1.substr(flName1.lastIndexOf('.') + 1); //flName.split('.')[1];
                            var durInSec1 = $(this).attr('cDuration'); //convertTimeToSeconds($(this).attr('cDuration'));
                            // var durInMilliSec = $(this).attr('cdDuration');
                            //var durInSec = durInMilliSec / 1000;
                            var cType1 = $(this).attr('ctype');
                            var chNum1 = $(this).attr('chNum');
                            var chName1 = $(this).attr('chName');
                            //var screenName1 = $(this).attr('sName').split(',')[0]; /// Commeted by ifuturz
                            var screenName1 = $(this).attr('sName').split(',')[0]; ///Added by ifututrz on 20161230 to get videos from string after split
                            var pages1 = $(this).attr('pages');
                            var isUserItem1 = $(this).attr('isuseritem');
                            var msgBody1 = $(this).attr('msgBody');  //// Added by ifuturz for Message,Email and Social
                            var IsRevcell = ($(this).attr('IsRevCell') != void 0) ? ($(this).attr('IsRevCell').toLowerCase() == 'true') : false;
                            var tag3 = $(this).attr('tag3');
                            var isPictureEvent = $(this).attr('isPictureEvent');
                            var isRevview = $(this).attr('isRevview');
                            var isVirtualInspection = $(this).attr('isVirtualInspection');
                            var revview_fname = $(this).attr('revview_fname');
                            var revview_sttime = $(this).attr('revview_sttime');
                            var revview_phonenumber = $(this).attr('revview_phonenumber');
                            var revview_agentname = $(this).attr('revview_agentname');

                            if (firstFile) {
                                sendStartTime(startTime1);
                            }
                            firstFile = false;
                            ////Added by ifuturz on 20161230 for Video file on timeline
                            if (!screenName1 == '') {
                                flName1 = screenName1;
                            }
                            if (isUserItem1 && flName1 == "") {
                                flName1 = "User Added Item";
                                fileExt1 = "uai";
                            }
                            ////End by ifuturz on 20161230 for Video file on timeline
                            temp.push({
                                'recid': recid1,
                                'CallId': callId1,
                                'flName': flName1,
                                'startTime': startTime1,
                                'fileExt': fileExt1,
                                'durInSec': cType1 == 3 ? "" : durInSec1,
                                'cType': cType1,
                                'chNum': chNum1,
                                'chName': chName1,
                                'screenName': screenName1,
                                'pages': pages1,
                                'isUserItem': isUserItem1,
                                'msgBody': msgBody1,          //// Added by ifuturz for Message,Email and Social
                                'IsRevcell': IsRevcell,
                                'tag3': tag3,
                                'isPictureEvent': isPictureEvent,
                                'isRevview': isRevview,
                                'isVirtualInspection': isVirtualInspection,
                                'revview_fname': revview_fname,
                                'revview_sttime': revview_sttime,
                                'revview_phonenumber': revview_phonenumber,
                                'revview_agentname': revview_agentname,
                            });
                            items_playlist_count++;
                        });
                    }
                    items_playlist_count = 0;

                    sortedPlaylist = temp.sort(sortByDate);
                    if (sortedPlaylist.length == 0) {
                        bIsZeroItem = true;
                        //var starttime = "";
                        //sendStartTime(null);
                    }
                    var bIsCurrentUserAddedFile = bIsHasFile;
                    if (bIsAddUserFileDialog) {
                        if (bIsCurrentUserAddedFile) {

                            var dt = new Date();
                            var callId = "";
                            var flName = userAddedFile;
                            var extInfo = flName.split('.');
                            var startTime = vis.moment(dt).format('YYYY-MM-DD HH:mm:ss');//dt.getFullYear() + "0" + dt.getMonth() + "0" + dt.getDay() + dt.getHours() + dt.getMinutes() + dt.getSeconds();
                            //var fileExt = extInfo[1];
                            var fileExt = extInfo[extInfo.length - 1].toLowerCase();
                            var durInSec = 60;

                            var datea = startTime;//startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                            var datea_Stoptime = getEndDate(datea, durInSec * 1000);

                            //iconCss1 = setTimelineIcon(cType, fileExt);
                            if (fileExt == "jpg" || fileExt == "png" || fileExt == "jpeg" || fileExt == "gif") {
                                iconCss1 = setTimelineIcon('9', fileExt);
                            } else {
                                iconCss1 = setTimelineIcon('10', fileExt);
                            }
                            items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span class="' + iconCss1 + '" ></span> ' + flName, start: datea, end: datea_Stoptime, group: items_playlist_count, title: flName, callId: callId });
                            if (playlistIsSyncedFromClient == 0)
                                groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                            else
                                groups.push({ id: items_playlist_count, content: "<a href='#' data-id=" + items_playlist_count + "></a>" });
                            bNotSavedAUF = true;
                            items_playlist_count++;

                            if (bIsZeroItem) {
                                plA = new PlayerList();
                                plA.loadItems(items, groups, true);
                                plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth
                                bIsTLLoadedInAUF = true;
                                bIsAddUserFileDialog = true;
                                bNotSavedAUF = true;
                            }
                        }

                        for (i = 0; i < sortedPlaylist.length; i++) {

                            msgURI = '';

                            var recid = sortedPlaylist[i].recid;
                            var callId = sortedPlaylist[i].CallId;
                            var flName = sortedPlaylist[i].flName;
                            var startTime = sortedPlaylist[i].startTime;
                            var fileExt = sortedPlaylist[i].fileExt;
                            var durInSec = sortedPlaylist[i].durInSec;
                            var cType = sortedPlaylist[i].cType;
                            var chNum = sortedPlaylist[i].chNum;
                            var chName = sortedPlaylist[i].chName;
                            var screenName = sortedPlaylist[i].screenName;
                            var pages = sortedPlaylist[i].pages;
                            var isUserItem = sortedPlaylist[i].isUserItem;
                            isUserItemIcon = sortedPlaylist[i].isUserItem;
                            var msgBody = sortedPlaylist[i].msgBody;        //// Added by ifuturz for Message,Email and Social
                            var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);

                            var datea_Stoptime = getEndDate(datea, durInSec * 1000);

                            var isPictureEvent = sortedPlaylist[i].isPictureEvent;
                            var isRevView = sortedPlaylist[i].isRevView;
                            var isVirtualInspection = sortedPlaylist[i].isVirtualInspection;
                            var TagName = sortedPlaylist[i].TagName;

                            ////Added by ifuturz on 20161228
                            if (isUserItem && flName == "User Added Item") {
                                flName = "User Added Item";
                                fileExt = "uai";
                            }
                            //tooltip = "";
                            //tooltip = '<div>';
                            //tooltip += 'Channel : ' + flName.split('.')[0] + '<br/>';
                            //tooltip += 'Date : ' + datea + '<br/>';
                            //tooltip += 'Time : ' + datea.split(' ')[1] + '<br/>';
                            //tooltip += 'Duration : ' + timeFormate(durInSec) + '';
                            //tooltip += '</div>';

                            switch (cType) {
                                case "3":
                                    if (!isUserItem) {
                                        tooltip = "";
                                        tooltip = '<div>';
                                        tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName.replace(/\+/g, ' ')) : "") + '<br/>';
                                        tooltip += '<b>Time :</b> ' + datea + '<br/>';
                                        tooltip += '<b>Phone Number :</b> ' + ani_ph + '<br/>';
                                        tooltip += '<b>Text :</b> ' + tag3 + '';
                                        tooltip += '</div>';
                                    }
                                    else {
                                        tooltip = "";
                                        tooltip = '<div>';
                                        tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                        tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                        tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                        tooltip += '<b>Message :</b> ' + msgBody + ''
                                        tooltip += '</div>';
                                    }
                                    break;
                                case "4":
                                case "5":
                                    tooltip = "";
                                    tooltip = '<div>';
                                    tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                    tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                    tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                    tooltip += '<b>Message :</b> ' + msgBody + '';
                                    tooltip += '</div>';
                                    break;
                                default:
                                    tooltip = "";
                                    tooltip = '<div>';
                                    tooltip += isOnlyIQ3ModeEnabled ? '<b>Inspector :</b> ' + chName + '<br/>' : '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                    tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                    tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                    tooltip += '<b>Duration :</b> ' + timeFormate(durInSec) + '<br/>';
                                    //tooltip += '<b>Caller ID :</b> ' + callerId;
                                    if (!isOnlyIQ3ModeEnabled)
                                        tooltip += '<b>Caller ID :</b> ' + ((callerId == null || callerId == undefined || callerId == 'undefined') ? '' : callerId) + '<br/>';
                                    if (cType == 1)
                                        tooltip += '<b>Call Tag :</b> ' + TagName + '<br/>';
                                    tooltip += '</div>';
                                    break;
                            }
                            ht = $($.parseHTML(tooltip));

                            iconCss1 = setTimelineIcon(cType, fileExt, isRevview, isVirtualInspection);
                            items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-toggle="popover" data-custom-title="Metadata" data-content="' + ht.html() + '"><span data-evt-id="' + callId + '" class="' + iconCss1 + '"></span> ' + '<span style="margin-left: 3px;">' + chName + ' ( ' + chNum + ' )' + ' | ' + datea + '</span></span>', start: datea, end: datea_Stoptime, group: items_playlist_count, callId: callId, flName: flName });
                            if (playlistIsSyncedFromClient == 0)
                                groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                            else
                                groups.push({ id: items_playlist_count, content: "<a href='#' data-id=" + items_playlist_count + "></a>" });
                            items_playlist_count++;
                        }
                    }
                    else {
                        for (i = 0; i < sortedPlaylist.length; i++) {
                            msgURI = '';

                            var recid = sortedPlaylist[i].recid;
                            var callId = sortedPlaylist[i].CallId;
                            var flName = sortedPlaylist[i].flName;
                            var startTime = sortedPlaylist[i].startTime;
                            var fileExt = sortedPlaylist[i].fileExt;
                            var durInSec = sortedPlaylist[i].durInSec;
                            var cType = sortedPlaylist[i].cType;
                            var tag3 = sortedPlaylist[i].tag3;
                            var ani_ph = sortedPlaylist[i].ani_ph;
                            var chNum = sortedPlaylist[i].chNum;
                            var chName = sortedPlaylist[i].chName;
                            var screenName = sortedPlaylist[i].screenName;
                            var pages = sortedPlaylist[i].pages;
                            var isUserItem = sortedPlaylist[i].isUserItem;
                            isUserItemIcon = sortedPlaylist[i].isUserItem;
                            var msgBody = sortedPlaylist[i].msgBody;        //// Added by ifuturz for Message,Email and Social
                            var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);
                            var IsRevcell = sortedPlaylist[i].IsRevcell;
                            var datea_Stoptime = getEndDate(datea, durInSec * 1000);
                            var TagName = sortedPlaylist[i].TagName;
                            var isRevView = sortedPlaylist[i].isRevView;
                            var isVirtualInspection = sortedPlaylist[i].isVirtualInspection;
                            //if (isUserItem == "true" || isUserItem == true) {

                            //    let ext = fileExt.toLowerCase();
                            //    JL(frontendLogger).info('preventing')
                            //    if (ext == "doc" || ext == "docx" || ext == "pdf" || ext == "ppt" || ext == "pptx" || ext == "xls" || ext == "xlsx"
                            //        || ext == "jpg" || ext == "png" || ext == "jpeg" || ext == "gif") {
                            //        addItemInAttachedFiles(sortedPlaylist[i]);
                            //        continue;
                            //    }
                            //}
                            if (isUserItem && flName == "User Added Item") {
                                flName = "User Added Item";
                                fileExt = "uai";
                            }


                            switch (cType) {
                                case "3":
                                    if (isUserItem == "false" || isUserItem == false) {
                                        tooltip = "";
                                        tooltip = '<div>';
                                        tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName.replace(/\+/g, ' ')) : "") + '<br/>';
                                        tooltip += '<b>Time :</b> ' + datea + '<br/>';
                                        tooltip += '<b>Phone Number :</b> ' + ani_ph + '<br/>';
                                        tooltip += '<b>Text :</b> ' + tag3 + '';
                                        tooltip += '</div>';
                                    }
                                    else {
                                        tooltip = "";
                                        tooltip = '<div>';
                                        tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                        tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                        tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                        tooltip += '<b>Message :</b> ' + msgBody + ''
                                        tooltip += '</div>';
                                    }
                                    break;
                                case "4":
                                case "5":
                                    tooltip = "";
                                    tooltip = '<div>';
                                    tooltip += '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                    tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                    tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                    tooltip += '<b>Message :</b> ' + msgBody + '';
                                    tooltip += '</div>';
                                    break;
                                default:
                                    tooltip = "";
                                    tooltip = '<div>';
                                    tooltip += isOnlyIQ3ModeEnabled ? '<b>Inspector :</b> ' + chName + '<br/>' : '<b>Channel :</b> ' + chNum + (chName != "" ? (" - " + chName) : "") + '<br/>';
                                    tooltip += '<b>Date :</b> ' + datea.split(' ')[0] + '<br/>';
                                    tooltip += '<b>Time :</b> ' + datea.split(' ')[1] + '<br/>';
                                    tooltip += '<b>Duration :</b> ' + timeFormate(durInSec) + '<br/>';
                                    //tooltip += '<b>Caller ID :</b> ' + callerId;
                                    if (!isOnlyIQ3ModeEnabled)
                                        tooltip += '<b>Caller ID :</b> ' + ((callerId == null || callerId == undefined || callerId == 'undefined') ? '' : callerId) + '<br/>';
                                    if (cType == 1)
                                        tooltip += '<b>Call Tag :</b> ' + TagName + '<br/>';
                                    tooltip += '</div>';
                                    break;
                            }

                            ht = $($.parseHTML(tooltip));

                            iconCss1 = setTimelineIcon(cType, fileExt, isRevview, isVirtualInspection);
                            items.push({ x: items_playlist_count, id: items_playlist_count, content: '<span data-toggle="popover" data-custom-title="Metadata" data-content="' + ht.html() + '"><span data-evt-id="' + callId + '" class="' + iconCss1 + '"></span> ' + '<span style="margin-left: 3px;">' + chName + ' ( ' + chNum + ' )' + ' | ' + datea + '</span></span>', start: datea, end: datea_Stoptime, group: items_playlist_count, callId: callId, flName: flName });
                            if (playlistIsSyncedFromClient == 0)
                                groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                            else
                                groups.push({ id: items_playlist_count, content: "<a href='#' class='remove_item' data-id=" + items_playlist_count + "><img src='../assets/icons/common/iconDelete.png' /></a>" });
                            items_playlist_count++;
                        }
                    }

                    if (sortedPlaylist.length > 0)//arivu
                    {
                        //PlayerList for TL 
                        if (bIsAddUserFileDialog) {
                            plA = new PlayerList();
                            plA.loadItems(items, groups, true);
                            plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth
                            //bIsAddUserFileDialog = false;
                            bIsCurrentUserAddedFile = true;
                            //bIsTLLoadedInAUF = true;
                        }
                        else {
                            //pl = new PlayerList();
                            //pl.loadItems(items, groups, false);
                            //// pl.init(items, groups);
                            //pl.init(items, groups, 'visualization');


                            plA = new PlayerList();
                            plA.loadItems(items, groups, false);
                            plA.init(items, groups, 'visualization_addfiles'); //original: Vasanth

                            // plA.syncScroll();
                            bIsTLLoadedInAUF = true;
                            bIsCurrentUserAddedFile = false;

                        }
                    }
                    $('.remove_item').on('click', function () {

                        try {
                            //if (bIsCurrentUserAddedFile) {
                            //    //////Added by ifuturz 20161219
                            //    var fileName = items[$(this).data('id')].flName;
                            //    var lastItem = false;
                            //    deleteAddUserFile(playlistId, userId, null, fileName, lastItem, bIsCurrentUserAddedFile);
                            //    //////Added by ifuturz 20161219
                            //    plA.removeItem([$(this).data('id')]);
                            //    getFileDetails(null);
                            //    bIsCurrentUserAddedFile = false;
                            //    //bIsZeroItem = false;
                            //}
                            //else {
                            //    for (i = 0; i < items.length; i++) {
                            //        if ($(this).data('id') == items[i].id) {
                            //            var callid = items[i].callId;
                            //            removePlaylistUserItem(callid);
                            //            $("#tblPlaylist tbody tr[callid='" + callid + "']").remove(); // ADDED BY KM IFUTURZ TO REMOVE ROW TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                            //            //var fileName = items[i].title;
                            //            var fileName = items[i].flName;
                            //            var lastItem = false;
                            //            if (items.length == 1) {
                            //                lastItem = true;
                            //            }
                            //            bIsTLLoadedInAUF = false;
                            //            bIsZeroItem = false;
                            //            deleteAddUserFile(playlistId, userId, null, fileName, lastItem, bIsCurrentUserAddedFile);
                            //            //getplaylistDetail(playlistId);
                            //        }
                            //    }
                            //    plA.removeItem([$(this).data('id')]);
                            //    pl.removeItem([$(this).data('id')]);
                            //    //Added by ifutrz to delete on User Added item list delete
                            //    plAuserAdded.removeItem([$(this).data('id')]);
                            //    //End by ifutrz to delete on User Added item list delete
                            //    getFileDetails(null);
                            //    if (items.length == 1) {
                            //        //bIsZeroItem = true;
                            //    }
                            //}

                            for (i = 0; i < items.length; i++) {
                                if ($(this).data('id') == items[i].id) {
                                    var callid = items[i].callId;
                                    if (!bNotSavedAUF)
                                        removePlaylistUserItem(callid);
                                    $("#tblPlaylist tbody tr[callid='" + callid + "']").remove(); // ADDED BY KM IFUTURZ TO REMOVE ROW TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                                    //var fileName = items[i].title;
                                    var fileName = items[i].flName;
                                    var lastItem = false;
                                    if (items.length == 1) {
                                        lastItem = true;
                                    }
                                    bIsTLLoadedInAUF = false;
                                    bIsZeroItem = false;
                                    //deleteAddUserFile(playlistId, userId, null, fileName, lastItem, bIsCurrentUserAddedFile);
                                    if (fileName != 'null' && fileName != 'User Added Item' && callid === '') {
                                        fileName = items[i].title;
                                        deleteAddUserFile(playlistId, userId, null, fileName, lastItem, bIsCurrentUserAddedFile);
                                    }
                                    //getplaylistDetail(playlistId);
                                }
                            }
                            plA.removeItem([$(this).data('id')]);
                            getFileDetails(null);
                            pl.removeItem([$(this).data('id')]);
                            //Added by ifutrz to delete on User Added item list delete
                            plAuserAdded.removeItem([$(this).data('id')]);
                            //End by ifutrz to delete on User Added item list delete

                            if (items.length == 1) {
                                //bIsZeroItem = true;
                            }

                            displaylink(); // ADDED BY KM IFUTURZ TO RELOAD TIMELINE VIEW AND CREATE NEW OBJECTS OF sequencer.js ARRAYS TO RESOLVE NEXT PREVIOUS ISSUE AFTER FILE DELETION
                        } catch (e) {
                            //alert(e.message);
                        }
                    });
                    // bIsCurrentUserAddedFile = false;
                } catch (e) {
                    //alert(e.message);
                }
                //KM IFUTURZ - STOP

                $('#divtimelineviewdesign').hide();

                $('#idvideolist_listview').html('');
                current_screencallid = "";
                screenvideo_obj = null;
                deleteallmedia();
                Reset();
                SetBuffering(false);
                currentloadedscreen = 'ListView';
                islistview = true;
                playbackItems = [];
                nActiveItem = 0;
                nActiveIndex = -1;
                nOldActiveItem = 0;
                nState = FilePlayerState.None;
                containsvideo = false;
                is_multiblobplay = false;
                $('#divTimeline').css('display', 'none');
                hideMetadata();
                $('#divListview').css('display', 'block');
                //$('#videofileplayer1').css({ 'width': '0px', 'height': '0px' });

                //$("lnkListview").removeClass("inActiveplLV").addClass("activeplLV");
                $('#hdnActiveForm').val('');
                slReset();
                //slDisableTimeline();
                //slCtl.Content.JS2SL.SetPlaylistParameters(helperServiceIp, userId, playlistId, false, nDiffMinutes);
                SetPlaylistParameters(helperServiceIp, userId, playlistId, false, nDiffMinutes);
                $('.body.row').css('z-index', '2'); // swap z-index
                var recid = '';
                var cType = '';
                var callId = '';
                var flName = '';
                var startTime = '';
                var fileExt = '';
                var durInSec = '';
                var durInMilliSec = '';
                var cType = '';
                var chNum = '';
                var chName = '';
                var screenName = '';
                var bookmark = '';
                var vod = '';
                var vid = '';
                IsContainText911 = 0;

                //initializePlayListTableUsingDataTable();
                if (playlistTable.rows().count() != 0) {
                    $("#tblPlaylist > tbody > tr").each(function () {
                        bIsZeroItem = false;
                        recid = $(this).attr('recid');
                        cType = $(this).attr('ctype');
                        callId = $(this).attr('callId');
                        flName = $(this).attr('fName');
                        startTime = $(this).attr('stTime');
                        fileExt = flName.substr(flName.lastIndexOf('.') + 1); // flName.split('.')[1];
                        durInSec = $(this).attr('cDuration'); // convertTimeToSeconds($(this).attr('cDuration'));
                        // durInMilliSec = $(this).attr('cdDuration');
                        //  durInSec = durInMilliSec / 1000;
                        chNum = $(this).attr('chNum');
                        chName = $(this).attr('chName');
                        //screenName = $(this).attr('sName').split(',')[0];
                        screenName = $(this).attr('sName');
                        bookmark = $(this).find('input[id=hdnBookmarkXml]').val();
                        var pages = $(this).attr('pages');
                        var isUserItem = $(this).attr('isuseritem');
                        var isPictureEvent = $(this).attr('isPictureEvent');
                        var IsRevCell = ($(this).attr('IsRevCell') != void 0) ? ($(this).attr('IsRevCell').toLowerCase() == 'true') : false;
                        var isRevview = $(this).attr('isRevview');
                        var revview_fname = $(this).attr('revview_fname');
                        var revview_sttime = $(this).attr('revview_sttime');

                        //isUserItemIcon = sortedPlaylist[i].isUserItem;
                        var sdate = new Date(getStartTimeDate(startTime));
                        var edate = new Date((new Date(getStartTimeDate(startTime)).getTime()) + (durInSec * 1000));

                        //JL(frontendLogger).info(fileExt);
                        if (cType == "1" || cType == "13") {
                            //JL(frontendLogger).info(callId + '-' + vod);

                            // For revcell calls for which Revcell channel disabled
                            if ((IsRevCell == false || IsRevCell == "false") && fileExt.toLowerCase() != "dsf" && !isUserItem)
                                IsRevCell = true;

                            if (IsRevCell == true) {
                                vod = createRevcellURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                slAddMedia(callId, vod);
                            } else {
                                switch (fileExt.toLowerCase()) {
                                    case "dsf":
                                        if (isRevview == true || isRevview == "true") {
                                            vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);

                                            var vid = getRevviewURI(revview_fname, 3, "" + revview_sttime, durInSec, chNum, chName, recid);
                                            var revviewEvent = {
                                                videoURI: vid,
                                                isRevview: true,
                                                playOffset: getVideoOffsetForRevview(startTime, revview_sttime),
                                                startTime: moment(startTime + "", "YYYYMMDDHHmmss"),
                                            };

                                            slAddMedia1(callId, vod, bookmark, revviewEvent);
                                        }
                                        else {
                                            vod = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                                            if (vod) {
                                                slAddMedia1(callId, vod, bookmark);
                                            }
                                        }
                                        break;
                                    case "mp3":
                                        if (isUserItem)
                                            vod = createDocumentURI(5, startTime, flName, durInSec);
                                        else
                                            vod = createMediaURI(recid, 5, startTime, flName, durInSec, chNum, chName);
                                        slAddMedia(callId, vod);
                                        break;
                                    case "wav":
                                        vod = createDocumentURI(1, startTime, flName, durInSec, pages);
                                        slAddMedia(callId, vod);
                                        break;
                                }
                            }
                        } else if (cType == "2") {
                            //vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                            if (isUserItem == 'true')
                                vid = createDocumentURI(3, startTime, flName, durInSec, 0);
                            else
                                vid = createVideoURI(flName, 3, startTime, durInSec, chNum, chName);
                            slAddMedia(callId, vid);
                            containsvideo = true;
                            //setTimeout(function () { slAddMedia(callId, vid); }, 1000);
                        } else if (cType == "3") {
                            msgURI = createMessageURI(6, startTime, flName, msgBody, chNum, chName, durInSec);
                            slAddSocialMedia(callId, msgURI);
                            IsContainText911 += 1;
                        } else if (cType == "6") {
                            //if (flName)
                            //    vid = createScreenURI(recid, 0, startTime, flName, durInSec, screenName, chNum, chName);
                            //else
                            //    vid = createScreenURIWithoutDSF(recid, 3, startTime, durInSec, screenName, chNum, chName);
                            //slAddMedia1(callId, vid, bookmark);
                            //containsvideo = true;
                            var audioUri = createMediaURI(recid, 0, startTime, flName, durInSec, chNum, chName);
                            var screensUri = createQuadScreenURI(recid, 3, startTime, durInSec, screenName, flName);

                            //slAddMedia(callId, screensUri[0]);

                            if (flName)
                                audioUri += "&screen=withaudio";
                            else
                                audioUri += "&screen=withoutaudio";

                            AddQuadMedia(callId, audioUri, screensUri);
                            containsvideo = true;
                        } else if (cType == "7" || cType == "11") {

                            if (isPictureEvent == "true" && cType == "7") {
                                vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName, recid);
                                slAddMedia(callId, vid, isPictureEvent);
                            }
                            else {

                                var fileExt = flName.split('.');
                                var ftype = "";

                                vid = createInquireURI(flName, 3, startTime, durInSec, chNum, chName, recid);
                                var bookmark_inquire = "<inquire></inquire>";

                                slAddMedia(callId, vid);
                                if (fileExt[1] == "mp3" || fileExt[1] == "m4a" || fileExt[1] == "wav") {
                                    ftype = "audio";
                                }
                                else
                                    ftype = "video";

                                containsvideo = true;
                            }

                        } else if (cType == "9") { //User Added Image
                            vod = createDocumentURI(9, startTime, flName, durInSec, pages);
                            //JL(frontendLogger).info(vod);
                            //slAddMedia(callId, vod);
                            slAddMediaListView(callId, vod, sdate, edate);
                            containsvideo = true;
                        } else if (cType == "10") { //User Added Document
                            //$("#divMainPlayerContainer").css('height', $('#divMainPlayerContainer').height() - $('#divTimeline').height() + 45);
                            vod = createDocumentURI(10, startTime, flName, durInSec, pages);
                            //JL(frontendLogger).info(vod);
                            slAddMediaListView(callId, vod, sdate, edate);
                            //slAddMediaListView(callId, vod, sdate, edate);
                            containsvideo = true;
                        }
                    });
                }

                $('#tblPlaylist > tbody > tr').show(); // $('#tblPlaylist > tbody > tr').attr("style", "display: inline;");
                $('#txtPlSearch').val('').show();

                window.setTimeout(function () {
                    playlistTable.columns.adjust();
                    configureListViewHeight();
                }, 500);
                //ShowHideText911Message(IsContainText911);
                //logging
                var msgData = loginUserName;
                var params = '{Id:' + $('#cbxLoadPlayList :selected').val() + ',Name:' + $('#cbxLoadPlayList').find(":selected").text() + '}';
                logActivity(loginUserId, 51, msgData, params);
                try {
                    if (!containsvideo) {
                        $('#divLvVideoPlayer').hide();
                        $('#divLvVideoPlayerContainer').hide();
                        //$('#divLvVideoPlayer').remove();
                        //$('#divListViewTable').top = '0px';
                        //!important
                        $('#divListViewTable').css({ 'top': '0px' });

                    } else {
                        swapVideoPlayerContents('Listview');

                        $('#divFileVideoPlayer').css({ 'width': '0px', 'height': '0px' });

                        $('#divFileVideoPlayer').css({ 'width': '0px', 'height': '0px' });

                        //$('#videofileplayer').css({ 'width': '0px', 'height': '0px' });
                        calculateAndAdjustVPPosition();
                    }
                } catch (e) {
                    //alert('DisplayLink ' + e.message);
                }
            }

            if (typeof (switchUserViewMode) == "function") {
                switchUserViewMode();
            }
        }
    } catch (e) {
        //alert('DisplayLink ' + e.message);
        JL(frontendLogger).info("EXCEPTION : DisplayLink");
        JL(frontendLogger).fatalException("Exception inside displaylink:", e);
    }
}

function changeLayout(mode) {
    //JL(frontendLogger).info(mode);
    switch (mode) {
        case "listMode":

            $("#playlistindex").show();
            $("#playlisttool").hide();

            break;
        case "detailMode":

            $("#playlistindex").hide();
            $("#playlisttool").show();

            configureTimelineViewHeight();
            break;
    }
}

function secondTimerDisplaySettings(display) {
    ;
    if (display == 1) {
        $('#divProgressTimer2').show();
        $('#divProgressTimer').css({ 'top': '20%', 'bottom': '0' });
    }
    else {
        $('#divProgressTimer2').hide();
        $('#divProgressTimer').css({ 'top': '45%', 'bottom': '7%' });
        //$('#divProgressTimer').removeAttr("top");
    }
}

//#endregion


//#region Playlist DataTable Related Functions

function initializePlayListTableUsingDataTable() {
    console.log('initializePlayListTableUsingDataTable 11');
    try {

        playlistTable = $('#tblPlaylist').DataTable({
            dom: 'Rlfrtip',
            columnDefs: [
                { width: "36px", targets: 0, orderable: false, bSortable: false, className: "dt-center" },
                { width: "36px", targets: 1, orderable: false, bSortable: false, className: "dt-center" },
                { width: "36px", targets: 2, className: "dt-left" },
                { width: "120px", targets: 3, className: "dt-left" },
                { width: "160px", targets: 4, className: "dt-left" },
                { width: "160px", targets: 5, className: "dt-left" },
                { width: "130px", targets: 6, className: "dt-left" },
                { width: "64px", targets: 7, className: "dt-left" },
                { width: "120px", targets: 8, className: "dt-left" },
                { width: "64px", targets: 9, className: "dt-left" },
                { width: "64px", targets: 10, className: "dt-left" },
                { width: "140px", targets: 11, className: "dt-left" },
                { width: "64px", targets: 12, className: "dt-center" },
                { width: "200px", targets: 13, className: "dt-left" },
                { width: "200px", targets: 14, className: "dt-left" }
            ],
            language: {
                infoEmpty: "No Items are added to the repository",
            },
            scrollY: '300px',
            scrollX: true,
            paging: false,
            info: false,
            searching: false,
            colResize: {
                tableWidthFixed: false,
                exclude: [0, 1],
                minColumnWidth: 50
            },
            colReorder: {
                fixedColumnsLeft: 2,
                enable: false,
                allowReorder: false
            },
            autoWidth: false,
            responsive: false,
            destroy: true,
            //fixedColumns: true,
            order: [[6, "desc"]]
        });

        //playlistTable.column(7).visible(false);
        //playlistTable.column(14).visible(false);
        playlistTable.columns.adjust().draw();
        hideColumnsByHeaderNames('#tblPlaylist', ["Bookmarks", "Text 911", "Group Name", "Inspector", "Duration","Channel Name"]);

        playlistTable.columns().every(function () {
            console.log(`Column ${this.index()} visible:`, this.visible());
        });

        $('#tblPlaylist thead th').each(function (index) {
            console.log('Column Index:', index, 'Header Name:', $(this).text().trim());
        });

        $('#tblPlaylist tbody tr').each(function (rowIndex) {
            $(this).find('td').each(function (colIndex) {
                console.log('Row ' + rowIndex + ', Col ' + colIndex + ': ' + $(this).text().trim());
            });
        });

        playlistTable.on('search.dt order.dt column-reorder', function () {
            playlistTable.column(0, { search: 'applied', order: 'applied' }).nodes().each(function (cell, i) {
                $(cell).find('span').text(i + 1);
            });

            displaylink();

        }).draw();

        //To adjust Columns
        //window.setTimeout(playlistTable.columns.adjust, 2000);
        playlistTable.columns.adjust().draw();

        //ShowHideText911Message(IsContainText911);
    }
    catch (e) {
        console.log("initializePlayListTableUsingDataTable => Execption,",e.message);
    }
}


function hideColumnsByHeaderNames(tableSelector, columnNamesToHide) {
    console.log('columnNamesToHide--', columnNamesToHide);
    const table = $(tableSelector).DataTable();
    const headers = [];

    $(`${tableSelector} thead th`).each(function (index) {
        const headerText = $(this).text().trim();
        headers.push(headerText);
        console.log('headerText--', headerText);
        if (columnNamesToHide.includes(headerText)) {
            console.log(`Hiding column '${headerText}' at index ${index}`);
            table.column(index).visible(false);
        }
    });
}

//#endregion


//#region Media Functions
function getRecorderVod(recid) {
    if (isECEnabled) {
        for (var i = 0; i < jsonRecorders.length; i++) {
            if (jsonRecorders[i]['Id'] == recid) {
                //JL(frontendLogger).info('found=' + jsonRecorders[i].Id + '-' + jsonRecorders[i].Name + '-' + jsonRecorders[i].IP + '-' + jsonRecorders[i].VOD);
                return jsonRecorders[i].VOD;
            }
        }
    }
    else {
        return vodServerURL;
    }
}

function getRecorderIP(recid) {
    if (isECEnabled) {
        for (var i = 0; i < jsonRecorders.length; i++) {
            if (jsonRecorders[i]['Id'] == recid) {
                //JL(frontendLogger).info('found=' + jsonRecorders[i].Id + '-' + jsonRecorders[i].Name + '-' + jsonRecorders[i].IP + '-' + jsonRecorders[i].VOD);
                return jsonRecorders[i].IP;
            }
        }
    }
    else {
        return screenURL;
    }
}

function getRecorderIsPrimary(recid) {
    if (isECEnabled) {
        for (var i = 0; i < jsonRecorders.length; i++) {
            if (jsonRecorders[i]['Id'] == recid) {
                //JL(frontendLogger).info('found=' + jsonRecorders[i].Id + '-' + jsonRecorders[i].Name + '-' + jsonRecorders[i].IP + '-' + jsonRecorders[i].VOD);
                return jsonRecorders[i].IsPrimary;
            }
        }
    }
    else {
        return true;
    }
}

function createMediaURI(recid, mediaType, sTime, fName, durInSec, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var vod = getRecorderVod(recid);
    if (vod) {
        //JL(frontendLogger).info(vod + "main=0&sub=0&ver=1&user=&attr=0&recid=" + recId + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName);
        if ($('#hdnActiveTab').val() == "Playlist")
            return vod + "main=0&sub=0&ver=1&user=&attr=0&recid=" + recid + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&tenantId=" + tenantId + "&IsPrimary=" + getRecorderIsPrimary(recid);
        else
            return vod + "main=0&sub=0&ver=1&user=&attr=0&recid=" + recid + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&isPlaylist=1&PlaylistID=" + currentPlaylistId + "&tenantId=" + tenantId + "&IsPrimary=true";
    }

    ////return vodServerURL + "main=0&sub=0" + "&media=" + mediaType + "&ver=1&user=&attr=0&date=" + pdate + "&file=" + fName + "&index=" + ptime;
    //return vodServerURL + "main=0&sub=0" + "&media=" + mediaType + "&ver=1&user=&attr=0&date=" + pdate + "&file=" + fName + "&index=" + ptime + "&time=" + ptime + "&duration=" + durInSec;
    //JL(frontendLogger).info(vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName);
    //return vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName;
}

function createMessageURI_Silverlight(fName, mediaType, sTime, msgBody, chNum, chName, Ani_ph, NG911Text) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    return clientRootURL + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&text=" + msgBody + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&ani_ph=" + Ani_ph + "&ng911text=" + NG911Text;
    ;
}

function createMessageURI(mediaType, sTime, fName, msgBody, chNum, chName, durInSec) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    ///Commented by ifuturz for message text
    //return clientRootURL + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&text=" + msgBody + "&tel=" + chNum + "&chName=" + chName;
    ///End by ifuturz for message text
    ///Added by ifuturz for message text
    return clientRootURL + "&media=" + mediaType + "&file=" + fName + ".uai" + "&date=" + pdate + "&time=" + ptime + "&text=" + msgBody + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&duration=" + durInSec;
    ///End by ifuturz for message text
}

function createVideoURI(fName, mediaType, sTime, durInSec, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    return videoURL + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
}

function createInquireURI(fName, mediaType, sTime, durInSec, chNum, chName, recId) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    //   
    //JL(frontendLogger).info("KM Inquire URL");
    // JL(frontendLogger).info(inquireURL + pdate + "/" + fName);
    if (tenantId > 0) {
        return inquireURL + tenantId + "/" + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&recid=" + recId + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
    }
    else {
        return inquireURL + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&recid=" + recId + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
    }
    //  return inquireURL + pdate + "/" + fName;
}

function createRevcellURI(recid, mediaType, sTime, fName, durInSec, screenName, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    //   
    //JL(frontendLogger).info("KM Inquire URL");
    // JL(frontendLogger).info(inquireURL + pdate + "/" + fName);

    if (isECEnabled) {
        var recip = getRecorderIP(recid);

        if (isHttps)
            return "https://" + recip + "/Revcell/" + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&recid=" + recid + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&Revcell=" + 1;
        else
            return "http://" + recip + "/Revcell/" + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&recid=" + recid + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&Revcell=" + 1;
    }
    else {
        return RevcellURL + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&recid=" + recid + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&Revcell=" + 1;
    }


    //  return inquireURL + pdate + "/" + fName;
}

function createScreenURI(recid, mediaType, sTime, fName, durInSec, screenName, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    if (isECEnabled && !getRecorderIsPrimary(recid)) {
        var recip = getRecorderIP(recid);
        var screen = "http://" + recip + "/videofiles/" + pdate + "/" + screenName;
        //JL(frontendLogger).info("http://" + recip + ":4510/VOD/VSWebVODAgent.dll?" + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName + "&screen=" + encodeURIComponent(screen));
        return "http://" + recip + ":4510/VOD/VSWebVODAgent.dll?" + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&screen=" + encodeURIComponent(screen);
    }
    else {
        if (isDemo && isValidDemoScreenName(screenName))
            pdate = "DemoScreens";
        var screen = screenURL + pdate + "/" + screenName;
        //JL(frontendLogger).info(vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName + "&screen=" + encodeURIComponent(screen));
        return vodServerURL + "main=0&sub=0&ver=1&user=&attr=0" + "&file=" + fName + "&index=" + ptime + "&media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "") + "&screen=" + encodeURIComponent(screen);
    }
}

function createScreenURIWithoutAudio(fName, mediaType, sTime, durInSec, chNum, chName) {
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    if (isDemo && isValidDemoScreenName(fName))
        pdate = "DemoScreens";
    return screenURL + pdate + "/" + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
}

function createScreenURIWithoutDSF(recid, mediaType, startTime, durInSec, screenName, chNum, chName) {
    var screenURI;
    var pdate = startTime.substring(0, 8);
    var ptime = startTime.substring(8, 14);
    if (isECEnabled) {
        var recip = getRecorderIP(recid);
        screenURI = "http://" + recip + "/videofiles/" + pdate + "/" + screenName + '?media=' + mediaType + "&file=" + fName + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSec;
    }
    else {
        if (isDemo && isValidDemoScreenName(screenName))
            pdate = "DemoScreens";
        screenURI = screenURL + pdate + "/" + screenName + '?media=' + mediaType + "&file=" + fName + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSec + "&tel=" + chNum + "&chName=" + chName.replace(/#/g, "");
    }
    return screenURI;
}

function createQuadScreenURI(recid, mediaType, sTime, durInSec, screenNames, fName) {

    var screenFileNames = screenNames.split(',');
    var quadScreenURI = [];
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var durInSecInt = parseInt(durInSec, 10);
    $.each(screenFileNames, function (index, value) {
        //JL(frontendLogger).info(index +'-'+ value);
        if (isECEnabled && !getRecorderIsPrimary(recid)) {
            var recip = getRecorderIP(recid);
            var screenURI = "http://" + recip + "/videofiles/" + pdate + "/" + value + '?media=' + 3 + "&file=" + fName + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSecInt;
            //JL(frontendLogger).info(screenURI);
            quadScreenURI.push(screenURI);
        }
        else {
            if (isDemo && isValidDemoScreenName(value))
                pdate = "DemoScreens";
            var screenURI = screenURL + pdate + "/" + value + '?media=' + 3 + "&file=" + fName + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSecInt;
            //JL(frontendLogger).info(screenURI);
            quadScreenURI.push(screenURI);
        }
    });
    return quadScreenURI;
}

function createQuadScreenURI_Silverlight(recid, mediaType, sTime, durInSec, screenNames, fName) {

    var screenFileNames = screenNames.split(',');
    var quadScreenURI = [];
    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var durInSecInt = parseInt(durInSec, 10);
    $.each(screenFileNames, function (index, value) {
        //JL(frontendLogger).info(index +'-'+ value);
        if (isECEnabled) {
            var recip = getRecorderIP(recid);
            var screenURI = "http://" + recip + "/videofiles/" + pdate + "/" + value + '?media=' + 3 + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSecInt;
            //JL(frontendLogger).info(screenURI);
            quadScreenURI.push(screenURI);
        }
        else {
            if (isDemo && isValidDemoScreenName(value))
                pdate = "DemoScreens";
            var screenURI = screenURL + pdate + "/" + value + '?media=' + 3 + '&date=' + pdate + '&time=' + ptime + '&duration=' + durInSecInt;
            //JL(frontendLogger).info(screenURI);
            quadScreenURI.push(screenURI);
        }
    });
    return quadScreenURI;
}

function createDocumentURI_Silverlight(mediaType, sTime, fName, durInSec, pages) {

    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var userId = $('#header-login-name').attr('uid');
    //var plId = $("#ulPlaylists > li.active").attr("id");

    var element = $("#cbxLoadPlayList").find('option:selected');
    var plid = element.val();
    var durInSecInt = parseInt(durInSec, 10);
    //JL(frontendLogger).info(docURL + fName + "?media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&pages=" + pages + "&userAdded=1");
    if (isMTEnable == undefined || isMTEnable == false)
        return docURL + userId + '/' + plid + '/' + fName + "?media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSecInt + "&pages=" + pages + "&userAdded=1";
    else
        return docURL + 'Tenant_' + tenantId + '/' + userId + '/' + plid + '/' + fName + "?media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSecInt + "&pages=" + pages + "&userAdded=1";
}

function createDocumentURI(mediaType, sTime, fName, durInSec, pages) {

    var pdate = sTime.substring(0, 8);
    var ptime = sTime.substring(8, 14);
    var userId = $('#header-login-name').attr('uid');
    //var plId = $("#ulPlaylists > li.active").attr("id");

    var element = $("#cbxLoadPlayList").find('option:selected');
    var plid = element.val();
    var durInSecInt = parseInt(durInSec, 10);
    //JL(frontendLogger).info(docURL + fName + "?media=" + mediaType + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSec + "&pages=" + pages + "&userAdded=1");
    if (isMTEnable == undefined || isMTEnable == false)
        return docURL + userId + '/' + plid + '/' + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSecInt + "&pages=" + pages + "&userAdded=1";
    else
        return docURL + 'Tenant_' + tenantId + '/' + userId + '/' + plid + '/' + fName + "?media=" + mediaType + "&file=" + fName + "&date=" + pdate + "&time=" + ptime + "&duration=" + durInSecInt + "&pages=" + pages + "&userAdded=1";
}

function createPaperClipURI(fName) {
    var userId = $('#header-login-name').attr('uid');

    var element = $("#cbxLoadPlayList").find('option:selected');
    var plid = element.val();

    if (isMTEnable == undefined || isMTEnable == false)
        return paperclipURL + userId + '/' + plid + '/' + fName;
    else
        return paperclipURL + 'Tenant_' + tenantId + '/' + userId + '/' + plid + '/' + fName;
}

function createDocumentURITimeline(fName) {
    var userId = $('#header-login-name').attr('uid');
    var element = $("#cbxLoadPlayList").find('option:selected');
    var plid = element.val();

    if (isMTEnable == undefined || isMTEnable == false)
        return docURL + userId + '/' + plid + '/' + fName;
    else
        return docURL + 'Tenant_' + tenantId + '/' + userId + '/' + plid + '/' + fName;
}

function convertTimeToSeconds(hms) {
    //var hms = '02:04:33';
    var a = hms.split(':');
    var seconds = (+a[0]) * 60 * 60 + (+a[1]) * 60 + (+a[2]);
    //JL(frontendLogger).info(seconds);
    return seconds;
}

function SecondsTohhmmss(totalSeconds) {
    var hours = Math.floor(totalSeconds / 3600);
    var minutes = Math.floor((totalSeconds - (hours * 3600)) / 60);
    var seconds = totalSeconds - (hours * 3600) - (minutes * 60);

    // round seconds
    seconds = Math.round(seconds * 100) / 100

    var result = (hours < 10 ? "0" + hours : hours);
    result += ":" + (minutes < 10 ? "0" + minutes : minutes);
    result += ":" + (seconds < 10 ? "0" + seconds : seconds);
    return result;
}
//#endregion Media Functions

//#region J2 SL Interfaces
function slAddMedia1_Silverlight(callId, uri, bookmarks) {
    if (noOfPlayingCalls < noOfMaxCallsToPlay) {
        noOfPlayingCalls++;
        ////alert(bookmarks);
        slCtl.Content.JS2SL.AddMedia(callId, uri);
        slCtl.Content.JS2SL.PutBookmarksXml(callId, bookmarks);
        //slCtl.Content.JS2SL.PutBookmarksXml(callId, "<Root><Record><Id>1</Id><CallIndex>2</CallIndex><Pos>10</Pos><Text>Bookmark at 10sec</Text></Record><Record><Id>2</Id><CallIndex>2</CallIndex><Pos>55</Pos><Text>Bookmark at 55sec</Text></Record><Record><Id>3</Id><CallIndex>2</CallIndex><Pos>5</Pos><Text>Bookmark at 5sec</Text></Record></Root>");
        //slCtl.Content.JS2SL.PlayMedia(callId);
    }
}

function slAddMedia_Silverlight(callId, uri) {
    //JL(frontendLogger).info(noOfPlayingCalls);
    slCtl.Content.JS2SL.AddMedia(callId, uri);
}

function slAddInquireMedia(callId, uri, bookmark, interview_details, ftype) {
    //JL(frontendLogger).info(noOfPlayingCalls);
    // KM File Player
    slCtl.Content.JS2SL.AddInquireMedia(callId, uri, bookmark, interview_details, ftype);
    //$('#btnSearchSaveCalls').attr('src', '../assets/images/player/export_active.png');
    //    setinquirefilename(uri);

    // TODO: handle Attached files View Report Link 
}

// IGOR Start
function slAddMedia1(callId, uri, bookmarks, revviewEvent) {
    //
    if (noOfPlayingCalls < noOfMaxCallsToPlay) {
        noOfPlayingCalls++;
        ////alert(bookmarks);
        //  slCtl.Content.JS2SL.

        AddMedia(callId, uri, null, revviewEvent);
        //  slCtl.Content.JS2SL.PutBookmarksXml(callId, bookmarks);
        //slCtl.Content.JS2SL.PutBookmarksXml(callId, "<Root><Record><Id>1</Id><CallIndex>2</CallIndex><Pos>10</Pos><Text>Bookmark at 10sec</Text></Record><Record><Id>2</Id><CallIndex>2</CallIndex><Pos>55</Pos><Text>Bookmark at 55sec</Text></Record><Record><Id>3</Id><CallIndex>2</CallIndex><Pos>5</Pos><Text>Bookmark at 5sec</Text></Record></Root>");
        //slCtl.Content.JS2SL.
        ///      PlayMedia(callId);
    }
}

function slAddMedia(callId, uri, isPictureEvent, revviewEvent) {
    //JL(frontendLogger).info(noOfPlayingCalls);
    //slCtl.Content.JS2SL.

    AddMedia(callId, uri, isPictureEvent, revviewEvent);
}
//IGOR End

///Added by ifuturz for social media
function slAddSocialMedia(callId, uri) {
    AddSocialMedia(callId, uri);
}
///End by ifuturz for social media
///Added by ifuturz for ListView
function slAddMediaListView(callId, uri, sdate, edate) {
    AddMediaListView(callId, uri, sdate, edate);
}
///End by ifuturz for ListView
function slAddQuadMedia(callId, auri, suris) {
    enableQuadView = true;
    slCtl.Content.JS2SL.slAddQuadMedia(callId, auri + "&screen=" + suris[0], suris[1], suris[2], suris[3], enableQuadView);
}

function slAddQuadScreen(callId, suris) {
    enableQuadView = true;
    slCtl.Content.JS2SL.slAddQuadMedia(callId, suris[0], suris[1], suris[2], suris[3], enableQuadView);
}

function slPlayMedia_Silverlight(callId) {
    slCtl.Content.JS2SL.PlayMedia(callId);
}

function slDeleteMedia_Silverlight(callId) {
    noOfPlayingCalls--;
    slCtl.Content.JS2SL.DeleteMedia(callId);
}

function slReset_Silverlight() {
    slCtl.Content.JS2SL.Reset();
}

function slEnableTimeline_Silverlight() {
    slCtl.Content.JS2SL.EnableTimeline(true);
}

function slDisableTimeline_Silverlight() {
    slCtl.Content.JS2SL.EnableTimeline(false);
}

function slHaveVideo_Silverlight() {
    var haveVideo = slCtl.Content.JS2SL.HaveVideo();
    return haveVideo;
    //JL(frontendLogger).info(haveVideo);
}
//IGOR Start

function slPlayMedia(callId) {
    // slCtl.Content.JS2SL.
    if (boolIEBrowser)
        slPlayMedia_Silverlight(callId);
    else
        PlayMedia(callId);
}

function slPlayMedia_Inquire(callId) {
    // slCtl.Content.JS2SL.

    // KM on 20160705

    $('#divSearchVideoPane').show();
    html5player_adjust();

    //$('#divSearchVideoPane_Quad').show();
    //html5player_adjust_Quad();
    //PlayMedia(callId);
}

function slDeleteMedia(callId) {
    noOfPlayingCalls--;
    // slCtl.Content.JS2SL.
    DeleteMedia(callId);
}

function slReset() {
    // slCtl.Content.JS2SL.

    Reset();
}

function slEnableTimeline() {
    // slCtl.Content.JS2SL.EnableTimeline(true);
}

function slDisableTimeline() {
    //  slCtl.Content.JS2SL.EnableTimeline(false);
}

function slHaveVideo() {

    if (boolIEBrowser) {
        var haveVideo = slCtl.Content.JS2SL.HaveVideo();
        return haveVideo;
    } else
        return containsvideo;
    // var haveVideo = slCtl.Content.JS2SL.HaveVideo();
    // return haveVideo;
    //JL(frontendLogger).info(haveVideo);
}
//IGOR End
function slAddUserFile() {
    var existingItems = parseInt($("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "']").attr('noofitems'), 10);
    if ((existingItems <= 499 && !boolIEBrowser) || (existingItems <= 99 && boolIEBrowser))
        slCtl.Content.JS2SL.ShowAddUserFileDialog();
    else
        showErrorDialog(langKeys[currentLanguage]['emsgPlLimit']);
}

// SL 2 JS interfaces
function slFileEvent_OnSaveBookmark(callId, position, bookmark, saveType) {
    ////alert("Here should be code to save bookmark for call id " + callId + ", position " + position + ", type: " + saveType + ".\nBookmark:\n" + bookmark);
    //res = JSON.search(jsonRecorders, '//*[IsPrimary="true"]');
    //JL(frontendLogger).info('found=' + res[0].Id + '-' + res[0].Name + '-' + res[0].IP + '-' + res[0].VOD + '-' + res[0].IsPrimary);
    var recid = $("#tblCalls tbody tr[id=" + callId + "]").attr("recid");

    JL(frontendLogger).info('Position  ::  ' + position);
    JL(frontendLogger).info('bookmark  ::  ' + bookmark);

    var callType;
    $('#tblCalls tbody tr').each(function () {
        if (callId == $(this).attr('id')) {
            callType = $(this).attr('ctype');
        }
    });
    var jsonStr = {
        PersistType: saveType == 0 ? 'Update' : 'Insert'
        , RecorderId: recid
        , Bookmark: {
            CallId: callId,
            Position: position,
            Text: bookmark
        }
    };
    var data2send = JSON.stringify(jsonStr);
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: $(this).find('form').serialize(),
        //data: { method: 'SaveBookmarkByRecorder', bmRequest: data2send },
        data: { method: isECEnabled ? 'SaveBookmarkByRecorder' : 'SaveBookmark', bmRequest: data2send, isSharedPage: isSharedPage },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI(null, langKeys[currentLanguage]['sBmSave']);
                    updateRow(callId, response.xmldata, response.csvdata);
                    if (callType == 7 || callType == 11) {
                        getInqFileVideoBookmarks(callId);
                    }
                    slCtl.Content.JS2SL.ResumePlay();
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in slFileEvent_OnSaveBookmark(): ' + jqXHR.responseText);
        }
    });
}

function slFileEvent_OnDialogStart() {
    document.onmousedown = slDisableRightClick;
    document.oncontextmenu = new Function("return false");
    var slDiv = document.getElementById('silverlightControlHost');
    //$("#divCallSearchResults").css('z-index', '2');//$("#divCallSearchResults").parent().css('z-index', '2');//$("#divCallSearchResults").parent().parent().css('z-index', '2');
    if (slDiv != null) {
        //slDiv.style.zIndex = 99;
        reOrderzIndex('SLDialogOpen');
    }
    //$("#divMainPlayerContainer").css('z-index', '3');
}

function slFileEvent_OnDialogEnd() {
    var slDiv = document.getElementById('silverlightControlHost');
    if (slDiv != null) {
        //slDiv.style.zIndex = 1;
        reOrderzIndex('SLDialogClose');
    }
    document.onmousedown = null;
    document.oncontextmenu = null;
    //$("#divMainPlayerContainer").css('z-index', '1');
}

function slFileEvent_OnPlay(callId, itemType) {
    //JL(frontendLogger).info(itemType);
    //JL(frontendLogger).info(callId + "-" + itemType + "-" + $('#hdnActiveTab').val());
    //$('#tblCalls tbody tr td.chkCallCol').removeClass('playCol');
    //$('#tblCalls tbody tr[id=' + callId + '] td.chkCallCol').addClass('playCol');

    callId = callId.trim();
    var callType;
    $('#tblCalls tbody tr input.tblrowChk').closest('td').removeClass('playCol');
    $('#tblCalls tbody tr[id=' + callId + '] input.tblrowChk').closest('td').addClass('playCol');


    if ($('#hdnActiveTab').val() == "Listview") {
        $('#tblPlaylist tbody tr').removeClass('selected');
        $("#tblPlaylist tbody tr[callid='" + callId + "']").addClass('selected');
        if (itemType == 3 || itemType == 9 || itemType == 10) {
            //$('#divLvVideoPlayer').slideDown(3000);//slider working
            //$('#divLvVideoPlayerContainer').slideDown(3000);
            $('#divListViewTable').stop().animate({ top: 282 }, 3000);

            setTimeout(function () {
                configureListViewHeight();
            }, 3500);
        }
        else {
            //$('#divLvVideoPlayer').slideUp(5000);//slider working
            //$('#divLvVideoPlayerContainer').slideUp(5000);
            $('#divListViewTable').stop().animate({ top: 0 }, 5000);

            setTimeout(function () {
                configureListViewHeight();
            }, 5500);
        }
    }
    //$('#tblCalls tbody tr').each(function () {
    //    if (callId == $(this).attr('id')) {
    //        callType = $(this).attr('ctype');
    //    }
    //    if (callType == 7) {
    //        $("#InterviewBookmarkDetails").show();
    //        $("#divSearchVideoPane").show();
    //        DisplayInterviewDetails(callId);
    //        getInqFileVideoBookmarks(callId);
    //    }
    //    else if (callType == 6 || callType == 2) {
    //        $("#InterviewBookmarkDetails").hide();
    //        // $("#divSearchVideoPane").show();
    //    }
    //})
    //if ($('#hdnActiveTab').val() == "Search" && itemType == 3) {
    //    //$('#videoContainerSV #divSearchVideoPlayer').html($('#divLvVideoPlayer').html());
    //    swapVideoPlayerContents('Search');
    //    reOrderzIndex('searchVideoView');
    //    callType = $('#tblCalls tbody tr[id=' + callId + ']').attr('ctype');
    //    adjustQuadPlayerSize(callId, itemType);
    //}
    //else
    reOrderzIndex('searchNonVideoView');
}

function slFileEvent_OnPlay_LV(callId, itemType, revviewEvent) {
    //JL(frontendLogger).info(itemType);
    //JL(frontendLogger).info(callId + "-" + itemType + "-" + $('#hdnActiveTab').val());
    //$('#tblCalls tbody tr td.chkCallCol').removeClass('playCol');
    //$('#tblCalls tbody tr[id=' + callId + '] td.chkCallCol').addClass('playCol');

    try {
        if (callId.startsWith("IQ3_")) {
            var $tr = $("#tblPlaylist tbody tr[callid='" + callId + "']");
            //$("#ulPlaylistExportReportFiles").html("");
            //addItemInExportReportFiles($tr);
        }
        else {
            //$("#ulPlaylistUserAddedFiles").html("");
        }
    }
    catch (e) {
        console.error(e);
    }

    try {
        ClearBookmarksInPlayer();

        callId = callId.trim();
        var callType;
        if ($('#hdnActiveTab').val() == "Listview") {
            $('#tblPlaylist tbody tr').removeClass('selected');
            $("#tblPlaylist tbody tr[callid='" + callId + "']").addClass('selected');
            if (itemType == 3 || itemType == 9 || itemType == 10 || revviewEvent) {
                //$('#divLvVideoPlayer').slideDown(3000);//slider working
                //$('#divLvVideoPlayerContainer').slideDown(2000);
                $('#divListViewTable').stop().animate({ top: 282 }, 2000);

                setTimeout(function () {
                    configureListViewHeight();
                }, 3500);
            }
            else {
                //$('#divLvVideoPlayer').slideUp(5000);//slider working
                //$('#divLvVideoPlayerContainer').slideUp(2000);
                $('#divListViewTable').stop().animate({ top: 0 }, 2000);

                setTimeout(function () {
                    configureListViewHeight();
                }, 2500);
            }
        }
    }
    catch (error) {
        console.error(e);
    }

    try {
        $.ajax({
            type: 'POST',
            url: handlersBaseURL + '/VoiceRecHandlers/VRHandler.ashx',
            data: { 'method': 'FetchInqFileVideoBookmarks', 'CallId': callId, isSharedPage: isSharedPage },
            success: function (response) {
                var $tr = $('#tblPlaylist tbody tr[callid=' + callId + ']');
                if (response.data != null)
                    ShowBookmarksInPlayerDoubleClick($tr, response.data);
            },
            error: function (response) { // Failure
                console.error('Error occurred fetching bookmark(): ' + jqXHR.responseText);
            }
        });
    }
    catch (e) {
        console.error(e);
    }

    //$('#tblCalls tbody tr').each(function () {
    //    if (callId == $(this).attr('id')) {
    //        callType = $(this).attr('ctype');
    //    }
    //    if (callType == 7) {
    //        $("#InterviewBookmarkDetails").show();
    //        $("#divSearchVideoPane").show();
    //        //DisplayInterviewDetails(callId);
    //        //getInqFileVideoBookmarks(callId);
    //    }
    //    else {
    //        $("#InterviewBookmarkDetails").hide();
    //    }
    //});
    //if ($('#hdnActiveTab').val() == "Search" && itemType == 3) {
    //    //$('#videoContainerSV #divSearchVideoPlayer').html($('#divLvVideoPlayer').html());
    //    //swapVideoPlayerContents('Search');
    //    reOrderzIndex('searchVideoView');
    //    callType = $('#tblCalls tbody tr[id=' + callId + ']').attr('ctype');
    //    adjustQuadPlayerSize(callId, itemType);
    //}
    //else
    reOrderzIndex('searchNonVideoView');
}

function slFileEvent_OnStop() {
    //JL(frontendLogger).info('slFileEvent_OnStop');
    reOrderzIndex('searchNonVideoView');
    //$('#tblCalls tbody tr td.chkCallCol').removeClass('playCol');
    $('#tblCalls tbody tr input.tblrowChk').closest('td').removeClass('playCol');

    $('#tblPlaylist tbody tr').removeClass('selected');
}

function slFileEvent_OnDelete(callId, userInitiated) {
    //JL(frontendLogger).info("callid " + callId + " deleted by user: " + userInitiated);
    if (userInitiated && $('#hdnActiveTab').val() == 'Timeline') {
        var pldId = $("#tblPlaylist tbody tr[callid='" + callId + "']").attr('id');
        var RevSyncServerPlaylistID = $("#tblPlaylist tbody tr[callid='" + callId + "']").attr('RevSyncServerID');
        if (pldId)
            removePlaylistItem(pldId, RevSyncServerPlaylistID);
        //showConfirmationDialog("Are you sure to delete the item from Playist permanently?", objDel, deletePlItem);
    }
}

function slFileEvent_OnRegisterUserItem(itemInfo) {
    var jsonObj = $.parseJSON(itemInfo);
    var RevSyncServerPlaylistID = parseInt($("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "'").attr('RevSyncServerID'), 10);
    jsonObj.RevSyncServerPlaylistID = RevSyncServerPlaylistID;
    addUserItemInPlaylist(jsonObj);
    return 1;
}

function slFileEvent_OnRemoveUserItem(callId) {
    //JL(frontendLogger).info(callId);
    var plName = currentPlaylistName;
    var response = 0;
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: { callId: callId, plName: plName, method: 'RemoveUserPlaylistItem', isSharedPage: isSharedPage },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI(null, langKeys[currentLanguage]['sPldItemDel']);
                    //$("#tblPlaylist tbody tr[callid='" + callId + "']").remove();
                    //playlistTable.row("[callid='" + callId + "']").remove().draw();
                    updateTableAfterRemovingItems(callId);

                    var existingItems = parseInt($("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "']").attr('noofitems'), 10);
                    var deletedItems = response.data;
                    var newItems = existingItems - deletedItems;
                    //$("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "'").attr('noofitems', newItems);
                    updateNewNoItemsInPlaylist(newItems);
                    response = 1;
                    return 1;
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    return 0;
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred slFileEvent_OnRemoveUserItem(): ' + jqXHR.responseText);
        }
    });
    return response;
}

//#endregion

//#region

function addUserItemInPlaylist(obj) {
   
    var plName = currentPlaylistName;
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        //data: { method: 'AddCustomItemInPlaylist', playlistId: obj.nPlaylistId, RevSyncServerPlaylistID: obj.RevSyncServerPlaylistID, plName: plName, userId: obj.nUserId, userItem: JSON.stringify(obj).replace(/\/Date/g, "\\\/Date").replace(/\)\//g, "\)\\\/") }, //userItem: JSON.stringify(obj) },
        data: {
            method: 'AddCustomItemInPlaylist', playlistId: currentPlaylistId, RevSyncServerPlaylistID: obj.RevSyncServerPlaylistID,
            plName: plName, userId: obj.nUserId, userItem: JSON.stringify(obj).replace(/\/Date/g, "\\\/Date").replace(/\)\//g, "\)\\\/"),
            isSharedPage: isSharedPage
        },
        success: function (response) {
            switch (response.success) {
                case true:
                    var plItem = response.data;
                    $.unblockUI();
                    $('#divAddManualFile').mmsDialog("close");
                    $.growlUI(null, langKeys[currentLanguage]['sPlUserAddedItem']);

                    var existingItems = parseInt($("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "']").attr('noofitems'), 10);
                    var newItems = existingItems + 1;
                    updateNewNoItemsInPlaylist(newItems);

                    var vod = plItem.PlayerItemURI;
                    if (obj.nFileType == 1 || obj.nFileType == 3 || obj.nFileType == 5 || obj.nFileType == 9 || obj.nFileType == 10) {
                        vod = (isMTEnable == undefined || isMTEnable == false)
                            ? docURL + vod
                            : docURL + 'Tenant_' + tenantId + '/' + vod;
                    } else {
                        vod = clientRootURL + vod;
                    }

                    buildPlRow(plItem);
                    bIsAddUserFileDialog = false;
                    bIsHasFile = false;
                    bIsTLLoadedInAUF = false;
                    bIsDestroyTLInAUF = true;
                    displaylink();

                    // 🔽 Added this line to trigger repository click handling
                    var rowElement = $("#tPlaylist tbody tr[playlistid='" + currentPlaylistId + "']");
                    handleAddNewRepositoryClick(rowElement);
                    break;

                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error in addUserItemInPlaylist()' + jqXHR.responseText);
        }
    });
}
function handleAddNewRepositoryClick(element) {
    $('#btnAddNewRepository').hide();
    isPlayersAutoHidden = true;

    var playlistId = parseInt($(element).closest('tr').attr('playlistId'), 10);
    var playlistName = $(element).closest('tr').attr('playlistName');
    var playlistType = $(element).closest('tr').attr('PlaylistType');

    if (playlistType == 2) {
        isMTRplaylist = true;
    } else {
        isMTRplaylist = false;
    }

    currentPlaylistId = playlistId;
    currentPlaylistName = playlistName;

    resetPlaylistDetailsDialog();
    changeLayout('listMode');

    $('#hdnActiveTab').val("Timeline");
    $("#divPlaylistTabs").css('display', 'none');

    getplaylistDetail(playlistId, playlistName);
}

function buildPlRow(userItem) {
    var imgMsgToolTip = '';
    if (userItem.FileType == 3 || userItem.FileType == 4 || userItem.FileType == 5 || userItem.FileType == 7) {
        imgMsgToolTip = ' msgBody="' + encodeURIComponent(userItem.sText) + '" data-content="' + userItem.sText + '" data-toggle="popover" data-placement="right" data-original-title="Message"';
    }

    var $tr = $('<tr id="" isuserItem="true" chNum="0" chName="" sname="" callid="' + userItem.sCallId + '" ctype="' + userItem.FileType + '" recid="' + userItem.nRecorderId + '" fname="' + userItem.sFileName + '" sttime="' + userItem.StartTimeString + '" cduration="' + userItem.nDurationSec + '" msgBody="' + userItem.sText + '" pages="' + userItem.nPageCount + '">');

    $tr.append($('<input type="hidden" id="hdnBookmarkXml">'));
    $tr.append($('<td>').html(playlistTable.rows().count() + 1));
    $tr.append($('<td class="delItem">').html('<a id="lnkDelete" class="deleteIcon ml" style="float:left;" href="javascript:void(0);" lkey="imgDelete"></a>'));
    //$('<td>').html('<img id="imgMsg" src="../assets/icons/search/' + userItem.FileType + '.png"/>'),
    $tr.append($('<td style="text-align: center;">').html('<img id="imgMsg" src="../assets/icons/search/' + userItem.FileType + '.png"' + imgMsgToolTip + '/>'));
    $tr.append($('<td>').html(''));
    $tr.append($('<td>').html(''));
    $tr.append($('<td>').html(''));
    $tr.append($('<td>').html(formatStringToDatetime(userItem.StartTimeString)));
    $tr.append($('<td>').html(formatSecondsAsTime(userItem.nDurationSec)));
    $tr.append($('<td>').html(userItem.ANI_PH));
    $tr.append($('<td style="text-align: center;">').html('<input id="chkPlRetain" class="gridrowChk" type="checkbox" disabled="" value="' + userItem.sCallId + '" onclick="javascript:plRetainCall(this)" name="chkPlRetain">'));
    $tr.append($('<td abbr="colCallTag">').html(''));
    $tr.append($('<td class="bookmark">').html(''));
    $tr.append($('<td>').html(''));
    $tr.append($('<td>').html(''));
    $tr.append($('<td>').html(''));

    //JL(frontendLogger).info($tr);
    //$tr.find('td:')
    //$('#tblPlaylist').append($tr);
    if (playlistTable != null) {
        playlistTable.row.add($tr);
        playlistTable.draw();
    }
    //$("#tblPlaylist img").popover({ trigger: "hover", delay: { show: 200, hide: 100 }, html: 'true' });
}

function updateRow(callId, xmlData, csvData) {

    //$("#tblPlaylist > tbody > tr[callid='" + callId + "']").find('input[id=hdnBookmarkXml]').val(xmlData);
    //$("#tblPlaylist > tbody > tr[callid='" + callId + "'] td[class=bookmark]").html(csvData);

    if (csvData != void 0 && csvData != '') {
        var bookmarkContent = csvData.length <= 20 ? csvData : csvData.substring(0, 20);
        bookmarkContent = bookmarkContent + '<a id="lnkBm" noOfBm="21" href="javascript:void(0);" style="padding-left: 3px; text-align: right;" class="bm-details-Listview"><img height="16" alt="" src="../assets/icons/search/bookmark-icon-16.png"></a>';

        $("#tblPlaylist > tbody > tr[callid='" + callId + "'] td[class=bookmark]").html(bookmarkContent);
    }

    var $tr = $("#tblPlaylist tbody tr[id='" + callId + "']").closest("tr");
    ShowBookmarksInPlayerDoubleClick($tr);
}

//#endregion

//#region Resizing Functions

//Resize callsTable when File Player is Expanded/Close
$(function () {
    $('#lnkExpandClose').on('click', function () {
        setTimeout(configureTimelineVisualizationHeight, 500);
    });
});

$(window).resize(function () {
    resizeMainPlaylistTable();
    configureTimelineViewHeight();
    configureListViewHeight();

    setTimeout(function () {
        configureTimelineVisualizationHeight();
        html5player_adjust_listview();
    }, 1000);

    try {
        //To remove focus from Playlist Select
        if (document.activeElement != void 0) {
            if (document.activeElement.tagName == "SELECT") {
                JL(frontendLogger).info("Removing focus from Playlist Select on resize.");
                $(document.activeElement).blur();
            }
        }
    }
    catch (e) {

    }
});

function configureTimelineViewHeight() {
    JL(frontendLogger).info('inside configureTImelineViewHeight');
    let timelineViewHeight = $(window).height() - $('#divPlSearch').height() - $("#logo").height() - getPlayerHeight();
    JL(frontendLogger).info('timelineViewHeight - ', timelineViewHeight);
    //$('#divtimelineviewdesign').css('height', (timelineViewHeight * 4)/10 );
    //$('#divtimelineviewdesign').css('max-height', $(window) / 2);
    //$('#divtimelineviewdesign').css('position', 'static');
    //$('#visualization').css('height', ((timelineViewHeight * 4) / 10) - $('#divtimelineviewdesign div.widget-title').outerHeight());

    //$("#divtimelineviewdesign").css("bottom", getPlayerHeight() + 10);

    if (($("#divLvVideoPlayer0").width() < (timelineViewHeight * 6) / 10) && $('#divLvVideoPlayer0').width() != 0) {
        $('#divTimeline').css('height', $('#divLvVideoPlayer0').width());
    }
    else {
        $('#divTimeline').css('height', (timelineViewHeight * 6) / 10);
    }

    //window.setTimeout(configureTimelineVisualizationHeight, 500);

    let videoPlayerHeight = $('#divTimeline').height() - 100;
    JL(frontendLogger).info('videoPlayerHeight - ', videoPlayerHeight);
    if (!isFullScreen()) {
        $('#divLvVideoPlayer0').css('height', videoPlayerHeight);
        $('#divLvVideoPlayer1').css('height', videoPlayerHeight);
        $('#divLvVideoPlayer2').css('height', videoPlayerHeight);
        $('#divLvVideoPlayer3').css('height', videoPlayerHeight);
    }
    else {
        $('#divLvVideoPlayer0, #divLvVideoPlayer1, #divLvVideoPlayer2, #divLvVideoPlayer3').css({ 'height': $(window).height() });
    }
}

function showHideTimelineVideos(isVisible) {
    if (isVisible && !isPlayersHidden && !isPlayersAutoHidden) {
        if (!$("#divTimeline").is(":visible")) {
            $("#divTimeline").show();
            configureTimelineVisualizationHeight();
        }
    }
    else {
        if ($("#divTimeline").is(":visible")) {
            $("#divTimeline").hide();
            configureTimelineVisualizationHeight();
        }
    }
}

function configureTimelineVisualizationHeight() {
    //Based on
    let timelineViewHeight = $(window).height() - $('#divPlSearch').height() - $("#logo").height() - $("#divPlaylistTabs").height() - getPlayerHeight();

    $("#playlistContainer").height(timelineViewHeight - 28);

    JL(frontendLogger).info("timelineViewHeight height - ", timelineViewHeight);
    timelineViewHeight = timelineViewHeight - 114;
    //if ($("#divTimeline").is(":visible") && slHaveVideo()) {
    if ($("#divTimeline").is(":visible")) {
        JL(frontendLogger).info("timelineViewHeight height - ", timelineViewHeight);
        resizeTimelineVisualization(timelineViewHeight - $("#divTimeline").height());
    }
    else {
        JL(frontendLogger).info("timelineViewHeight height - ", timelineViewHeight);
        resizeTimelineVisualization(timelineViewHeight);
    }
}

function resizeTimelineVisualization(height) {
    JL(frontendLogger).info("inside TimelineVisualization height - ", height);

    if (isNaN(height) || height < minimumTimelineVisualizationHeight) {
        height = minimumTimelineVisualizationHeight;
    }

    $("#divtimelineviewdesign .vis-center").height(height);
    $("#divtimelineviewdesign .vis-left").height(height);
    $("#divtimelineviewdesign .vis-right").height(height);
    $("#divtimelineviewdesign .vis-panel.vis-bottom").css('top', height);

    var extraHeight = height + $("#divtimelineviewdesign .vis-panel.vis-bottom").height();

    $("#divtimelineviewdesign .vis-panel.vis-background").height(extraHeight);
    $("#divtimelineviewdesign .vis-background.vis-horizontal").height(height);
    $("#divtimelineviewdesign .vis-timeline.vis-bottom").height(extraHeight);
}

function configureListViewHeight() {
    JL(frontendLogger).info('inside configureListviewHeight');
    let listViewHeight = $(window).height() - $('#divPlSearch').height() - $("#logo").height() - getPlayerHeight() - 30;
    JL(frontendLogger).info('listViewHeight - ', listViewHeight);

    //if ($("#divLvVideoPlayerContainer").is(":visible")) {
    //    $('#divListViewTable').css('height', (listViewHeight * 4) / 10);
    //}
    //else {
    //    $('#divListViewTable').css('height', listViewHeight);
    //}
    //$('#divtimelineviewdesign').css('max-height', $(window) / 2);

    $('#divListViewTable').css('position', 'static');

    playlistTableSize = $('#divListViewTable').height() - $('#divListViewTable div.widget-title').outerHeight() - $('#tblPlaylist_wrapper div.dataTables_scrollHead').outerHeight() - 10;

    JL(frontendLogger).info('insise listview playlistTableSize - ', playlistTableSize);

    if (playlistTableSize < 77) playlistTableSize = 77;

    //Playlist Table View Height
    /*$('#tblPlaylist_wrapper div.dataTables_scrollBody').height(playlistTableSize);*/
    //redrawPlaylistTable();

    $('#divLvVideoPlayerContainer').css('height', (listViewHeight * 6) / 10);

    if (!isFullScreen()) {
        let videoPlayerHeight = $('#divLvVideoPlayerContainer').height() - 50;
        JL(frontendLogger).info('videoPlayerHeight - ', videoPlayerHeight);
        $('#divLvVideoPlayer').css('height', videoPlayerHeight);
        //$('#divLvVideoPlayer video').css({ 'height': videoPlayerHeight, 'max-height': videoPlayerHeight, 'width': 'auto' });
        $('#divLvVideoPlayer > video').each(function (i, item) {
            //debugger
            //console.log(this.src);
            //console.log($(this).attr('src'));
            $(this).removeClass('hide');
            $(this).css({ 'height': videoPlayerHeight, 'max-height': videoPlayerHeight, 'width': 'auto' });
            //if (this.src === '')
            if ($(this).attr('src') === '' || $(this).attr('src') === undefined) {
                $(this).addClass('hide');
            }
        });
    }
    else {
        let fullScreenHeight = $(window).height() - 40;
        $("#divLvVideoPlayer").height(fullScreenHeight);

        //Because Timeline
        if (currentloadedscreen == "ListView") {
            $("#divLvVideoPlayer").css('position', '');
            if ($("#divLvVideoPlayer").children('video').length) {
                //$(videoPlayer).children('video').css({ 'height': fullScreenHeight, 'max-height': fullScreenHeight });
                $("#divLvVideoPlayer").children('video').css({ 'width': '100%', 'height': fullScreenHeight, 'max-height': fullScreenHeight });
            }
            if ($("#divLvVideoPlayer").children('img').length)
                $("#divLvVideoPlayer").children('img').css({ 'height': fullScreenHeight, 'max-height': fullScreenHeight });
        }
    }
}

function getPlayerHeight() {
    var isIE = checkMSIEBrowser();
    var playerHeight;
    if (isIE) {
        playerHeight = 100; //$('#silverlightControlHost').height();
    }
    else
        playerHeight = $('#divFilePlayer').parent().parent().parent().height();
    if (playerHeight == NaN || playerHeight == null)
        playerHeight = 0;
    JL(frontendLogger).info('playerHeight - ', playerHeight);
    return playerHeight;
}
//#endregion

//#region Playlist Table

function redrawPlaylistTable() {
    if (playlistTable != null) {
        playlistTable.draw();
    }
}

//#endregion

function SetFilePlayerTitlesLocalization() {
    //alert('SetFilePlayerTitlesLocalization');
    $('#btnPlay').attr('title', langKeys[currentLanguage]['Play']);
    $('#btnBegin').attr('title', langKeys[currentLanguage]['Previous']);
    $('#btnRewind').attr('title', langKeys[currentLanguage]['Rewind']);
    $('#btnStop').attr('title', langKeys[currentLanguage]['Stop']);
    $('#btnFF').attr('title', langKeys[currentLanguage]['FastForward']);
    $('#btnNext').attr('title', langKeys[currentLanguage]['Next']);
    $('#btnVolumedown').attr('title', langKeys[currentLanguage]['Mute']);
    $('#btnVolumeup').attr('title', langKeys[currentLanguage]['MaximumVolume']);
    $('#btnslowplay').attr('title', langKeys[currentLanguage]['SlowSpeed']);
    $('#btnFastplay').attr('title', langKeys[currentLanguage]['FastSpeed']);
    $('#btnLooping').attr('title', langKeys[currentLanguage]['Release']);
    $('#btnSearchSaveCalls').attr('title', langKeys[currentLanguage]['SaveCalls']);
    $('#btnSearchSettings').attr('title', langKeys[currentLanguage]['Settings']);

    //$("input[id='chkSkipSilence']")[0].nextSibling.nodeValue = langKeys[currentLanguage]['SkipSilence'];
}

function swapVideoPlayerContents(mode) {
    //JL(frontendLogger).info('call swapVideoPlayerContents(' + mode + ')');
    if (boolIEBrowser) {
        switch (mode) {
            case "Search":
                if ($('#divSearchVideoPlayer').html() == '') {
                    $('#divSearchVideoPlayer').html($('#divLvVideoPlayer').html());
                    $('#divLvVideoPlayer').html('');

                    adjustQuadPlayer();
                }
                break;
            case "Listview":
                if ($('#divSearchVideoPlayer').html() != '') {
                    $('#divLvVideoPlayer').html($('#divSearchVideoPlayer').html());
                    $('#divSearchVideoPlayer').html('');

                    //$("#videoContainerSV").width(350).height(232);  //width:350px,height:232px
                    //$('#objLVPlayer').width(320).height(240);
                    $('#objLVPlayer').attr({ 'width': 320, 'height': 240 });
                    //$('#divSearchVideoPane #videoContainerSV object').attr({ 'width': '320', 'height': '240' });
                }
                configureListViewHeight();
                break;
        }
    } else {
        switch (mode) {
            case "Search":
                if ($('#divSearchVideoPlayer').html() == '') {
                    $('#divSearchVideoPlayer').html(searchareapanel);
                    $('#divLvVideoPlayer').html('');
                    var obj = document.getElementById('videofileplayer');
                    obj.style.left = '0px';
                    adjustQuadPlayer();
                }
                break;
            case "Listview":
                if ($('#divSearchVideoPlayer').html() != '') {
                    $('#divLvVideoPlayer').html($('#divSearchVideoPlayer').html());
                    searchareapanel = $('#divSearchVideoPlayer').html();
                    $('#divSearchVideoPlayer').html('');
                    $('#objLVPlayer').attr({ 'width': 320, 'height': 240 });

                    $('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });
                    //$('#videofileplayer').css({ 'width': '320px', 'height': '240px' });
                    //$('#videofileplayer').css({ 'width': '80%', 'height': '80%' });

                    var obj = document.getElementById('videofileplayer');
                    //obj.style.left = '600px';
                    //obj.style.left = '50%';
                }
                configureListViewHeight();
                break;
        }
    }
}

function removeContainersElementsTL() {
    try {
        for (k = 0; k < 4; k++) {
            //$('#divLvVideoPlayer' + k).html(''); // COMMENTED BY KM IFUTURZ - 27042018.
            $('#divLvVideoPlayer' + k + ' > video')[0].src = ''; // ADDED BY KM IFUTURZ - TO RESOLVE VIDEO PLAY ISSUE AFTER STOP AND PLAY - 27042018.
        }
    } catch (e) { }
}

//#region html5 video elements

var bVideoPanelLoaded = false;
var bDemoVideo = false;
function html5player_adjust(callid) {
    bVideoPanelLoaded = true;
    var height = $(window).height() - ($(window).height() * 0.3);
    var videoWidth = $(window).width() - ($(window).width() * 0.5);
    var videoHeight = $(window).height() - ($(window).height() * 0.5);
    // $('#divCloseVideo').removeClass('hide');
    //  $('#divVideoContainer').css({ 'height': height });
    //  $('#divVideoContent').css({ 'height': height });
    // $('#divSearchVideoPlayer').css({ 'height': $(window).height() - ($(window).height() * 0.4) });
    // $('#divSearchVideoPlayer').css({ 'height': '100%' });
    //  $('#divSearchVideoPane').css({ 'width': $('#callList').width(), 'height': $('#callList').height() });
    //$('#divSearchVideoPane').css({ 'width': $('#divCallRecords').width(), 'height': $('#divCallRecords').height() + 100 });
    //$('#divSearchVideoPane #videoContainerSV').css({ 'width': $('#divCallRecords').width(), 'height': $('#divCallRecords').height() + 100 });
    //$('#divSearchVideoPane #videoContainerSV #divSearchVideoPlayer').css({ 'width': $('#divCallRecords').width(), 'height': $('#divCallRecords').height() + 100 });
    ////$('#divSearchVideoPane #videoContainerSV object').attr({ 'width': $('#videoContainerSV').width() - 500, 'height': $('#videoContainerSV').height() }); //arivu Should remember km
    //var currentWidth = $('#videoContainerSV').width() - $('#videoContainerSV').width() * 60 / 100;
    //$('#divSearchVideoPane #videoContainerSV object').attr({ 'width': currentWidth, 'height': $('#videoContainerSV').height() + 100 }); //arivu Should remember KM

    //$('#divFileVideoPlayer').css({ 'width': currentWidth, 'height': $('#divCallRecords').height() - 20 });
    //$('#divSearchVideoPlayer').css({ 'width': currentWidth, 'height': $('#divCallRecords').height() - 20 });


    //$('#videofileplayer').css({ 'width': videoWidth, 'height': videoHeight });

    // $('#divSearchVideoPane').show();
}

function html5player_adjust_listview() {
    //$('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });
    //$('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });
    ////$('#videofileplayer').css({ 'width': '320px', 'height': '240px' });
    //$('#videofileplayer').css({ 'width': '100%', 'height': '100%' });
    calculateAndAdjustVPPosition();
}

function html5player_adjust_timelineview() {

    //KM divLvVideoPlayer_timeline

    if ($('#divSearchVideoPlayer').html() != '') {
        $('#divLvVideoPlayer_timeline').html($('#divSearchVideoPlayer').html());
        $('#divSearchVideoPlayer').html('');
        $('#objLVPlayer').attr({ 'width': 320, 'height': 240 });
    }

    $('#divFileVideoPlayer').css({ 'width': '320px', 'height': '240px' });

    $('#videofileplayer').css({ 'height': '100%' });
}

//#endregion

//#region Time functions
function timeFormate(s) {
    var fm = [
        Math.floor(s / 60 / 60) % 24, // HOURS
        Math.floor(s / 60) % 60, // MINUTES
        Math.floor(s) % 60 // SECONDS
    ];
    return $.map(fm, function (v, i) { return ((v < 10) ? '0' : '') + v; }).join(':');
}

function setTimelineIcon(cType, fileExt, isRevview, isVirtualInspection) {
    try {
        switch (cType) {
            case "1": //Audio
                switch (fileExt.toLowerCase()) {
                    case "dsf":
                        if (isRevview == true || isRevview == "true") {
                            iconCss = "callTypeSprite callType1_revview call-type-pl pull-left";
                        }
                        else {
                            iconCss = "callTypeSprite callType1 call-type-pl pull-left";
                        }
                        break;
                    case "mp3":
                        if (isUserItemIcon) {
                            iconCss = "callTypeSprite callType1 call-type-pl pull-left";
                        }
                        else {
                            iconCss = "callTypeSprite callType1 call-type-pl pull-left";
                        }
                        break;
                    case "wav":
                        iconCss = "callTypeSprite callType1 call-type-pl pull-left";
                        break;
                }
                break;
            case "2": //Video
                if (isUserItemIcon) {
                    iconCss = "callTypeSprite callType2 call-type-pl pull-left";
                } else {
                    iconCss = "callTypeSprite callType6 call-type-pl pull-left";
                }
                break;
            case "3": //SMS
                iconCss = "callTypeSprite callType3 call-type-pl pull-left";
                break;
            case "4": //Social
                iconCss = "callTypeSprite callType4 call-type-pl pull-left";
                break;
            case "5": //Email
                iconCss = "callTypeSprite callType5 call-type-pl pull-left";
                break;
            case "6": //Screens
                iconCss = "callTypeSprite callType6 call-type-pl pull-left";
                break;
            case "7": //Inquire
                if (fileExt.toLowerCase() == "mov" || fileExt.toLowerCase() == "mp4") {
                    if (isVirtualInspection == true || isVirtualInspection == "true") {
                        iconCss = "callTypeSprite callType8_virtual call-type-pl pull-left";
                    }
                    else {
                        iconCss = "callTypeSprite callType8 call-type-pl pull-left";
                    }
                }
                else if (fileExt.toLowerCase() == "zip") {
                    iconCss = "callTypeSprite callType12 call-type-pl pull-left";
                }
                else {
                    if (isVirtualInspection == true || isVirtualInspection == "true") {
                        iconCss = "callTypeSprite callType7_virtual call-type-pl pull-left";
                    }
                    else {
                        iconCss = "callTypeSprite callType7 call-type-pl pull-left";
                    }
                }
                break;
            case "8": //Inquire
                iconCss = "callTypeSprite callType7 call-type-pl pull-left";
                break;
            case "9": //User Added Image
                // durInSec = 60;
                iconCss = "callTypeSprite callType9 call-type-pl pull-left";
                break;
            case "10": //User Added Document
                iconCss = "callTypeSprite callType10 call-type-pl pull-left";
                break;
            case "11": //MD
                if (fileExt.toLowerCase() == "mov" || fileExt.toLowerCase() == "mp4") {
                    iconCss = "callTypeSprite callType11 call-type-pl pull-left";
                }
                break;
            case "13": //Teams
                iconCss = "callTypeSprite callType13 call-type-pl pull-left";
                break;
            default:
                break;
        }
    } catch (e) {
        JL(frontendLogger).info("EXCEPTION : setTimelineIcon");
        JL(frontendLogger).fatalException("Exception inside setTimelineIcon", e);
    }
    return iconCss;
}

function getStartTimeDate(starttime) {
    var startDateTime = starttime;
    var dateTimeString = vis.moment(startDateTime).format('YYYY-MM-DD');
    var dateString = startDateTime.substring(0, 8);
    //var dateToDisplay = dateString.substring(0, 4) + "-" + dateString.substring(4, 6) + "-" + dateString.substring(6, 8);
    var startTimeString = startDateTime.substring(8, 14);
    var hr = startTimeString.substring(0, 2);
    var mins = startTimeString.substring(2, 4);
    var secs = startTimeString.substring(4, 6);
    return dateTimeString + ' ' + hr + ':' + mins + ':' + secs
}

function totalDurationTimeline() {
    var totalTimelineDuration = 0;
    $("#tblPlaylist > tbody > tr").each(function () {
        var durInSec = $(this).attr('cDuration');
        totalTimelineDuration += durInSec * 1000;
        totalduration.html(msToTime(totalTimelineDuration));
    });
}

function formatStringToDatetime(dtString) {
    //20130314134436
    //JL(frontendLogger).info(dtString);
    var yr = dtString.substring(0, 4);
    var mon = dtString.substring(4, 6);
    var dy = dtString.substring(6, 8);
    var hr = dtString.substring(8, 10);
    var min = dtString.substring(10, 12);
    var sec = dtString.substring(12, 14);

    var formatedDate = mon + "-" + dy + "-" + yr + " " + hr + ":" + min + ":" + sec;
    //JL(frontendLogger).info(formatedDate);
    return formatedDate;
}

function formatSecondsAsTime(secs) {
    var hr = Math.floor(secs / 3600);
    var min = Math.floor((secs - (hr * 3600)) / 60);
    var sec = Math.floor(secs - (hr * 3600) - (min * 60));
    if (hr < 10) { hr = "0" + hr; }
    if (min < 10) { min = "0" + min; }
    if (sec < 10) { sec = "0" + sec; }
    if (hr) { hr = "00"; }
    //JL(frontendLogger).info(hr + ':' + min + ':' + sec);
    return hr + ':' + min + ':' + sec;
}

//#endregion

//#region Playlist Functions

function updateCustomFields(CustomFields) {
    //$("#tblcustomfields").empty();
    //var table = $("#tblcustomfields");
    //if (CustomFields == null || CustomFields.length == 0) {
    //    table.append($("<tr><td class='meta-label text-left'>" + "No data to display" + "</td></tr>"));
    //} else {
    //    for (i_val = 0; i_val < CustomFields.length ; i_val++) {
    //        table.append($("<tr><td class='meta-label text-left'>" + CustomFields[i_val].CustomFieldHeader + "</td><td class='meta-info-value text-left' style='width: 100px;'>" + CustomFields[i_val].CustomFieldData + "</td></tr>"));
    //    }
    //}

    $('#divPlMetadata #divCustomFields').empty();
    if (CustomFields == null || CustomFields.length == 0) {
        var customFieldHtml =
            '<div class="list-group-item msg-line" id="0">' +
            '<p class="list-group-item-text">' +
            '<strong class="msg-sender"></strong>No data to display</p>' +
            '</div>';
        $('#divPlMetadata #divCustomFields').append(customFieldHtml);
    }
    else {
        for (i_val = 0; i_val < CustomFields.length ; i_val++) {
            var customFieldHtml =
                '<div class="list-group-item msg-line" id="' + CustomFields[i_val].Id + '">' +
                '<p class="list-group-item-text">' +
                '<strong class="msg-sender">' + CustomFields[i_val].CustomFieldHeader + ':</strong> ' +
                CustomFields[i_val].CustomFieldData +
                '</p>' +
                '</div>';
            $('#divPlMetadata #divCustomFields').append(customFieldHtml);
        }
    }
}

function getPlaylistEventDetail(eventId, revviewEvent) {
    let isMDEvent = (eventId.indexOf("MD") >= 0 || eventId.indexOf("md") >= 0) ? true : false;
    if (eventId) {
        if (!revviewEvent) {
            $.ajax({
                url: handlersBaseURL + '/InquireHandlers/InquireRxHandler.ashx',
                data: { 'method': 'GetCaseDetails', 'eventId': eventId, isSharedPage: isSharedPage },
                type: 'POST',
            }).then(function (response) {

                $.ajax({
                    type: 'POST',
                    url: vrHandlerURL,
                    data: { 'method': 'GetChatTranscript', 'EventId': eventId, isSharedPage: isSharedPage },
                    cache: false
                }).then(function (response) {
                    var $selector = $('.inquire-case-info #pl-messages-list');
                    if (isMDEvent) $selector = $('.md-case-info #pl-messages-list');
                    if (revviewEvent) $selector = $('.revview-case-info #pl-messages-list');
                    $selector.html('');

                    if (response.data != null && response.data.length > 0) {
                        for (var i = 0; i < response.data.length; i++) {
                            var messages = JSON.parse(response.data[i].Transcript);
                            //JL(frontendLogger).info(messages);
                            for (var j = 0; j < messages.length; j++) {
                                //JL(frontendLogger).info(messages[i]);
                                buildAndDisplayMessage(messages[j], $selector)
                            }
                        }
                    }
                    discardDatePartFromChat($selector);
                }, function (jqXHR, textStatus, errorThrown) { // Failure
                    showExceptionDialog('Error occurred GetChatTranscript : ' + jqXHR.responseText);
                });

                var data = response.data;

                // Common for Both MD and IQ3
                $('.pl-case-no').text(data.Event.Id);

                if (data.Event.GPS == '' || data.Event.GPS == "N/A"  || data.Event.GPS.length == 0) {
                    $("#divPlMetadata").find('#trLocation').hide();
                }
                else {
                    $("#divPlMetadata").find('#trLocation').show();
                    $('.pl-case-location').text(data.Event.GPS);
                }

                $('.pl-case-date').text(data.Event.DateTime);
                $('.pl-case-qb-dialogid').text(data.QBDialogId);

                //updateCustomFields(data.CustomFields);
                UpdatePreInspectionData(data.Inspection.PreInspectionDataList, 'divPlMetadata #divCustomFields');

                if (data.Inspection.Sections != null && data.Inspection.Sections.length > 0) {
                    populateInquireInspectionBookmarkData(data.Inspection.Sections, 'divBookmarks');
                }

                if (isMDEvent) {
                    if (data.Patient) {
                        $('.pl-case-patient-name').text(data.Patient.FullName);
                        $('.pl-case-patient-height').text(data.Patient.Height);
                        $('.pl-case-patient-weight').text(data.Patient.Weight);
                        if (data.Patient.DOB != null)
                            $('.pl-case-patient-dob').text(parseDate(data.Patient.DOB, true));
                        $('.pl-case-patient-gender').text(data.Patient.GenderString);
                    }
                    $('.pl-case-complaint').text(data.PrimaryComplaint);

                    $.each(data.PatientVitals, function (key, val) {
                        var rowHtml = '<tr><td class="meta-label text-left">' + val.VitalSign.Name + ':</td><td class="meta-info-value text-left"><span class="">' + val.Reading + '</span></td></tr>';
                        $(".pl-case-vitals-info table tbody").append(rowHtml);
                    });
                    var drugNames = '', drugImages = '', medNames = '', medImages = '', eegNames = '', eegImages = '';
                    if (data.Drugs) {
                        drugNames = $.map(data.Drugs, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.DrugImages) {
                        drugImages = $.map(data.DrugImages, function (v, i) {
                            return v;
                        }).join(",");
                    }
                    if (data.Medications) {
                        medNames = $.map(data.Medications, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.MedicationImages) {
                        medImages = $.map(data.MedicationImages, function (v, i) {
                            return v;
                        }).join(",");
                    }
                    if (data.EEGs) {
                        eegNames = $.map(data.EEGs, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.EEGImages) {
                        eegImages = $.map(data.EEGImages, function (v, i) {
                            return v;
                        }).join(",");
                    }

                    var dateString = data.CreatedDate.substr(6);
                    var currentTime = new Date(parseInt(dateString));

                    var month = ("0" + (currentTime.getMonth() + 1)).slice(-2);
                    var day = ("0" + currentTime.getDate()).slice(-2);
                    var year = currentTime.getFullYear();
                    var date = year + month + day;

                    var medRow = '<tr><td class="meta-label text-left">Medications:</td><td class="meta-info-value text-left"><span class="case-medications"><a id="lnkVitalMedications" med-names="' + medNames + '" med-images="' + medImages + '" date="' + date + '" href="javascript:void(0);" style="text-decoration: underline;" class="" lkey="">view</a></span></td></tr>';
                    var eegRow = '<tr><td class="meta-label text-left">EEG:</td><td class="meta-info-value text-left"><span class="case-EEGs"><a id="lnkVitalEEG" eeg-names="' + eegNames + '" eeg-images="' + eegImages + '" date="' + date + '" href="javascript:void(0);" style="text-decoration: underline;" class="" lkey="">view</a></span></td></tr>';
                    $('.pl-case-vitals-info > table > tbody').append(medRow);
                    $('.pl-case-vitals-info > table > tbody').append(eegRow);
                }
                else {
                    $('.pl-case-Interviewer').text(data.Event.Interviewer);
                    $('.pl-case-Interviewee').text(data.Event.Interviewee);

                    if (data.Event.PrimaryArea == '' || data.Event.PrimaryArea == "N/A" || data.Event.PrimaryArea.length == 0) {
                        $("#divPlMetadata").find('#trType').hide();
                    }
                    else {
                        $("#divPlMetadata").find('#trType').show();
                        $('.pl-case-Type').text(data.Event.PrimaryArea);
                    }

                    $('.pl-case-Notes').text(data.Event.Notes);
                    if (data.Event.Notes.trim() == "" || data.Event.Notes == null || data.Event.Notes == "(null)" || data.Event.Notes == "null") $('.pl-case-Notes').text("N/A");
                    //valusetfoeeventnotes(data.Event.Notes);
                }
            });
        }
        else {

            $('.ID-CallID').text(revviewEvent.CallId);
            $('.ID-CallerID').text(revviewEvent.RevViewPhoneNumber);
            $('.ID-StartTime').text(moment("" + revviewEvent.StartTimeString, "YYYYMMDDhhmmss").format("MM/DD/YYYY hh:mm:ss"));
            $('.ID-Ext').text(revviewEvent.ChannelId);
            $('.ID-ExtName').text(revviewEvent.ChannelName.replace('+', ' '));
            $('.ID-AgentName').text(revviewEvent.RevViewAgentName);
        }

        //$.ajax({
        //    type: 'POST',
        //    url: vrHandlerURL,
        //    data: { 'method': 'GetChatTranscript', 'EventId': eventId, isSharedPage: isSharedPage },
        //    cache: false
        //}).then(function (response) {
        //    var $selector = $('.inquire-case-info #pl-messages-list');
        //    if (isMDEvent) $selector = $('.md-case-info #pl-messages-list');
        //    if (revviewEvent) $selector = $('.revview-case-info #pl-messages-list');
        //    $selector.html('');
        //    var messages = JSON.parse(response.data.Transcript);
        //    for (var i = 0; i < messages.length; i++) buildAndDisplayMessage(messages[i], $selector)
        //    discardDatePartFromChat($selector);
        //}, function (jqXHR, textStatus, errorThrown) { // Failure
        //    showExceptionDialog('Error occurred getPlaylistEventDetail(): ' + jqXHR.responseText);
        //});
    }
}

//#endregion

//#region Time Functions

function toTimeString(seconds) {
    return (new Date(seconds * 1000)).toUTCString().match(/(\d\d:\d\d:\d\d)/)[0];
}

function buildAndDisplayMessage(message, $selector) {
    //JL(frontendLogger).info('inside buildAndDisplayMessage(message)');
    var isSystemNotification = false;
    if (message.Body)
        isSystemNotification = message.Body.substring(0, 4) === SNNotation ? true : false;
    if (isSystemNotification) {
        JL(frontendLogger).info('A system message.');
    }
    else {
        JL(frontendLogger).info('Not a system message.');
        var senderName = getNameByEmailId(message.From) !== "" ? getNameByEmailId(message.From) : message.From;
        var messageHtml = buildChatTranscriptHTML(message.Body, senderName, message.DateCreated, message.Index);
        //$('#messages-list').prepend(messageHtml);
        //$('#messages-list').append(messageHtml);
        $selector.append(messageHtml);
        //$('#messages-list').scrollTop($('#messages-list').prop('scrollHeight'));
        //JL(frontendLogger).info('senderName = ' + senderName);
        //JL(frontendLogger).info('messageHtml = ' + messageHtml);
    }
}

//#endregion

//#region Full Screen Functions

function fullScreenEventHandlers() {
    var bIEBrowser = checkMSIEBrowser();
    if (!bIEBrowser) {
        $("#silverlightControlHost").css('z-index', '0');
    }
    $(document).off('dblclick', '#divLvVideoPlayer0').on('dblclick', '#divLvVideoPlayer0', function (e) {
        if ($(this).children("video").length) {
            var vPlayer = $(this).children("video");
            //setFullScreen(vPlayer[0]);
            var vidName = vPlayer.attr('src').split('/').pop();
            toggleFullScreen(vidName);
            //toggleFullScreen(vidName, $(this));
        }
        else
            JL(frontendLogger).info('No video element found inside #divLvVideoPlayer0');
    });
    $(document).off('dblclick', '#divLvVideoPlayer1').on('dblclick', '#divLvVideoPlayer1', function (e) {
        if ($(this).children("video").length) {
            var vPlayer = $(this).children("video");
            //setFullScreen(vPlayer[0]);
            var vidName = vPlayer.attr('src').split('/').pop();
            toggleFullScreen(vidName);
        }
        else
            JL(frontendLogger).info('No video element found inside #divLvVideoPlayer1');
    });
    $(document).off('dblclick', '#divLvVideoPlayer2').on('dblclick', '#divLvVideoPlayer2', function (e) {
        if ($(this).children("video").length) {
            var vPlayer = $(this).children("video");
            //setFullScreen(vPlayer[0]);
            var vidName = vPlayer.attr('src').split('/').pop();
            toggleFullScreen(vidName);
        }
        else
            JL(frontendLogger).info('No video element found inside #divLvVideoPlayer2');
    });

    $(document).off('dblclick', '#divLvVideoPlayer3').on('dblclick', '#divLvVideoPlayer3', function (e) {
        if ($(this).children("video").length) {
            var vPlayer = $(this).children("video");
            //setFullScreen(vPlayer[0]);
            var vidName = vPlayer.attr('src').split('/').pop();
            toggleFullScreen(vidName);
        }
        else
            JL(frontendLogger).info('No video element found inside #divLvVideoPlayer3');
    });

    $(document).off('dblclick', '#videofileplayer').on('dblclick', '#videofileplayer', function (e) {
        var vPlayer = $(this);
        var existingHeight = $(this).css('height');
        var existingWidth = $(this).css('width');
        //setFullScreen(vPlayer[0]);
        var vidName = getParameterByName('file', vPlayer.attr('src'));
        JL(frontendLogger).info('vidName:' + vidName);
        if (vidName) {
            toggleFullScreen(vidName);
            //toggleFullScreen(vidName, $('#divLvVideoPlayer'));
        }
        ////$('#videofileplayer').on('mozfullscreenchange webkitfullscreenchange fullscreenchange', function () {
        //$(document).on('mozfullscreenchange webkitfullscreenchange fullscreenchange', function () {
        //    this.fullScreenMode = !this.fullScreenMode;
        //    //JL(frontendLogger).info('fullScreenMode: ' + this.fullScreenMode);
        //    if (!this.fullScreenMode) {
        //        setTimeout(function () {
        //            $('#videofileplayer').css('height', existingHeight);
        //            $('#videofileplayer').css('width', existingWidth);
        //        }, 100);
        //    }
        //});
    });
}

function setFullScreen(vPlayer) {
    try {
        if (vPlayer.requestFullscreen) {
            vPlayer.requestFullscreen();
        } else if (vPlayer.mozRequestFullScreen) {
            vPlayer.mozRequestFullScreen();
        } else if (vPlayer.webkitRequestFullscreen) {
            vPlayer.webkitRequestFullscreen();
        }
    } catch (e) {
        JL(frontendLogger).fatalException("Exception inside setFullScreen: ", e);
    }
}

function toggleFullScreen(vidName) {
    var hfVal = $('#hdnActiveTab').val();

    var fullscreenElement = document.fullscreenElement || document.mozFullScreenElement ||
        document.webkitFullscreenElement || document.msFullscreenElement;
    if (fullscreenElement) {
        exitFullscreen();
    }
    else {
        if (hfVal == 'Timeline') {
            launchFullscreen(document.getElementById('container'));
            //$('#divFsMetadata').removeClass('hide');
            $('#divTimeline').css({ 'height': '100%' });
            $('#divLvVideoPlayer0').css({ 'height': '100%', 'width': '100%' });
            //$('#divTimeline .videoPanel > video').parent().css({ 'height': '100%', 'width': '100%' });
            //$vidContainer.css({ 'height': '100%', 'width': '100%' });
            loadEventMetadataByName(vidName);
        }
        /*else if (hfVal == 'Listview') {
            launchFullscreen(document.getElementById('container'));
            //$('#divFsMetadata').removeClass('hide');
            $('#divListViewTable').addClass('hide');
            $('#divListview').css({ 'height': '100%' });
            $('#divLvVideoPlayer').css({ 'height': '100%', 'width': '100%' });
            //$('#videofileplayer').css({ 'height': '100%', 'width': '100%' });
            $('.metadata-RHS').css({ 'z-index': '1' });
            loadEventMetadataByName(vidName);
            setTimeout(function () {
                $('#videofileplayer').css({ 'height': '100%', 'width': '100%' });
            }, 1000);

        }*/
    }
}

function launchFullscreen(element) {
    if (element.requestFullscreen) {
        //$('#divFsMetadata').removeClass('hide');
        $('#div-toggle-metadata').removeClass('hide');
        element.requestFullscreen();
    } else if (element.mozRequestFullScreen) {
        //$('#divFsMetadata').removeClass('hide');
        $('#div-toggle-metadata').removeClass('hide');
        element.mozRequestFullScreen();
    } else if (element.webkitRequestFullscreen) {
        //$('#divFsMetadata').removeClass('hide');
        $('#div-toggle-metadata').removeClass('hide');
        element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) {
        //$('#divFsMetadata').removeClass('hide');
        $('#div-toggle-metadata').removeClass('hide');
        element.msRequestFullscreen();
    } else {
        JL(frontendLogger).info("Fullscreen Unavailable");
    }
}

// Whack fullscreen
function exitFullscreen() {
    var hfVal = $('#hdnActiveTab').val();

    if (isFullScreen()) {

        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
        else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        }
        else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        }
    }
}

if (document.addEventListener) {
    //document.addEventListener('webkitfullscreenchange', exitHandler, false);
    //document.addEventListener('mozfullscreenchange', exitHandler, false);
    //document.addEventListener('fullscreenchange', exitHandler, false);
    //document.addEventListener('MSFullscreenChange', exitHandler, false);
    document.addEventListener('webkitfullscreenchange', exitHandler);
    document.addEventListener('mozfullscreenchange', exitHandler);
    document.addEventListener('fullscreenchange', exitHandler);
    document.addEventListener('MSFullscreenChange', exitHandler);
}

function exitHandler() {
    var hfVal = $('#hdnActiveTab').val();
    if (!isFullScreen()) {

        if (currentloadedscreen == "Timeline") {
            configureTimelineViewHeight();
        }
        else if (currentloadedscreen == "ListView") {
            //$("#videofileplayer").css('position', 'absolute');
        }

        $(".ui-new-dialog-content").mmsDialog("close");
    }
}

//#endregion

//#region

function reOrderzIndex(mode) {
    //JL(frontendLogger).info(mode);
    switch (mode) {
        case "SLDialogOpen": //on Search View Video Open
            $("#silverlightControlHost").css('z-index', '99');
            break;
        case "SLDialogClose": //.css({'z-index' : '100'}) //style.zIndex
            if ($("#divSearchVideoPane").css('z-index') == '4')
                $("#silverlightControlHost").css('z-index', '3');
            else
                $("#silverlightControlHost").css('z-index', '1');
            break;
        case "searchVideoView": //on Search View Video Open
            if (boolIEBrowser) {
                $("#divSearchVideoPane").css('z-index', '4');
                $("#silverlightControlHost").css('z-index', '3');
                searchVideoPlayerVisible('block');// $("#videoContainerSV").css('display', 'block');
                break;
            } else {
                $("#divSearchVideoPane").css('z-index', '4');
                searchVideoPlayerVisible('block');// $("#videoContainerSV").css('display', 'block');
                break;
            }
        case "searchNonVideoView":
            if (boolIEBrowser) {
                $("#divSearchVideoPane").css('z-index', '-1');
                $("#silverlightControlHost").css('z-index', '1');
                searchVideoPlayerVisible('none'); //$("#videoContainerSV").css('display', 'none');
                break;
            } else {
                $("#divSearchVideoPane").css('z-index', '-1');
                searchVideoPlayerVisible('none'); //$("#videoContainerSV").css('display', 'none');
                break;
            }
        //case "searchNormalView":      
        //    $("#divSearchVideoPane").css('z-index', '-1');    
        //    $("#silverlightControlHost").css('z-index', '1');    
        //    break;    
    }
}

function searchVideoPlayerVisible(display) {
    $("#videoContainerSV").css('display', display);
}
//#endregion


//#region Playlist Dialog
function playlistDialog() {
    debugger;
    $("#divPlaylist").mmsDialog({
        autoOpen: false,
        modal: true,
        resizable: true,
        title: 'Add New Repository',
        closeText: langKeys[currentLanguage]['ttClose'],
        open: function () {
            $(this).mmsDialog("option", "title", $(this).attr('plID') == 0 ? langKeys[currentLanguage]['msgDlgTitleCreatePl'] : langKeys[currentLanguage]['msgDlgTitleEditPl']); $('#lblPlErrorMsg').html('');
            $('#selectAllRepoCheckbox').prop('checked', false);
            $('#tPlaylist tbody .row-checkbox').prop('checked', false);
        },
        show: { effect: 'fade', complete: function () { $(this).find("#txtPlaylistName").focus(); } },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnSave'],
                click: function () {
                    if ($.trim($('#txtPlaylistName').val()) != "") {
                        //$.ajax(options);
                        $.blockUI();
                        if (validateIsSamePlName($(this).attr('plID'), $('#txtPlaylistName').val())) {
                            saveplaylist();
                        }
                        else {
                            showErrorDialog(langKeys[currentLanguage]['emsgSameNamePl']);
                            $.unblockUI();
                            return;
                        }
                        $(this).removeAttr('RevSyncServerID');
                        $(this).removeAttr('plID').mmsDialog("close");
                        $('#txtPlaylistName').val('');
                        $('#txtPlComments').val('');
                        $('#lblPlErrorMsg').html('');
                    }
                    else
                        $('#lblPlErrorMsg').html(langKeys[currentLanguage]['emsgReqFieldsMissing']).show();
                }
            }, {
                text: langKeys[currentLanguage]['msgBtnCancel'],
                click: function () {
                    $('#lblPlErrorMsg').html('');
                    // $(this).dialog("close");$("#txtPlaylistName").val('');
                    $(this).removeAttr('RevSyncServerID');
                    $(this).removeAttr('plID').mmsDialog("close");
                    $('#txtPlaylistName').val('');
                    $('#txtPlComments').val('');
                }
            }
        ]
    });
    //$("#divPlaylist").parent().appendTo($("form:first"));
}

$(document).on('change', '#selectAllRepoCheckbox', function () {
    var isChecked = $(this).is(':checked');
    console.log("Select All toggled. Is checked?", isChecked);
    var checkboxes =  $('#tPlaylist tbody .row-checkbox').prop('checked', isChecked);
    console.log("Checkboxes found: ", checkboxes.length);
});

$(document).on('change', '#tPlaylist .row-checkbox', function () {
    let total = $('#tPlaylist .row-checkbox').length;
    let checked = $('#tPlaylist .row-checkbox:checked').length;
    $('#selectAllRepoCheckbox').prop('checked', total === checked);
});

function exportDialog() {
    $("#divExportData").mmsDialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['msgDlgTitleExportExcel'],
        //show: { effect: 'fade', complete: function () { $(this).find("#txtPlaylistName").focus(); } },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnExport'],
                click: function () {
                    var radioGrp = $('input:radio[name="exportCall"]:checked');
                    if (radioGrp.length == 0) {
                        $('#lblExportCallsErrorMsg').html(langKeys[currentLanguage]['emsgSelectExportOption']).show();
                        return;
                    }
                    var totalRows = $('#tblCalls tbody tr').length;
                    //if (totalRows <= 1)
                    if (totalRows < 1) {
                        $('#lblExportCallsErrorMsg').html(langKeys[currentLanguage]['emsgNoRecord']).show();
                        return;
                    }
                    ////alert(radioGrp.val());

                    //exportDataCalls();
                    ////alert(getSearchCriteria(1));
                    if (radioGrp.val() == 'All') {
                        //if (isECEnabled && $('#header-login-name').attr('utype') == 1 && $('#header-login-name').attr('ugrp') == 1000)
                        if (bUserSearchLoaded) {
                            // No need to search if EC is enabled or not. Situation is handeled in server code. 
                            window.location = exportHandlerURL + "?exportOperation=exportUserSearchResults" + "&userSearchIndex=" + userSearchIndex + "&exportType=" + radioGrp.val() + "&data=";
                        }
                        else {
                            if (isECEnabled)
                                window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&jsonstr=" + populateSearchCriteriaEC(1) + "&exportType=" + radioGrp.val();
                            else
                                window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&jsonstr=" + populateSearchCriteria(1) + "&exportType=" + radioGrp.val();
                        }
                        $(this).mmsDialog("close");
                        return;
                    }
                    /*if (radioGrp.val() == 'Current') {
                        //var page = $('#tblCalls')[0]['p']['newp'];var rowsPerPage = $('#tblCalls')[0]['p']['rp'];var query = $('#tblCalls')[0]['p']['query'];var qtype = $('#tblCalls')[0]['p']['qtype'];
                        var currentPageNo = (callsTable != null) ? callsTable.page() + 1 : -1; //$('#tblCalls')[0]['p']['newp'];
                        //JL(frontendLogger).info(currentPageNo);
                        //if (isECEnabled && $('#header-login-name').attr('utype') == 1 && $('#header-login-name').attr('ugrp') == 1000)
                        if (bUserSearchLoaded) {
                            window.location = exportHandlerURL + "?exportOperation=exportUserSearchResults" + "&userSearchIndex=" + userSearchIndex + "&exportType=" + radioGrp.val() + "&currentPageNumber=" + currentPageNo + "&data=";
                        }
                        else {
                            if (isECEnabled)
                                window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&jsonstr=" + populateSearchCriteriaEC(currentPageNo) + "&exportType=" + radioGrp.val();
                            else
                                window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&jsonstr=" + populateSearchCriteria(currentPageNo) + "&exportType=" + radioGrp.val();
                        }
                        $(this).mmsDialog("close");
                        return;
                    }*/
                    var callToExport = exportDataCalls(radioGrp.val());
                    //if (callToExport.length > 0)
                    if (callToExport.length) {
                        $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
                        ////window.location = "../Handlers/VoiceRecHandlers/ExportToExcelHandler.ashx?data=" + exportDataCalls(radioGrp.val());
                        //window.location = handlersBaseURL + '/VoiceRecHandlers/ExportToExcelHandler.ashx' + "?data=" + callToExport;
                        //window.location = handlersBaseURL + '/VoiceRecHandlers/ExportToExcelHandler.ashx' + "?startDate=" + $('#txtDateStart').val() + "&endDate=" + $('#txtDateEnd').val() + "&data=" + callToExport;
                        //window.location = handlersBaseURL + '/VoiceRecHandlers/ExportToExcelHandler.ashx' + "?jsonstr=" + getSearchCriteria(1) + "&data=" + callToExport;
                        JL(frontendLogger).info('Before Ajax Call.');
                        JL(frontendLogger).info(new Date());
                        var sDate = '';
                        var eDate = '';
                        if (radioGrp.val() == 'Current') {
                            sDate = $('#tblCalls tbody tr input.tblrowChk:checkbox:first').parent().parent().parent('tr').attr('sttime');
                            eDate = $('#tblCalls tbody tr input.tblrowChk:checkbox:last').parent().parent().parent('tr').attr('sttime');
                        }
                        else {
                            sDate = $('#tblCalls tbody tr input.tblrowChk:checkbox:checked:first').parent().parent().parent('tr').attr('sttime');
                            eDate = $('#tblCalls tbody tr input.tblrowChk:checkbox:checked:last').parent().parent().parent('tr').attr('sttime');
                        }
                        $.ajax({
                            type: 'POST',
                            url: vrHandlerURL,
                            data: { method: 'SetCallIdsForExportData', CallIds: callToExport, StartDate: sDate, EndDate: eDate, isSharedPage: isSharedPage },
                            cache: false,
                            success: function (response) {
                                JL(frontendLogger).info('Success : Ajax Call.');
                                JL(frontendLogger).info(new Date());
                                switch (response.success) {
                                    case true:
                                        $.unblockUI();
                                        window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&data=Revcord";
                                        break;
                                    default:
                                        $.unblockUI();
                                        showErrorDialog(response.message);
                                        break;
                                }
                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                                $.unblockUI();
                                showExceptionDialog('An error has occurred updateRowContents(): ' + jqXHR.responseText);
                            }
                        });
                        //window.location = exportHandlerURL + "?exportOperation=exportCalls" + "&data=" + callToExport;
                        $.unblockUI();
                        $(this).mmsDialog("close");
                    } else {
                        showErrorDialog('No calls selected.Please select calls to export');
                    }
                    //ieAddPlaceHolders();
                }
            }, {
                text: langKeys[currentLanguage]['msgBtnCancel'],
                click: function () {
                    $('#lblExportCallsErrorMsg').html('');
                    //$('#divExportData').find("input:radio:checked").removeProp('checked');
                    //$('#divExportData').find('input[type=radio]:first').attr('checked', 'checked');
                    $('#divExportData input:radio:first').attr('checked', 'checked');
                    $(this).mmsDialog("close");
                }
            }]
    });
    //$("#divExportData").parent().appendTo($("form:first"));
}
//#endregion

//#region Attached Files
function addItemInAttachedFiles(item) {
    JL(frontendLogger).info('preventing user added item from adding to playlist in Timeline View');

    var recid = item.recid;
    var callId = item.CallId;
    var flName = item.flName;
    var startTime = item.startTime;
    var fileExt = item.fileExt;
    var durInSec = item.durInSec;
    var cType = item.cType;
    var tag3 = item.tag3;
    var ani_ph = item.ani_ph;
    var chNum = item.chNum;
    var chName = item.chName;
    var screenName = item.screenName;
    var pages = item.pages;
    var isUserItem = item.isUserItem;
    var msgBody = item.msgBody;        //// Added by ifuturz for Message,Email and Social
    var datea = startTime.substring(0, 4) + '-' + startTime.substring(4, 6) + '-' + startTime.substring(6, 8) + ' ' + startTime.substring(8, 10) + ':' + startTime.substring(10, 12) + ':' + startTime.substring(12, 14);
    var IsRevcell = item.IsRevcell;
    var datea_Stoptime = getEndDate(datea, durInSec * 1000);

    var $trHtml = $('<li>');
    $trHtml.append($('<div class="row" style="margin-left: 0px; margin-right: 0px;" callId="' + callId + '" startTime="' + startTime + '" cType="' + cType + '" flName="' + flName + '" durInSec="' + durInSec + '" pages="' + pages + '">' + //' + +'"
        '<div class="col-xs-10" style="padding-left: 0px; padding-right: 0px;" onclick="showAttachedPlaylistItem(this);">' +
        '<div style="display: flex; width: 100%; line-height: 1.8em;"><i class="fa ' + getAttachFilesIcon(fileExt) + '" style="font-size: 1.8em; color: #2f94d8;">&nbsp;</i><span title="' + flName + '" style="font-weight: 600; overflow: hidden; text-overflow: ellipsis;">' + flName + '</span></div>' +
        '</div>' +
        '<div class="col-xs-2 text-right" style="padding-right: 0px;">' +
        '<a id="lnkRemovePlAttachedFile" href="#" class="btn btn-danger btn-xs remove-tr"><i class="fa fa-times" style="margin: 0"></i></a>' +
        '</div>' +
        '</div>'));

    $("#ulPlaylistUserAddedFiles").append($trHtml);
}

//function addItemInExportReportFiles($row) {

//    var recid = $($row).attr('recid');
//    var callId = $($row).attr('callId');
//    var flName = $($row).attr('flName');
//    var startTime = $($row).attr('sttime');
//    var videoFileName = $($row).attr('fname');
//    var vesselId = $($row).attr('vesselId');
//    if (vesselId == undefined || vesselId == "undefined")
//        vesselId = "";
//    //debugger

//    var $trHtml = $('<li>');
//    $trHtml.append($('<div class="row" style="margin-left: 0px; margin-right: 0px;" callId="' + callId + '" vesselId="' + vesselId + '" startTime="' + startTime + '">' + 
//        '<div class="col-xs-8" style="padding-left: 0px; padding-right: 0px;" onclick="showAttachedPlaylistItem(this);">' +
//        '<div style="display: flex; width: 100%; line-height: 1.8em;"><i class="fa fa-ship" style="font-size: 1.8em; color: #2f94d8;">&nbsp;</i><a id="lnkViewIQ3Report" callId="' + callId + '" vesselId="' + vesselId + '" startTime="' + startTime + '" title="'
//        //+ callId + ' - '
//        + (vesselId.length > 0 ? callId + ' View Vessel Report' : callId + ' View Standard Report') + '" style="font-weight: 600; cursor: pointer; overflow: hidden; text-overflow: ellipsis;">' + (vesselId.length > 0 ? callId + ' View Vessel Report' : callId + ' View Standard Report') + '</a></div>' +
//        '</div>' +
//        '<div class="col-xs-2 text-right" style="padding-right: 0px;">' +
//        '<a id="lnkDownloadPdfReport" title="' + (vesselId.length > 0 ? callId + ' Download Pdf Report' : callId + ' Download Pdf Report') + '" href="#" callId="' + callId + '" starttime="' + startTime + '" vesselId="' + vesselId + '" videoFileName="' + videoFileName + '" class="btn btn-primary btn-xs"><i class="fa fa-download" style="margin: 0"></i></a>' +
//        '</div>' +
//        '<div class="col-xs-2 text-right" style="padding-right: 0px;">' +
//        '<a id="lnkDownloadIQ3Report" title="' + (vesselId.length > 0 ? callId + ' Download Vessel Report' : callId + ' Download Standard Report') + '" href="#" callId="' + callId + '" starttime="' + startTime + '" vesselId="' + vesselId + '" videoFileName="' + videoFileName + '" class="btn btn-primary btn-xs"><i class="fa fa-download" style="margin: 0"></i></a>' +
//        '</div>' +
//        '</div>'));

//    $("#ulPlaylistExportReportFiles").append($trHtml);
//}

function showAttachedPlaylistItem($row) {
    $div = $($row).parent(); //$($row).closest('div');

    let startTime = $($div).attr('startTime');
    let cType = $($div).attr('cType');
    let flName = $($div).attr('flName');
    let durInSec = $($div).attr('durInSec');
    let pages = $($div).attr('pages');
    let ext = '';
    if (flName)
        ext = flName.lastIndexOf('.') > -1 ? flName.substr(flName.lastIndexOf('.') + 1) : "";
    ext = ext.toLowerCase();

    let vod = "";
    let dialogTitle = "Attached File Viewer";

    switch (cType) {
        case "2":
            vod = createDocumentURI(3, startTime, flName, durInSec, 0);
            break;
        case "9":
            vod = createDocumentURI(9, startTime, flName, durInSec, pages);
            JL(frontendLogger).info('createDocumentURI for Image  :: ' + vod);
            break;
        case "10": //User Added Document
            vod = createDocumentURITimeline(flName);
            JL(frontendLogger).info('createDocumentURI for User Added Document  :: ' + vod);
            break;
    }

    let height = $(window).height() / 2;
    let width = $(window).width() / 2;

    let html = null;

    if (ext == "dsf" || ext == "mp4" || ext == "mov" || ext == "mp3" || ext == "m4a" || ext == "wav") {
        html = $("<div></div>", { style: "height: " + height + "px; width: " + width + "px;" });
        html.prepend('<video id="videoscreen" src="' + vod + '" style="height: 100%; width: 100%;" />');
    }
    else if (ext == "doc" || ext == "docx" || ext == "pdf" || ext == "ppt" || ext == "xls" || ext == "xlsx") { //ext == "ppt" || ext == "xls" || ext == "xlsx"

        if (ext == "pdf") {
            height = $(window).height() * 0.6;
            width = $(window).width() * 0.72;
            html = $("<div></div>", { style: "height: " + height + "px; width: " + width + "px;" });
            if (PDFObject.supportsPDFs) {
                PDFObject.embed(getDocumentPDFUrl(vod) + "#toolbar=0&navpanes=0&scrollbar=0", html);
            } else {
                html.prepend('<object data="' + getDocumentPDFUrl(vod) + '#toolbar=0&navpanes=0&scrollbar=0" style="height: 100%; width: 100%;"></object>');
            }
        }
        else {
            try {
                ws.send("GetDocument#" + vod);
            }
            catch (e) {
                JL(frontendLogger).fatalException("Exception inside showAttachedPlaylistItem: GetDocument", e);
            }
        }
    }
    else if (ext == "jpg" || ext == "png" || ext == "jpeg" || ext == "gif") {
        html = $("<div></div>", { style: "height: " + height + "px; width: " + width + "px;" });
        html.prepend('<img src="' + vod + '" style="width: 100%; height: 100%;" />');
    }

    //showExceptionDialog(html);
    if (html != null && html != "") {
        if ($(html).height() != height) {
            $(html).height(height);
        }

        showPlaylistFileViewerDialog(dialogTitle, html);
    }
}

function showAttachedPaperClipItem($row) {
    $div = $($row).parent(); //$($row).closest('div');

    let flName = $($div).attr('flName');
    let ext = flName.lastIndexOf('.') > -1 ? flName.substr(flName.lastIndexOf('.') + 1) : "";
    ext = ext.toLowerCase();

    let vod = "";
    let dialogTitle = "Attached File Viewer";

    vod = createPaperClipURI(flName);

    let height = $(window).height() / 2;
    let width = $(window).width() / 2;

    let html = null;

    if (ext == "dsf" || ext == "mp4" || ext == "mov" || ext == "mp3" || ext == "m4a" || ext == "wav") {
        html = $("<div></div>", { style: "height: " + height + "px; width: " + width + "px;" });
        html.prepend('<video id="videoscreen" src="' + vod + '" style="height: 100%; width: 100%;" />');
    }
    else if (ext == "doc" || ext == "docx" || ext == "pdf" || ext == "ppt" || ext == "xls" || ext == "xlsx") { //ext == "ppt" || ext == "xls" || ext == "xlsx"

        if (ext == "pdf") {
            height = $(window).height() * 0.6;
            width = $(window).width() * 0.72;
            html = $("<div></div>", { style: "height: " + height + "px; width: " + width + "px;" });
            if (PDFObject.supportsPDFs) {
                PDFObject.embed(getDocumentPDFUrl(vod) + "#toolbar=0&navpanes=0&scrollbar=0", html);
            } else {
                html.prepend('<object data="' + getDocumentPDFUrl(vod) + '#toolbar=0&navpanes=0&scrollbar=0" style="height: 100%; width: 100%;"></object>');
            }
        }
        else {
            try {
                ws.send("GetDocument#" + vod);
            }
            catch (e) {
                JL(frontendLogger).fatalException("Exception inside showAttachedPlaylistItem: GetDocument", e);
            }
        }
    }
    else if (ext == "jpg" || ext == "png" || ext == "jpeg" || ext == "gif") {
        html = $("<div></div>", { style: "height: " + height + "px; width: " + width + "px;" });
        html.prepend('<img src="' + vod + '" style="width: 100%; height: 100%;" />');
    }

    //showExceptionDialog(html);
    if (html != null && html != "") {
        if ($(html).height() != height) {
            $(html).height(height);
        }

        showPlaylistFileViewerDialog(dialogTitle, html);
    }
}

function ShowDocumentPDF(vod) {
    try {
        if ($("#divattachedfiless").mmsDialog('isOpen') && currentloadedscreen == "Timeline") {
            var dialogTitle = "Attached Document Viewer";
            var height = $(window).height() * 0.6;
            var width = $(window).width() * 0.72;
            var html = $("<div></div>", { style: "height: " + height + "px; width: " + width + "px;" });
            if (PDFObject.supportsPDFs) {
                PDFObject.embed(getDocumentPDFUrl(vod) + "#toolbar=0&navpanes=0&scrollbar=0", html);
            } else {
                html.prepend('<object data="' + getDocumentPDFUrl(vod) + '#toolbar=0&navpanes=0&scrollbar=0" style="height: 100%; width: 100%;"></object>');
            }

            if (html != null && html != "") {
                if ($(html).height() != height) {
                    $(html).height(height);
                }

                showPlaylistFileViewerDialog(dialogTitle, html, height, width);
            }
        }
    }
    catch (e) {
        JL(frontendLogger).fatalException("Exception inside ShowDocumentPDF", e);
    }
}

function getDocumentPDFUrl(url) {
    if (typeof (bIsTouchCapableDevice) == "boolean") {
        if (bIsTouchCapableDevice) {
            return "https://docs.google.com/gview?embedded=true&url=" + url;
        }
    }
    return url;
}

function getAttachFilesIcon(ext) {
    var iconClass = "";
    if (!ext.startsWith(".")) {
        ext = "." + ext;
    }
    ext = ext.toLowerCase();

    switch (ext) {
        case ".doc":
        case ".docx":
            iconClass = 'fa-file-word-o';
            break;
        case ".pdf":
            iconClass = 'fa-file-pdf-o';
            break;
        case ".xls":
        case ".xlsx":
            iconClass = 'fa-file-excel-o';
            break;
        case ".ppt":
        case ".pptx":
            iconClass = 'fa-file-powerpoint-o';
            break;
        case ".png":
        case ".jpg":
        case ".bmp":
            iconClass = 'fa-file-picture-o';
            break;
        case ".txt":
        case ".log":
            iconClass = 'fa-file-text';
            break;
        case ".mp4":
            iconClass = 'fa-file-video-o';
            break;
        case ".avi":
        case ".mpg":
            iconClass = 'fa-file-movie-o';
            break;
        case ".zip":
        case ".rar":
            iconClass = 'fa-file-zip-o';
            break;
        default:
            iconClass = 'fa-file';
            break;
    }

    return iconClass;
}
//#endregion

//#region Update Pl

function plRetainCall(checkbox) {
    if (isViewPlaylist) {
        showErrorDialog(langKeys[currentLanguage]['lPlSharedEditingNotAllowed']);
        checkbox.checked = false;
        return;
    }

    //var recid = $("#tblPlaylist tbody tr[callid=" + callId + "]").attr("recid");
    var $plRow = $(checkbox).closest('tr');
    var recid = $plRow.attr('recid');
    var callId = $plRow.attr('callid');//checkbox.value
    var startTime = $plRow.attr('sttime');
    var chkRetain = $(checkbox).attr('checked') ? true : false;
    if (isECEnabled)
        updateRetainCallByRecorder(recid, callId, chkRetain, startTime);
    else
        updateRetainCall(callId, chkRetain, startTime);
}

function updateRetainCall(callId, retainCall, startTime) {
    //JL(frontendLogger).info(callId + '-' + retainCall);
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data:
        {
            method: 'UpdateRetainStatus'
            , callId: callId
            , retainValue: retainCall
            , startTime: startTime
            , isSharedPage: isSharedPage
        },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, isOnlyIQ3ModeEnabled ? langKeys[currentLanguage]['sEventUpdatedSuccess'] : langKeys[currentLanguage]['sCfUpdate']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in updateRetainCall(): ' + jqXHR.responseText);
        }
    });
}

function updatePlRowContents(callId, newVal) {
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data:
        {
            method: 'UpdateCustomFields'
            , dataEntered: newVal
            , callIds: callId
            , fieldName: 'colCallComments'
            , userId: $('#header-login-name').attr('uid')
            //, fieldType:
            , isSharedPage: isSharedPage
        },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, isOnlyIQ3ModeEnabled ? langKeys[currentLanguage]['sEventUpdatedSuccess'] : langKeys[currentLanguage]['sCfUpdate']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in updatePlRowContents(): ' + jqXHR.responseText);
        }
    });
}

function updateRetainCallByRecorder(recid, callId, retainCall, startTime) {
    //var recid = $("#tblPlaylist tbody tr[callid=" + callId + "]").attr("recid");
    //JL(frontendLogger).info(recid + '-' + callId + '-' + retainCall);
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data:
        {
            method: 'UpdateRetainStatusByRecorder'
            , recId: recid
            , callId: callId
            , retainValue: retainCall
            , startTime: startTime
            , isSharedPage: isSharedPage
        },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    $.growlUI(null, isOnlyIQ3ModeEnabled ? langKeys[currentLanguage]['sEventUpdatedSuccess'] : langKeys[currentLanguage]['sCfUpdate']);
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred in updateRetainCallByRecorder(): ' + jqXHR.responseText);
        }
    });
}

//#endregion

//#region Chat Transcript
function getAllUsers() {
    JL(frontendLogger).info('inside getAllUsers()');
    $.ajax({
        type: 'POST',
        url: "../Handlers/UserManagerHandlers/UserManagerHandler.ashx",
        data: { method: 'GetAllUsers', isSharedPage: isSharedPage },
        cache: false,
        success: function (response) {
            switch (response.Status) {
                case "Success":
                    allUsers = response.Data;
                    break;
                default:
                    JL(frontendLogger).info('An error has occurred while fetching list of users.');
                    allUsers = [];
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            //showExceptionDialog('Error occurred in getAllUsers(): ' + jqXHR.responseText);
            JL(frontendLogger).info('Error occurred in getAllUsers()');
            JL(frontendLogger).info(jqXHR);
        }
    });
}

function getNameByEmailId(emailId) {
    var userName = "";
    for (i = 0; i < allUsers.length; i++) {
        //JL(frontendLogger).info('i = ' + i + ' Id = ' + allUsers[i].UserID.toLowerCase() + ' toFind = ' + emailId.toLowerCase());
        if (allUsers[i].UserID.toLowerCase() == emailId.toLowerCase()) {
            userName = allUsers[i].UserName;
            break;
        }
    }
    return userName;
}
function buildChatTranscriptHTML(messageText, messageSenderId, dateTime, messageId) {
    var messageHtml =
        '<div class="list-group-item msg-line" id="' + messageId + '">' +
        '<time datetime="' + dateTime + '" class="pull-right-time" style="font-size: small;">'
        + formatDate(dateTime) +
        '</time>' +
        '<p class="list-group-item-text">' +
        '<strong class="msg-sender">' + messageSenderId + ':</strong> ' +
        messageText +
        '</p>' +
        '</div>';
    return messageHtml;
}

function formatDate(data) {
    var dateString = data.substr(6);
    var currentTime = new Date(parseInt(dateString));
    var month = currentTime.getMonth() + 1;
    var day = currentTime.getDate();
    var year = currentTime.getFullYear();

    var hours = currentTime.getHours();
    var minutes = currentTime.getMinutes();
    var seconds = currentTime.getSeconds();

    var time = (hours.toString().length > 1 ? hours : "0" + hours) + ':' + (minutes.toString().length > 1 ? minutes : "0" + minutes) + ':' + (seconds.toString().length > 1 ? seconds : "0" + seconds);

    return (month.toString().length > 1 ? month : "0" + month) +
        "/" +
        (day.toString().length > 1 ? day : "0" + day) +
        "/" +
        year + " " + time;
}
function discardDatePartFromChat($selector) {
    $selector.find('.pull-right-time').each(function () {
        //JL(frontendLogger).info($(this).html());
        $(this).html($(this).html().substring(11));
    });
}
//#endregion Chat Transcript

//#region Filter / Search Playlist
function filterPlaylist(inputVal) {
    var rows = $('#tPlaylist tbody tr');
    rows.each(function (index, row) {
        var allCells = $(row).find('td');
        if (allCells.length) {
            var found = false;
            allCells.each(function (index, td) {
                var regExp = new RegExp(inputVal, 'i');
                if (regExp.test($(td).text())) {
                    found = true;
                    return false;
                }
            });
            if (found == true)
                $(row).show();
            else
                $(row).hide();
        }
    });
    if ($(".dataTables_scrollBody").hasScrollBar())
        $(".dataTables_scrollBody").css('width', '100%');
    else
        $(".dataTables_scrollBody").css('width', '99%');
}
//#endregion Filter / Search Playlist

function calculateAndAdjustVPPosition() {
    var containerWidth = $("#divLvVideoPlayerContainer").width();
    var lvVideoPlayerWidth = $("#videofileplayer").width();
    var left = parseInt((containerWidth / 2) - (lvVideoPlayerWidth / 2), 10);
    $("#videofileplayer").css('left', left);
    //$("#videofileplayer").css('position', 'absolute');
}

function IsPlaylistNameAlreadyExists(plName) {
    var cPlName = '';
    var returnVal = false;
    $("#tPlaylist tbody tr").each(function () {
        cPlName = $(this).attr('playlistname');
        if (plName == cPlName) {
            returnVal = true;
            return returnVal;
        }
    });
    return returnVal;
}
function viewBookmarkPicture(e) {
    destroyDialog("#divInformationDialog");

    var fileName = $(e).attr('fileName');
    var eventDate = $(e).attr('eventDate');
    $("#divInformationDialog").mmsDialog({
        autoOpen: true,
        width: 600,
        height: 400,
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['lmBookmarkPicture'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        close: function () {
            $("#lblInformationMessage").html('');
            $(this).mmsDialog("destroy");
        }
    });

    $("#lblInformationMessage").html('<img class="pictureBM" src="' + inquireURL + (tenantId > 0 ? '/' + tenantId + '/' : '/') + eventDate + '/' + fileName + '" style="height: 100%; width: 100%;"/><br/>');

    $("#divInformationDialog").mmsDialog(
        {
            buttons: [{
                text: langKeys[currentLanguage]['msgBtnOK'],
                click: function () {
                    $(this).mmsDialog("close");
                    $("#lblInformationMessage").html('');
                }
            }]
        });
}

$('.pictureBM').live('dblclick', function () {
    viewBookmarkPictureInFullScreen(this);
});

function viewBookmarkPictureInFullScreen(e) {
    debugger
    var fileName = $(e).attr('fileName');
    var eventDate = $(e).attr('eventDate');
    if (e.requestFullscreen) {
        e.requestFullscreen();
    }
    else if (e.mozRequestFullScreen) {
        e.mozRequestFullScreen();
    }
    else if (e.webkitRequestFullscreen) {
        e.webkitRequestFullscreen();
    }
    else if (e.msRequestFullscreen) {
        e.msRequestFullscreen();
    }
}

function viewBookmarkNotes(e) {
    destroyDialog("#divInformationDialog");

    var viewBookmarkNotes = $(e).parent().parent().attr('bmnotes');
    $("#divInformationDialog").mmsDialog({
        autoOpen: true,
        width: 400,
        height: 'auto',
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['InqBookmarkNotes'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        adjustOnFullScreen: true,
        close: function () {
            $("#lblInformationMessage").html('');
            $(this).mmsDialog("destroy");
        }
    });

    //$("#lblInformationMessage").html('<img src="' + inquireURL + '/' + eventDate + '/' + fileName + '" style="height: 100%; width: 100%;"/><br/>');
    $("#lblInformationMessage").html(viewBookmarkNotes);

    $("#divInformationDialog").mmsDialog(
        {
            buttons: [{
                text: langKeys[currentLanguage]['msgBtnOK'],
                click: function () {
                    $(this).mmsDialog("close");
                    $("#lblInformationMessage").html('');
                }
            }]
        });
}

function viewBookmarkCannedNotes(e) {
    destroyDialog("#divInformationDialog");

    var viewBookmarkCannedNotes = $(e).parent().parent().attr('cannednote');
    $("#divInformationDialog").mmsDialog({
        autoOpen: true,
        width: 400,
        height: 'auto',
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['InqBookmarkCannedNotes'],
        modal: true,
        adjustOnFullScreen: true,
        dialogClass: 'top-on-video',
        adjustOnFullScreen: true,
        close: function () {
            $(this).mmsDialog("destroy");
            $("#lblInformationMessage").html('');
        }
    });
    $("#lblInformationMessage").html(viewBookmarkCannedNotes);
    $("#divInformationDialog").mmsDialog(
        {
            buttons: [{
                text: langKeys[currentLanguage]['msgBtnOK'],
                click: function () {
                    $(this).mmsDialog("close");
                    $("#lblInformationMessage").html('');
                }
            }]
        });
}

function viewMarkermeasurement(e) {
    destroyDialog("#divInformationDialog");

    var Markermeasurement = $(e).parent().parent().attr('Markermeasurement');
    $("#divInformationDialog").mmsDialog({
        autoOpen: true,
        width: 400,
        height: 'auto',
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['InqBookmarkMarkerMeasurement'],
        modal: true,
        dialogClass: 'top-on-video',
        adjustOnFullScreen: true,
        close: function () {
            $(this).mmsDialog("destroy");
            $("#lblInformationMessage").html('');
        }
    });
    $("#lblInformationMessage").html(Markermeasurement);
    $("#divInformationDialog").mmsDialog(
        {
            buttons: [{
                text: langKeys[currentLanguage]['msgBtnOK'],
                click: function () {
                    $(this).mmsDialog("close");
                    $("#lblInformationMessage").html('');
                }
            }]
        });
}


function buildHTMLForGBMTitle(parentId, primaryAreaTitle, path) {
    if (primaryAreaTitle == null || primaryAreaTitle == "undefined" || primaryAreaTitle == "")
        primaryAreaTitle = "Bookmark List";
    var html = "<div class='widget widget-blue' id='" + parentId + "'><div class='widget-title'><div class='widget-controls'><a href='#' class='widget-control widget-control-minimize widget-control-minimize-bm' data-toggle='tooltip' data-placement='top' data-original-title='Minimize'><i class='fa fa-minus-circle'></i></a></div><h3 style='background: transparent !important;'><i class='fa fa-bookmark'></i><span>" + primaryAreaTitle + "</span></h3></div><div class='widget-content' style='padding: 0px;'><div class='table-responsive bm-contents'></div></div></div>";
    return html;
}

function buildHTMLForGBMContents(parentId, path, isInternalContent, isBookmark) {
    var html = '';
    if (isInternalContent) {
        if (isBookmark)
            html = "<tr><td colspan=9 style='font-weight:bold; font-style:italic;'>BUILD BOOKMARK HTML</td></tr>";
        else {
            html = "<tr fullPath='" + path + "'><td colspan=9 style='font-weight:bold; font-style:italic;'>" + path + "</td></tr>";
        }
    }
    else
        html = "<table id='" + parentId + "' style='width:100%;'></table>";
    return html;
}


function fetchAndShowInspectionBookmarkDialog($tr) {
    var callId = $tr.attr("callid").trim();
    var startTime = $tr.find('td[abbr="colStartTime"]').html();
    $("#divPopupBookmarks").empty();
    var $dialog = $("#divPopupBookmarks").mmsDialog({
        autoOpen: false,
        resizable: false,
        modal: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['cplBookmarks'] + moment(startTime, 'YYYYMMDDHHmmss', true).format("MM-DD-YYYY HH:mm:ss"),
        maxWidth: 'auto',
        width: 500,
        height: 400,
        close: function () { $(this).mmsDialog('destroy'); $("#divPopupBookmarks").empty(); },
        open: function () {
            $.blockUI();
            $.ajax({
                type: 'POST',
                url: handlersBaseURL + '/InquireHandlers/InquireRxHandler.ashx',
                data: { 'method': 'GetCaseDetails', 'eventId': callId },
                success: function (response) {
                    $.unblockUI();

                    debugger
                    $("#divBookmarks").empty();
                    $('#messages-list').html('');

                    if (response.data.Inspection.Sections != null && response.data.Inspection.Sections.length > 0)
                        populateInquireInspectionBookmarkData(response.data.Inspection.Sections, 'divPopupBookmarks');
                },
                error: function (response) { $.unblockUI(); showExceptionDialog('Error occurred fetchAndShowGroupBookmarkDialog(): ' + jqXHR.responseText); }
            });
        },
        buttons: {
            "Close": function () {
                $(this).mmsDialog('close');
            }
        },
    });
    $dialog.mmsDialog('open');
}

function fetchAndShowGroupBookmarkDialog($tr) {
    var callId = $tr.attr("callid").trim();
    var startTime = $tr.find('td[abbr="colStartTime"]').html();
    $("#divPopupBookmarks").empty();
    var $dialog = $("#divPopupBookmarks").mmsDialog({
        autoOpen: false,
        resizable: false,
        modal: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['cplBookmarks'] + moment(startTime, 'YYYY-MM-DD HH:mm:ss', true).format("MM-DD-YYYY HH:mm:ss"),
        maxWidth: 'auto',
        width: 500,
        height: 400,
        adjustOnFullScreen: true,
        close: function () {
            $(this).mmsDialog('destroy');
            $("#divPopupBookmarks").empty();
        },
        open: function () {
            $.blockUI();

            $.ajax({
                type: 'POST',
                url: handlersBaseURL + '/VoiceRecHandlers/VRHandler.ashx',
                data: { 'method': 'FetchInqFileVideoBookmarks', 'CallId': callId, isSharedPage: isSharedPage },
                success: function (response) {
                    $.unblockUI();
                    var bmdata = [];
                    var bmRows = "";
                    bmdata = response.data;

                    if (bmdata != null) {
                        bmdata.sort(function (bm1, bm2) {
                            return bm1.BookMarkPos > bm2.BookMarkPos ? 1 : -1;
                        });
                        for (var i = 0; i < bmdata.length; i++) {

                            var sortedData = [];
                            sortedData = bmdata;
                            var hr = "" + Math.floor(sortedData[i].BookMarkPos / 3600);
                            var hrlength = hr.length;
                            if (hrlength == undefined) {
                                hr = "00";
                            }
                            else {
                                if (hrlength < 2) {
                                    hr = "0" + hr;
                                }
                            }
                            var min = "" + Math.floor(sortedData[i].BookMarkPos / 60);
                            var minlength = min.length;
                            if (minlength == undefined) {
                                min = "00";
                            }
                            else {
                                if (minlength < 2) {
                                    min = "0" + min;
                                }
                            }
                            var sec = "" + Math.floor(sortedData[i].BookMarkPos % 60);
                            var seclength = sec.length;
                            if (seclength == undefined) {
                                sec = "00";
                            }
                            else {
                                if (seclength < 2) {
                                    sec = "0" + sec;
                                }
                            }

                            var PosText = hr + ":" + min + ":" + sec;
                            PosText = new Date(sortedData[i].BookMarkPos * 1000).toISOString().substr(11, 8);
                            var bookmarkText = sortedData[i].BookMarkText;
                            var bmNotes = sortedData[i].BookMarkNotes;
                            var cannedNote = sortedData[i].CannedNote;
                            var Markermeasurement = sortedData[i].Markermeasurement;
                            var bookmarkTextLen = sortedData[i].BookMarkText.length;
                            var isPicture = sortedData[i].IsPicture;
                            var pictureFileName = sortedData[i].PictureFileName;
                            var eventDateTime = sortedData[i].EventDateTime;
                            var eventDate = eventDateTime.substring(0, 8);
                            var parentId = "bmParent_" + sortedData[i].ParentId;
                            var fullPath = sortedData[i].FullPath;



                            if (fullPath != null && fullPath != "undefined" && fullPath != "") {
                                fullPath = fullPath + bookmarkText;
                                bookmarkText = "";  // Deliberately set Bookmark text to empty. Since entire bookmark is already under the FullPath
                                var primaryArea = fullPath.split(";;")[0];
                                var path = fullPath.split(";;")[1];

                                if ($("#" + parentId).length == 0) {
                                    var html = buildHTMLForGBMTitle(parentId, primaryArea, path);       //Title
                                    $("#divPopupBookmarks").append(html);
                                }
                                if ($("#" + parentId).length == 1) {
                                    if ($("#" + parentId).find("#" + parentId).length == 0) {
                                        var html = buildHTMLForGBMContents(parentId, path, false, false);       //Table
                                        $("#" + parentId).find(".bm-contents").append(html);
                                    }
                                    if ($("#" + parentId).find("table#" + parentId).length == 1) {
                                        var html = html = "<tr id='" + sortedData[i].MarkerId + "' fullPath='" + path + "'><td style='display:none;'></td><td style='display:none;'></td><td colspan=6 style='font-weight:bold; font-style:italic;'>" + path + "</td></tr>";
                                        $("#" + parentId).find("table#" + parentId).append(html);

                                        var row = "";
                                        var bmTime = $("#divPopupBookmarks table[id='" + parentId + "'] tr[posText='" + PosText + "']").length;
                                        if (bmTime == 0) {
                                            row = "<tr style='border-bottom: 1px solid lightgray;' height=30px id='" + sortedData[i].MarkerId + "' position='" + sortedData[i].BookMarkPos + "' bookmark='" + sortedData[i].BookMarkText + "' posText='" + PosText + "' bmNotes='" + bmNotes + "' Markermeasurement='" + Markermeasurement + "' cannedNote='" + cannedNote + "'><td style='display:none;font-family: Georgia, 'Times New Roman', Times, serif;' > <input type='hidden' id='hdnMarkerId'  />" + sortedData[i].MarkerId
                                                + "</td><td width='70'  style='cursor:pointer;' onclick='sendBookmarkPosition(this);'>" + PosText + "</td><td style='cursor:pointer;' width='200' onclick='sendBookmarkPosition(this);'>"
                                                + bookmarkText + "</td>"
                                                + (isPicture == true ? "<td style='cursor:pointer; text-align: center; width: 20px;'><img class='bmicon' style='width:16px; height:16px;' src='" + inquireURL + (tenantId > 0 ? '/' + tenantId + '/' : '/') + eventDate + '/' + pictureFileName + "' fileName='" + pictureFileName + "' eventDate='" + eventDate + "' title='" + langKeys[currentLanguage]['lmViewPicture'] + "' onclick='viewBookmarkPicture(this);'/></td>" : "<td style='cursor:pointer; text-align: center; width: 20px;'></td>")
                                                + "<td style='display:none;'><input id='hdnBookmark' type='hidden'/>"
                                                + bookmarkText + "</td><td style='display:none;'><input id='hdnPlaybakPosition' type='hidden'/>"
                                                + sortedData[i].BookMarkPos + "</td>"
                                                + (bmNotes.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='Notes' src='../assets/icons/um/notes.jpg' onclick='viewBookmarkNotes(this)';>" : "<td width=10px; style='cursor:pointer;'>")
                                                + (Markermeasurement.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='" + langKeys[currentLanguage]['InqBookmarkMarkerMeasurement'] + "' src='../assets/icons/monitor/IQ3Measurement.png' onclick='viewMarkermeasurement(this)';>" : "<td width=10px; style='cursor:pointer;'>")
                                                + (cannedNote.length > 0 ? "<td width=25px; style='cursor:pointer;'><i title='Canned Notes' class='fa fa-1-2x fa-book' style='padding-top: 4px;' onclick='viewBookmarkCannedNotes(this)';>" : "<td width=25px; style='cursor:pointer;'>")
                                                + "</td></tr>";
                                            $("#" + parentId).find("table#" + parentId).append(row);
                                        }
                                    }
                                }
                            }
                            else {
                                if ($("#" + parentId).length == 0) {
                                    var html = buildHTMLForGBMTitle(parentId, "", "");
                                    $("#divPopupBookmarks").append(html);
                                    var tableHtml = buildHTMLForGBMContents(parentId, "", false, false);
                                    $("#" + parentId).find(".bm-contents").append(tableHtml);
                                }
                                var bmTime = $("#divPopupBookmarks table[id='" + parentId + "'] tr[posText='" + PosText + "']").length;
                                if (bmTime == 0) {
                                    if (bookmarkTextLen < 30) {
                                        $(".bm-contents table[id='" + parentId + "']").append("<tr height=30px id='" + sortedData[i].MarkerId + "' position='" + sortedData[i].BookMarkPos + "' bookmark='" + sortedData[i].BookMarkText + "' posText='" + PosText + "' bmNotes='" + bmNotes + "' Markermeasurement='" + Markermeasurement + "' cannedNote='" + cannedNote + "'><td style='display:none;font-family: Georgia, 'Times New Roman', Times, serif;' > <input type='hidden' id='hdnMarkerId'  />" + sortedData[i].MarkerId
                                            + "</td><td width='70'  style='cursor:pointer;' onclick='sendBookmarkPosition(this);'>" + PosText + "</td><td style='cursor:pointer;' width='200' onclick='sendBookmarkPosition(this);'>"
                                            + sortedData[i].BookMarkText + "</td>"
                                            + (isPicture == true ? "<td style='cursor:pointer; text-align: center; width: 20px;'><img class='bmicon' style='width:16px; height:16px;' src='" + inquireURL + (tenantId > 0 ? '/' + tenantId + '/' : '/') + eventDate + '/' + pictureFileName + "' fileName='" + pictureFileName + "' eventDate='" + eventDate + "' title='" + langKeys[currentLanguage]['lmViewPicture'] + "' onclick='viewBookmarkPicture(this);'/></td>" : "<td style='cursor:pointer; text-align: center; width: 20px;'></td>")
                                            + "<td style='display:none;'><input id='hdnBookmark' type='hidden'/>"
                                            + bookmarkText + "</td><td style='display:none;'><input id='hdnPlaybakPosition' type='hidden'/>"
                                            + sortedData[i].BookMarkPos + "</td>"
                                            + (bmNotes.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='Notes' src='../assets/icons/um/notes.jpg' onclick='viewBookmarkNotes(this)';></td>" : "<td></td>")
                                            + (Markermeasurement.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='" + langKeys[currentLanguage]['InqBookmarkMarkerMeasurement'] + "' src='../assets/icons/monitor/IQ3Measurement.png' onclick='viewMarkermeasurement(this)';>" : "<td width=10px; style='cursor:pointer;'>")
                                            + (cannedNote.length > 0 ? "<td width=25px; style='cursor:pointer;'><i title='Canned Notes' class='fa fa-1-2x fa-book' style='padding-top: 4px;' onclick='viewBookmarkCannedNotes(this)';>" : "<td width=25px; style='cursor:pointer;'>")
                                            + "</td></tr>");
                                    }
                                    else {
                                        var truncatedBookmarkText = bookmarkText.substring(0, 17);
                                        var NewBookmarkText = truncatedBookmarkText + ".."
                                        $(".bm-contents table[id='" + parentId + "']").append("<tr height=30px id='" + sortedData[i].MarkerId + "' position='" + sortedData[i].BookMarkPos + "' bookmark='" + sortedData[i].BookMarkText + "' posText='" + PosText + "' bmNotes='" + bmNotes + "' Markermeasurement='" + Markermeasurement + "' cannedNote='" + cannedNote + "'><td style='display:none;font-family: Georgia, 'Times New Roman', Times, serif;' > <input type='hidden' id='hdnMarkerId'  />" + sortedData[i].MarkerId
                                            + "</td><td width='70'  style='cursor:pointer;' onclick='sendBookmarkPosition(this);'>" + PosText + "</td><td style='cursor:pointer;' width='200' white-space='nowrap' title='" + bookmarkText + "' onclick='sendBookmarkPosition(this);'>"
                                            + NewBookmarkText + "</td>"
                                            + (isPicture == true ? "<td style='cursor:pointer; text-align: center; width: 20px;'><img class='bmicon' style='width:16px; height:16px;' src='" + inquireURL + (tenantId > 0 ? '/' + tenantId + '/' : '/') + eventDate + '/' + pictureFileName + "' fileName='" + pictureFileName + "' eventDate='" + eventDate + "' title='" + langKeys[currentLanguage]['lmViewPicture'] + "' onclick='viewBookmarkPicture(this);'/></td>" : "<td style='cursor:pointer; text-align: center; width: 20px;'></td>")
                                            + "<td style='display:none;'><input id='hdnBookmark' type='hidden'/>"
                                            + bookmarkText + "</td><td style='display:none;'><input id='hdnPlaybakPosition' type='hidden'/>"
                                            + sortedData[i].BookMarkPos + "</td>"
                                            + (bmNotes.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='Notes' src='../assets/icons/um/notes.jpg' onclick='viewBookmarkNotes(this)';></td>" : "<td></td>")
                                            + (Markermeasurement.length > 0 ? "<td width=10px; style='cursor:pointer;'><img title='" + langKeys[currentLanguage]['InqBookmarkMarkerMeasurement'] + "' src='../assets/icons/monitor/IQ3Measurement.png' onclick='viewMarkermeasurement(this)';>" : "<td width=10px; style='cursor:pointer;'>")
                                            + (cannedNote.length > 0 ? "<td width=25px; style='cursor:pointer;'><i title='Canned Notes' class='fa fa-1-2x fa-book' style='padding-top: 4px;' onclick='viewBookmarkCannedNotes(this)';>" : "<td width=25px; style='cursor:pointer;'>")
                                            + "</td></tr>");
                                    }
                                }
                            }
                        }
                    }
                },
                error: function (response) { // Failure
                    $.unblockUI();
                    showExceptionDialog('Error occurred fetchAndShowGroupBookmarkDialog(): ' + jqXHR.responseText);
                }
            });
        },
        buttons: {
            "Close": function () {
                $(this).mmsDialog('close');
            }
        },
    });
    $dialog.mmsDialog('open');
}

var previousRow = [];
function sendBookmarkPosition(e) {
    if (currentloadedscreen == "ListView") {
        if (previousRow.length !== 0) {
            var textPos = previousRow.length - 1;
            previousRow[textPos].removeClass("highlightRow");
        }

        var row = $(e).parent();
        previousRow.push(row);
        //var allText = row[0].innerText.split('~~~~~');

        // var position = allText[1];
        //var position = row[0].cells[4].innerText;
        var position = $(row).attr('position');
        position = parseInt(position, 10);
        row.addClass("highlightRow");
        //  slCtl.Content.JS2SL.SetPlayBakPosition(position);
        // setPlaybackPosition(position);
        vplayer.currentTime = position;
    }
}

function validatePlaylistAttributes() {
    var returnValue = false;
    if ($('#txtEditPlaylistName').val().length == 0) {
        showErrorDialog(langKeys[currentLanguage]['emsgPlaylistNameEmpty']);
        returnValue = false;
        return returnValue;
    }
    if ($('#txtEditPlaylistName').val().length > 50) {
        showErrorDialog(langKeys[currentLanguage]['emsgMaxLenghtOfPlaylistName']);
        returnValue = false;
        return returnValue;
    }
    else
        returnValue = true;

    if ($('#txtPLComments').val().length > 50)
    {
        showErrorDialog(langKeys[currentLanguage]['emsgMaxLengthOfPlaylistComments']);
        returnValue = false;
        return returnValue;
    }
    else
        returnValue = true;
    return returnValue;
}


//#region Email Dialog
$(function () {
    $(document).on('click', '#btnAddEmailRecipient', function () {
        var email = $('#txtAddEmailRecipient').val().trim();
        if (email != '') {
            if (isValidEmail(email)) {
                var recipients = getRecipientsFromDialog();
                var pattern = /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i
                var systemUsers = [];
                if (allUsers.length) {
                    $.each(allUsers, function (i, user) {
                        if (pattern.test(user.UserID)) {
                            systemUsers.push(user.UserID);
                        }
                    });
                }
                if (recipients.includes(email) == false && systemUsers.includes(email) == false) {
                    $('#tblRecipientsForEmail tbody').append("<tr UserEmail='" + email + "'><td><input type='checkbox'/></td><td class='col-serial'></td><td>" + email + "</td></tr>");
                    $('#txtAddEmailRecipient').val('');

                    $('#tblSysUsersForEmail tbody tr').each(function () {
                        $(this).show();
                    });
                    addSerialNumber($('#tblRecipientsForEmail tbody'));
                }
                else {
                    showInfoDialog(langKeys[currentLanguage]["erError"], langKeys[currentLanguage]['erErrExistsRecipientEmail']);
                }
            }
            else {
                showCustomInfoDialog(langKeys[currentLanguage]['setupInvalidmail'], "<b>" + email + "</b> " + langKeys[currentLanguage]['lmNotAValidEmailAddress']);
            }
        }
        else {
            showErrorDialog(langKeys[currentLanguage]['srErrBlankRecipientEmail']);
        }
    });

    $('#tblSysUsersForEmail tbody').on('click', 'tr input:checked', function () {
        $(this).closest('tr').remove().appendTo('#tblRecipientsForEmail tbody');
        $('#tblSysUsersForEmail tbody tr').each(function () {
            $(this).show();
        });
        $(this).attr('checked', false);

        addSerialNumber($('#tblRecipientsForEmail tbody'));
        addSerialNumber($('#tblSysUsersForEmail tbody'));
    });

    $('#tblSysUsersForEmail thead').on('click', 'tr input:checked', function () {
        $('#txtAddEmailRecipient').val('');
        $("#tblSysUsersForEmail tbody tr").each(function () {
            if ($(this).is(":visible"))
                $(this).remove().appendTo("#tblRecipientsForEmail tbody");
        });

        $("#tblSysUsersForEmail tbody tr").each(function () {
            $(this).show();
        });

        $(this).attr("checked", false);

        addSerialNumber($('#tblRecipientsForEmail tbody'));
        addSerialNumber($('#tblSysUsersForEmail tbody'));
    });

    $('#tblRecipientsForEmail tbody').on('click', 'tr input:checked', function () {
        var row = $(this).closest('tr');
        if ($(row).attr('IsSystemUser') == 'true') {
            $(row).remove().appendTo('#tblSysUsersForEmail tbody');
        }
        else {
            $(row).remove();
        }
        $(this).attr('checked', false);

        addSerialNumber($('#tblRecipientsForEmail tbody'));
        addSerialNumber($('#tblSysUsersForEmail tbody'));
    });

    $('#tblRecipientsForEmail thead').on('click', 'tr input:checked', function () {
        $('#tblRecipientsForEmail tbody tr').each(function () {
            if ($(this).attr('IsSystemUser') == 'true') {
                $(this).remove().appendTo('#tblSysUsersForEmail tbody');
            }
            else {
                $(this).remove();
            }
        });

        $(this).attr('checked', false);
        addSerialNumber($('#tblRecipientsForEmail tbody'));
        addSerialNumber($('#tblSysUsersForEmail tbody'));
    });

    $('#txtAddEmailRecipient').keyup(function () {
        searchSystemUserByEmail($(this).val());
    });

});
function addSerialNumber($tbl) {
    $tbl.find('tr').each(function (index) {
        $(this).find('td.col-serial').html(index + 1);
    });
}
function searchSystemUserByEmail(inputVal) {
    try {
        $('#tblSysUsersForEmail tbody tr').each(function () {
            let userEmail = $(this).attr("UserEmail");
            var found = false;

            if (userEmail) {
                var regExp = new RegExp(inputVal, 'i');
                if (regExp.test(userEmail)) {
                    found = true;
                }
            }

            if (found == true)
                $(this).show();
            else
                $(this).hide();
        });
    } catch (e) {
        JL(frontendLogger).fatalException('Error in searchSystemUserByEmail : ' + e);
    }
}
function removeAllEmailRecipients() {
    try {
        $('#tblRecipientsForEmail tbody tr').each(function () {
            if ($(this).attr('IsSystemUser') == 'true') {
                $(this).remove().appendTo('#tblSysUsersForEmail tbody');
            }
            else {
                $(this).remove();
            }
        });
    } catch (e) {
        JL(frontendLogger).fatalException('Error in removeAllEmailRecipients : ' + e);
    }
}
function getRecipientsFromDialog() {
    try {
        var RecipientList = [];
        $("#tblRecipientsForEmail tbody tr").each(function () {
            RecipientList.push($(this).attr("UserEmail"));
        });

        return RecipientList;
    } catch (e) {
        JL(frontendLogger).fatalException('Error in getRecipientsFromDialog : ' + e);
    }
}
function isValidEmail(email) {
    var validationFilter = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/
    if (!(email == "" || email == null) && validationFilter.test(email))
        return true;
    else
        return false;
}
//#endregion Email Dialog

function sendPlaylistShareLink(sharedWithEmailsCSV, playlistId, playlistName, playlistComments, noOfItems) {
    $.blockUI();
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: {
            method: 'SendPlaylistShareLink', SharedWithEmailsCSV: sharedWithEmailsCSV,
            PlaylistId: playlistId, PlaylistName: playlistName, PlaylistComments: playlistComments,
            NoOfItems: noOfItems, isSharedPage: isSharedPage
        },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $.unblockUI();
                    console.log(response);
                    const spacedEmailAddresses = sharedWithEmailsCSV.replace(/,/g, ', ');
                    $.pnotify({ title: false, text: langKeys[currentLanguage]['plShareSuccess'] + spacedEmailAddresses, opacity: 1, type: "info", delay: 3000 });
                    $("#dlgPlaylistShare").mmsDialog("close");
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred sendPlaylistShareLink(): ' + jqXHR.responseText);
        }
    });
}

function playerToggleScreen(playerNo) {

    if (isFullScreen()) {
        exitFullscreen();
    }
    else {

        if ($("#divLvVideoPlayer" + playerNo + " > div > embed").length > 0) {
            var plToToggle = $("#divLvVideoPlayer" + playerNo).parent().parent()[0];

            if (plToToggle.requestFullscreen) {
                plToToggle.requestFullscreen();
            } else if (plToToggle.mozRequestFullScreen) {
                plToToggle.mozRequestFullScreen();
            } else if (plToToggle.webkitRequestFullscreen) {
                plToToggle.webkitRequestFullscreen();
            } else if (plToToggle.msRequestFullscreen) {
                plToToggle.msRequestFullscreen();
            }

            setTimeout(function () {
                var embed = $("#divLvVideoPlayer" + playerNo + " > div > embed");
                pdf_src = embed.src;
                PDFObject.embed(pdf_src, $("#divLvVideoPlayer" + playerNo).children('div')[0]);
            }, 2000);

            //plToToggle
        }
        else if ($("#divLvVideoPlayer" + playerNo).children().length > 0) {
            var plToToggle = $("#divLvVideoPlayer" + playerNo).parent().parent()[0];

            if (plToToggle == void 0 || videoPlayer.childElementCount == 0) {
                //Skipping if Container is null or already fullscreen

            }
            else {
                setTimeout(function () {
                    //Because Timeline
                    if (currentloadedscreen == "Timeline") {
                        showMetadataFullScreen(plToToggle);
                    }
                }, 100);
            }

            if (plToToggle.requestFullscreen) {
                plToToggle.requestFullscreen();
            } else if (plToToggle.mozRequestFullScreen) {
                plToToggle.mozRequestFullScreen();
            } else if (plToToggle.webkitRequestFullscreen) {
                plToToggle.webkitRequestFullscreen();
            } else if (plToToggle.msRequestFullscreen) {
                plToToggle.msRequestFullscreen();
            }
        }
    }
}

function showMetadataFullScreen(elem) {
    try {
        var callId = $(elem).find('.playlist-timeline-video-player').attr('sCallId');
        var revviewEvent = tblPlaylistData.find(t => t.CallId == callId);

        if (callId.startsWith('IQ3')) {
            hideMetadata();
            getPlaylistEventDetailFullScreen(callId, revviewEvent, elem);
            reloadAccordian();
        }
    }
    catch (e) {

    }
}

function getPlaylistEventDetailFullScreen(eventId, revviewEvent, elem) {
    let isMDEvent = (eventId.indexOf("MD") >= 0 || eventId.indexOf("md") >= 0) ? true : false;
    if (eventId) {
        if (!revviewEvent) {
            $.ajax({
                url: handlersBaseURL + '/InquireHandlers/InquireRxHandler.ashx',
                data: { 'method': 'GetCaseDetails', 'eventId': eventId, isSharedPage: isSharedPage },
                type: 'POST',
            }).then(function (response) {
                var data = response.data;

                // Common for Both MD and IQ3
                $(elem).find('.case-no').text(data.Event.Id);

                if (data.Event.GPS == '' || data.Event.GPS == "N/A" || data.Event.GPS.length == 0) {
                    $(elem).find('#trLocation').hide();
                }
                else {
                    $(elem).find('#trLocation').show();
                    $(elem).find('.case-location').text(data.Event.GPS);
                }

                $(elem).find('.case-date').text(data.Event.DateTime);
                $(elem).find('.case-qb-dialogid').text(data.QBDialogId);

                //updateCustomFieldsFullScreen(data.CustomFields);
                UpdatePreInspectionData(data.Inspection.PreInspectionDataList, 'divCustomFields');


                if (data.Inspection.Sections != null && data.Inspection.Sections.length > 0) {
                    populateInquireInspectionBookmarkData(data.Inspection.Sections, 'divBookmarksFullScreen');
                }

                if (isMDEvent) {
                    if (data.Patient) {
                        $(elem).find('.case-patient-name').text(data.Patient.FullName);
                        $(elem).find('.case-patient-height').text(data.Patient.Height);
                        $(elem).find('.case-patient-weight').text(data.Patient.Weight);
                        if (data.Patient.DOB != null)
                            $(elem).find('.case-patient-dob').text(parseDate(data.Patient.DOB, true));
                        $(elem).find('.case-patient-gender').text(data.Patient.GenderString);
                    }
                    $(elem).find('.case-complaint').text(data.PrimaryComplaint);

                    $.each(data.PatientVitals, function (key, val) {
                        var rowHtml = '<tr><td class="meta-label text-left">' + val.VitalSign.Name + ':</td><td class="meta-info-value text-left"><span class="">' + val.Reading + '</span></td></tr>';
                        $(elem).find(".case-vitals-info table tbody").append(rowHtml);
                    });
                    var drugNames = '', drugImages = '', medNames = '', medImages = '', eegNames = '', eegImages = '';
                    if (data.Drugs) {
                        drugNames = $.map(data.Drugs, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.DrugImages) {
                        drugImages = $.map(data.DrugImages, function (v, i) {
                            return v;
                        }).join(",");
                    }
                    if (data.Medications) {
                        medNames = $.map(data.Medications, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.MedicationImages) {
                        medImages = $.map(data.MedicationImages, function (v, i) {
                            return v;
                        }).join(",");
                    }
                    if (data.EEGs) {
                        eegNames = $.map(data.EEGs, function (v, i) {
                            return v.Name;
                        }).join(",");
                    }
                    if (data.EEGImages) {
                        eegImages = $.map(data.EEGImages, function (v, i) {
                            return v;
                        }).join(",");
                    }

                    var dateString = data.CreatedDate.substr(6);
                    var currentTime = new Date(parseInt(dateString));

                    var month = ("0" + (currentTime.getMonth() + 1)).slice(-2);
                    var day = ("0" + currentTime.getDate()).slice(-2);
                    var year = currentTime.getFullYear();
                    var date = year + month + day;

                    var medRow = '<tr><td class="meta-label text-left">Medications:</td><td class="meta-info-value text-left"><span class="case-medications"><a id="lnkVitalMedications" med-names="' + medNames + '" med-images="' + medImages + '" date="' + date + '" href="javascript:void(0);" style="text-decoration: underline;" class="" lkey="">view</a></span></td></tr>';
                    var eegRow = '<tr><td class="meta-label text-left">EEG:</td><td class="meta-info-value text-left"><span class="case-EEGs"><a id="lnkVitalEEG" eeg-names="' + eegNames + '" eeg-images="' + eegImages + '" date="' + date + '" href="javascript:void(0);" style="text-decoration: underline;" class="" lkey="">view</a></span></td></tr>';
                    $(elem).find('.case-vitals-info > table > tbody').append(medRow);
                    $(elem).find('.case-vitals-info > table > tbody').append(eegRow);
                }
                else {
                    $(elem).find('.case-Interviewer').text(data.Event.Interviewer);
                    $(elem).find('.case-Interviewee').text(data.Event.Interviewee);

                    if (data.Event.PrimaryArea == '' || data.Event.PrimaryArea == "N/A" || data.Event.PrimaryArea.length == 0) {
                        $(elem).find('#trType').hide();
                    }
                    else {
                        $(elem).find('#trType').show();
                        $(elem).find('.case-Type').text(data.Event.PrimaryArea);
                    }

                    $(elem).find('.case-Notes').text(data.Event.Notes);
                    fullScreenCaseNotes = data.Event.Notes;
                    //valusetfoeeventnotes(data.Event.Notes);
                }
            });
        }
        else {

            $(elem).find('.ID-CallID').text(revviewEvent.CallId);
            $(elem).find('.ID-CallerID').text(revviewEvent.RevViewPhoneNumber);
            $(elem).find('.ID-StartTime').text(moment("" + revviewEvent.StartTimeString, "YYYYMMDDhhmmss").format("MM/DD/YYYY hh:mm:ss"));
            $(elem).find('.ID-Ext').text(revviewEvent.ChannelId);
            $(elem).find('.ID-ExtName').text(revviewEvent.ChannelName.replace('+', ' '));
            $(elem).find('.ID-AgentName').text(revviewEvent.RevViewAgentName);
        }

        $.ajax({
            type: 'POST',
            url: vrHandlerURL,
            data: { 'method': 'GetChatTranscript', 'EventId': eventId, isSharedPage: isSharedPage },
            cache: false
        }).then(function (response) {
            var $selector = $(elem).find('#case-container #messages-list');
            if (isMDEvent)
                $selector = $(elem).find('#case-container #messages-list');

            if (revviewEvent)
                $selector = $(elem).find('#case-container #messages-list');
            $selector.html('');

            if (response.data != null && response.data.length > 0) {
                for (var i = 0; i < response.data.length; i++) {
                    var messages = JSON.parse(response.data[i].Transcript);
                    //JL(frontendLogger).info(messages);
                    for (var j = 0; j < messages.length; j++) {
                        //JL(frontendLogger).info(messages[i]);
                        buildAndDisplayMessage(messages[j], $selector)
                    }
                }
            }
            discardDatePartFromChat($selector);
        }, function (jqXHR, textStatus, errorThrown) { // Failure
            // hang on this step if the error occur
            showExceptionDialog('Error occurred getPlaylistEventDetail(): ' + jqXHR.responseText);
            //showErrorDialog('something went wrong:: getPlaylistEventDetail request ::' + textStatus + " - " + errorThrown);
        });
    }
}

function updateCustomFieldsFullScreen(CustomFields, elem) {

    $('#divCustomFields').empty();
    if (CustomFields == null || CustomFields.length == 0) {
        var customFieldHtml =
            '<div class="list-group-item msg-line" id="0">' +
            '<p class="list-group-item-text">' +
            '<strong class="msg-sender"></strong>No data to display</p>' +
            '</div>';
        $('#divCustomFields').append(customFieldHtml);
    }
    else {
        for (i_val = 0; i_val < CustomFields.length; i_val++) {
            var customFieldHtml =
                '<div class="list-group-item msg-line" id="' + CustomFields[i_val].Id + '">' +
                '<p class="list-group-item-text">' +
                '<strong class="msg-sender">' + CustomFields[i_val].CustomFieldHeader + ':</strong> ' +
                CustomFields[i_val].CustomFieldData +
                '</p>' +
                '</div>';
            $('#divCustomFields').append(customFieldHtml);
        }
    }
}

function reloadAccordian() {
    $("div.accordian").accordion({

        autoHeight: true,
        collapsible: true,
        heightStyle: 'content',//'content', //'auto',//'fill',//'panel',//
        activate: function (event, ui) {
            JL(frontendLogger).info('activate');
            event.preventDefault();
        },
        beforeActivate: function (event, ui) {
            if (ui.newHeader[0]) {
                var currHeader = ui.newHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            } else {
                var currHeader = ui.oldHeader;
                var currContent = currHeader.next('.ui-accordion-content');
            }
            var isPanelSelected = currHeader.attr('aria-selected') == 'true';
            currHeader.toggleClass('ui-corner-all', isPanelSelected).toggleClass('accordion-header-active ui-state-active ui-corner-top', !isPanelSelected).attr('aria-selected', ((!isPanelSelected).toString()));
            currHeader.children('.ui-icon').toggleClass('ui-icon-triangle-1-e', isPanelSelected).toggleClass('ui-icon-triangle-1-s', !isPanelSelected);
            currContent.toggleClass('accordion-content-active', !isPanelSelected)
            if (isPanelSelected) { currContent.slideUp(); } else { currContent.slideDown(); }

            return false;
        }
    });
}

function EventNotes() {
    $("#divInquireEventNotes").mmsDialog({
        autoOpen: true,
        closeOnEscape: true,
        draggable: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        resizable: false,
        title: langKeys[currentLanguage]['sViewNotes'],
        modal: true,
        //position: 'center',
        width: 360,
        maxHeight: '400',
        height: 'auto',
        show: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        },
        hide: {
            effect: 'fade',
            duration: 1000,
            easing: 'easeOutExpo'
        },
        adjustOnFullScreen: true,
        beforeClose: function () {
        },
        close: function () {
            $(this).mmsDialog("destroy");
        },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnClose'],
                click: function () {
                    $.unblockUI();
                    $(this).mmsDialog("close");

                    return false;
                }
            }
        ]
    });
}

function viewEventNotes() {
    showCustomInfoDialog('Event Notes', fullScreenCaseNotes);
}

function showCommentsDialog($tr) {

    var comments = "";

    var callId = $tr.attr('callid');
    var fullComments = $tr.find('td[abbr=colComment] p.full-comments').text();
    comments = "<div>" + fullComments + "</div>";

    var $dialog = $(comments).mmsDialog({
        autoOpen: false,
        resizable: false,
        modal: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: isOnlyIQ3ModeEnabled ? 'Event comments' : 'Call Comments',
        maxWidth: 'auto',
        width: 500,
        height: 250,
        close: function () {
            $(this).mmsDialog('destroy').remove();
        },
        buttons: {
            "Close": function () {
                $(this).mmsDialog('close');
            }
        }
    });
    $dialog.mmsDialog('open');
}


function updateColCommentsContents(callIds, newVal, columnClass) {
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: vrHandlerURL,
        data: {
            method: 'UpdateCustomFields'
            , dataEntered: newVal
            , callIds: callIds
            , fieldName: columnClass
            , userId: $('#header-login-name').attr('uid')
        },
        cache: false,
        success: function (response) {
            var substr = callIds.split(',');
            switch (response.success) {
                case true:
                    $.unblockUI();
                    //$.growlUI(null, langKeys[currentLanguage]['sCfUpdate']);
                    $.pnotify({ title: false, text: isOnlyIQ3ModeEnabled ? langKeys[currentLanguage]['sEventUpdatedSuccess'] : langKeys[currentLanguage]['sCfUpdate'], opacity: 1, type: "info", delay: 3000 });

                    for (var i = 0; i < substr.length; i++) {
                        var html = '';
                        if (newVal.length) {
                            var initialText = newVal.substring(0, 16);
                            html = initialText;
                            html += '<a class="read-full-comments" style="padding-left: 3px; text-align: right;" href="javascript:void(0);"><img height="16" src="../assets/icons/search/comments-16.png" alt=""></a>';
                            html += '<p class="full-comments hide">' + newVal + '</p>';
                        }
                        let callId = substr[i].substring(1, substr[i].length - 1);

                        $("#tblPlaylist tbody tr[callid='" + callId + "']").find('td[abbr=col' + columnClass + ']').html(html);

                    }
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    for (var i = 0; i < substr.length; i++) {

                        let callId = substr[i].substring(1, substr[i].length - 1);
                        $("#tblPlaylist tbody tr[callid='" + callId + "']").find('td[abbr=col' + columnClass + ']').html('');
                    }
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('An error has occurred updateRowContents(): ' + jqXHR.responseText);
        }
    });
}

const distance = 200;
function scrollLft() {
    document.getElementById('tab').scrollBy({
        left: -distance,
        behavior: 'smooth'
    });
}

function scrollRight() {
    document.getElementById('tab').scrollBy({
        left: distance,
        behavior: 'smooth'
    });
}

$(function () {

    $('#divPlaylistInspectionReport').mmsDialog({
        autoOpen: false,
        modal: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: langKeys[currentLanguage]['msgDlgTitleIQ3InspectionReport'],
        resizable: false,
        minWidth: $(window).width() * 0.8,
        minHeight: $(window).height() * 0.9,
        close: function () {
            g_Inspection = null;
            g_HasPhotoMarker = false;
            g_SelectedPhotoFileName = '';
            resetAdvancedReportOptions();
        },
        open: function (event, ui) {
            resetPlaylistInspectionReportFields();
        },
        buttons: [{
            id: 'btnAdvancedReportOptions',
            text: langKeys[currentLanguage]['rptAdvancedReportOptions'],
            click: function () {
                $('#divAdvancedReportOptions').mmsDialog("open");
            }
        },{
            id: 'btnGenerateInspectionReport',
            text: langKeys[currentLanguage]['rptGenerateReport'],
            click: function () {
                buildAndGenerateCombinedInspectionReport();
            }
        },
        {
            text: langKeys[currentLanguage]['msgBtnCancel'],
            click: function () {
                $(this).mmsDialog("close");
            }
        }]

    });
});

function getInspectionsForPlaylist(eventIds, isVesselReport) {
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: inspectionHandlerURL,
        data: { method: 'GetInspectionsForPlaylist', EventIds: eventIds, IsVesselReport: isVesselReport },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    var inspections = response.data;
                    console.log(inspections);
                    $('#divPlaylistInspectionReport').mmsDialog("open");
                    setPlaylistInspectionData(inspections, isVesselReport);
                    $.unblockUI();
                    break;
                default:
                    $.unblockUI();
                    console.error(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            console.error('Error occurred in getInspectionsForPlaylist(eventId): ' + jqXHR.responseText);
        }
    });
}

function validateUniqueTemplates() {
    if (obj_PlaylistInspectionTemplates.length == 0) {
        showInformationDialog(langKeys[currentLanguage]['imsgPlaylistNoIQ3Event']);
        return;
    }
    else if (obj_PlaylistInspectionTemplates.length == 1) {
        // This is the case where only one inspection template has been used with all the IQ3 events in the playlist.
        // Hence we are good to generate the Combined IQ3 report.
        debugger
        g_EventsDataJSON = [];
        var eventIds = '';
        if (obj_PlaylistDetails) {
            eventIds = $.map(obj_PlaylistDetails, function (v, i) {
                if (v.FileType == 7 || v.FileType == 8) {
                    g_EventsDataJSON.push({
                        EventId: v.CallId.trim(),
                        EventDate: v.StartTimeString.substring(0, 8)
                    });
                    return v.CallId.trim();
                }
            }).join(",");
        }
        getInspectionsForPlaylist(eventIds, false);
    }
    else if (obj_PlaylistInspectionTemplates.length > 1) {
        // This is the case where we have IQ3 events having different inspection templates. This is the problem case. 
        // We need to display details against all IQ3 events under their respective Inspection Template. Details may include EventId, StartTime, Duration. 
        var tblHtml = '';
        $.each(obj_PlaylistInspectionTemplates, function (i, obj_PlaylistInspectionTemplate) {
            tblHtml += '<tr><td colspan="4" style="text-align: left;"><b>' + obj_PlaylistInspectionTemplate.Title + '</b></td></tr>';
            $.each(obj_PlaylistDetails, function (i, obj_PlaylistDetail) {
                if (obj_PlaylistInspectionTemplate.Id == obj_PlaylistDetail.InspectionTemplateId && (obj_PlaylistDetail.FileType == 7 || obj_PlaylistDetail.FileType == 8)) {
                    tblHtml += '<tr><td>' + obj_PlaylistDetail.CallId + '</td><td>' + obj_PlaylistDetail.ChannelName + '</td><td>' + ConvertStartTimeToDateTime(obj_PlaylistDetail.StartTimeString) + '</td><td>' + obj_PlaylistDetail.Duration + '</td></tr>';
                }
            });
        });

        $("#divIQ3ReportValidator").mmsDialog({
            modal: true,
            resizable: false,
            closeText: langKeys[currentLanguage]['ttClose'],
            title: langKeys[currentLanguage]['plSharePlaylist'],
            width: '800',
            minHeight: '400',
            height: '400',
            show: 'fade',
            open: function (event, ui) {
                $('#divIQ3ReportValidator table tbody').html(tblHtml);
            },
            close: function (event, ui) {
                $('#divIQ3ReportValidator table tbody').html('');
            },
            buttons: [
                {
                    text: langKeys[currentLanguage]['msgBtnOK'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
        });
        $("#divIQ3ReportValidator").mmsDialog("open");
    }
}

function setPlaylistInspectionData(inspections, isVesselReport) {
    var totalMarkers = 0;
    var totalSeconds = 0;
    var bAnyPhoto = false;
    $.each(inspections, function (i, inspection) {
        totalMarkers += inspection.NoOfMarkers;
        totalSeconds += parseInt(inspection.Duration, 10) / 1000;

        var hasPhotoMarker = inspection.HasPhotoMarker;
        var photoFileNameCSV = inspection.PhotoFileNameCSV;

        var photoFileNames = inspection.PhotoFileNameCSV.split(",");
        if (hasPhotoMarker) {
            $.each(photoFileNames, function (i, photoFileName) {
                console.log(photoFileName);
                bAnyPhoto = true;
                var eventDate = moment(inspection.StartTime, 'YYYYMMDDHHmmss', true).format("YYYYMMDD")
                var dynamicRow = '';
                if (i == 0)
                    dynamicRow = '<tr><td><input type="radio" checked="checked" name="radMarker" selectedPhotoFileName="' + photoFileName + '" /></td><td><img src=' + inquireURL + (tenantId > 0 ? '/' + tenantId + '/' : '/') + eventDate + '/' + photoFileName + ' width="400" height="" style="border: 2px solid black;" /></td></tr>';
                else
                    dynamicRow = '<tr><td><input type="radio" name="radMarker" selectedPhotoFileName="' + photoFileName + '" /></td><td><img src=' + inquireURL + (tenantId > 0 ? '/' + tenantId + '/' : '/') + eventDate + '/' + photoFileName + ' width="400" height="" style="border: 2px solid black;" /></td></tr>';
                $('#divPhotoMarkerSelector table tbody').append(dynamicRow);
            });
        }
    });


    var reportType = isVesselReport ? 'Vessel Report' : 'Standard Report';
    $('#divPlaylistInspectionReport').attr('isVesselReport', isVesselReport);
    $('.pl-insp-templateId').text(inspections[0].InspectionTemplateId);
    $('.pl-insp-templateTitle').text(inspections[0].Title);

    $('.pl-insp-NoOfMarkers').text(totalMarkers);
    $('.pl-insp-ReportType').text(reportType);

    $('.pl-insp-inspectorId').text(inspections[0].InspectorId);
    $('.pl-insp-inspectorName').text(inspections[0].InspectorName);

    $('.pl-insp-TotalNoOfEvents').text(inspections.length);
    $('.pl-insp-total-duration').text(moment().startOf('day').seconds(totalSeconds).format('HH:mm:ss'));

    var tId = parseInt(tenantId, 10);
    g_HasPhotoMarker = bAnyPhoto;


    if (bAnyPhoto) {
        $('#divPhotoMarkerSelector').mmsDialog('open');
    }
}

function resetPlaylistInspectionReportFields() {
    $('.pl-insp-templateId').text('');
    $('.pl-insp-templateTitle').text('');

    $('.pl-insp-NoOfMarkers').text('');
    $('.pl-insp-ReportType').text('');

    $('.pl-insp-inspectorId').text('');
    $('.pl-insp-inspectorName').text('');

    $('.pl-insp-TotalNoOfEvents').text('');
    $('.pl-insp-total-duration').text('');

    $('#txtPlaylistReportTitle').val('');
    $('#txtPlaylistReportNotes').val('');
    $('#txtPlaylistReportInspectionType').val('');
    $('#ddlPhotoAttachmentTypeForPlaylistReport').val('0');
}

function buildAndGenerateCombinedInspectionReport() {
    var eventId = $('.insp-eventId').text();
    var title = $('#txtPlaylistReportTitle').val() == $('#txtPlaylistReportTitle').attr('defaultvalue') ? '' : $('#txtPlaylistReportTitle').val();
    var notes = $('#txtPlaylistReportNotes').val() == $('#txtPlaylistReportNotes').attr('defaultvalue') ? '' : $('#txtPlaylistReportNotes').val();
    var inspectionType = $('#txtPlaylistReportInspectionType').val();

    var hasPhotoMarker = false;
    var selectedPhotoFileName = "";
    var photoAttachmentType = $('#ddlPhotoAttachmentTypeForPlaylistReport').val();

    if (g_HasPhotoMarker) {
        hasPhotoMarker = g_HasPhotoMarker;
        selectedPhotoFileName = g_SelectedPhotoFileName;
    }

    if (title == "") {
        showInformationDialog(langKeys[currentLanguage]['imsgReportTitleIsRequired']);
        return;
    }

    if (inspectionType == "") {
        showInformationDialog(langKeys[currentLanguage]['imsgInspectionTypeIsRequired']);
        return;
    }

    if (inspectionType.length > 150) {
        showInformationDialog(langKeys[currentLanguage]['imsgInspectionTypeMaxLength']);
        return;
    }

    if (photoAttachmentType == "0" || photoAttachmentType == 0) {
        showInformationDialog(langKeys[currentLanguage]['imsgSelectPhotoLocation']);
        return;
    }

    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        type: 'POST',
        url: inspectionHandlerURL,
        data: {
            method: 'BuildAndGenerateCombinedStandardInspectionReport',
            Title: title,
            InspectionType: inspectionType,
            Notes: notes,
            HasPhotoMarker: hasPhotoMarker,
            SelectedPhotoFileName: selectedPhotoFileName,
            EventDatas: JSON.stringify(g_EventsDataJSON),
            PhotoAttachmentType: photoAttachmentType,
            IQ3AdvancedReportCriteria: JSON.stringify(g_IQ3AdvancedReportCriteria)
        },
        cache: false,
        success: function (response) {
            switch (response.success) {
                case true:
                    $('#divPlaylistInspectionReport').mmsDialog("close");
                    window.setTimeout(function () {
                        $.unblockUI();
                        window.location = inspectionHandlerURL + '?method=DownloadInspectionReport' + "&FileName=" + response.filename;
                    }, 1000);
                    break;
                default:
                    $.unblockUI();
                    console.error(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            console.error('Error occurred in BuildAndGenerateCombinedStandardInspectionReport(): ' + jqXHR.responseText);
        }
    });
}

function showRevviewMTRSign(eventId, revviewEvent) {
    getMTRPlaylistSignDetail(eventId, revviewEvent);
}

function getMTRPlaylistSignDetail(eventId, revviewEvent) {
    let isMDEvent = (eventId.indexOf("MD") >= 0 || eventId.indexOf("md") >= 0) ? true : false;
    if (eventId) {
        console.log('signdocumentId--', eventId);
        if (true) {
            $.ajax({
                url: handlersBaseURL + '/VoiceRecHandlers/MTRReportHandler.ashx',
                data: { 'method': 'GetSignOffHistory', 'eventId': eventId},//palylistid, reportid/certificateid
                type: 'POST',
            }).then(function (response) {
                console.log('getMTRPlaylistSignDetail', response);
                var data = response.data;

                if (Array.isArray(data)) {
                    data.forEach(item => {
                        if (item.SignatureDateString) {
                            item.SignatureDate = item.SignatureDateString.split(' ')[0]; // "yyyy-MM-dd"
                        }
                    });
                }

                //var data = [
                //    {
                //        "CertificateID": "1",
                //        "SignatureID": "1",
                //        "SignatoryName": "John",
                //        "SignatureDate": "20-05-2024"
                //    },
                //    {
                //        "CertificateID": "2",
                //        "SignatureID": "2",
                //        "CertificateID": "2",
                //        "SignatoryName": "Sam",
                //        "SignatureDate": "21-05-2024"
                //    }
                //];

                addMTRItemInAttachedFiles(data);
                openMtrSignHistoryPopup();
            });
        }
    }
}

function addMTRItemInAttachedFiles(items) {
    JL(frontendLogger).info('Adding items to attached files section');

    $("#mtrulPlaylistUserAddedFiles").empty();

    items.forEach(function (item) {
        var SignatureID = item.SignatureID;
        var CertificateID = item.CertificateID;
        var SignatoryName = item.SignatoryName;
        var SignatureDate = item.SignatureDate;
        console.log('addMTRItemInAttachedFiles SignatureDate', SignatureDate);
        var $trHtml = $('<li>').html(`
            <div style="padding: 10px; border-bottom: 1px solid #ddd;">
                <p><strong>User:</strong> ${SignatureID}</p>
                <p><strong>Signed by:</strong> ${SignatoryName}</p>
                <p><strong>Signature Date:</strong> ${SignatureDate}</p>
            </div>
        `);

        $("#mtrulPlaylistUserAddedFiles").append($trHtml);
    });
}

function openMtrSignHistoryPopup() {
    if (nState == FilePlayerState.Play || nState == FilePlayerState.PendingPlay)
        btnPause_Click();

    $("#mtrdivattachedfiless").mmsDialog({
        autoOpen: true,
        maxwidth: 'auto',
        width: '300px',
        height: 'auto',
        resizable: false,
        closeText: langKeys[currentLanguage]['ttClose'],
        close: function () {
            if (nState == FilePlayerState.Pause)
                btnPause_Click();
            try {
                $(this).mmsDialog('destroy');
            } catch (e) {

            }
        },
        title: 'Signoff History',
        modal: true
    });
}

function viewSignoffHistory(currentObj) {
    let eventId = $(currentObj).attr("callid");
    showRevviewMTRSign(eventId.trim());
}

function viewSignedDocument(currentObj) {
    debugger;
    const signLink = $(currentObj).data('link');
    window.open(signLink + "#toolbar=0&navpanes=0&scrollbar=0");
    console.log('signLink'.signLink);
}

//#region Repository related pages
function updateRepositoryModalDisplay(status) {
    $('#addNewRepositoryModal').modal(status);
}

function saveplaylist() {
    debugger
    console.log('saveplaylist--', saveplaylist);
    var playlist = {
        Id: $('#divPlaylist').attr('plID'),
        Name: $("#txtPlaylistName").val(),
        Comments: ""
    };

    var saveType = playlist.Id > 0 ? 'Update' : 'Insert'; //playlist.Id > 0 ? 1 : 0;
    var oldPlName = playlist.Id > 0 ? $("#ulPlaylists > li[id='" + playlist.Id + "']").find('a').html() : '';

    //Fetch repo list to check for duplicates
    $.ajax({
        type: 'POST',
        url: playlistHandlerURL,
        data: {
            operation: saveType,
            playlist: JSON.stringify(playlist),
            method: 'Get repo'
        },
        success: function (response) {
            if (response.success && response.repoNames) {
                var repoNames = response.repoNames;

                var isDuplicate = repoNames
                    .filter(name => name.toLowerCase() !== (playlist.Id > 0 ? oldPlName.toLowerCase() : ''))
                    .some(name => name.toLowerCase() === playlist.Name.toLowerCase());

                if (isDuplicate) {
                    showErrorDialog('<span style="color:red;">A repository with the same name already exists. Please choose a different name.</span>');
                    $.unblockUI();
                    return;
                }

                //  Save the playlist now
                $.ajax({
                    type: 'POST',
                    url: playlistHandlerURL,
                    data: {
                        operation: saveType,
                        playlist: JSON.stringify(playlist),
                        method: 'SavePlaylist',
                        oldPlName: oldPlName
                    },
                    success: function (response) {
                        if (response.success) {
                            fetchAndDisplayAllPlaylists();
                            $.growlUI(null, langKeys[currentLanguage]['sPlSave']);
                        } else {
                            $.unblockUI();
                            showErrorDialog(response.message);
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        $.unblockUI();
                        showExceptionDialog('Error occurred saveplaylist(): ' + jqXHR.responseText);
                    }
                });
            } else {
                showErrorDialog('Failed to fetch repository list.');
                $.unblockUI();
            }
        },
        error: function (jqXHR) {
            $.unblockUI();
            showExceptionDialog('Error fetching repo list: ' + jqXHR.responseText);
        }
    });
}

function validateIsSamePlName(plId, plName) {
    ////alert(plId + ' - ' + plName);
    var cPlId = 0;
    var cPlName = '';
    var returnVal = true;

    $("#divPlaylists ul span").each(function () {
        cPlId = $(this).attr('id');
        cPlName = $(this).find('p strong').html();
        //JL(frontendLogger).info(cPlId + " - " + cPlName);
        if (plName == cPlName) {
            returnVal = false;
            return returnVal;
        }
    });
    return returnVal;
}
//#endregion