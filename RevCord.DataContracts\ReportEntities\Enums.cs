﻿//***********************************************************************
// AssemblyName   : 
// Author               :   <PERSON><PERSON>
// Created ON           :   15-March-2012
//
// Last Modified By     :   <PERSON><PERSON>
// Last Modified On     :   21-March-2012
// Description          :   
//***********************************************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.ComponentModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace RevCord.DataContracts.ReportEntities
{
    public enum ReportType : short
    {
        [DescriptionAttribute("Advanced")]
        Advance = 1,
        [DescriptionAttribute("Evaluation")]
        Evaluation = 2,
        [DescriptionAttribute("Standard")]
        Standard = 3,
        [DescriptionAttribute("911 PBX")]
        PBX911 = 4,
        [DescriptionAttribute("Map")]
        Map = 5,
        [DescriptionAttribute("Activity")]
        Activity = 6,

    }

    public enum CustomSearchType : short
    {
        [DescriptionAttribute("Inspection Template")]
        InspectionTemplate = 1,
        [DescriptionAttribute("Bookmark Text")]
        BookmarkText = 2,
        [DescriptionAttribute("Bookmark Note")]
        BookmarkNote = 3,
        [DescriptionAttribute("Pre Inspection Title")]
        PreInspectionTitle = 4,
        [DescriptionAttribute("Pre Inspection Data")]
        PreInspectionData = 5,
    }

    public enum ReportSubType : short
    {
        Excel = 1,
        BarChart = 2,
        PieChart = 3,
        StackedBarChart = 4,
        CallsAudited = 7,
        CallsNotAudited = 8
    }

    public enum StandardReportSubType : short
    {
        DetailCallReportByAgent = 1,
        DetailCallReportByChannel = 2,
        HourlyBreakdownByAgent = 3,
        TalkTimeByAgent = 4,
        TalkTimeByChannel = 5,
        CallsByAgentDateRange = 6,
        CallsAudited = 7,
        CallsNotAudited = 8
    }


    public enum XAxisData : short
    {
        None = 0,
        [DescriptionAttribute("Day")]
        Day = 1,
        [DescriptionAttribute("Month Week Day")]
        MonthWeekDay = 2,
        [DescriptionAttribute("Month")]
        Month = 3,
        [DescriptionAttribute("Week Day")]
        WeekDay = 4,
        [DescriptionAttribute("Hour")]
        Hour = 5,
    }

    public enum GroupData : short
    {
        [DescriptionAttribute("No Group Data")]
        None = 0,
        [DescriptionAttribute("By Channel Name")]
        ByChannelName = 1,
        [DescriptionAttribute("By Agent")]
        ByAgent,
        [DescriptionAttribute("By Channel")]
        ByChannel,
        [DescriptionAttribute("By Group")]
        ByGroup,
        [DescriptionAttribute("By Total")]
        ByTotal,
        [DescriptionAttribute("By Call Tag")]
        ByCallTag,
    }

    public enum YAxisData : short
    {
        [DescriptionAttribute("No YAxisData")]
        None = 0,
        [DescriptionAttribute("Number of Calls")]
        NumberOfCalls = 1,
        [DescriptionAttribute("Call Duration")]
        CallDuration,
        [DescriptionAttribute("Average Call Duration")]
        AverageCallDuration = 3,
        [DescriptionAttribute("All in One")]
        AllInOne = 4,
        //[DescriptionAttribute("Number of Calls With Details")]
        //NumberOfCallsWithDetail = 5,

        //Evaluation Report Extended YAxisData
        [DescriptionAttribute("Report by Agents")]
        ReportByAgents = 6,
        [DescriptionAttribute("Report by Groups")]
        ReportByGroups = 7,
        [DescriptionAttribute("Report by Scorers")]
        ReportByScorers = 8,
        [DescriptionAttribute("Report by Agent's Score")]
        ReportByAgentScore = 9,
        [DescriptionAttribute("Report by Agent's Monthly Score")]
        ReportByAgentMontlyScore = 10,
        [DescriptionAttribute("Report by Evaluator Scoring")]
        ReportByEvaluatorScoring = 11
    }

    public enum CallStatus : short
    {
        Attended = 1,
        Transferred = 2,
        Abandoned = 3,
    }

    public enum ReportCategory : short
    {
        [DescriptionAttribute("Rdlc")]
        Rdlc = 1,
        [DescriptionAttribute("Map")]
        Map = 2,
        [DescriptionAttribute("Activity")]
        Activity = 3,
    }

    public enum TypeOfReport : short
    {
        //[EnumMember(Value = "Advanced Report")]
        [EnumMember(Value = "Standard Report")]
        [DescriptionAttribute("Advanced")]
        Advanced = 1,

        [EnumMember(Value = "Evaluation Report")]
        [DescriptionAttribute("Evaluation")]
        Evaluation = 2,

        //TODO::Not In Use, will remove later
        [EnumMember(Value = "StandardReport")]
        [DescriptionAttribute("Standard")]
        Standard = 3,

        [EnumMember(Value = "911 PBX Report")]
        [DescriptionAttribute("911 PBX")]
        PBX911 = 4,

        [EnumMember(Value = "Map Report")]
        [DescriptionAttribute("Map")]
        Map = 5,

        [EnumMember(Value = "Activity Report")]
        [DescriptionAttribute("Activity")]
        Activity = 6,
    }
    public enum SavedReportMode : short
    {
        [DescriptionAttribute("None")]
        None = 0,
        [DescriptionAttribute("All Reports")]
        All = 1,
        [DescriptionAttribute("Recent Reports")]
        Recent = 2,
        [DescriptionAttribute("My Reports")]
        MyReports = 3,
        [DescriptionAttribute("Scheduled Reports")]
        Scheduled = 4,
        [DescriptionAttribute("Shared Reports")]
        Shared = 5,
        [DescriptionAttribute("Favorite Reports")]
        Favorite = 6,

    }

    public enum PeriodicDateType : short
    {
        [DescriptionAttribute("Use Date Controls")]
        UseDateControls = 0,
        [DescriptionAttribute("Last 7 Days")]
        Last7Days = 1,
        [DescriptionAttribute("Last 30 Days")]
        Last30Days = 2,
        [DescriptionAttribute("Last 90 Days")]
        Last90Days = 3,
    }

    public enum MMSPermission : short
    {
        [DescriptionAttribute("Setup")]
        Setup = 1,
        [DescriptionAttribute("IR Lite")]
        IRLite = 2,
        [DescriptionAttribute("Monitor")]
        Monitor = 3,
        [DescriptionAttribute("Search")]
        Search = 4,
        [DescriptionAttribute("Evaluation")]
        Evaluation = 5,
        [DescriptionAttribute("Dashboard")]
        Dashboard = 6,
        [DescriptionAttribute("Reports")]
        Reports = 7,
        [DescriptionAttribute("IR Full")]
        IRFull = 8,
        [DescriptionAttribute("Save And Email")]
        SaveAndEmail = 9,
        [DescriptionAttribute("Invitation")]
        Invitation = 10,
        [DescriptionAttribute("Avris View")]
        AvrisView = 11,
        [DescriptionAttribute("IQ3 View")]
        IQ3View = 12,
        [DescriptionAttribute("Is Enterprise")]
        IsEnterprise = 13,
    }

    public enum RoleType
    {
        [DescriptionAttribute("Module Based")]
        ModuleBased = 1,
        [DescriptionAttribute("Channel Based")]
        ChannelBased = 2,
    }
}