﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.SoloEntities
{
    public class LicenseInfoCheck
    {
        public string ResultCode { get; set; }
        public string ProductID { get; set; }
        public string ProductName { get; set; }
        public string LatestVersion { get; set; }
        public string ProdOptionID { get; set; }
        public string OptionName { get; set; }
        public string TCFixedValue { get; set; }
        public string EnteredDate { get; set; }
        public string Status { get; set; }
        public string Quantity { get; set; }
        public string ReplacedBy { get; set; }
        public string RemainingActivations { get; set; }
        public string RemainingDeactivations { get; set; }
        public DateTime DownloadExpiration { get; set; }
        public string LicenseUpdate { get; set; }
        public string CurrentVersion { get; set; }
        public string InvoiceNo { get; set; }
        public string LicenseeEmail { get; set; }
        public string LicenseeName { get; set; }
        public string IsTestLicense { get; set; }
        public string LicenseCounter { get; set; }
        public string CustomerID { get; set; }
        public string Company { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string StateProvince { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public string UDefChar1 { get; set; }
        public string UDefChar2 { get; set; }
        public string UDefChar3 { get; set; }
        public string UDefChar4 { get; set; }
        public string UDefChar5 { get; set; }
        public string UDefChar6 { get; set; }
        public string UDefChar7 { get; set; }
        public string UDefChar8 { get; set; }
        public string UDefChar9 { get; set; }
        public string UDefChar10 { get; set; }
        public int UDefNum1 { get; set; }
        public int UDefNum2 { get; set; }
        public int UDefNum3 { get; set; }
        public int UDefNum4 { get; set; }
        public int UDefNum5 { get; set; }
        public decimal UDefFloat1 { get; set; }
        public decimal UDefFloat2 { get; set; }
        public decimal UDefFloat3 { get; set; }
        public decimal UDefFloat4 { get; set; }
        public decimal UDefFloat5 { get; set; }
        public DateTime UDefDate1 { get; set; }
        public DateTime UDefDate2 { get; set; }
        public DateTime UDefDate3 { get; set; }
        public DateTime UDefDate4 { get; set; }
    }
}
