﻿//var iwburl = '../Handlers/IwbHandlers/IwbHandler.ashx';
var aiApiUrl = 'https://iwbai.revcord.com/api';//'http://45.23.20.2:9009/api'
var $modelPlaceholder = $('#divDialogContents');
var wpss;
var jobs;
var jobsTable;
var orgTable, docTable;
var welders;
var signoff_documentId = '';
var signoff_iwbPdfFileName = '';
var signoff_iwbpdffilelink = "";
var addDocumentPopup = null;
//let editDocumentPopup = null;
// State-City Mapping for all US states

$(document).ready(function () {
    $.blockUI();
    checkUserPermission();

    /*if (loginUserType == 8) {
        $('.lm-doc').addClass('d-none');
        $('.lm-org').addClass('d-none');
        $('.lm-job').addClass('d-none');
        $('.lm-test').addClass('d-none');
        $('.lm-work').addClass('d-none');
    }
    else {
        $('.lm-mywpq').addClass('d-none');
    }*/

    let url = window.location.pathname.split("/")[2];
    switch (url) {
        case 'ManageOrganizations.aspx':
            /*if (loginUserType == 1) {
                $('#btnAddNewOrg, .filter-row').removeClass('d-none');
            }*/
            getOrganizations();
            break;
        case 'ManageJobs.aspx':
            //getWeldersDDL();
            getAllJobs();
            break;
        case 'ViewJobs.aspx':
            viewAllJobs();
            break;
        case 'ViewTests.aspx':
            viewAllTests();
            break;
        case 'ManageDocuments.aspx':
            getDocuments();
            break;
        case 'ManageWelders.aspx':
            if (loginUserType == 8) {
                $('#btnAddWelder, .filter-row').addClass('d-none');
            }
            getWelders();
            break;
        case 'ManageTests.aspx':
            getTests();
            break;
        case 'Dashboard.aspx':
            switch (loginUserType) {
                case 5:
                    $('.contractor-view').addClass('d-none');
                    $('.welder-view').addClass('d-none');
                    $('.insurance-view').removeClass('d-none');
                    break;
                case 1:
                case 6:
                case 7:
                    $('.contractor-view').removeClass('d-none');
                    $('.welder-view').addClass('d-none');
                    $('.insurance-view').addClass('d-none');
                    break;
                case 8:
                    getWelderDashboardData();
                    $('.contractor-view').addClass('d-none');
                    $('.insurance-view').addClass('d-none');
                    $('.welder-view').removeClass('d-none');
                    break;
            }
            break;

        default:
            break;
    }
    // Build sign off request dialog
    $('#divSignOffRequestDialog').mmsDialog({
        autoOpen: false,
        modal: true,
        resizable: false,
        width: 800,
        height: 600,
        scrollY: false,
        open: function () {
            $('#divSignOffRequestDialog input[type=text]').val('');
            $('#txtRequestTitle').val('');
            $('#txtRequestNotes').val('');
            $('#txtNewRecipient').val('');
            $("#tblSysUsers tbody tr").each(function () {
                $(this).show();
            });
            $("#tblRecipient tbody tr").each(function () {
                if ($(this).attr('isSystemUser') == "true" || $(this).attr('isSystemUser') == "True") {
                    $(this).remove().appendTo("#tblSysUsers");
                }
                else {
                    $(this).remove();
                }
            });
        },
        close: function () {
            $('#divSignOffRequestDialog input[type=text]').val('');
            //$("#divSysUserList").empty();
        },
        title: langKeys[currentLanguage]['msgDlgTitleInitSignoffRequest'],
        buttons: [
            {
                text: 'Initialize SignOff Request',
                id: 'btnSendSignOffRequest',
                click: function () {
                    var validationResult = validateSignOffRequest();
                    if (!validationResult.IsValid) {
                        $('.validation-summary').append(validationResult.Messagesmarkup);
                        return;
                    }
                    else {
                        $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
                        var title = $('#txtRequestTitle').val();
                        var notes = $('#txtRequestNotes').val();
                        var isOriginalDocument = false;
                        var revSignOffRequest = getRevSignRequest(title, notes, "20250114", "", tenantId, isOriginalDocument);
                        $.ajax({
                            url: '../Handlers/VoiceRecHandlers/RevSignHandler.ashx',
                            type: 'POST',
                            data: {
                                'method': 'SaveIwbDocumentSignoffRequest',
                                'RevSignRequest': revSignOffRequest,
                                IwbDocumentId: signoff_documentId,
                                IwbPdfFileName: signoff_iwbPdfFileName,
                                IwbPdfLink: signoff_iwbpdffilelink
                            },
                            success: function (result) {
                                switch (result.success) {
                                    case true:
                                        $.unblockUI();
                                        //$.pnotify({ title: langKeys[currentLanguage]['tManageSign'], text: result.message, opacity: 1, type: "info", delay: delayDataTableNotification });
                                        toastr.success(result.message);
                                        if (result.redirectUrl.length > 0) {
                                            $("#divSignOffRequestDialog").attr("redirecturl", result.redirectUrl);
                                        }
                                        $("#divSignOffRequestDialog").mmsDialog('close');
                                        signoff_documentId = "";
                                        signoff_pdfFileNameName = "";
                                        break;
                                    default:
                                        $.unblockUI();
                                        break;
                                }
                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                                $.unblockUI();
                                showExceptionDialog('An error has occurred SaveIwbDocumentSignoffRequest(): ' + jqXHR.responseText);
                            }
                        });
                    }
                }
            },
            {
                text: 'Close',
                id: 'btnCloseSignOffRequestDialog',
                click: function () {
                    $(this).mmsDialog('close');
                }
            }
        ]
    });

    $(document).on('change', '#selectAllCheckbox', function () {
        var isChecked = $(this).is(':checked');
        console.log("Select All toggled. Is checked?", isChecked);
        var checkboxes = $('#tblSignOffRequestUsers tbody .row-checkbox').prop('checked', isChecked);
        console.log("Checkboxes found: ", checkboxes.length);
    });

    $(document).on('change', '#tblSignOffRequestUsers .row-checkbox', function () {
        let total = $('#tblSignOffRequestUsers .row-checkbox').length;
        let checked = $('#tblSignOffRequestUsers .row-checkbox:checked').length;
        $('#selectAllCheckbox').prop('checked', total === checked);
    });

    // Open sign off request dialog
    $(document).on('click', '.signoff-lnk', function () {
        signoff_documentId = $(this).closest('tr').data('id').toString();
        signoff_iwbPdfFileName = $(this).closest('tr').data('iwbpdffilename').toString();
        signoff_iwbpdffilelink = $(this).closest('tr').data('iwbpdffilelink').toString();
        $('#divSignOffRequestDialog').mmsDialog("open");
    });

});

function checkUserPermission() {
    if ("permissions" in window) {
        // global variable v is defined
        $.each(permissions, function (key, item) {
            //console.log(key);
            //console.log(item);
            if (item.CssClassName != '')
                $('.' + item.CssClassName).removeClass('d-none');
        });
    } else {
        // global variable v is not defined
        console.log('permissions not exist');
    }

    $.unblockUI();
}

var stateCities = {
    "AL": ["Birmingham", "Montgomery", "Mobile", "Huntsville"],
    "AK": ["Anchorage", "Juneau", "Fairbanks", "Sitka"],
    "AZ": ["Phoenix", "Tucson", "Mesa", "Chandler"],
    "AR": ["Little Rock", "Fort Smith", "Fayetteville", "Springdale"],
    "CA": ["Los Angeles", "San Diego", "San Francisco", "Sacramento"],
    "CO": ["Denver", "Colorado Springs", "Aurora", "Fort Collins"],
    "CT": ["Bridgeport", "New Haven", "Hartford", "Stamford"],
    "DE": ["Wilmington", "Dover", "Newark", "Middletown"],
    "FL": ["Miami", "Orlando", "Tampa", "Jacksonville"],
    "GA": ["Atlanta", "Augusta", "Savannah", "Columbus"],
    "HI": ["Honolulu", "Hilo", "Kailua", "Kapolei"],
    "ID": ["Boise", "Meridian", "Nampa", "Idaho Falls"],
    "IL": ["Chicago", "Aurora", "Naperville", "Rockford"],
    "IN": ["Indianapolis", "Fort Wayne", "Evansville", "South Bend"],
    "IA": ["Des Moines", "Cedar Rapids", "Davenport", "Sioux City"],
    "KS": ["Wichita", "Overland Park", "Kansas City", "Topeka"],
    "KY": ["Louisville", "Lexington", "Bowling Green", "Owensboro"],
    "LA": ["New Orleans", "Baton Rouge", "Shreveport", "Lafayette"],
    "ME": ["Portland", "Lewiston", "Bangor", "Auburn"],
    "MD": ["Baltimore", "Frederick", "Gaithersburg", "Bowie"],
    "MA": ["Boston", "Worcester", "Springfield", "Lowell"],
    "MI": ["Detroit", "Grand Rapids", "Warren", "Sterling Heights"],
    "MN": ["Minneapolis", "Saint Paul", "Rochester", "Bloomington"],
    "MS": ["Jackson", "Gulfport", "Southaven", "Hattiesburg"],
    "MO": ["Kansas City", "St. Louis", "Springfield", "Columbia"],
    "MT": ["Billings", "Missoula", "Great Falls", "Bozeman"],
    "NE": ["Omaha", "Lincoln", "Bellevue", "Grand Island"],
    "NV": ["Las Vegas", "Henderson", "Reno", "North Las Vegas"],
    "NH": ["Manchester", "Nashua", "Concord", "Derry"],
    "NJ": ["Newark", "Jersey City", "Paterson", "Elizabeth"],
    "NM": ["Albuquerque", "Las Cruces", "Rio Rancho", "Santa Fe"],
    "NY": ["New York City", "Buffalo", "Rochester", "Yonkers"],
    "NC": ["Charlotte", "Raleigh", "Greensboro", "Durham"],
    "ND": ["Fargo", "Bismarck", "Grand Forks", "Minot"],
    "OH": ["Columbus", "Cleveland", "Cincinnati", "Toledo"],
    "OK": ["Oklahoma City", "Tulsa", "Norman", "Broken Arrow"],
    "OR": ["Portland", "Salem", "Eugene", "Gresham"],
    "PA": ["Philadelphia", "Pittsburgh", "Allentown", "Erie"],
    "RI": ["Providence", "Warwick", "Cranston", "Pawtucket"],
    "SC": ["Charleston", "Columbia", "North Charleston", "Mount Pleasant"],
    "SD": ["Sioux Falls", "Rapid City", "Aberdeen", "Brookings"],
    "TN": ["Nashville", "Memphis", "Knoxville", "Chattanooga"],
    "TX": ["Houston", "Dallas", "Austin", "San Antonio"],
    "UT": ["Salt Lake City", "West Valley City", "Provo", "West Jordan"],
    "VT": ["Burlington", "Essex", "South Burlington", "Rutland"],
    "VA": ["Virginia Beach", "Norfolk", "Chesapeake", "Richmond"],
    "WA": ["Seattle", "Spokane", "Tacoma", "Vancouver"],
    "WV": ["Charleston", "Huntington", "Morgantown", "Parkersburg"],
    "WI": ["Milwaukee", "Madison", "Green Bay", "Kenosha"],
    "WY": ["Cheyenne", "Casper", "Laramie", "Gillette"]
};

$(function () {
});

$(document).on('click', '.dash-view-all-jobs', function (e) {
    e.preventDefault();
    document.location.href = 'ViewJobs.aspx';
});
$(document).on('click', '.dash-view-all-tests', function (e) {
    e.preventDefault();
    document.location.href = 'ViewTests.aspx';
});

$(document).on('click', '#btnAddRecipient', function () {
    var email = $('#txtNewRecipient').val().trim();
    if (email != "") {
        if (isValidEmailAddress(email)) {
            var numberOfRecipients = $("#tblRecipient tbody tr").length;
            if (numberOfRecipients >= 0) {
                $("#tblRecipient").append($(GenerateParticipantHTML(email)));
                $('#txtNewRecipient').val('');
                $("#tblSysUsers tbody tr").each(function () {
                    $(this).show();
                });
            }
            else {
                toaster.error(langKeys[currentLanguage]['seMaxTwoRecipientAllowed']);
            }
        }
        else {
            toastr.error("<b>" + email + "</b> " + langKeys[currentLanguage]['lmNotAValidEmailAddress']);
        }
    }
    else {
        toastr.error(langKeys[currentLanguage]['seRecipientEmailEmpty']);

    }
});

$(document).on('click', '#tblSysUsers tbody tr input:checked', function (e) {
    $tr = $(this).closest('tr');
    $('#txtNewRecipient').val('');
    var numberOfRecipients = $("#tblRecipient tbody tr").length;
    if (numberOfRecipients >= 0) {
        $($tr).remove().appendTo("#tblRecipient");
    }
    /*else {
        showErrorDialog(langKeys[currentLanguage]['seMaxTwoRecipientAllowed']);
    }*/

    $("#tblSysUsers tbody tr").each(function () {
        $(this).show();
    });

    $(this).attr("checked", false);

    if ($("#tblRecipient tbody tr").length == 1)
        $("#tblRecipient tbody tr:first").attr('isEndUser', 'true');
});

$(document).on('click', '#tblSysUsers thead th input:checked', function (e) {
    $tr = $(this).closest('tr');
    $('#txtNewRecipient').val('');

    $("#tblSysUsers tbody tr").each(function () {
        var numberOfRecipients = $("#tblRecipient tbody tr").length;
        if ($(this).is(":visible")) {
            $(this).remove().appendTo("#tblRecipient");
            $('#txtNewRecipient').val('');
            $("#tblSysUsers tbody tr").each(function () {
                $(this).show();
            });
        }
        /*else {
            showErrorDialog(langKeys[currentLanguage]['seMaxTwoRecipientAllowed']);
            return false;
        }*/
    });

    $("#tblSysUsers tbody tr").each(function () {
        $(this).show();
    });
    $(this).attr("checked", false);
});

$(document).on('click', '#tblRecipient tbody tr input:checked', function (e) {
    $tr = $(this).closest('tr');
    if ($($tr).attr('isSystemUser') == "true" || $($tr).attr('isSystemUser') == "True") {
        if ($($tr).attr('isEndUser') == 'true')
            $($tr).css('background', '').removeAttr('isEndUser');
        $($tr).remove().appendTo("#tblSysUsers");
    }
    else {
        $($tr).remove();
    }

    $(this).attr("checked", false);
});

$(document).on('click', '#tblRecipient thead th input:checked', function (e) {
    $("#tblRecipient tbody tr").each(function () {
        if ($(this).attr('isSystemUser') == "true" || $(this).attr('isSystemUser') == "True") {
            if ($(this).attr('isEndUser') == 'true')
                $(this).css('background', '').removeAttr('isEndUser');
            $(this).remove().appendTo("#tblSysUsers");
        }
        else {
            $(this).remove();
        }
    });

    $(this).attr("checked", false);
});

$(document).on('keyup', '#txtNewRecipient', function () {
    searchSysUser($(this).val());
});

$(document).on('click', 'button.add-job', function () {
    loadUserControlAddJob();
});

$(document).on('click', 'a.add-new-job', function () {
    loadUserControlAddJob();
});

$(document).on('click', '.save-job', function () {
    saveJob();
});

$(document).on('click', '.apply-job', function (e) {
    e.preventDefault();
    var jobId = $(e.currentTarget).data('id');

    Swal.fire({
        title: "Are you sure?",
        text: "You want to apply for this job!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Apply!"
    }).then((result) => {
        if (result.isConfirmed) {
            applyForJob(jobId, loginUserId);
        }
    });
});

$(document).on('click', '.view-job-applicants', function (e) {
    e.preventDefault();
    var jobId = $(e.currentTarget).data('id');
    loadJobApplicants(jobId);
});

$(document).on('click', 'a.add-org', function (e) {
    //debugger
    e.preventDefault();
    var welderId = $(e.currentTarget).data('id');
    $('#bsAddModal .modal-title').html('Add New Organization');
    $('#bsAddModal .modal-body').html('<p>Please wait...</p>');

    $('#bsAddModal').modal('show');

    loadAddOrganization();

    $('#bsAddModal').on('shown.bs.modal', function () {
        $('#bsAddModal .modal-footer .save').addClass('save-org');
    });
    $('#bsAddModal').on('hidden.bs.modal', function () {
        $('#bsAddModal .modal-footer .save').removeClass('save-org');
        $('#bsAddModal .modal-body').html('');
    });

});
$(document).on('click', '.save-org', function () {
    saveOrganization();
});

$(document).on('click', 'a.view-locations', function (e) {
    e.preventDefault();
    var orgId = $(this).data('id');
    $('#hdnOrganizationId').val(orgId);
    loadLocationsListing(orgId);

});

$(document).on('click', 'a.add-location', function (e) {
    e.preventDefault();
    var orgId = $(e.currentTarget).data('id');
    var orgLocId = 0;//$('#hdnOrgLocId').val();

    $('#bsAddModal .modal-title').html('Add New Location');
    $('#bsAddModal .modal-body').html('<p>Please wait...</p>');
    $('#bsAddModal').modal('show');

    loadAddLocation(orgId, orgLocId);

    $('#bsAddModal').on('shown.bs.modal', function () {
        $('#bsAddModal .modal-footer .save').addClass('save-loc');
    });
    $('#bsAddModal').on('hidden.bs.modal', function () {
        $('#bsAddModal .modal-footer .save').removeClass('save-loc');
        $('#bsAddModal .modal-body').html('');
    });

});

$(document).on('click', 'a.edit-location', function (e) {
    e.preventDefault();
    var orgId = $(e.currentTarget).data('org-id');
    var orgLocId = $(e.currentTarget).data('id');

    $('#bsAddModal .modal-title').html('Edit Location');
    $('#bsAddModal .modal-body').html('<p>Please wait...</p>');
    $('#bsAddModal').modal('show');

    loadAddLocation(orgId, orgLocId);

    $('#bsAddModal').on('shown.bs.modal', function () {
        $('#bsAddModal .modal-footer .save').addClass('save-loc');
    });
    $('#bsAddModal').on('hidden.bs.modal', function () {
        $('#bsAddModal .modal-footer .save').removeClass('save-loc');
        $('#bsAddModal .modal-body').html('');
    });

});



$(document).on('click', '.save-loc', function () {
    var orgId = $('#bsAddModal #hdnOrganizationId').val();
    saveLocation(orgId);
});

$(document).on('click', 'a.add-doc', function (e) {
    e.preventDefault();
    loadAddDocument();
});

$(document).on('click', 'a.add-welder', function (e) {
    e.preventDefault();
    loadAddUser();
});

$(document).on('click', 'a.view-user-profile', function (e) {
    e.preventDefault();
    loadViewUser();
});

$(document).on('change', '.ddl-welders', function (e) {
    var currentValue = $(this).val();
    //debugger
    if (currentValue == '0')
        $('.view-job-history').addClass('d-none');
    else
        $('.view-job-history').removeClass('d-none');

    getJobs(currentValue);
});

$('#addJobModal').on('show.bs.modal', function () {
    // Clear previous values
    $('#ddlJobTitle').html('<option value="">Choose Job Title</option>');

    $.ajax({
        type: 'GET',
        url: iwburl,
        cache: false,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetJobTitles' },
        success: function (response) {
            console.log('GetJobTitles--', response);
            if (response.success && response.data) {
                var ddl = $('#ddlJobTitle');
                ddl.empty();
                ddl.append('<option value="">Choose Job Title</option>');
                $.each(response.data, function (i, item) {
                    ddl.append($('<option>', {
                        value: item.Id,
                        text: item.Title
                    }));
                });
            } else {
                alert("No job titles found.");
            }
        },
        error: function (xhr, status, error) {
            console.error('Error fetching job titles:', error);
        }
    });
});


//$(document).on('click', 'a.assign-job', function (e) {
//    e.preventDefault();
//    var welderId = $(e.currentTarget).data('id');
//    //loadUserControlAddJob(welderId);
//    loadUserControlAssignJob(welderId);
//});

$(document).on('click', 'a.assign-job', function (e) {
    e.preventDefault();
    var applicantId = $(this).data("id");
    $('#ddlJobTitle').attr("applicantid", applicantId);
    $('#addJobModal').modal('show');
});

function formatDate(date) {
    const d = new Date(date);
    const month = d.getMonth() + 1;
    const day = d.getDate();
    const year = d.getFullYear();

    return `${month < 10 ? '0' + month : month}/${day < 10 ? '0' + day : day}/${year}`;
}

$('#ddlJobTitle').on('change', function () {
    var selectedJobId = $(this).val();

    if (selectedJobId) {
        $.blockUI({ baseZ: 10000, message: '<h2><i class="fa fa-spinner fa-spin fa-5x fa-fw"></i><span class="sr-only">' + langKeys[currentLanguage]['Processing'] + '</h2>' });
        $.ajax({
            type: 'GET',
            url: iwburl,
            cache: false,
            contentType: 'application/json; charset=utf-8',
            dataType: 'json',
            data: { method: 'GetJobDetailsById', jobId: selectedJobId },
            success: function (response) {
                console.log('GetJobDetailsById--', response);
                if (response.success) {
                    const jobDetails = response.data;

                    const startDateFormatted = formatDate(jobDetails.StartDate);
                    const endDateFormatted = formatDate(jobDetails.EndDate);
                    console.log('startDateFormatted--', startDateFormatted);

                    $('#lblJobStartDate').html("Start Date : " + startDateFormatted);
                    $('#lblJobEndDate').html("End Date : " + endDateFormatted);
                    $('#lblJobLocations').html("Location : " + jobDetails.Location);
                    $('#lblJobWPS').html("WPS : " + jobDetails.RecordNumber.replaceAll(',', ', '));
                } else {
                    console.log('Failed to fetch job details');
                }

                $.unblockUI();
            },
            error: function (error) {
                $.unblockUI();
                console.error('Error fetching job details:', error);
            }
        });
    }
});


$('#addJobModal').on('hidden.bs.modal', function () {

    $('#ddlJobTitle').val('');
    $('#startDate').val('');
    $('#endDate').val('');
    $('#location').val('');
});

$(document).on('click', '#btnSearchOrg', function (e) {
    e.preventDefault();
    getOrganizations();
});
$(document).on('click', '#btnResetOrg', function (e) {
    e.preventDefault();
    $('#txtFilterOrgName').val('');
    $('#rbOrgType0').prop('checked', true);
    getOrganizations();
});

$(document).on('click', '#btnSearchWelder', function (e) {
    e.preventDefault();
    getWelders();
});
$(document).on('click', '#btnResetWelder', function (e) {
    e.preventDefault();
    $('#txtFilterWelderName').val('');
    $('#txtFilterStencilNo').val('');
    getWelders();
});

$(document).on('click', '#btnSearchDoc', function (e) {
    e.preventDefault();
    getDocuments();
});
$(document).on('click', '#btnResetDoc', function (e) {
    e.preventDefault();
    $('#txtFilterDocName').val('');
    $('#txtFilterAlias').val('');
    $('#rbFilterDocType0').prop('checked', true);
    getDocuments();
});

$(document).on('click', '#btnSearchJob', function (e) {
    e.preventDefault();
    getAllJobs();
});
$(document).on('click', '#btnResetDoc', function (e) {
    e.preventDefault();
    $('#txtFilterJobName').val('');
    $('#txtFilterWpsName').val('');
    getAllJobs();
});

$(document).on('click', '#btnSearchTest', function (e) {
    e.preventDefault();
    getTests();
});

$(document).on('click', '#btnResetTest', function (e) {
    e.preventDefault();
    $('#txtFilterScheduleName').val('');
    $('#txtFilterTestCode').val('');
    $('#ddlFilterTestStatus').val('');
    getTests();
});

$('#lnkManageOrganizations').click(function myfunction(e) {
    loadOrganizationsControl();
});

$(document).on('change', '#ddlState', function (e) {
    //debugger
    //console.log(this.value);
    var state = this.value; // Get selected state

    var $selectCity = $('#ddlCity');
    $selectCity.find('option').remove();
    $selectCity.append('<option value="0">Choose</option>');
    $.each(stateCities[state], function (key, item) {
        $selectCity.append('<option value=' + item + '>' + item + '</option>');
    });

    $selectCity.select2({
        placeholder: 'Choose',
        theme: 'bootstrap-5',
        width: '100%',
        //dropdownParent: $('#divDialogContents')
    });
});


$(document).on('click', 'a.view-user-jobs', function (e) {
    e.preventDefault();
    var welderId = $(e.currentTarget).data('id');
    loadUserJobs(welderId);
});

$(document).on('click', 'a.job-status-update', function (e) {
    e.preventDefault();
    var jobId = $(e.currentTarget).data('id');
    var statusId = 3;
    updateJobStatus(jobId, statusId);
});

$(document).on('click', 'a.view-work-history', function (e) {
    e.preventDefault();
    var welderId = $(e.currentTarget).data('id');//$('.ddl-welders option:selected').val();
    loadWorkHistory(welderId);
    console.log('welderId-->', welderId)
});

$(document).on('click', 'a.view-ratings', function (e) {
    debugger

    e.preventDefault();
    var welderId = $(this).data('id');
    $('#viewWelderRatingsPopup').modal('show');
    loadWelderRatings(welderId);
});


$(document).on('click', '.save-doc', function () {
    saveDocument();
});

$(document).on('click', '#lnkRemoveDocument', function (e) {
    docDropzone.removeAllFiles();
});

$(document).on('click', '#lnkRemoveProfileImg', function (e) {
    //imgDropzone.removeAllFiles();

    Swal.fire({
        title: "Are you sure?",
        text: "You want to delete the profile picture",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Delete Image!"
    }).then((result) => {
        if (result.isConfirmed) {
            console.log('remove picture');
            removeUserImg();
        }
    });

});

$(document).on('click', 'a.add-test', function () {
    loadAddTest();
});

$(document).on('click', 'a.view-test-attendees', function (e) {
    e.preventDefault();
    var testId = $(this).data('id');
    console.log('testId => ', testId);
    var currentStatus = $(this).attr("status");

    loadTestAttendees(testId, currentStatus);
});

$(document).on('click', 'a.test-invitation', function (e) {
    e.preventDefault();
    var testId = $(this).data('id');
    $.blockUI({ baseZ: 10000, message: '<h2><i class="fa fa-spinner fa-spin fa-5x fa-fw"></i><span class="sr-only">' + langKeys[currentLanguage]['Processing'] + '</h2>' });
    loadTestInvitation(testId);
});



$(document).on('click', 'a.view-sign-off', function (e) {
    e.preventDefault();
    var documentId = $(this).data('id');
    updateViewSignOffPopup(true, documentId);
});

$(document).on('click', 'a.edit-doc', function (e) {
    e.preventDefault();
    var documentId = $(this).data('id');
    updateEditDocumentPopup(true, documentId);
});

function updateEditDocumentPopup(isShow, documentId) {
    $("#editDocumentModal").modal(isShow ? "show" : "hide");
    $('#edit-validation-summary').html('');
    if (isShow) {
        loadDocumentForEdit(documentId);
    }
}

function loadDocumentForEdit(documentId) {
    $.ajax({
        type: 'GET',
        url: iwburl,
        cache: false,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetDocumentById', documentId: documentId },
        success: function (response) {
            console.log('loadDocumentForEdit --> ', response);
            let doc = response.Document;
            console.log('Document value-->', doc);

            $('#hdnDocumentId').val(doc.Id);
            $('#edittxtName').val(doc.Name);
            $('#edittxtDescription').val(doc.Description);
            //$('#txtFileName').val(doc.FileName);

            $('input[name="rbDoc"][value="' + doc.Type + '"]').prop('checked', true);
        },
        error: function () {
            alert("Failed to load document details.");
        }
    });
}

$(document).on('click', 'a.upload-wpq', function (e) {
    e.preventDefault();
    var welderId = $(e.currentTarget).data('id');
    console.log('welderId-->', welderId)
    $("#uploadWPQModal").modal("show");

    // Reset modal fields
    $('#selectJob').val('');
    $('input[name="uploadType"]').prop('checked', false);
    $('#uploadFields').addClass('d-none');
    $('#wpqFile').val('');
    $('#txtJobEndDate').val('');
    $('#userIdHidden').val(welderId);
    loadJobsByWelder(welderId)
});

$('input[name="uploadType"]').on('change', function () {
    if ($(this).val() === "upload") {
        $('#uploadFields').removeClass('d-none');
    } else {
        $('#uploadFields').addClass('d-none');
    }
});

$('#btnContinueUpload').on('click', function () {
    var selectedType = $('input[name="uploadType"]:checked').val();
    if (selectedType === "upload") {
        alert('Proceeding with upload.');
    } else if (selectedType === "manual") {
        alert('Proceeding with manual.');
    } else {
        alert('Please select upload type.');
    }
});

function validateUploadWpq() {
    $('.uploadwpq-validation-summary').html('');
    var validationResult = {
        IsValid: true,
        Messagesmarkup: $("<ul></ul>")
    };

    var selectedJob = $('#selectJob').val();
    var uploadType = $("input[name='uploadType']:checked").val();

    if (selectedJob === "") {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please select a job.</li>");
    }

    if (!uploadType) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please select an upload type.</li>");
    }

    if (!validationResult.IsValid) {
        $('.uploadwpq-validation-summary').html(validationResult.Messagesmarkup);
    }

    return validationResult.IsValid;
}


$(function () {
    $('#txtExpDate').datepicker({
        dateFormat: 'dd-mm-yy',
        minDate: 0,
        changeMonth: true,
        changeYear: true,
        beforeShow: function (input, inst) {
            // Move datepicker to body to avoid z-index issues
            setTimeout(function () {

                inst.dpDiv.css('z-index', 2000).appendTo('body');

            }, 0);
        }
    });

    // Open datepicker only when clicking calendar icon
    $('#expDateCalendarIcon').click(function () {
        $('#txtExpDate').datepicker('show');
    });

    // Optional: prevent manual typing in input
    $('#txtExpDate').on('keydown paste', function (e) {
        e.preventDefault();
    });
});


$('#btnUploadWpq').click(function () {
    if (validateUploadWpq()) {
        uploadWpq();
    }
});


$('#closeModalBtn').on('click', function () {
    $('#wpqFile').val('');
    $('#txtExpDate').val('');
    $('#selectJob').val('');
    $('#userIdHidden').val('');

    $("#uploadWPQModal").modal("hide");
});


function loadJobsByWelder(welderId) {
    console.log('welderId', welderId);
    $.ajax({
        type: "GET",
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=GetJobsByWelder&welderId=' + welderId,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (response) {
            console.log('loadJobsByWelder resp--', response);

            $('#selectJob').empty();
            $('#selectJob').append('<option value="">Select Job</option>');

            if (Array.isArray(response.jobs)) {
                response.jobs.forEach(function (job) {
                    $('#selectJob').append('<option value="' + job.Id + '">' + job.Name + '</option>');
                });
            } else {
                console.error("Jobs list is missing or not in array format.");
            }
        },
        error: function (err) {
            console.error("Error loading jobs: ", err);
        }
    });
}



$(document).on('click', '.apply-for-test', function (e) {
    e.preventDefault();
    var testId = $(e.currentTarget).data('id');
    var welderId = loginUserId;
    Swal.fire({
        title: "Are you sure?",
        text: "You want to apply for this test!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Register me!"
    }).then((result) => {
        if (result.isConfirmed) {
            registerWelderForTest(testId, welderId);
        }
    });
});

$(document).on('click', '.apply-test', function (e) {
    e.preventDefault();
    var testId = $(e.currentTarget).data('id');
    var welderId = loginUserId;
    Swal.fire({
        title: "Are you sure?",
        text: "You want to apply for this test!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Apply now!"
    }).then((result) => {
        if (result.isConfirmed) {
            registerWelderForTest(testId, welderId);
        }
    });
});

function loadOrganizationsControl(userId) {
    //debugger
    $.ajax({
        type: 'POST',
        url: 'Default.aspx/LoadUserControlOrganization',
        data: JSON.stringify({ userId: userId }),
        contentType: 'application/json; charset=utf-8',
        dataType: 'JSON',
        success: function (response) {

            $('#divDialogContents').html(response.d);
            $('#divDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Organizations',
                maxwidth: 'auto',
                width: '80%',
                height: 600,
                open: function (event, ui) {
                    getOrganizations(userId);
                },
                close: function () {
                    $('#divDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });
            $('#divDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}


function getWps() {
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetWpss' },
        success: function (response) {
            wpss = response.data.map(function (item) {
                //return { id: item.Id, text: item.WeldingStandard };
                //return { id: item.Id, text: item.Name };
                return { id: item.Id, text: item.RecordNo };
            });
            loadWps();
        }
    });
}
function loadWps() {
    var $select = $('.ddl-wps');
    $select.find('option').remove();
    $select.append('<option value="" disabled>Choose</option>');
    $.each(wpss, function (key, item) {
        $select.append('<option value=' + item.id + '>' + item.text + '</option>');
    });

    $select.select2({
        placeholder: 'Choose',
        theme: 'bootstrap-5',
        width: '100%',
        dropdownParent: $('.ui-new-dialog-widget-content'),
        multiple: true
    });
}

function saveWps() {
    var wps = {
        FileName: $('#').val(),
        WeldingStandard: $('#').text(),
        RecordNo: $('#').text(),
        DateQualified: $('#').val(),
        CompanyName: $('#').text(),
        Comments: $('#').text()
    };
    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { wps: JSON.stringify(wps), method: 'SaveWps' },
            success: function (response) {
                //var res = JSON.parse(response);
                switch (response.success) {
                    case true:
                        //updateView(response.data);
                        $.growlUI(null, langKeys[currentLanguage]['sSave']);
                        break;
                    default:
                        $.unblockUI();
                        showErrorDialog(response.message);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred saveWps(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        showExceptionDialog(e.message);
    }
}



///Jobs

function getWeldersDDL() {
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetWelders' },
        success: function (response) {
            weldersDDL = response.data.map(function (item) {
                return { id: item.UserNum, text: item.UserName };
            });
            loadWelders();
        }
    });
}

function loadWelders() {
    var $select = $('.ddl-welders');
    $select.find('option').remove();
    $select.append('<option value="0">Choose Welder</option>');
    $.each(weldersDDL, function (key, item) {
        $select.append('<option value=' + item.id + '>' + item.text + '</option>');
    });
    $select.select2({
        width: '100%'
    });
}

function loadUserControlAddJob(userId) {
    //debugger
    $.ajax({
        type: 'POST',
        url: "ManageJobs.aspx/LoadUserControlAddNew",
        data: JSON.stringify({ userControlId: userId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divDialogContents').html(response.d);
            loadWps();

            $('#divDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Add Job',
                width: 500,
                maxwidth: 'auto',
                open: function (event, ui) {
                    getWps();
                    $('#hdnWelderId').val(userId);
                },
                close: function () {
                    $('#divDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        saveJob();
                    }
                },
                {
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divDialogContents').mmsDialog('open');

        },
        error: function (error) {
            console.log(error);
        }
    });
}

function loadUserControlAssignJob(userId) {
    //debugger
    $.ajax({
        type: 'POST',
        url: "ManageWelders.aspx/LoadUserControlAssignJob",
        data: JSON.stringify({ userControlId: userId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divDialogContents').html(response.d);
            //loadJobs();

            $('#divDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Assign Job',
                width: 300,
                maxwidth: 'auto',
                open: function (event, ui) {
                    //getWps();
                    $('#hdnWelderId').val(userId);
                },
                close: function () {
                    $('#divDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        debugger
                        var jobId = $(".ddl-jobs :selected").val();
                        assignJobToWelder(userId, jobId);
                    }
                },
                {
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divDialogContents').mmsDialog('open');

        },
        error: function (error) {
            console.log(error);
        }
    });
}

function createJobsTable() {
    $('#tblJobs').dataTable({
        data: jobs,
        destroy: true,
        responsive: true,
        ordering: false,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: false,
        responsive: true,
        //order: [[1, "asc"]],
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.Id);
        },
        initComplete: (settings, json) => {
            console.log('initComplete');
            //var api = this.api();
            var api = new $.fn.dataTable.Api(settings);
            if (loginUserType === 8) {
                // Hide Office column
                api.column(5).visible(false);
            }
        },
        columns: [
            {
                data: '#', sortable: false, width: "5%",
                render: function (data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'Title', name: 'title', width: '30%' },
            { data: 'StartDate', width: '15%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            { data: 'EndDate', width: '15%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            //{ data: 'WPS', width: '15%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            //{ data: 'CompletedDate', width: '20%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            {
                data: 'CurrentStatus', defaultContent: 'N/A', width: '10%',
                render: function (data, type, row) {
                    var className = 'bg-danger ';
                    switch (data) {
                        case 'Initiated':
                        default:
                            className = 'bg-danger ';
                            break;
                        case 'In Progress':
                            className = 'bg-info ';
                            break;
                        case 'Completed':
                            className = 'bg-success ';
                            break;
                    }
                    return '<span class="badge ' + className + ' p-2 px-3 rounded">' + data + '</span>';
                }
            },
            {
                data: 'Id',
                title: 'Action(s)',
                width: '25%',
                class: loginUserType === 8 ? 'd-none' : '',
                render: function (data, type, row, meta) {
                    var className = '';//loginUserType === 8 ? 'd-none' : 'd-inline';
                    return '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm job-status-update me-1 ' + className + '"><i class="fa fa-check me-1"></i>Mark as Complete</a>';
                },
                /*createdCell: function (td, cellData, rowData, row, col) {
                    debugger
                    var className = loginUserType === 8 ? 'd-none' : 'd-inline';
                    //$(td).addClass(className);
                    $(col).addClass(className);
                    //if (cellData < 0)
                    //$(td).text(cellData.substr(1));
                },
                visible: loginUserType === 8 ? true : false*/
            }
        ],
        language: {
            paginate: {
                previous: 'Prev',
            },
            //processing: "Loading Data...",
            zeroRecords: "No matching records found"
        },
    });

}

function getJobs(welderId) {
    //debugger
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetJobs', welderId: welderId },
        success: function (response) {
            jobs = response.data;
            createJobsTable();

        }
    });
}

function getAllJobs() {
    $('#tblJobs').dataTable({
        destroy: true,
        processing: true,
        serverSide: true,
        ordering: false,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: true,
        pageLength: pageSize,
        responsive: true,
        //order: [[1, "asc"]],
        ajax: {
            url: iwburl,
            type: 'GET',
            datatype: 'json',
            headers: {
                'Content-Type': 'application/json',
            },
            data: function (data, settings) {
                var api = new $.fn.dataTable.Api(settings);
                var pageIndex = api === undefined ? 1 : api.page.info().page + 1;
                var jobName = $('#txtFilterJobName').val();
                var jobWps = $('#txtFilterWpsName').val();
                console.log('Serch filter param check for manage jobs - > ', jobName, jobWps);
                return { method: 'GetJobs', pageIndex: pageIndex, pageSize: pageSize, jobName: jobName, jobWps: jobWps };
            },
            //error: function (jqXHR, textStatus, errorThrown) { }
            dataSrc: function (json) {
                var totalRows = json.rowCount;
                json.recordsTotal = totalRows;
                json.recordsFiltered = totalRows;
                return json.data;
            }
        },
        initComplete: (settings, json) => {
            //checkUserPermission();
            initializeDataTableTooltips();
        },
        drawCallback: function (settings) {
            //checkUserPermission();
            initializeDataTableTooltips();
        },
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.Id);
        },
        columns: [
            {
                data: '#', sortable: false, width: "5%",
                render: function (data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'Title', name: 'title', width: '20%', sortable: false },
            { data: 'StartDate', width: '10%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            { data: 'EndDate', width: '10%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            { data: 'WpsName', width: '15%', defaultContent: 'N/A', },
            { data: 'Location', width: '5%', defaultContent: 'N/A', },
            { data: 'NoOfApplicants', defaultContent: '0', width: '5%' },
            {
                data: 'CurrentStatus', defaultContent: 'N/A', width: '10%',
                render: function (data, type, row) {
                    var className = 'bg-danger ';
                    switch (data) {
                        case 'Initiated':
                        default:
                            className = 'bg-danger ';
                            break;
                        case 'In Progress':
                            className = 'bg-info ';
                            break;
                        case 'Completed':
                            className = 'bg-success ';
                            break;
                    }
                    return '<span class="badge ' + className + ' p-2 px-3 rounded">' + data + '</span>';
                }
            },
            {
                data: 'Id',
                title: 'Action(s)',
                width: '20%',
                class: loginUserType === 8 ? 'd-none' : '',
                render: function (data, type, row, meta) {
                    var className = '';
                    var finishButton = "";
                    var viewButton = '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm view-job-applicants me-1 ' + className + '"><i class="fa fa-user me-1"></i>View Applicants</a>';
                    if (row.CurrentStatus != "Completed") {
                        finishButton = '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm me-1 job-status-update ' + className + '"><i class="fa fa-check me-1"></i>Mark as Complete</a>';
                    }
                    return viewButton + "&nbsp;" + finishButton;
                }
            }
        ],
        language: {
            paginate: {
                previous: 'Prev',
                next: 'Next',
            },
            //processing: "Loading Data...",
            zeroRecords: "No matching records found"
        },

    });
    initializeDataTableTooltips();
}



function saveJob() {
    var job = {
        Title: $('#txtJobTitle').val(),
        StartDate: $('#txtJobStartDate').val(),
        EndDate: $('#txtJobEndDate').val(),
        WPSData: "," + $('#ddlWPS').val().join(",") + ",",
        CurrentStatus: 1,
        PerformerId: 0,
        NoOfPositions: $('#txtNoOfPositions').val(),
        Location: $('#txtLocation').val()
    };

    var validationResult = validateJob(job);
    if (!validationResult.IsValid) {
        $('.validation-summary').append(validationResult.Messagesmarkup);
        return;
    }
    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { job: JSON.stringify(job), method: 'SaveJob' },
            success: function (response) {
                response = JSON.parse(response);

                switch (response.success) {
                    case true:
                        toastr.success('Job saved successfully.');
                        $('#divDialogContents').mmsDialog('close');
                        getAllJobs();
                        break;
                    default:
                        $.unblockUI();
                        toastr.error(response.message);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                toastr.error('Error occurred.');
                console.log('Error occurred saveJob(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        showExceptionDialog(e.message);
    }
}
function validateJob(job) {
    $('.validation-summary').html('');
    try {
        var validationResult = {
            IsValid: true,
            Messagesmarkup: $("<ul></ul>")
        };

        debugger
        if (job.Title.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter job title.</li>");
        }
        if (job.Title.length > 99) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Job title should not exceed 100 characters.</li>");
        }

        if (job.StartDate.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter job start date.</li>");
        }

        if (job.EndDate.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter job end date.</li>");
        }

        if (job.NoOfPositions == "" || job.NoOfPositions == "0") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter number of positions.</li>");
        }

        if (job.Location.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter location.</li>");
        }

        if (job.WpsId == 0 || job.WpsId == '0' || job.WpsId == '') {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please choose wps.</li>");
        }

        /*if (job.PerformerId == 0 || job.PerformerId == '0' || job.PerformerId == '') {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Job can't be assigned without welder.</li>");
        }*/


        return validationResult;
    }
    catch (e) {
        console.error('Error in validateJob : ' + e);
    }
}

function assignJobToWelder(welderId, jobId) {
    if (jobId == 0 || jobId == '0' || jobId == '') {
        $('.validation-summary').append("<li>Please choose job.</li>");
        return;
    }
    debugger
    try {
        $.blockUI();
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { method: 'AssignJob', jobId: jobId, userId: welderId },
            success: function (response) {
                $.unblockUI();
                debugger
                response = JSON.parse(response);
                switch (response.success) {
                    case true:
                        toastr.success('Job assigned successfully.');
                        $('#divDialogContents').mmsDialog('close');
                        break;
                    default:
                        toastr.error(response.message);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                toastr.error('Error occurred.');
                console.log('Error occurred assignJobToWelder(): ' + jqXHR.responseText);
            }
        });
    } catch (e) {
        console.log(e.message);
        //showExceptionDialog(e.message);
    }
}


function viewAllJobs() {
    $('#tblViewJobs').dataTable({
        destroy: true,
        processing: true,
        serverSide: true,
        ordering: true, //false,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: true,
        pageLength: pageSize,
        responsive: true,
        //order: [[1, "asc"]],
        ajax: {
            url: iwburl,
            type: 'GET',
            datatype: 'json',
            headers: {
                'Content-Type': 'application/json',
            },
            data: function (data, settings) {
                var api = new $.fn.dataTable.Api(settings);
                var pageIndex = api === undefined ? 1 : api.page.info().page + 1;
                var jobName = $('#txtFilterJobName').val();
                var jobWps = $('#txtFilterWpsName').val();
                return { method: 'GetJobs', pageIndex: pageIndex, pageSize: 10, jobName: jobName, jobWps: jobWps };
            },
            //error: function (jqXHR, textStatus, errorThrown) { }
            dataSrc: function (json) {
                var totalRows = json.rowCount;
                json.recordsTotal = totalRows;
                json.recordsFiltered = totalRows;
                return json.data;
            }
        },
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.Id);
        },
        columns: [
            {
                data: '#', sortable: false, width: "5%",
                render: function (data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'Title', name: 'title', width: '20%' },
            { data: 'StartDate', width: '15%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            { data: 'EndDate', width: '15%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            { data: 'WPS', width: '15%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
            {
                data: 'CurrentStatus', defaultContent: 'N/A', width: '10%',
                render: function (data, type, row) {
                    var className = 'bg-danger ';
                    switch (data) {
                        case 'Initiated':
                        default:
                            className = 'bg-success ';
                            data = 'Open';
                            break;
                        case 'In Progress':
                            className = 'bg-info ';
                            break;
                        case 'Completed':
                            className = 'bg-danger ';
                            data = 'Expired';
                            break;
                    }
                    return '<span class="badge ' + className + ' p-2 px-3 rounded">' + data + '</span>';
                }
            },
            {
                data: 'Id',
                title: 'Action(s)',
                width: '20%',
                render: function (data, type, row, meta) {
                    return row.CurrentStatus === 'Initiated' ? '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm me-1 apply-job"><i class="fa-solid fa-file-pen me-1"></i>Apply</a>' : '';
                }
            }
        ], language: {
            paginate: {
                previous: 'Prev',
            },
            processing: "Loading Data...",
            zeroRecords: "No matching records found"
        },

    });
}

function loadUserJobs(userId) {
    //debugger
    $.ajax({
        type: 'POST',
        url: "ManageWelders.aspx/LoadUserControlJobs",
        data: JSON.stringify({ userId: userId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            //debugger
            $('#divDialogContents').html(response.d);
            $('#divDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Jobs',
                maxwidth: 'auto',
                width: '70%',
                open: function (event, ui) {
                    getJobs(userId);
                },
                close: function () {
                    $('#divDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });
            $('#divDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}
function updateJobStatus(jobId, statusId) {
    $.ajax({
        type: 'POST',
        url: iwburl,
        //contentType: 'application/json; charset=utf-8',
        //dataType: 'json',
        data: { method: 'UpdateJobStatus', jobId: jobId, statusId: statusId },
        success: function (response) {
            //debugger;
            response = JSON.parse(response);
            switch (response.success) {
                case true:
                    //$.pnotify({ title: 'Job', text: 'Job updated successfully.', opacity: 1, type: "info", delay: pageNotificationTimeout });
                    toastr.success('Job updated successfully.');
                    try {
                        $('#0divAddDialogContents').mmsDialog("close");
                    } catch (e) {
                        console.log("updateJobStatus => Dialog execption", e.message);
                    }
                    redirectWpqForm(jobId, true);
                    break;
                default:
                    $.unblockUI();
                    toastr.error('Fail to updated job status.');
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred updateJobStatus(): ' + jqXHR.responseText);
        }
    });
}

function assignJob() {
    const jobId = $('#ddlJobTitle').val();

    if (!jobId) {
        alert('Please select a job title before assigning.');
        return;
    }

    const applicantId = parseInt($('#ddlJobTitle').attr("applicantid"));
    applyForJob(parseInt(jobId), applicantId);
}

function applyForJob(jobId, applicantId) {
    var jobApplicant = {
        JobId: jobId,
        ApplicantId: applicantId,
        CurrentStatus: 1
    };

    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { method: 'ApplyForJob', jobApplicant: JSON.stringify(jobApplicant) },
            success: function (response) {
                response = JSON.parse(response);
                switch (response.success) {
                    case true:
                        Swal.fire({
                            icon: "success",
                            title: "You have successfully applied for a job."
                        }).then((result) => {
                            window.top.location.href = window.top.location.href;
                        });
                        break;
                    default:
                        $.unblockUI();
                        toastr.error(response.message);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred applyForJob(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        console.log(e.message);
    }
}


function loadJobApplicants(jobId) {
    $.ajax({
        type: 'POST',
        url: 'ManageJobs.aspx/LoadUserControlViewApplicants',
        data: JSON.stringify({ userId: 1000 }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divDialogContents').html(response.d);
            $('#divDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Job Applicants',
                maxwidth: 'auto',
                width: '70%',
                open: function (event, ui) {
                    getJobApplicants(jobId);
                },
                close: function () {
                    $('#divDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });
            $('#divDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function getJobApplicants(jobId) {
    //debugger
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetJobApplicants', jobId: jobId },
        success: function (response) {
            var jobApplicants = response.data;

            $('#tblJobApplicants').dataTable({
                data: jobApplicants,
                destroy: true,
                responsive: true,
                ordering: false,
                searching: false,
                autoWidth: false,
                lengthChange: false,
                paging: false,
                responsive: true,
                //order: [[1, "asc"]],
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('data-id', data.Id);
                },
                columns: [
                    {
                        data: '#', sortable: false, width: "5%",
                        render: function (data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    { data: 'User.UserName', width: '20%' },
                    { data: 'User.UserEmail', width: '15%' },
                    { data: 'User.SocialSecurityNumber', width: '15%' },
                    { data: 'User.WelderStencilNumber', width: '15%' },
                    { data: 'ApplyDate', defaultContent: 'N/A', width: '15%', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
                    {
                        data: 'id',
                        title: 'Assign',
                        width: '15%',
                        render: function (data, type, row, meta) {
                            if (row.CurrentStatus == "Applied") {
                                let encodedData = encodeURIComponent(JSON.stringify(row));
                                let acceptHtml = '<a data-id="' + data + '" href="javascript:void(0);" class="btn btn-sm" onclick="updateApplicantJobStatus(2, this, JSON.parse(decodeURIComponent(\'' + encodedData + '\')));"><i class="fa-brands fa-hire-a-helper me-2"></i>Hire</a>';
                                let rejectHtml = '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-smr" onclick="updateApplicantJobStatus(3,this,JSON.parse(decodeURIComponent(\'' + encodedData + '\')));"><i class="fa-solid fa-circle-xmark  me-2"></i>Reject</a>';
                                return acceptHtml + rejectHtml;
                            }
                            else {
                                return row.CurrentStatus;
                            }
                        }
                    }
                ],
                language: {
                    paginate: {
                        previous: 'Prev',
                    },
                    processing: "Loading Data...",
                    zeroRecords: "No matching records found"
                },
            });

        }
    });
}




function loadWorkHistory(userId) {
    $.ajax({
        type: 'POST',
        url: 'ManageWelders.aspx/LoadUserControlWorkHistory',

        data: JSON.stringify({ userId: 1000 }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divDialogContents').html(response.d);
            $('#divDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Work History',
                maxwidth: 'auto',
                width: '70%',
                open: function (event, ui) {
                    getWorkHistory(userId);
                },
                close: function () {
                    $('#divDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function getWorkHistory(userId) {
    debugger
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetWorkHistory', userId: userId },
        success: function (response) {
            var user = response.user;
            var workHistories = response.workHistories;

            $('.div-details #txtUserName').val(user.UserName);
            $('#txtStencilNumber').val(user.WelderStencilNumber);
            $('#txtRating').val('1');
            $('#txtCityState').val(user.City + ', ' + user.State);
            $('#txtSSN').val(user.SocialSecurityNumber);


            $('#tblWorkHistory').dataTable({
                data: workHistories,
                destroy: true,
                responsive: true,
                ordering: false,
                searching: false,
                autoWidth: false,
                lengthChange: false,
                paging: false,
                responsive: true,
                //order: [[1, "asc"]],
                createdRow: function (row, data, dataIndex) {
                    console.log('getWorkHistory click view--', row.FileName);
                    $(row).attr('data-id', data.Id);
                },
                columns: [
                    {
                        data: '#', sortable: false, width: "5%",
                        render: function (data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    { data: 'OrganizationName', width: '35%', defaultContent: 'N/A', render: function (data, type, row) { return (data === '') ? 'N/A' : data; } },
                    { data: 'FromDate', defaultContent: 'N/A', width: '20%', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
                    { data: 'ToDate', defaultContent: 'N/A', width: '20%', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
                    { data: 'Rating', name: 'address', width: '10%' },
                    {
                        data: 'Id',
                        title: 'WPQ',
                        width: '10%',
                        render: function (data, type, row, meta) {
                            const pdfUrl = siteBaseUrl + 'SystemUploadedFiles/Iwb/OutputReports/' + row.FileName;
                            return '<a output-file="' + pdfUrl + '" href="javascript:void(0);" ' +
                                'class="btn btn-sm btn-secondary view-wpq me-1" onclick="showPdfFile(this);" ' +
                                'title="Click to view WPQ PDF">' +
                                '<i class="fa fa-file-pdf me-1"></i>View</a>';
                        }
                    }
                ],
                language: {
                    paginate: {
                        previous: 'Prev',
                    },
                    processing: "Loading Data...",
                    zeroRecords: "No matching records found"
                },
            });

        }
    });
}


//Test Schedule

function loadAddTest() {
    //debugger
    $.ajax({
        type: 'POST',
        url: 'ManageTests.aspx/LoadUserControlAddNew',
        data: JSON.stringify({ userId: 1000 }),
        contentType: 'application/json; charset=utf-8',
        dataType: 'JSON',
        success: function (response) {
            $('#divAddDialogContents').html(response.d);
            $('#divAddDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Add New Test Schedule',
                maxwidth: 'auto',
                width: '40%',
                open: function (event, ui) {
                    getTestTypes();
                },
                close: function () {
                    $('#divAddDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        saveTest();
                    }
                },
                {
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divAddDialogContents').mmsDialog('open');

        },
        error: function (error) {
            console.log(error);
        }
    });
}

function getTestTypes() {
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetTestTypes' },
        success: function (response) {
            var tests = response.data.map(function (item) {
                return { id: item.Id, text: item.CostCode };
            });

            var $select = $('.ddl-TestType');
            $select.find('option').remove();
            $select.append('<option value="0">Choose</option>');
            $.each(tests, function (key, item) {
                $select.append('<option value=' + item.id + '>' + item.text + '</option>');
            });

            $select.select2({
                placeholder: 'Choose',
                theme: 'bootstrap-5',
                width: '100%',
                dropdownParent: $('.ui-new-dialog-widget-content')
            });

        }
    });
}

function validateTest(test) {
    $('.validation-summary').html('');
    try {
        var validationResult = {
            IsValid: true,
            Messagesmarkup: $("<ul></ul>")
        };

        if (test.Name.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter test name.</li>");
        }
        if (test.StartDate.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter start date.</li>");
        }

        if (test.NoOfPositions == "" || test.NoOfPositions == "0") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter number of positions.</li>");
        }
        if (test.Deadline.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter deadline date.</li>");
        }
        if (test.TypeId == '0') {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please choose test type.</li>");
        }

        return validationResult;
    }
    catch (e) {
        console.error('Error in validateTest : ' + e);
    }
}

function saveTest() {
    var test = {
        Name: $('#txtScheduleTitle').val(),
        StartDate: $('#txtScheduleDate').val(),
        Deadline: $('#txtDeadlineDate').val(),
        NoOfPositions: $('#txtNoOfPositions').val(),
        TypeId: $("#ddlTestType :selected").val(),
        Description: $('#txtDescription').val()
    };
    var validationResult = validateTest(test);
    if (!validationResult.IsValid) {
        $('.validation-summary').append(validationResult.Messagesmarkup);
        return;
    }
    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { method: 'SaveTest', test: JSON.stringify(test) },
            success: function (response) {
                var response = JSON.parse(response);
                debugger;
                switch (response.success) {
                    case true:
                        toastr.success('Test saved successfully.');
                        $('#divAddDialogContents').mmsDialog("close");
                        getTests();
                        break;
                    default:
                        $.unblockUI();
                        showErrorDialog(response.data);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred saveTest(): ' + jqXHR.responseText);
            }
        });
    } catch (e) {
        showExceptionDialog(e.message);
    }
}

function getTests() {
    docTable = $('#tblTests').dataTable({
        destroy: true,
        processing: true,
        serverSide: true,
        ordering: false,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: true,
        pageLength: pageSize,
        responsive: true,
        //order: [[1, "asc"]],
        ajax: {
            url: iwburl,
            type: 'GET',
            datatype: 'json',
            headers: {
                'Content-Type': 'application/json',
            },
            data: function (data, settings) {
                var api = new $.fn.dataTable.Api(settings);
                var pageIndex = api === undefined ? 1 : api.page.info().page + 1;
                var testName = $('#txtFilterScheduleName').val();
                var testTypeId = $('#txtFilterTestCode').val();
                var schStatus = $('#ddlFilterTestStatus').val();
                console.log("Selected Test Status:", schStatus);
                console.log('Get Test Search input -> ', testName, testTypeId, schStatus);
                return { method: 'GetTests', pageIndex: pageIndex, pageSize: pageSize, testName: testName, testTypeId: testTypeId, schStatus: schStatus };
            },
            //error: function (jqXHR, textStatus, errorThrown) { }
            dataSrc: function (json) {
                var totalRows = json.rowCount;
                json.recordsTotal = totalRows;
                json.recordsFiltered = totalRows;
                return json.data;
            }
        },
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.Id);
        },
        columns: [
            {
                data: '#', sortable: false, width: "3%",
                render: function (data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'Name', width: '20%' },
            {
                data: 'StartDate',
                width: '9%',
                defaultContent: 'N/A',
                render: function (data, type, row) {
                    return moment(data).format('DD/MMM/YYYY'); //moment(data).format('DD/MMM/YYYY HH:MM A');
                }
            },
            {
                data: 'TypeCode',
                width: '10%',
                defaultContent: 'N/A'
            },
            { data: 'NoOfPositions', width: '10%', defaultContent: '0' },
            {
                data: 'Deadline',
                width: '9%',
                defaultContent: 'N/A',
                render: function (data, type, row) {
                    return moment(data).format('DD/MMM/YYYY');//moment(data).format('DD/MMM/YYYY HH:MM A');
                }
            },
            { data: 'NoOfApplicants', defaultContent: '0', width: '5%' },
            {
                data: 'CurrentStatus', defaultContent: 'N/A', width: '10%',
                render: function (data, type, row) {
                    var className = 'bg-danger ';
                    switch (data) {
                        case 'Scheduled':
                        default:
                            className = 'bg-secondary ';
                            break;
                        case 'In Progress':
                            className = 'bg-primary ';
                            break;
                        case 'Completed':
                            className = 'bg-success ';
                            break;
                    }
                    return '<span class="badge ' + className + ' p-2 px-3 rounded">' + data + '</span>';
                }
            },
            {
                data: 'Id',
                title: 'Action(s)',
                width: '25%',
                render: function (data, type, row, meta) {
                    //console.log("Row => ", row);
                    let inviteWelderButton = '';
                    let welderButton = '<a data-id="' + data + '" href="javascript:void(0);" status="' + row.CurrentStatus + '" class="btn btn-sm view-test-attendees"><i class="fa fa-eye me-1"></i> View Welders</a>';
                    let finishButton = "";

                    if (row.CurrentStatus != "Completed") {
                        inviteWelderButton = '<a data-id="' + data + '" href="javascript:void(0);" class="btn btn-sm test-invitation"><i class="fa-solid fa-envelope me-1"></i> Invite Welders</a>';
                        finishButton = '<a data-id="' + data + '" href="javascript:void(0);" class="btn btn-sm finish-test" onclick="UpdateTestStatus(this,3);"><i class="fa fa-eye me-1"></i> Finish</a>';
                    }

                    return inviteWelderButton + "&nbsp;" + welderButton + "&nbsp;" + finishButton;
                }
            }
        ],
        language: {
            paginate: {
                previous: 'Prev',
            },
            //processing: "Loading Data...",
            zeroRecords: "No matching records found"
        },

    });
}

function loadTestAttendees(testId, currentTestStatus) {
    $.ajax({
        type: 'POST',
        url: "ManageTests.aspx/LoadUserControlViewTestAttendees",
        data: JSON.stringify({ userId: loginUserId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divAddDialogContents').html(response.d);
            $('#divAddDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Test Attendees',
                maxwidth: 'auto',
                width: '70%',
                open: function (event, ui) {
                    getTestAttendees(testId, currentTestStatus);
                },
                close: function () {
                    $('#divAddDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divAddDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}



function getTestAttendees(testId, currentTestStatus) {
    debugger
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetTestAttendees', testId: testId },
        success: function (response) {
            console.log('GetTestAttendees->', response.data);
            var testAttendees = response.data;
            $('#tblTestAttendees').dataTable({
                data: testAttendees,
                destroy: true,
                responsive: true,
                ordering: false,
                searching: false,
                autoWidth: false,
                lengthChange: false,
                paging: false,
                responsive: true,
                //order: [[1, "asc"]],
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('data-id', data.Id);
                },
                columns: [
                    {
                        data: '#', sortable: false, width: "5%",
                        render: function (data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    { data: 'Welder.UserName', width: '15%' },
                    { data: 'Welder.UserEmail', width: '20%' },
                    { data: 'Welder.SocialSecurityNumber', width: '15%' },
                    { data: 'Welder.WelderStencilNumber', width: '15%' },
                    //{ data: 'RegistrationStatus', name: 'RegistrationStatus', width: '20%' },
                    //{ data: 'PassStatus', name: 'PassStatus', width: '20%' },
                    {
                        data: "PassStatus",
                        width: '25%',
                        render: function (data, type, row, meta) {
                            console.log("Rwo data => ", row);
                            let viewWpqButton = "";
                            let submitRatingButton = '';
                            switch (data) {
                                case 'Pass':
                                    //let viewWpqButton = "";

                                    if (row.WPQData.Id > 0) {
                                        if (row.WPQData.IsDraft) {
                                            viewWpqButton = '&nbsp; <a href="javascript:void(0);" class="btn btn-sm me-2" onclick="redirectWpqForm(' + row.TestId + ',false, ' + row.Welder.UserNum + ',' + row.WPQData.Id + ');"><i class="fa fa-eye me-1"></i> Edit WPQ</a>';
                                        } else {
                                            //viewWpqButton = '&nbsp; <a output-file="' + siteBaseUrl + 'SystemUploadedFiles/Iwb/OutputReports/' + row.WPQData.FileName + '" href="javascript:void(0);" class="btn btn-sm me-1" onclick="showPdfFile(this);"' + 'title="Click to view WPQ PDF">View WPQ</a>';
                                            viewWpqButton = '&nbsp; <a output-file="' + siteBaseUrl + 'SystemUploadedFiles/Iwb/OutputReports/' + row.WPQData.FileName + '" href="javascript:void(0);" class="btn btn-info btn-sm me-1" onclick="showPdfFile(this);"' + 'title="Click to view WPQ PDF">View WPQ</a>';

                                        }
                                    }

                                    submitRatingButton = '&nbsp; <a href="javascript:void(0);" class="btn btn-sm btn-primary" onclick="loadSubmitRating(' + row.Welder.UserNum + ',' + row.TestId + ',' + row.Id + ');"><i class="fa fa-star me-1"></i> Submit Rating</a>';

                                    submitRatingButton += '&nbsp; <a data-id="' + row.Id + '" data-usernum="' + row.Welder.UserNum + '" href="javascript:void(0);" class="btn btn-sm btn-secondary view-ratings"><i class="fa fa-eye me-1"></i>View Ratings</a>';

                                    return '<span class="badge bg-success p-2 px-3 rounded">' + data + '</span>' + viewWpqButton + submitRatingButton;
                                    break;
                                case 'Fail':
                                    return '<span class="badge bg-danger p-2 px-3 rounded">' + data + '</span>';
                                    break;
                                default:
                                    if (currentTestStatus == "Completed") {
                                        return `<div class="container">
                                                <div class="d-flex">
                                                    Fail
                                                    <div class="form-switch form-check ms-2">
                                                        <input type="checkbox" class="form-check-input" id="chkTestStatus" recordid="`+ row.Id + `" attendeeid="` + row.Welder.UserNum + `" testid="` + row.TestId + `" onchange="startWPQProcess(this);">
                                                    </div>
                                                    <label for="chkTestStatus" class="form-check-label">Pass</label>
                                                </div>
                                            </div>`;
                                    }
                                    else {
                                        return "N/A";
                                    }

                                    break;
                            }
                            //return '<a href=/App/Projects/Detail/' + row.id + '>' + (row.projectNo != null ? row.projectNo : "See Project") + '</a>';
                        }
                    }
                    /*{
                        data: 'Id',
                        title: 'Action(s)',
                        width: '15%',
                        render: function (data, type, row, meta) {
                            return '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm edit-location me-2"><i class="fa fa-pencil me-1"></i>Edit</a>' +
                                '<a data-id=' + data + ' onclick="locationConfirmation(' + data + ')" class="btn btn-sm confirm-text delete-location me-2"><i class="fa fa-trash" data-toggle="tooltip" data-placement="left" title="Delete"></i>Delete</a>';
                        }
                    }*/
                ],
                language: {
                    paginate: {
                        previous: 'Prev',
                    },
                    processing: "Loading Data...",
                    zeroRecords: "No matching records found"
                },
            });
        }
    });
}

//Welders

function getWelders() {
    $('#tblWelders').DataTable({
        destroy: true,
        processing: true,
        serverSide: true,
        ordering: false,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: true,
        pageLength: pageSize, // define pageSize somewhere globally like let pageSize = 10;
        responsive: true,
        ajax: {
            url: iwburl,
            type: 'GET',
            datatype: 'json',
            headers: {
                'Content-Type': 'application/json',
            },
            data: function (data, settings) {
                const api = new $.fn.dataTable.Api(settings);
                const pageIndex = api === undefined ? 1 : api.page.info().page + 1;
                const welderName = $('#txtFilterWelderName').val();
                const stencilNo = $('#txtFilterStencilNo').val();
                return {
                    method: 'GetWelders',
                    pageIndex: pageIndex,
                    pageSize: pageSize,
                    welderName: welderName,
                    stencilNo: stencilNo
                };
            },
            dataSrc: function (json) {
                const totalRows = json.rowCount;
                json.recordsTotal = totalRows;
                json.recordsFiltered = totalRows;
                return json.data;
            }
        },
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.UserNum);
        },
        initComplete: function (settings, json) {
            checkUserPermission();
        },
        drawCallback: function (settings) {
            checkUserPermission();
        },
        columns: [
            {
                data: '#',
                width: "5%",
                render: function (data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'UserName', width: '15%' },
            { data: 'UserEmail', width: '15%' },
            { data: 'SocialSecurityNumber', width: '10%', defaultContent: 'N/A' },
            { data: 'WelderStencilNumber', width: '10%', defaultContent: 'N/A' },
            {
                data: 'DOB',
                width: '10%',
                defaultContent: 'N/A',
                render: function (data, type, row) {
                    return data === '0001-01-01T00:00:00' ? 'NA' : moment(data).format('DD/MMM/YYYY');
                }
            },
            {
                data: 'City',
                width: '10%',
                defaultContent: 'N/A',
                render: function (data, type, row) {
                    return data === '' ? 'N/A' : `${data}, ${row.State}`;
                }
            },
            {
                data: 'UserNum',
                width: '25%',
                title: 'Action(s)',
                render: function (data, type, row, meta) {
                    const className = loginUserType === 8 ? 'd-none' : 'd-inline';
                    const adminClassName = loginUserType === 1 ? '' : 'd-none';
                    return `<a data-id="${data}" href="javascript:void(0);" class="btn btn-sm assign-job me-1 ${className}">
                            <i class="fa fa-pencil me-1"></i>Assign Job
                            </a>
                            <a data-id="${data}" href="javascript:void(0);" class="btn btn-sm view-work-history">
                            <i class="fa fa-eye me-1"></i>View Work History
                            </a>
                            <a data-id="${data}" href="javascript:void(0);" class="btn btn-sm upload-wpq ${adminClassName}">
                            <i class="fa fa-eye me-1"></i>Upload WPQ
                            </a>`;
                }
            }
        ],
        language: {
            paginate: {
                previous: 'Prev',
            },
            processing: "Loading Data...",
            zeroRecords: "No matching records found"
        }
    });
}

function loadAddUser() {
    $.ajax({
        type: 'POST',
        url: 'ManageWelders.aspx/LoadUserControlAddNew',
        data: JSON.stringify({ userId: loginUserId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divAddDialogContents').html(response.d);
            $('#divAddDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Add New Welder',
                maxwidth: 'auto',
                width: '50%',
                height: 600,
                open: function (event, ui) {
                    $('#ddlState').select2({
                        theme: 'bootstrap-5',
                        width: '100%',
                        dropdownParent: $('.ui-new-dialog-widget-content')
                    });
                },
                close: function () {
                    $('#divAddDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        saveUser();
                    }
                },
                {
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divAddDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function formatJsonDate(jsonDate) {
    if (!jsonDate || jsonDate.indexOf('/Date') === -1) return '';
    var timestamp = parseInt(jsonDate.replace(/\/Date\((-?\d+)\)\//, '$1'), 10);
    var date = new Date(timestamp);
    if (isNaN(date)) return '';
    return date.toISOString().split('T')[0];
}

function loadViewUser() {
    $.ajax({
        type: 'POST',
        url: 'ManageWelders.aspx/LoadUserControlView',
        data: JSON.stringify({ userId: loginUserId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divAddDialogContents').html(response.d);

            // Initialize dialog
            $('#divAddDialogContents').mmsDialog({
                autoOpen: true,
                modal: true,
                width: '55%',
                height: 400,
                title: 'User Profile',
                open: function () {
                    $('#ddlState').select2({
                        theme: 'bootstrap-5',
                        width: '100%',
                        dropdownParent: $('.ui-new-dialog-widget-content')
                    });

                    // 🔽 Call function to bind user data
                    loadUserProfileData();
                },
                close: function () {
                    $('#divAddDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divAddDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function loadUserProfileData() {
    $.ajax({
        url: iwburl,
        type: 'GET',
        data: { method: 'GetUserDataByUser', userId: loginUserId },
        dataType: 'json',
        success: function (response) {
            if (!response || !response.user) {
                alert('User data not found.');
                return;
            }

            var user = response.user;

            $('#lblEmailAddress').text(user.Email || 'N/A');
            $('#lblFullName').text(user.UserName || 'N/A');
            $('#lblPhone').text(user.Phone || 'N/A');
            $('#lblSSN').text(user.SSN || 'N/A');
            $('#lblDOB').text(formatJsonDate(user.DOB) || 'N/A');
            $('#lblState').text(user.State || 'N/A');
            $('#lblCity').text(user.City || 'N/A');

            if (user.ProfileImageUrl) {
                $('#imgUser').attr('src', user.ProfileImageUrl);
            } else {
                $('#imgUser').attr('src', '../Uploads/UserImages/user.jpg');
            }
        },
        error: function () {
            alert('Failed to load user profile.');
        }
    });
}



function saveUser() {
    debugger
    var validationResult = validateUser();
    if (!validationResult.IsValid) {
        $('.validation-summary').append(validationResult.Messagesmarkup);
        return;
    }

    //imgDropzone.processQueue();
    try {
        var userData = getUserData();
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { method: 'SaveWelder', UserData: JSON.stringify(userData) },
            success: function (response) {
                response = JSON.parse(response);
                switch (response.success) {
                    case true:
                        //updateView(response.data);
                        toastr.success('User saved successfully.');
                        $('#divAddDialogContents').mmsDialog('close');
                        getWelders();
                        break;
                    default:
                        $.unblockUI();
                        toastr.error(response.data);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred saveUser(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        showExceptionDialog(e.message);
    }
}

function validateUser() {
    $('.validation-summary').html('');

    var user = getUserData();
    var validationResult = {
        IsValid: true,
        Messagesmarkup: $("<ul></ul>")
    };

    if (user.UserID.length == 0) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter email address.</li>");
    }
    if (/^([a-zA-Z_\-\.0-9\s]{1,40})+$/.test(user.UserID)) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Invalid email address.</li>");
    }
    if (user.UserName.trim() == "") {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter user name.</li>");
    }

    if (user.UserPW.trim() == "") {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter password.</li>");
    }
    if (user.UserPW.trim() != "") {
        var uppercase = /[A-Z]/;
        var lowercase = /[a-z]/;
        var number = /[0-9]/;
        var special = /[\W]{1,}/;
        var pswd_length = user.UserPW.length < 8;

        if (pswd_length) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNew8Char'] + '</li>');
        }
        else if (!lowercase.test(user.UserPW)) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNewLChar'] + '</li>');
        }
        else if (!uppercase.test(user.UserPW)) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNewUChar'] + '</li>');
        }
        else if (!number.test(user.UserPW)) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNewDigit'] + '</li>');
        }
        else if (!special.test(user.UserPW)) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNewSChar'] + '</li>');
        }
    }



    if (!(user.UserPhone == "" || user.UserPhone == null) && !/^(\+?[0-9\-]{1,20})+$/.test(user.UserPhone)) {
        //var filter = /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter valid phone.</li>");
    }

    return validationResult;
}

function getUserData() {
    var emailFilter = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/
    var imageName = $('#imgUser').attr('src').split('/');
    debugger
    var usrprofile = {
        UserNum: $("#hfUserNum").val(),
        IsRevCell: false,
        RevSyncServerUserNum: 0,
        RecId: 1,
        IdentityNumber: $("#txtSSN").val(),
        UserID: $("#txtEmailAddress").val(),
        UserName: $("#txtFullName").val(),
        UserPW: $("#txtUserPassword").val(),
        UserEmail: emailFilter.test($("#txtEmailAddress").val()) == true ? $("#txtEmailAddress").val() : "",
        Ext: 0,
        SearchRest: 0,
        //JoinBeginDate: $("#txtJoinBeginDate").val(),
        //JoinEndDate: $("#txtJoinEndDate").val(),
        EOD: 0,
        POD: 0,
        Pause: 0,
        UserType: 8,
        IsIwbUser: true,
        DOB: $("#txtDOB").val(),
        City: $("#ddlCity :selected").text(),
        State: $("#ddlState :selected").text(),
        SocialSecurityNumber: $('#txtSSN').val(),
        UserPhone: $("#txtPhone").val(),
        UserFax: '',
        UserPic: imageName[imageName.length - 1] == 'user.jpg' ? '' : imageName[imageName.length - 1],
        HasShiftRest: false,
        IsEnterpriseUser: false,
        RoleId: 0,
        Is2FAEnabled: false,
    };

    return usrprofile;// JSON.stringify(usrprofile);
}


function registerUser() {
    var validationResult = validateRegisterUser();
    if (!validationResult.IsValid) {
        $('.validation-summary').append(validationResult.Messagesmarkup);
        return;
    }

    try {
        var userData = getRegisterUserData();

        var organization = {
            Name: $('#txtName').val(),
            Email: $('#txtEmail').val(),
            Password: $('#txtPassword').val(),
            Address: '',
            Phone: $('#txtPhone').val(),
            Type: $("input[name='rbUserType']:checked").attr('data-orgtype'),
            DOB: '',
            SocialSecurityNumber: $('#txtSSN').val(),
            State: $("#ddlState :selected").text(),
            City: $("#ddlCity :selected").text(),
        };

        //debugger;
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { method: 'RegisterUser', organization: JSON.stringify(organization), user: JSON.stringify(userData), password: $('#txtPassword').val() },
            success: function (response) {
                response = JSON.parse(response);
                debugger
                switch (response.success) {
                    case true:
                        toastr.success('User registered successfully.');
                        $('.form-right').html('<p class="text-success text-center">You have been registered successfully.</p>');
                        break;
                    default:
                        $.unblockUI();
                        if (response.data = 'user id is not unique...')
                            toastr.error('Email address is already in use. please use another email address.');
                        else
                            toastr.error(response.data);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                toastr.error('Error occurred registerUser(): ' + jqXHR.responseText);
            }
        });
    } catch (e) {
        toastr.error(e.message);
    }
}

function validateRegisterUser() {
    $('.validation-summary').html('');

    var user = getRegisterUserData();
    var validationResult = {
        IsValid: true,
        Messagesmarkup: $("<ul></ul>")
    };

    if (user.Email.length == 0) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter the email address.</li>");
    }
    if (/^([a-zA-Z_\-\.0-9\s]{1,40})+$/.test(user.Email)) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Invalid email address</li>");
    }
    if (user.Name.trim() == "") {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter your username.</li>");
    }

    if (user.Password.trim() == "") {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter your password.</li>");
    }
    if (user.Password.trim() != "") {
        var uppercase = /[A-Z]/;
        var lowercase = /[a-z]/;
        var number = /[0-9]/;
        var special = /[\W]{1,}/;
        var pswd_length = user.Password.length < 8;

        if (pswd_length) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNew8Char'] + '</li>');
        }
        else if (!lowercase.test(user.Password)) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNewLChar'] + '</li>');
        }
        else if (!uppercase.test(user.Password)) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNewUChar'] + '</li>');
        }
        else if (!number.test(user.Password)) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNewDigit'] + '</li>');
        }
        else if (!special.test(user.Password)) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append('<li>' + langKeys[currentLanguage]['errPwdNewSChar'] + '</li>');
        }
    }

    if (!(user.Phone == "" || user.Phone == null) && !/^(\+?[0-9\-]{1,20})+$/.test(user.Phone)) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter valid phone.</li>");
    }

    return validationResult;
}

function getRegisterUserData() {
    var userInfo = {
        Name: $('#txtName').val(),
        Email: $('#txtEmail').val(),
        Password: $('#txtPassword').val(),
        Address: '',
        State: $("#ddlState :selected").text(),
        City: $("#ddlCity :selected").text(),
        Phone: $('#txtPhone').val(),
        Type: $("input[name='rbUserType']:checked").val(),
        DOB: '',
        SocialSecurityNumber: $('#txtSSN').val()
    };
    return userInfo;// JSON.stringify(userInfo);
}



// Organization

function getOrganizations() {
    orgTable = $('#tblOrganizations').dataTable({
        destroy: true,
        //processing: false,
        serverSide: true,
        ordering: false,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: false,
        //pageLength: pageSize,
        responsive: true,
        //order: [[1, "asc"]],
        //scrollCollapse: false,
        scrollY: '67vh',
        //fixedHeader: true,
        ajax: {
            //url: iwburl + '?OrderBy=Name&OrderDirection=desc',
            url: iwburl,
            type: 'GET',
            datatype: 'json',
            headers: {
                'Content-Type': 'application/json',
            },
            data: function (data, settings) {
                var api = new $.fn.dataTable.Api(settings);
                var orgName = $('#txtFilterOrgName').val();
                var orgTypeId = $('input[name=rbOrgType]:checked').val();
                console.log('Organization search input -> ', orgName, orgTypeId);
                return { method: 'GetOrganizations', OrganizationName: orgName, OrganizationTypeId: orgTypeId };
            },
            //error: function (jqXHR, textStatus, errorThrown) { }
            dataSrc: function (json) {
                //debugger
                var totalRows = json.rowCount;
                json.recordsTotal = totalRows;
                json.recordsFiltered = totalRows;
                return json.data;
            }
        },
        /*initComplete: function (settings, json) {
            $('.dt-scroll-body thead tr').css({ visibility: 'collapse' });
        },*/
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.id);
        },
        columns: [
            {
                data: '#', sortable: false, width: "5%",
                render: function (data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'Name', width: '20%' },
            { data: 'Email', width: '20%' },
            { data: 'Phone', width: '10%', defaultContent: 'N/A' },
            { data: 'Address', width: '15%', defaultContent: 'N/A' },
            { data: 'Type', width: '10%' },
            {
                data: 'Id',
                title: 'Action(s)',
                width: '20%',
                render: function (data, type, row, meta) {
                    return '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm view-locations me-2"><i class="fa fa-eye me-1"></i>View Location(s)</a>' +
                        '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm add-location me-2"><i class="fa fa-plus me-1"></i>Add Location</a>';
                }
            }
        ],
        language: {
            paginate: {
                previous: 'Prev',
            },
            processing: "Loading Data...",
            zeroRecords: "No matching records found"
        },

    });

}

function loadAddOrganization() {
    $.ajax({
        type: 'POST',
        url: 'ManageOrganizations.aspx/LoadUserControlAddNew',
        data: JSON.stringify({ userId: loginUserId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {

            $('#divAddDialogContents').html(response.d);
            $('#divAddDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Add New Organization',
                maxwidth: 'auto',
                width: '50%',
                open: function (event, ui) {
                    $('#ddlOrgType').select2({
                        //placeholder: 'Choose',
                        minimumResultsForSearch: -1,
                        theme: 'bootstrap-5',
                        width: '50%',
                        dropdownParent: $('.ui-new-dialog-widget-content')
                    });
                },
                close: function () {
                    $('#divAddDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        saveOrganization();
                    }
                },
                {
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divAddDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}
function saveOrganization() {
    var organization = {
        Name: $('#txtOrgName').val(),
        Email: $('#txtOrgEmail').val(),
        Address: $('#txtOrgAddress').val(),
        Phone: $('#txtOrgPhone').val(),
        Type: $("#ddlOrgType :selected").val(),
        //Type: loginUserType === 1 ? 1 : 2,
        Password: $('#txtOrgPassword').val(),
    };
    var validationResult = validateOrganization(organization);
    if (!validationResult.IsValid) {
        //showErrorDialog(validationResult.Messagesmarkup.children().text());
        $('.validation-summary').append(validationResult.Messagesmarkup);
        //showErrorDialog(validationResult.Messagesmarkup);
        return;
    }
    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { organization: JSON.stringify(organization), password: $('#txtOrgPassword').val(), method: 'SaveOrganization' },
            success: function (response) {
                var response = JSON.parse(response);
                switch (response.success) {
                    case true:
                        //updateView(response.data);
                        toastr.success('Organization saved successfully.');
                        //$.pnotify({ title: 'Organization', text: 'Organization saved successfully.', opacity: 1, type: "info", delay: pageNotificationTimeout });
                        $('#divAddDialogContents').mmsDialog("close");
                        break;
                    default:
                        $.unblockUI();
                        //showErrorDialog(response.data);
                        toastr.error(response.data);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred saveOrganization(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        console.log(e.message);
    }
}

function validateOrganization(organization) {
    $('.validation-summary').html('');
    //var emailPattern = new RegExp(/^(("[\w-\s]+")|([\w-]+(?:\.[\w-]+)*)|("[\w-\s]+")([\w-]+(?:\.[\w-]+)*))(@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$)|(@\[?((25[0-5]\.|2[0-4][0-9]\.|1[0-9]{2}\.|[0-9]{1,2}\.))((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[0-9]{1,2})\.){2}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[0-9]{1,2})\]?$)/i);
    var emailPattern = /^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    try {
        var validationResult = {
            IsValid: true,
            Messagesmarkup: $("<ul></ul>")
        };

        if (organization.Name.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter organization name.</li>");
        }
        if (organization.Email.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter organization email.</li>");
        }
        else {
            if (!emailPattern.test(organization.Email)) {
                validationResult.IsValid = false;
                validationResult.Messagesmarkup.append("<li>Incorrect email address.</li>");
            }
        }

        if (organization.Type == '0') {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please choose organization type.</li>");
        }

        if (organization.Password.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter Organization account password.</li>");
        }
        else {
            if (organization.Password.length < 8) {
                validationResult.Messagesmarkup.append("<li>" + langKeys[currentLanguage]['errPwdNew8Char'] + "</li>");
                validationResult.IsValid = false;
            }
            else if (!/[a-z]/.test(organization.Password)) {
                validationResult.Messagesmarkup.append("<li>" + langKeys[currentLanguage]['errPwdNewLChar'] + "</li>");
                validationResult.IsValid = false;
            }
            else if (!/[A-Z]/.test(organization.Password)) {
                validationResult.Messagesmarkup.append("<li>" + langKeys[currentLanguage]['errPwdNewUChar'] + "</li>");
                validationResult.IsValid = false;
            }
            else if (!/[0-9]/.test(organization.Password)) {
                validationResult.Messagesmarkup.append("<li>" + langKeys[currentLanguage]['errPwdNewDigit'] + "</li>");
                validationResult.IsValid = false;
            }
            else if (!/[\W]{1,}/.test(organization.Password)) {
                validationResult.Messagesmarkup.append("<li>" + langKeys[currentLanguage]['errPwdNewSChar'] + "</li>");
                validationResult.IsValid = false;
            }
        }


        return validationResult;
    }
    catch (e) {
        console.error('Error in validateOrganization : ' + e);
    }
}

// Organization Location
function loadLocationsListing(orgId) {
    $.ajax({
        type: 'POST',
        url: "ManageOrganizations.aspx/LoadUserControlViewLocations",
        data: JSON.stringify({ userId: loginUserId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divDialogContents').html(response.d);
            $('#divDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Organization Locations',
                maxwidth: 'auto',
                width: '70%',
                open: function (event, ui) {
                    getLocations(orgId);
                },
                close: function () {
                    $('#divDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function getLocations(orgId) {
    //debugger
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetLocations', organizationId: orgId },
        success: function (response) {
            var locations = response.data;
            $('#tblLocations').dataTable({
                data: locations,
                destroy: true,
                responsive: true,
                ordering: false,
                searching: false,
                autoWidth: false,
                lengthChange: false,
                paging: false,
                responsive: true,
                //order: [[1, "asc"]],
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('data-id', data.Id);
                },
                columns: [
                    {
                        data: '#', sortable: false, width: "5%",
                        render: function (data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    { data: 'Address', name: 'address', width: '30%', className: 'col-address' },
                    { data: 'City', name: 'address', width: '20%', className: 'col-city' },
                    { data: 'State', name: 'address', width: '20%', className: 'col-state' },
                    {
                        data: 'Id',
                        title: 'Action(s)',
                        width: '25%',
                        render: function (data, type, row, meta) {
                            return '<a data-id=' + data + ' data-org-id=' + orgId + ' href="javascript:void(0);" class="btn btn-sm edit-location me-2"><i class="fa fa-pencil me-1"></i>Edit</a>' +
                                '<a data-id=' + data + ' onclick="locationConfirmation(' + data + ')" class="btn btn-sm confirm-text delete-location me-2"><i class="fa fa-trash" data-toggle="tooltip" data-placement="left" title="Delete"></i>Delete</a>';
                        }
                    }
                ],
                language: {
                    paginate: {
                        previous: 'Prev',
                    },
                    processing: "Loading Data...",
                    zeroRecords: "No matching records found"
                },
            });
        }
    });
}

function loadAddLocation(orgId, orgLocId) {
    //debugger
    $.ajax({
        type: 'POST',
        url: 'ManageOrganizations.aspx/LoadUserControlAddLocation',
        data: JSON.stringify({ userId: 1000 }),
        contentType: 'application/json; charset=utf-8',
        dataType: 'JSON',
        success: function (response) {
            $('#divAddDialogContents').html(response.d);
            $('#divAddDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Add New Location',
                maxwidth: 'auto',
                width: '40%',
                open: function (event, ui) {
                    //console.log('orgLocId=' + orgLocId);
                    $('#hdnOrganizationId').val(orgId);
                    $('#hdnOrgLocId').val(orgLocId);


                    if (orgLocId > 0) {
                        console.log('orgLocId=' + orgLocId);
                        var row = $("#tblLocations tbody tr[data-id='" + orgLocId + "']");
                        var address = row.find('td.col-address').text();
                        var state = row.find('td.col-state').text();
                        var city = row.find('td.col-city').text();
                        //debugger
                        //console.log(state);
                        //console.log(city);
                        $('#txtAddress').val(address);

                        $('#ddlState option').each(function () {
                            if ($(this).text() == state) {
                                $(this).prop('selected', true);
                            }
                        });

                        $('#ddlState').select2({
                            theme: 'bootstrap-5',
                            width: '100%',
                        });
                        $('#ddlState').trigger('change');

                        $('#ddlCity option').each(function () {
                            if ($(this).text() == city) {
                                $(this).prop('selected', true);
                            }
                        });

                        $('#ddlCity').select2({
                            theme: 'bootstrap-5',
                            width: '100%',
                        });
                    }
                    else {
                        $('#ddlState').select2({
                            theme: 'bootstrap-5',
                            width: '100%',
                            //dropdownParent: $('.ui-new-dialog-widget-content')//dropdownParent: $('#divAddDialogContents')
                        });
                    }
                },
                close: function () {
                    $('#divAddDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        saveLocation(orgId, orgLocId);
                    }
                },
                {
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divAddDialogContents').mmsDialog('open');

        },
        error: function (error) {
            console.log(error);
        }
    });
}
function saveLocation(orgId, orgLocId) {
    var location = {
        State: $("#ddlState :selected").text(),
        City: $("#ddlCity :selected").text(),
        Address: $('#txtAddress').val(),
        OrganizationId: orgId, //$('#hdnOrganizationId').val(),
        Id: orgLocId //$('#hdnOrgLocId').val(),
    };
    var validationResult = validateOrganizationLocation(location);
    if (!validationResult.IsValid) {
        $('.validation-summary').append(validationResult.Messagesmarkup);
        return;
    }

    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { location: JSON.stringify(location), method: 'SaveLocation' },
            success: function (response) {
                $.unblockUI();
                //debugger;
                response = JSON.parse(response);
                switch (response.success) {
                    case true:
                        if (location.Id > 0) {
                            var row = $("#tblLocations tbody tr[data-id='" + orgLocId + "']");
                            debugger
                            row.find('td.col-address').text(location.Address);
                            row.find('td.col-state').text(location.State);
                            row.find('td.col-city').text(location.City);
                        }
                        toastr.success('Location saved successfully.');
                        $('#divAddDialogContents').mmsDialog('close');

                        break;
                    default:
                        toastr.error(response.message);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred saveLocation(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        console.log(e.message);
    }
}

function validateOrganizationLocation(location) {
    $('.validation-summary').html('');
    try {
        var validationResult = {
            IsValid: true,
            Messagesmarkup: $("<ul></ul>")
        };

        if (location.State == 0 || location.State == '0' || location.State == '' || location.State == 'Choose') {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please choose organization state.</li>");
        }

        if (location.City == 0 || location.City == '0' || location.City == '' || location.City == 'Choose') {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please choose city.</li>");
        }

        if (location.Address.trim() == '') {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter organization address.</li>");
        }

        return validationResult;
    }
    catch (e) {
        console.error('Error in validateOrganization : ' + e);
    }
}

function locationConfirmation(locationId) {
    //showConfirmationDialog("Are you sure to delete this location?", locationId, deleteLocation);
    Swal.fire({
        title: "Are you sure?",
        text: "Once Deleted, you will not be able to retrive",
        icon: "warning",
        showConfirmButton: true,
        showCancelButton: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                cache: false,
                type: 'POST',
                async: true,
                url: iwburl,
                data: { method: 'DeleteLocation', id: locationId },
                success: function (response) {
                    debugger
                    switch (response.success) {
                        case true:
                            //$.growlUI('Success', langKeys[currentLanguage]['sDelete']);
                            toastr.success('Location deleted successfully.');
                            $.unblockUI();
                            //$("#tblLocations tbody tr[data-id='" + locationId + "']").remove();
                            var row = $("#tblLocations tbody tr[data-id='" + locationId + "']");
                            var siblings = row.siblings();
                            row.remove();// debug this, might not be needed
                            siblings.each(function (index) {
                                $(this).children('td').first().text(index + 1);
                            });
                            $('#tblLocations').DataTable().row(row).remove().draw(false);

                            break;
                        default:
                            $.unblockUI();
                            toastr.error(response.message);
                            break;
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $.unblockUI();
                    toastr.error('Error occurred deleteLocation(): ' + jqXHR.responseText);
                }
            });

        }
    });
    //deleteLocation(locationId);
}

function deleteLocation(locationId) {
    $.blockUI({ message: '<h1> ' + langKeys[currentLanguage]['Processing'] + ' </h1>' });
    $.ajax({
        cache: false,
        type: 'POST',
        async: true,
        url: iwburl,
        data: { method: 'DeleteLocation', id: locationId },
        success: function (response) {
            switch (response.success) {
                case true:
                    $.growlUI('Success', langKeys[currentLanguage]['sDelete']);
                    $.unblockUI();
                    $("#tblLocations tbody tr[data-id='" + locationId + "']").remove();
                    break;
                default:
                    $.unblockUI();
                    showErrorDialog(response.message);
                    break;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred deleteLocation(): ' + jqXHR.responseText);
        }
    });
}



//Test Invitation

function loadTestInvitation(testId) {
    $.ajax({
        type: 'POST',
        url: "ManageTests.aspx/LoadUserControlTestInvitation",
        data: JSON.stringify({ userId: loginUserId }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divAddDialogContents').html(response.d);
            $('#divAddDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Invite Welder(s) for Test',
                maxwidth: 'auto',
                width: '60%',
                open: function (event, ui) {
                    //$.unblockUI();
                    getWeldersData();
                    //getTestAttendees(testId, currentTestStatus);
                },
                close: function () {
                    $('#divAddDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnSave'],
                    click: function () {
                        saveTestInvitation(testId);
                    }
                },
                {
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divAddDialogContents').mmsDialog('open');
        },
        error: function (error) {
            $.unblockUI();
            console.log(error);
        }
    });
}

function getWeldersData() {
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetWelders' },
        success: function (response) {
            $.unblockUI();
            var weldersList = response.data.map(function (item) {
                return { id: item.UserNum, text: item.UserEmail };
            });
            var $select = $('.ddl-welders-from');
            $select.find('option').remove();
            $.each(weldersList, function (key, item) {
                $select.append('<option value=' + item.id + '>' + item.text + '</option>');
            });
        }
    });
}


function registerWelderForTest(testId, welderId) {
    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { method: 'RegisterWelderForTest', testId: testId, welderId: welderId },
            success: function (response) {
                var response = JSON.parse(response);
                switch (response.success) {
                    case true:
                        //updateView(response.data);
                        toastr.success('Welder successfully registered for Test.');
                        $('#divAddDialogContents').mmsDialog("close");
                        break;
                    default:
                        $.unblockUI();
                        toastr.error(response.data);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred registerWelderForTest(): ' + jqXHR.responseText);
            }
        });
    } catch (e) {
        showExceptionDialog(e.message);
    }
}


function validateInviteForTest() {
    $('.validation-summary').html('');

    var invite = getTestInvitationData();
    var validationResult = {
        IsValid: true,
        Messagesmarkup: $("<ul></ul>")
    };

    if (invite.TestInviteIds.length == 0) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please choose welder.</li>");
    }

    return validationResult;
}

function saveTestInvitation(testId) {
    var validationResult = validateInviteForTest();
    if (!validationResult.IsValid) {
        $('.validation-summary').append(validationResult.Messagesmarkup);
        return;
    }

    try {
        var inviteData = getTestInvitationData();
        debugger
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { method: 'SaveTestInvitation', testId: testId, welderIds: JSON.stringify(inviteData.TestInviteIds), welderEmails: JSON.stringify(inviteData.TestInviteEmails) },
            success: function (response) {
                switch (response.success) {
                    case true:
                        toastr.success('Invitation has been sent to welders.');
                        $('#divAddDialogContents').mmsDialog('close');
                        break;
                    default:
                        $.unblockUI();
                        showErrorDialog(response.message);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred saveTestInvitation(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        console.log(e.message);
        showExceptionDialog(e.message);
    }
}

function getTestInvitationData() {
    var testInviteIds = $('select#ddlTo option').map(function () { return $(this).val(); }).get();
    var testInviteEmails = $('select#ddlTo option').map(function () { return $(this).text(); }).get();

    var testInvitation = {
        TestInviteIds: testInviteIds,
        TestInviteEmails: testInviteEmails,
    };
    return testInvitation;
}

function viewAllTests() {
    $('#tblViewTests').dataTable({
        destroy: true,
        processing: true,
        serverSide: true,
        ordering: false,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: true,
        pageLength: pageSize,
        responsive: true,
        //order: [[1, "asc"]],
        ajax: {
            url: iwburl,
            type: 'GET',
            datatype: 'json',
            headers: {
                'Content-Type': 'application/json',
            },
            data: function (data, settings) {
                var api = new $.fn.dataTable.Api(settings);
                var pageIndex = api === undefined ? 1 : api.page.info().page + 1;
                var testName = $('#txtFilterTestName').val();
                var testTypeId = $('#txtFilterTestCode').val();
                return { method: 'GetTests', pageIndex: pageIndex, pageSize: pageSize, testName: testName, testTypeId: testTypeId };
            },
            //error: function (jqXHR, textStatus, errorThrown) { }
            dataSrc: function (json) {
                var totalRows = json.rowCount;
                json.recordsTotal = totalRows;
                json.recordsFiltered = totalRows;
                return json.data;
            }
        },
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.Id);
        },
        columns: [
            {
                data: '#', sortable: false, width: "5%",
                render: function (data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'Name', width: '20%' },
            {
                data: 'StartDate',
                width: '15%',
                defaultContent: 'N/A',
                render: function (data, type, row) {
                    return moment(data).format('DD/MMM/YYYY HH:MM A');
                }
            },
            { data: 'NoOfPositions', width: '10%', defaultContent: '0' },
            {
                data: 'TypeCode',
                width: '15%',
                defaultContent: 'N/A'
            },
            {
                data: 'Deadline',
                width: '15%',
                defaultContent: 'N/A',
                render: function (data, type, row) {
                    return moment(data).format('DD/MMM/YYYY HH:MM A');
                }
            },
            {
                data: 'CurrentStatus', defaultContent: 'N/A', width: '10%',
                render: function (data, type, row) {
                    var className = 'bg-danger ';
                    switch (data) {
                        case 'Scheduled':
                        default:
                            className = 'bg-secondary ';
                            break;
                        case 'In Progress':
                            className = 'bg-primary ';
                            break;
                        case 'Completed':
                            className = 'bg-success ';
                            break;
                    }
                    return '<span class="badge ' + className + ' p-2 px-3 rounded">' + data + '</span>';
                }
            },
            {
                data: 'Id',
                title: 'Action(s)',
                width: '15%',
                render: function (data, type, row, meta) {
                    return (row.CurrentStatus === 'Scheduled' || row.CurrentStatus === 'In Progress') ? '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm me-1 apply-test"><i class="fa-solid fa-file-pen me-1"></i>Apply</a>' : '';
                }
            }
        ],
        language: {
            paginate: {
                previous: 'Prev',
            },
            processing: "Loading Data...",
            zeroRecords: "No matching records found"
        },
    });

}

function getWelderDashboardData() {
    $.ajax({
        type: 'GET',
        url: iwburl,
        data: { method: 'WelderDashboard' },
        success: function (response) {
            //debugger
            //var response = JSON.parse(response);
            var jobs = response.jobs;
            var tests = response.tests;

            $('#tblNewJobs').dataTable({
                data: jobs,
                destroy: true,
                responsive: true,
                ordering: false,
                searching: false,
                autoWidth: false,
                lengthChange: false,
                paging: false,
                info: false,
                responsive: true,
                //order: [[1, "asc"]],
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('data-id', data.Id);
                },
                columns: [
                    {
                        data: '#', sortable: false, width: "5%",
                        render: function (data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    { data: 'Title', name: 'title', width: '20%' },
                    { data: 'StartDate', width: '15%', defaultContent: 'N/A', render: function (data, type, row) { return moment(data).format('DD/MMM/YYYY'); } },
                    { data: 'WpsName', width: '15%', defaultContent: 'N/A' },
                    { data: 'WpsName', width: '15%', defaultContent: 'N/A' },
                    {
                        data: 'Id',
                        title: 'Apply',
                        width: '15%',
                        render: function (data, type, row, meta) {
                            return '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm apply-job"><i class="fa-solid fa-file-pen me-1"></i>Apply</a>';
                        }
                    }
                ],
                language: {
                    paginate: {
                        previous: 'Prev',
                    },
                    processing: "Loading Data...",
                    zeroRecords: "No matching records found"
                },
            });

            $('#tblNewTests').dataTable({
                data: tests,
                destroy: true,
                responsive: true,
                ordering: false,
                searching: false,
                autoWidth: false,
                lengthChange: false,
                paging: false,
                info: false,
                responsive: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('data-id', data.Id);
                },
                columns: [
                    {
                        data: '#', sortable: false, width: "5%",
                        render: function (data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    { data: 'Name', width: '25%' },
                    {
                        data: 'StartDate',
                        width: '20%',
                        defaultContent: 'N/A',
                        render: function (data, type, row) {
                            return moment(data).format('DD/MMM/YYYY HH:MM A');
                        }
                    },
                    {
                        data: 'TypeCode',
                        width: '20%',
                        defaultContent: 'N/A'
                    },
                    { data: 'NoOfPositions', width: '10%', defaultContent: '0' },
                    {
                        data: 'CurrentStatus', defaultContent: 'N/A', width: '10%',
                        render: function (data, type, row) {
                            var className = 'bg-danger ';
                            switch (data) {
                                case 'Scheduled':
                                default:
                                    className = 'bg-secondary ';
                                    break;
                                case 'In Progress':
                                    className = 'bg-primary ';
                                    break;
                                case 'Completed':
                                    className = 'bg-success ';
                                    break;
                            }
                            return '<span class="badge ' + className + ' p-2 px-3 rounded">' + data + '</span>';
                        }
                    },
                    {
                        data: 'Id',
                        title: 'Action(s)',
                        width: '10%',
                        render: function (data, type, row, meta) {
                            return '<a data-id=' + data + ' href="javascript:void(0);" class="btn btn-sm apply-for-test me-1"><i class="fa-solid fa-file-pen me-1"></i>Apply for test</a>';
                        }
                    }
                ],

                language: {
                    paginate: {
                        previous: 'Prev',
                    },
                    processing: "Loading Data...",
                    zeroRecords: "No matching records found"
                },
            });


        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
            showExceptionDialog('Error occurred getWelderDashboardData(): ' + jqXHR.responseText);
        }
    });
}

function getDashboardJobs() {

}
function getDashboardTests() {
}
function getDashboardStats() {
}



function initializeDataTableTooltips() {
    //$('.dt-paging-button').each(function () {
    //$(".dt-paging-button:not(.disabled)").slice(2, -2).each(function () {
    $(".dt-paging-button").slice(2, -2).each(function () {
        if (!$(this).hasClass('disabled')) {
            $(this).attr('title', 'Go to page ' + $(this).text());
        }
    });
    $('.dt-paging-button').tooltip();
}


var mtrResponse;
var mtrModifiedData;
$(document).on('change', '.ddl-mtr-codes', function (e) {
    var codeIndex = $(this).val();

    //var selectedHeatNos = $('.ddl-heat-numbers').select2('data').map(item => item.text);
    //console.log(selectedHeatNos);
    //debugger
    if (codeIndex == '-1')
        $('#accordionExample .accordion-item').removeClass('d-none');
    else {
        $('#accordionExample .accordion-item').addClass('d-none');
        $('#accordionExample .codeIndex-' + codeIndex).removeClass('d-none');
    }

    //filterMTRContents();
    filterMTRReprot();
});

function filterMTRReprot() {
    //var selectedHeatNos = $('.ddl-heat-numbers').select2('data').map(item => item.text);
    var selectedHeatNos = $('.heatno-checkbox:checked').map(function () {
        return $(this).val();
    }).get();

    if ($.inArray('All', selectedHeatNos) != -1) {
        selectedHeatNos = [];
    }

    console.log('selectedHeatNos=' + selectedHeatNos);

    if (selectedHeatNos.length > 0) {
        $('.table-chemical tbody tr').addClass('d-none');
        $('.table-mechanical tbody tr').addClass('d-none');

        var $allChemTables = $('.table-chemical tbody');
        $allChemTables.each(function (i, tbody) {
            var sNo = 1;
            $(tbody).find('tr').each(function (ri, tr) {
                //debugger
                var $tr = $(this);
                var trHeatNumber = $(tr).attr('data-heatno');

                if ($.inArray(trHeatNumber, selectedHeatNos) != -1) {
                    //console.log(trHeatNumber + " is in array");
                    $(tr).removeClass('d-none');
                    $(tr).find('td:first-child').text(sNo++);
                }
            });
        });


        var $allMechTables = $('.table-mechanical tbody');
        $allMechTables.each(function (i, tbody) {
            console.log($(tbody));
            var sNo = 1;
            $(tbody).find('tr').each(function (ri, tr) {
                //debugger
                var $tr = $(this);
                var trHeatNumber = $(tr).attr('data-heatno');

                if ($.inArray(trHeatNumber, selectedHeatNos) != -1) {
                    //console.log(trHeatNumber + " is in array");
                    $(tr).removeClass('d-none');
                    $(tr).find('td:first-child').text(sNo++);
                }
            });
        });

    }
}


function filterMTRContents(/*codeIndex, heatNumbers*/) {
    //var codeIndex = $('.ddl-mtr-codes').val();
    //var selectedHeatNos = $('.ddl-heat-numbers').select2('data').map(item => item.text);

    var codeIndexes = $('.codeinfo-checkbox:checked').map(function () {
        return $(this).val();
    }).get();

    var selectedHeatNos = $('.heatno-checkbox:checked').map(function () {
        return $(this).val();
    }).get();

    console.log('Selected codeIndexes:', codeIndexes);
    console.log('Selected heat numbers:', selectedHeatNos);

    if ($.inArray('All', selectedHeatNos) != -1) {
        selectedHeatNos = [];
    }

    console.log('selectedHeatNos=' + selectedHeatNos);

    const sectionsInfo = mtrResponse.Sections;
    if (codeIndex === '-1') {
        $('.tbl-chemical tbody tr').removeClass('d-none');
        $('.tbl-mechanical tbody tr').removeClass('d-none');
        $('.tbl-heattreatment tbody tr').removeClass('d-none');

        $('.tbl-chemical tbody tr').each(function (index, tr) {
            if (selectedHeatNos.length > 0) {
                var trHeatNumber = $(tr).attr('data-heatno');
                if ($.inArray(trHeatNumber, selectedHeatNos) != -1) {
                    $(tr).removeClass('d-none');
                }
                else {
                    $(tr).addClass('d-none');
                }
            }
            $(this).find('td:first-child').text(index + 1);
        });

        $('.tbl-mechanical tbody tr').each(function (index, tr) {
            if (selectedHeatNos.length > 0) {
                var trHeatNumber = $(tr).attr('data-heatno');
                if ($.inArray(trHeatNumber, selectedHeatNos) != -1) {
                    $(tr).removeClass('d-none');
                }
                else {
                    $(tr).addClass('d-none');
                }
            }
            $(this).find('td:first-child').text(index + 1);
        });

        $('.tbl-heattreatment tbody tr').each(function (index, tr) {
            $(this).find('td:first-child').text(index + 1);
        });
    }
    else {
        //tbl-chemical
        //$('.tbl-chemical tbody tr').addClass('d-none');
        //$('.tbl-chemical tbody tr[data-codeIndex="' + codeIndex + '"]').removeClass('d-none');
        ////$('.tbl-chemical tbody tr[data-codeIndex="' + codeIndex + '"], tr[data-heatNo="' +  + f + '"]').removeClass('d-none');

        $('.tbl-chemical tbody tr').addClass('d-none');
        $('.tbl-chemical tbody tr').each(function (index, tr) {
            var trCodeIndex = $(tr).attr('data-codeIndex');
            var trHeatNumber = $(tr).attr('data-heatno');

            if (trCodeIndex === codeIndex) {
                $(tr).removeClass('d-none');
                if (selectedHeatNos.length > 0) {
                    if ($.inArray(trHeatNumber, selectedHeatNos) != -1) {
                        //console.log(trHeatNumber + " is in array");
                        $(tr).removeClass('d-none');
                    }
                    else {
                        //console.log(trHeatNumber + " is NOT in array");
                        $(tr).addClass('d-none');
                    }
                }
            }
        });

        $('.tbl-chemical tbody tr:not(.d-none)').each(function (index, tr) {
            $(this).find('td:first-child').text(index + 1);
        });

        //tbl-mechanical
        //$('.tbl-mechanical tbody tr').addClass('d-none');
        //$('.tbl-mechanical tbody tr[data-codeIndex="' + codeIndex + '"]').removeClass('d-none');

        $('.tbl-mechanical tbody tr').addClass('d-none');
        $('.tbl-mechanical tbody tr').each(function (index, tr) {
            var trCodeIndex = $(tr).attr('data-codeIndex');
            var trHeatNumber = $(tr).attr('data-heatno');

            if (trCodeIndex === codeIndex) {
                $(tr).removeClass('d-none');
                if (selectedHeatNos.length > 0) {
                    if ($.inArray(trHeatNumber, selectedHeatNos) != -1) {
                        //console.log(trHeatNumber + " is in array");
                        $(tr).removeClass('d-none');
                    }
                    else {
                        //console.log(trHeatNumber + " is NOT in array");
                        $(tr).addClass('d-none');
                    }
                }
            }
        });

        $('.tbl-mechanical tbody tr:not(.d-none)').each(function (index, tr) {
            $(this).find('td:first-child').text(index + 1);
        });

        //tbl-heattreatment
        $('.tbl-heattreatment tbody tr').addClass('d-none');
        $('.tbl-heattreatment tbody tr[data-codeIndex="' + codeIndex + '"]').removeClass('d-none');

        $('.tbl-heattreatment tbody tr:not(.d-none)').each(function (index, tr) {
            $(this).find('td:first-child').text(index + 1);
        });
    }
}


function buildValidationReportMTR(response) {
    debugger;
    console.log('buildValidationReportMTR--', response);
    mtrResponse = response;

    let $container = $('<div class="row" />');

    // Create card for Header Information
    let cardRoot = $('<div>').addClass('col-md-12').appendTo($container);
    let card = $('<div>')
        .addClass('card')
        .css({ margin: '10px', 'box-shadow': '0 0 6px rgba(0,0,0, .2)' })
        .appendTo(cardRoot);

    let cardHeader = $('<div>').addClass('card-header').appendTo(card);
    $('<h2>').text('Header Information').appendTo(cardHeader);
    let cardBody = $('<div>').addClass('card-body').appendTo(card);
    let $ulCodes = $('<ul>').addClass('list-group list-group-flush ul-headerinfo').appendTo(cardBody);


    var $selectCodes = $('<select class="form-select select select2 w-50 ddl-mtr-codes ms-2" style="display:inline;"><option value="-1">ALL</option></select>');
    var $option;
    const codes = response.HeaderInformation.CodeInfo;
    $.each(codes, function (index, item) {
        $option = $('<option value="' + index + '">' + item + '</option>');
        $selectCodes.append($option);
    });

    var $selectHeatNo = $('<select class="form-select select ddl-heat-numbers" multiple><option value="-1">All</option></select>');
    //var $selectHeatNo = $('<select class="form-select select ddl-heat-numbers w-50 ms-2" style="display:inline;" multiple="multiple"></select>');
    $option = '';
    const heatNos = response.HeaderInformation.HeatNumbers;
    console.log('heatNos=' + heatNos);
    $.each(heatNos, function (index, item) {
        $option = $('<option value="' + index + '">' + item + '</option>');
        $selectHeatNo.append($option);
    });


    // 1. Render StandardCodes
    const standardCodes = response.HeaderInformation.StandardCodes;
    if (standardCodes && typeof standardCodes === 'object') {
        $.each(standardCodes, function (codeType, codeList) {
            let codesStr = Array.isArray(codeList) ? codeList.join(', ') : codeList;
            //console.log(codesStr);
            let $li = $('<li class="list-group-item">').html(`<label class="fw-bold">${codeType} : </label> ${codesStr}`);
            $ulCodes.append($li);
        });
    }

    // 2. Render other header fields
    $.each(response.HeaderInformation, function (key, value) {
        let displayValue = 'not applicable';
        if (Array.isArray(value)) {
            displayValue = value.length > 0 ? value.join(', ') : 'not applicable';
        } else if (typeof value === 'object') {
            displayValue = Object.entries(value).map(([k, v]) => `${k}: ${v}`).join(', ');
        } else if (typeof value === 'string') {
            displayValue = value;
        }

        let $li = $('<li class="list-group-item">').html(`<label class="fw-bold">${key} : </label> ${displayValue}`);
        if (key === 'CodeInfo') {
            $li = $('<li class="list-group-item">').html(`<label class="fw-bold">${key} : </label> `).append($selectCodes);
        }
        if (key === 'HeatNumbers') {
            $li = $('<li class="list-group-item">').html(`<label class="fw-bold">${key} : </label> `).append($selectHeatNo);
        }


        $ulCodes.append($li);
    });


    let $reportOutputDiv = $('<div>').addClass('col-md-12 report-output').appendTo($container);
    let $reportSectionDiv = $('<div id="accordionExample">').addClass('accordion').appendTo($reportOutputDiv);

    // Code Group
    $.each(codes, function (codeIndex, item) {
        let $codeSection = $('<div>').addClass('accordion-item codeIndex-' + codeIndex).appendTo($reportSectionDiv);
        
        let $codeSectionHeader = $('<h2>').attr('id', 'heading_' + codeIndex).addClass('accordion-header').append(
            $('<button>')
                .addClass('accordion-button collapsed fw-bold')
                .attr('type', 'button')
                .attr('data-bs-toggle', 'collapse')
                .attr('data-bs-target', '#collapse_' + codeIndex)
                .attr('aria-expanded', 'false')
                .attr('aria-controls', 'collapse_' + codeIndex)
                .text(item)
        );
        $codeSectionHeader.appendTo($codeSection);
        // show
        let $codeSectionRoot = $('<div>').addClass('accordion-collapse collapse').attr('id', 'collapse_' + codeIndex).attr('data-bs-parent', '#accordionExample').attr('aria-labelledby', 'heading_' + codeIndex);
        $codeSectionRoot.appendTo($codeSection);

        let $codeSectionBody = $('<div>').addClass('accordion-body codeindex-' + codeIndex).appendTo($codeSectionRoot);

        // Sections rendering
        const sectionsInfo = response.Sections;
        console.log('sectionsInfo=>', sectionsInfo);
        //debugger
        $.each(sectionsInfo, function (sectionIndex, sectionItem) {
            let panelTitle = sectionItem.Name.replace(/_/g, " ");
            let cardRoot = $('<div>').addClass('col-md-12').appendTo($codeSectionBody);
            let card = $('<div>')
                .addClass('card')
                .css({ margin: '10px', 'box-shadow': '0 0 6px rgba(0,0,0, .2)' })
                .appendTo(cardRoot);

            let cardHeader = $('<div>').addClass('card-header').appendTo(card);
            $('<h2>').text(panelTitle).appendTo(cardHeader);

            let cardBody = $('<div>').addClass('card-body').appendTo(card);
            let responsiveDiv = $('<div>').addClass('table-responsive').appendTo(cardBody);

            var tblClass = ' tbl-' + panelTitle.replace(/\s/g, '').toLowerCase() + '-' + codeIndex;
            var tableClass = ' table-' + panelTitle.replace(/\s/g, '').toLowerCase();
            let $table = $('<table>').addClass('table table-striped table-bordered ' + tableClass + tblClass).appendTo(responsiveDiv);
            let $thead = $('<thead>').appendTo($table);
            let $thHtml = $('<tr><th class="w-5">#</th><th class="w-20">Name</th><th class="w-15">Input Value</th><th class="w-15">Standard Value</th><th class="w-10">Difference</th><th class="w-25">Reason</th><th class="w-20">Heat No</th><th class="w-10">Compliance</th></tr>');
            $thHtml.appendTo($thead);

            let $tbody = $('<tbody>').appendTo($table);
            let rowCount = 0;
            //debugger
            if (Array.isArray(sectionItem.Elements[codeIndex])) {

                $.each(sectionItem.Elements[codeIndex], function (elemIndex, element) {
                    //debugger
                    let statusClass = element.Status === 'Compliant' ? 'text-success' : element.Status === 'Non-Compliant' ? 'text-danger' : '';

                    let elementName = element.Symbol ? `${element.Name} (${element.Symbol})` : element.Name;
                    let heatNoMatch = element.HeatNo && element.HeatNo.match(/([A-Z0-9]{2,})/i);
                    let heatNo = heatNoMatch ? heatNoMatch[1] : '';

                    let $tr = $('<tr data-codeIndex="' + codeIndex + '" data-heatNo="' + heatNo + '" >').append(
                        $('<td>').text(++rowCount),
                        $('<td>').text(elementName.replace(/_/g, " ")),
                        $('<td>').text(element.Input || ''),
                        $('<td>').text(element.Standard || ''),
                        $('<td>').text(element.Difference || ''),
                        $('<td>').text(element.Reason || ''),
                        $('<td>').text(heatNo),
                        $('<td>').addClass(statusClass).text(element.Status || '')
                    );

                    $tr.appendTo($tbody);
                });
            }

        });
    });

    showAIResults($container);
    renderStep2Checkboxes(response.HeaderInformation);

}

function renderStep2Checkboxes(headerInfo) {
    const $codeInfoContainer = $('#codeInfoList').empty();
    const $heatNoContainer = $('#heatNumberList').empty();

    const codeInfos = headerInfo.CodeInfo || [];
    const heatNumbers = headerInfo.HeatNumbers || [];

    // Render CodeInfo checkboxes
    codeInfos.forEach((code, index) => {
        const $checkbox = $(`
            <div class="form-check">
                <input class="form-check-input codeinfo-checkbox" type="checkbox" value="${index}" id="codeinfo-${index}" checked>
                <label class="form-check-label" for="codeinfo-${index}">${code}</label>
            </div>
        `);
        $codeInfoContainer.append($checkbox);
    });

    renderHeatNumbersBySelectedCodes(codeInfos, heatNumbers);

    // Bind checkbox events
    $(document).off('change.codeinfo').on('change.codeinfo', '.codeinfo-checkbox', function () {
        const selectedCodes = $('.codeinfo-checkbox:checked').map(function () {
            return parseInt($(this).val());
        }).get();

        renderHeatNumbersBySelectedCodes(codeInfos, heatNumbers, selectedCodes);
        filterMTRReprot(); // refresh view
    });

    $(document).off('change.heatno').on('change.heatno', '.heatno-checkbox', function () {
        filterMTRReprot(); // re-filter when heat number changed
    });
}

function renderHeatNumbersBySelectedCodes(codeInfos, heatNumbers, selectedCodes = []) {
    const $heatNoContainer = $('#heatNumberList').empty();

    const codesToUse = selectedCodes.length > 0 ? selectedCodes : codeInfos.map((_, i) => i);
    const filteredHeatNos = [];

    codesToUse.forEach(codeIndex => {
        if (Array.isArray(heatNumbers[codeIndex])) {
            filteredHeatNos.push(...heatNumbers[codeIndex]);
        }
    });

    const uniqueHeatNos = [...new Set(filteredHeatNos)];

    uniqueHeatNos.forEach((heatNo, idx) => {
        const $checkbox = $(`
            <div class="form-check">
                <input class="form-check-input heatno-checkbox" type="checkbox" value="${heatNo}" id="heatno-${idx}" checked>
                <label class="form-check-label" for="heatno-${idx}">${heatNo}</label>
            </div>
        `);
        $heatNoContainer.append($checkbox);
    });
}


function buildResponseMTR(response) {
    console.log('buildResponseMTR', response);
    mtrResponse = response;
    //debugger;

    let $container = $('<div class="row" />');

    // Create card for Header Information
    let cardRoot = $('<div>').addClass('col-md-12').appendTo($container);
    let card = $('<div>')
        .addClass('card')
        .css({ margin: '10px', 'box-shadow': '0 0 6px rgba(0,0,0, .2)' })
        .appendTo(cardRoot);

    let cardHeader = $('<div>').addClass('card-header').appendTo(card);
    $('<h2>').text('Header Information').appendTo(cardHeader);
    let cardBody = $('<div>').addClass('card-body').appendTo(card);
    let $ulCodes = $('<ul>').addClass('list-group list-group-flush ul-headerinfo').appendTo(cardBody);


    var $selectCodes = $('<select class="form-select select select2 w-50 ddl-mtr-codes ms-2" style="display:inline;"><option value="-1">ALL</option></select>');
    var $option;
    const codes = response.HeaderInformation.CodeInfo;
    $.each(codes, function (index, item) {
        $option = $('<option value="' + index + '">' + item + '</option>');
        $selectCodes.append($option);
    });

    var $selectHeatNo = $('<select class="form-select select ddl-heat-numbers" multiple><option value="-1">All</option></select>');
    //var $selectHeatNo = $('<select class="form-select select ddl-heat-numbers w-50 ms-2" style="display:inline;" multiple="multiple"></select>');
    $option = '';
    const heatNos = response.HeaderInformation.HeatNumbers;
    console.log('heatNos=' + heatNos);
    $.each(heatNos, function (index, item) {
        $option = $('<option value="' + index + '">' + item + '</option>');
        $selectHeatNo.append($option);
    });
    /*$selectHeatNo.select2({
        placeholder: 'All',
        theme: 'bootstrap-5',
        width: '100%',
        dropdownParent: $('.ui-new-dialog-widget-content'),
        multiple: true
    });*/
    

    // 1. Render StandardCodes
    const standardCodes = response.HeaderInformation.StandardCodes;
    if (standardCodes && typeof standardCodes === 'object') {
        $.each(standardCodes, function (codeType, codeList) {
            let codesStr = Array.isArray(codeList) ? codeList.join(', ') : codeList;
            //console.log(codesStr);
            let $li = $('<li class="list-group-item">')
                .html(`<label class="fw-bold">${codeType} : </label> ${codesStr}`);
            $ulCodes.append($li);
        });
        delete response.HeaderInformation.StandardCodes;
    }

    // 2. Render other header fields
    $.each(response.HeaderInformation, function (key, value) {
        let displayValue = 'not applicable';
        if (Array.isArray(value)) {
            displayValue = value.length > 0 ? value.join(', ') : 'not applicable';
        } else if (typeof value === 'object') {
            displayValue = Object.entries(value).map(([k, v]) => `${k}: ${v}`).join(', ');
        } else if (typeof value === 'string') {
            displayValue = value;
        }

        let $li = $('<li class="list-group-item">').html(`<label class="fw-bold">${key} : </label> ${displayValue}`);
        if (key === 'CodeInfo') {
            $li = $('<li class="list-group-item">').html(`<label class="fw-bold">${key} : </label> `).append($selectCodes);
        }
        if (key === 'HeatNumbers') {
            $li = $('<li class="list-group-item">').html(`<label class="fw-bold">${key} : </label> `).append($selectHeatNo);
        }


        $ulCodes.append($li);
    });

    // Sections rendering
    const sectionsInfo = response.Sections;
    console.log('sectionsInfo=>', sectionsInfo);

    $.each(sectionsInfo, function (sectionIndex, sectionItem) {
        let panelTitle = sectionItem.Name.replace(/_/g, " ");
        let cardRoot = $('<div>').addClass('col-md-12').appendTo($container);
        let card = $('<div>')
            .addClass('card')
            .css({ margin: '10px', 'box-shadow': '0 0 6px rgba(0,0,0, .2)' })
            .appendTo(cardRoot);

        let cardHeader = $('<div>').addClass('card-header').appendTo(card);
        $('<h2>').text(panelTitle).appendTo(cardHeader);

        let cardBody = $('<div>').addClass('card-body').appendTo(card);
        let responsiveDiv = $('<div>').addClass('table-responsive').appendTo(cardBody);

        var tblClass = ' tbl-' + panelTitle.replace(/\s/g, '').toLowerCase();
        let $table = $('<table>').addClass('table table-striped table-bordered ' + tblClass).appendTo(responsiveDiv);
        let $thead = $('<thead>').appendTo($table);
        let $thHtml = $(`
            <tr>
                <th class="w-5">#</th>
                <th class="w-20">Name</th>
                <th class="w-15">Input Value</th>
                <th class="w-15">Standard Value</th>
                <th class="w-10">Difference</th>
                <th class="w-25">Reason</th>
                <th class="w-20">Heat No</th>
                <th class="w-10">Compliance</th>
            </tr>
        `);
        $thHtml.appendTo($thead);

        let $tbody = $('<tbody>').appendTo($table);
        let rowCount = 0;

        if (Array.isArray(sectionItem.Elements)) {
            $.each(sectionItem.Elements, function (elemGrpIndex, elementGroup) {
                if (!Array.isArray(elementGroup)) return;

                $.each(elementGroup, function (elemIndex, element) {
                    let statusClass = element.Status === 'Compliant' ? 'text-success' :
                        element.Status === 'Non-Compliant' ? 'text-danger' : '';

                    let elementName = element.Symbol ? `${element.Name} (${element.Symbol})` : element.Name;
                    let heatNoMatch = element.HeatNo && element.HeatNo.match(/([A-Z0-9]{2,})/i);
                    let heatNo = heatNoMatch ? heatNoMatch[1] : '';

                    let $tr = $('<tr data-codeIndex="' + elemGrpIndex + '" data-heatNo="' + heatNo + '" >').append(
                        $('<td>').text(++rowCount),
                        $('<td>').text(elementName.replace(/_/g, " ")),
                        $('<td>').text(element.Input || ''),
                        $('<td>').text(element.Standard || ''),
                        $('<td>').text(element.Difference || ''),
                        $('<td>').text(element.Reason || ''),
                        $('<td>').text(heatNo),
                        $('<td>').addClass(statusClass).text(element.Status || '')
                    );

                    $tr.appendTo($tbody);
                });
            });
        }
    });

    showAIResults($container);
    renderStep2Checkboxes(response.HeaderInformation);
}

function processDocumentForOCRV1(docType) {
    debugger;
    //var uploadedFile = $('#dZUploadDocument').get(0).dropzone.getAcceptedFiles()[0];
    var uploadedFile = $('#uploadDropzone').get(0).dropzone.getAcceptedFiles()[0];
    var formData = new FormData();
    formData.append('file', uploadedFile);
    formData.append('docType', docType);

    $.blockUI({ baseZ: 10000, message: '<h2><i class="fa fa-spinner fa-spin fa-5x fa-fw"></i><span class="sr-only">' + langKeys[currentLanguage]['Processing'] + '</h2>' });

    $.ajax({
        type: 'POST',
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=TryAI',
        data: formData,
        contentType: false,
        processData: false,
        success: function (response) {
            console.log('processDocumentForOCRV1 response--', response);
            $.unblockUI();
            debugger

            try {
                response = JSON.parse(response);
                console.log("processDocumentForOCRV1 response => ", response);

                var html = '';
                switch (docType) {
                    case 'MTR':
                        var validationResult = response.ValidationResult;

                        if (validationResult.CompliantStatus === 'Error') {
                            Swal.fire({
                                title: "Unable to Scan",
                                text: validationResult.Reason,
                                icon: "error",
                            });
                            return;
                        }
                        if (validationResult.ResponeInformation === undefined || validationResult.ResponeInformation.Acknowledge === 'Failed') {
                            Swal.fire({
                                title: "Unable to Scan",
                                text: "Unable to scan the required information for MTR Validation, please ensure document is complete and legible.",
                                icon: "error",
                            });
                            return;
                        }
                        console.log('Compliant Status--', validationResult.ResponeInformation.CompliantStatus);
                        //if (validationResult.ResponeInformation.Acknowledge === 'Success' && validationResult.ResponeInformation.CompliantStatus == 'Non-Compliant')
                        if (validationResult.ResponeInformation.Acknowledge === 'Success' && $.inArray(false, validationResult.ResponeInformation.CompliantStatus) !== -1) {
                            //if (validationResult.ResponeInformation.Acknowledge === 'Success' && validationResult.ResponeInformation.CompliantStatus.every(status => status === true)) {
                            //if (
                            //    validationResult.ResponeInformation &&
                            //    validationResult.ResponeInformation.Acknowledge === 'Success' &&
                            //    Array.isArray(validationResult.ResponeInformation.CompliantStatus) &&
                            //    validationResult.ResponeInformation.CompliantStatus.includes(false)
                            //) {

                            console.log('Non-Compliant');
                            Swal.fire({
                                title: "Document has been scanned but Non-Compliant",
                                text: "Do you want to modify the result ?",
                                icon: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#3085d6",
                                cancelButtonColor: "#d33",
                                confirmButtonText: "Yes",
                                cancelButtonText: "No"
                            }).then((result) => {
                                $('#divAddDialogContents').attr("displayalert", "yes");

                                if (result.isConfirmed) {
                                    //console.log('Open input form');
                                    createMTRInputForm(response.KeyValues);
                                }
                                else {
                                    //html = buildResponseMTR(validationResult);
                                    html = buildValidationReportMTR(validationResult);
                                }


                            });
                            return;
                        }
                        else {
                            $('#divAddDialogContents').attr("displayalert", "yes");
                            //html = buildResponseMTR(validationResult);
                            html = buildValidationReportMTR(validationResult);
                            $('#step1Content').addClass('d-none');
                            $('#step2Content').removeClass('d-none');
                            return;
                        }
                        break;
                    case 'WPS':
                        html = buildResponseWPS(response);
                        break;
                    case 'PQR':
                        html = buildResponsePQR(response);
                        break;
                    case 'WPQ':
                        html = buildResponseWPQ(response);
                        break;
                    default:
                        html = '';
                        break;
                }
            } catch (ex) {
                console.log("Unable to parse json => ", response);

                Swal.fire({
                    title: "Unable to Process",
                    text: "Unable to process the AI response. Please try again later",
                    icon: "error",
                });
                return;
            }

        },
        error: function (jqXHR, textStatus, errorThrown) {
            //debugger
            var responseTitle = $(jqXHR.responseText).filter('title').get(0);
            //alert($(responseTitle).text() + "\n" + formatErrorMessage(jqXHR, errorThrown)); 

            Swal.fire({
                title: responseTitle,
                text: formatErrorMessage(jqXHR, errorThrown),
                icon: "error",
            });

            $.unblockUI();
            console.log('Error occurred processDocumentForOCR()');
            console.log(jqXHR.responseText);
        }
    });
}

// working function start--

//function createMTRInputForm(response) {
//    console.log('createMTRInputForm', response);

//    // Show step 2
//    $('#step1Content').addClass('d-none');
//    $('#step2Content').removeClass('d-none');

//    // Highlight stepper
//    $('.wizard-step').removeClass('active completed');
//    $('#step1Header').addClass('completed');
//    $('#step2Header').addClass('active');

//    // Render PDF in step 2
//    renderStep2PDF();

//    // 👉 Render Code Info
//    const codeInfos = [...(response.ASME || []), ...(response.ASTM || [])];
//    const $codeContainer = $('#codeInfoList').empty();

//    codeInfos.forEach((code, idx) => {
//        const id = `code_${idx}`;
//        $codeContainer.append(`
//            <div class="form-check">
//                <input class="form-check-input codeinfo-checkbox" type="checkbox" value="${code}" id="${id}" checked>
//                <label class="form-check-label" for="${id}">${code}</label>
//            </div>
//        `);
//    });

//    // 👉 Render Heat Numbers
//    const heatNos = (response.CHEMICAL || [])[0]?.VALUE?.map(v => v["primary key"]) || [];
//    const $heatContainer = $('#heatNumberList').empty();

//    $('#codeInfoList')
//        .prev('p') // select the <p> before #codeInfoList
//        .text(`The AI found ${codeInfos.length} Code Info value${codeInfos.length !== 1 ? 's' : ''}. Select which to include in the review.`);

//    $('#heatNumberList')
//        .prev('p') // select the <p> before #heatNumberList
//        .text(`The AI found ${heatNos.length} heat number${heatNos.length !== 1 ? 's' : ''}. Select which to include in the review.`);

//    heatNos.forEach((heat, idx) => {
//        const id = `heat_${idx}`;
//        $heatContainer.append(`
//            <div class="form-check">
//                <input class="form-check-input heatno-checkbox" type="checkbox" value="${heat}" id="${id}" checked>
//                <label class="form-check-label" for="${id}">${heat}</label>
//            </div>
//        `);
//    });
//}

//$('#btnContinueReview').on('click', function () {
//    console.log(" Button Clicked--");
//    // ✅ Validate Code Info selection
//    const selectedCodes = $('.codeinfo-checkbox:checked').map(function () {
//        return $(this).val();
//    }).get();

//    if (selectedCodes.length === 0) {
//        alert('Please select at least one Code Info value.');
//        return;
//    }

//    // ✅ Get selected heat numbers
//    const selectedHeats = $('.heatno-checkbox:checked').map(function () {
//        return $(this).val();
//    }).get();

//    // ✅ Populate Step 3 list
//    const $list = $('#selectedHeatNoList').empty();
//    if (selectedHeats.length === 0) {
//        $list.append('<li class="list-group-item text-muted">No heat numbers selected.</li>');
//    } else {
//        selectedHeats.forEach(h => {
//            $list.append(`<li class="list-group-item">${h}</li>`);
//        });
//    }

//    // ✅ Switch to Step 3
//    $('.step-section').addClass('d-none');
//    $('#step3Content').removeClass('d-none');
//    $('.wizard-step').removeClass('active completed');
//    $('#step2Header').addClass('completed');
//    $('#step3Header').addClass('active');
//});

// working function end--


let globalHeatNumbers = []; // store for Step 3

function createMTRInputForm(response) {
    console.log('createMTRInputForm', response);

    // Show step 2
    $('#step1Content').addClass('d-none');
    $('#step2Content').removeClass('d-none');

    // Update wizard header
    $('.wizard-step').removeClass('active completed');
    $('#step1Header').addClass('completed');
    $('#step2Header').addClass('active');

    renderStep2PDF();

    // 👉 Render Code Info
    const codeInfos = [...(response.ASME || []), ...(response.ASTM || [])];
    const $codeContainer = $('#codeInfoList').empty();

    $('#codeInfoList')
        .prev('p')
        .text(`The AI found ${codeInfos.length} Code Info value${codeInfos.length !== 1 ? 's' : ''}. Select which to include in the review.`);

    codeInfos.forEach((code, idx) => {
        const id = `code_${idx}`;
        $codeContainer.append(`
            <div class="form-check">
                <input class="form-check-input codeinfo-checkbox" type="checkbox" value="${code}" id="${id}" checked>
                <label class="form-check-label" for="${id}">${code}</label>
            </div>
        `);
    });

    // 👉 Store Heat Numbers for Step 3
    globalHeatNumbers = (response.CHEMICAL || [])[0]?.VALUE?.map(v => v["primary key"]) || [];
}


$(document).on('click', '#btnContinueReview', function () {
    debugger;
    const selectedCodes = $('.codeinfo-checkbox:checked').map(function () {
        return $(this).val();
    }).get();

    if (selectedCodes.length === 0) {
        alert('Please select at least one Code Info value.');
        return;
    }

    // 👉 Render heat numbers as checkboxes in Step 3
    const $heatContainer = $('#heatNumberListStep3').empty();
    $('#heatNoSummaryStep3').text(`The AI found ${globalHeatNumbers.length} heat number${globalHeatNumbers.length !== 1 ? 's' : ''}. Select which to include in the review.`);

    if (globalHeatNumbers.length === 0) {
        $heatContainer.append('<p class="text-muted">No heat numbers detected.</p>');
    } else {
        globalHeatNumbers.forEach((heat, idx) => {
            const id = `step3_heat_${idx}`;
            $heatContainer.append(`
                <div class="form-check">
                    <input class="form-check-input step3-heat-checkbox" type="checkbox" value="${heat}" id="${id}" checked>
                    <label class="form-check-label" for="${id}">${heat}</label>
                </div>
            `);
        });
    }

    // Move to Step 3
    $('#step2Content').addClass('d-none');   // only hide current step
    $('#step3Content').removeClass('d-none'); // show next step

    $('.wizard-step').removeClass('active completed');
    $('#step2Header').addClass('completed');
    $('#step3Header').addClass('active');
});








//function createMTRInputForm(response) {
//    console.log('createMTRInputForm', response);
//    var $formTitle = 'Modify Results',
//        $requiredText = 'All fields are required',
//        dimensionTitle = 'Dimentions',
//        $asme = response.ASME,
//        $astm = response.ASTM,
//        $uns = response.UNS,
//        $grade = response.GRADE;

//    //debugger
//    var $newForm = $('<div />', { class: 'row col-12' })
//        .append(
//            // ASME Information
//            $('<div class="col-md-6" />').append(
//                $('<div class="form-group" />').append(
//                    $('<label />', { class: 'control-label fw-bold', html: 'ASME' }),
//                $('<input />', { class: 'form-control asme', type: 'text', value: $asme.join(), 'data-control-id': 'ASME', placeholder: '' }),
//                ),
//            ),
//            $('<div class="col-md-6" />').append(
//                $('<div class="form-group" />').append(
//                    $('<label />', { class: 'control-label fw-bold', html: 'ASTM' }),
//                    $('<input />', { class: 'form-control astm', type: 'text', value: $astm.join(), 'data-control-id': 'ASTM', placeholder: '' }),
//                ),
//            ),
//            $('<div class="col-md-6" />').append(
//                $('<div class="form-group" />').append(
//                    $('<label />', { class: 'control-label fw-bold', html: 'UNS' }),
//                    $('<input />', { class: 'form-control uns', type: 'text', value: $uns.join(), 'data-control-id': 'UNS', placeholder: '' }),
//                ),
//            ),
//            $('<div class="col-md-6" />').append(
//                $('<div class="form-group" />').append(
//                    $('<label />', { class: 'control-label fw-bold', html: 'GRADE' }),
//                    $('<input />', { class: 'form-control grade', type: 'text', value: $grade.join(), 'data-control-id': 'GRADE', placeholder: '' }),
//                ),
//            ),
//        );

//    var i = 1;
//    $newForm.append($('<h2 />').append(dimensionTitle));
//    $.each(response.DIMENSION, function (index, item) {
//        $newForm.append(
//            $('<div class="col-md-6" />').append(
//                $('<div class="form-group Dimension" />').append(
//                    $('<label />', { class: 'control-label fw-bold', html: index }),
//                    $('<input />', { class: 'form-control ' + index, type: 'text', value: item, 'data-control-id': index, id: 'txtdimension' + i++, placeholder: index }),
//                )
//            )
//        );
//    });

//    i = 1;
//    var elementValue = '';
//    $newForm.append($('<h2 />').append('Chemical'));
//    $.each(response.CHEMICAL, function (index, item) {
//        //debugger
//        if ($.type(item.VALUE) == 'array')
//            elementValue = item.VALUE[0].value;
//        else
//            elementValue = item.VALUE;

//        $newForm.append(
//            $('<div class="col-md-6 Chemical" />').append(
//                $('<div class="form-group" />').append(
//                    $('<label />', { class: 'control-label fw-bold', html: item.ELEMENT }),
//                    $('<input />', { class: 'form-control ' + item.ELEMENT, type: 'text', value: elementValue, 'data-control-id': item.ELEMENT, id: 'txtChemical' + i++, placeholder: item.ELEMENT }),
//                )
//            )
//        );

//    });

//    i = 1;
//    $newForm.append($('<h2 />').append('Mechanical'));

//    console.log("response.MECHANICAL => ", response.MECHANICAL);
//    $.each(response.MECHANICAL, function (index, item) {

//        //const seenKeys = {}; const uniqueValues = [];

//        //$.each(item.VALUE, function (i, data)
//        //{
//        //    const key = data["primary key"].join("|");
//        //    if (!seenKeys[key])
//        //    {
//        //        seenKeys[key] = true;
//        //        uniqueValues.push(data);
//        //    }
//        //});
//        if ($.type(item.VALUE) == 'array')
//            elementValue = item.VALUE[0].value;
//        else
//            elementValue = item.VALUE;

//        $newForm.append(
//            $('<div class="col-md-6 Mechanical" />').append(
//                $('<div class="form-group" />').append(
//                    $('<label />', { class: 'control-label fw-bold', html: item.ELEMENT.replace("_", " ") }),
//                    $('<input />', { class: 'form-control ' + item.ELEMENT, type: 'text', value: elementValue, 'data-control-id': item.ELEMENT, id: 'txtMechanical' + i++, placeholder: item.ELEMENT }),
//                )
//            )
//        );

//    });

//    modifyMtrResults($newForm);
//}

function modifyMtrResults(html) {
    $('#divDialogContents').html(html);
    $('#divDialogContents').mmsDialog({
        autoOpen: true,
        closeOnEscape: true,
        draggable: true,
        resizable: false,
        modal: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: 'Modify Results of OCR',
        maxwidth: 'auto',
        width: '70%',
        height: 800,
        open: function (event, ui) {

        },
        close: function () {
            $('#divDialogContents').html('');
        },
        buttons: [{
            text: 'Send',
            click: function () {
                scanOCRInput();
            }
        },
        {
            text: langKeys[currentLanguage]['msgBtnClose'],
            click: function () {
                $(this).mmsDialog("close");
            }
        }]
    });

    $('#divDialogContents').mmsDialog('open');
}

function scanOCRInput() {
    debugger
    var modifiedOnTimestamp = new Date().toISOString();
    console.log('modifiedOnTimestamp', modifiedOnTimestamp);
    var dimensionCtrls = $('div.Dimension');
    var dimensionJsonString = "{";
    let objDimension = {};

    $.each(dimensionCtrls, function (index, item) {
        var inputCtrl = $(item).find('input');
        var propName = $(inputCtrl).data('control-id');
        var propValue = $(inputCtrl).val();
        dimensionJsonString += '"' + propName + '"' + ":" + '"' + propValue + '"' + ","
        objDimension[propName] = propValue;
    });
    dimensionJsonString = dimensionJsonString.slice(0, -1);
    dimensionJsonString += "}";

    /*var newArray = dimensionCtrls.map(function (i, obj) {
        debugger
        var inputCtrl = $(obj).find('input');
        var propName = $(inputCtrl).data('control-id');
        var propValue = $(inputCtrl).val();
        return { propName: propValue };
    }); 
    debugger;*/


    var chemicalArr = [];
    $.each($('div.Chemical'), function (i, item) {
        var inputCtrl = $(item).find('input');
        var propName = $(inputCtrl).data('control-id').replace(/_/g, " ");
        var propValue = $(inputCtrl).val();
        chemicalArr.push({
            ELEMENT: propName,
            VALUE: propValue
        });
    });

    var mechanicalArr = [];
    $.each($('div.Mechanical'), function (i, item) {
        var inputCtrl = $(item).find('input');
        var propName = $(inputCtrl).data('control-id').replace(/_/g, " ");
        var propValue = $(inputCtrl).val();
        mechanicalArr.push({
            ELEMENT: propName,
            VALUE: propValue
        });
    });

    var staticData = { 'ASME': ['SA240'], 'ASTM': ['A240', 'A480', 'A262'], 'UNS': ['S30400', 'S30403'], 'GRADE': ['304L', '304'], 'DIMENSION': { 'THICKNESS': '1.125', 'WIDTH': '99.0', 'LENGTH': '258.0' }, 'HEAT_TREATMENT': 'Solution annealed @ 1900 degrees Fahrenheit minimum, then water cooled or rapidly cooled by air', 'CHEMICAL': [{ 'ELEMENT': 'CARBON', 'SYMBOL': 'C', 'VALUE': 0.021 }, { 'ELEMENT': 'MANGANESE', 'SYMBOL': 'MN', 'VALUE': 1.79 }, { 'ELEMENT': 'PHOSPHORUS', 'SYMBOL': 'P', 'VALUE': 0.033 }, { 'ELEMENT': 'SULFUR', 'SYMBOL': 'S', 'VALUE': 0.001 }, { 'ELEMENT': 'SILICON', 'SYMBOL': 'SI', 'VALUE': 0.51 }, { 'ELEMENT': 'CHROMIUM', 'SYMBOL': 'CR', 'VALUE': 18.21 }, { 'ELEMENT': 'NICKEL', 'SYMBOL': 'NI', 'VALUE': 8.51 }, { 'ELEMENT': 'COBALT', 'SYMBOL': 'CO', 'VALUE': 0.25 }, { 'ELEMENT': 'COPPER', 'SYMBOL': 'CU', 'VALUE': 0.51 }, { 'ELEMENT': 'MOLYBDENUM', 'SYMBOL': 'MO', 'VALUE': 0.47 }, { 'ELEMENT': 'NITROGEN', 'SYMBOL': 'N', 'VALUE': 0.031 }, { 'ELEMENT': 'COLUMBIUM', 'SYMBOL': 'NB', 'VALUE': 0.045 }, { 'ELEMENT': 'TITANIUM', 'SYMBOL': 'TI', 'VALUE': 0.012 }, { 'ELEMENT': 'ALUMINUM', 'SYMBOL': 'AL', 'VALUE': 0.006 }, { 'ELEMENT': 'TIN', 'SYMBOL': 'SN', 'VALUE': 0.003 }, { 'ELEMENT': 'TANTALUM', 'SYMBOL': 'TA', 'VALUE': 0.001 }], 'MECHANICAL': [{ 'ELEMENT': 'HARDNESS_HRBW', 'VALUE': 71 }, { 'ELEMENT': 'GRAIN_SIZE', 'VALUE': 6 }, { 'ELEMENT': 'YIELD_STRENGTH_PSI', 'VALUE': 42017 }, { 'ELEMENT': 'TENSILE_STRENGTH_PSI', 'VALUE': 82514 }, { 'ELEMENT': 'ELONGATION_IN_2', 'VALUE': 53.3 }], 'NAME': 'new castle.pdf' }
    var jsonData = {
        "ASME": [
            $('.asme').val()
        ],
        "ASTM": [
            $('.astm').val()
        ],
        "UNS": [
            $('.uns').val()
        ],
        "GRADE": [
            $('.grade').val()
        ],
        "DIMENSION": objDimension,
        "HEAT_TREATMENT": "Solution annealed @ 1900 degrees Fahrenheit minimum, then water cooled or rapidly cooled by air",
        "CHEMICAL": chemicalArr,
        "MECHANICAL": mechanicalArr,
        "NAME": "new castle.pdf",
        "ModifiedOn": modifiedOnTimestamp
    };

    mtrModifiedData = jsonData;

    var inputData =
    {
        'method': 'SaveModifiedAIResponse',
        jsonData: JSON.stringify(jsonData)
    }
    debugger

    $.blockUI();

    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=SaveModifiedAIResponse',
        data: inputData,
        success: function (response) {
            debugger;
            $.unblockUI();

            console.log("respnse => ", response);

            response = JSON.parse(response);
            var responseData = JSON.parse(response.data)

            var validationResult = responseData.ValidationResult;
            //html = buildResponseMTR(validationResult);
            html = buildValidationReportMTR(validationResult);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();

            var responseTitle = $(jqXHR.responseText).filter('title').get(0);
            //alert($(responseTitle).text() + "\n" + formatErrorMessage(jqXHR, errorThrown)); 

            Swal.fire({
                title: responseTitle,
                text: formatErrorMessage(jqXHR, errorThrown),
                icon: "error",
            });
            $.unblockUI();
            console.log('Error occurred scanOCRInput()');
            console.log(jqXHR.responseText);
        }
    });
}

function showAIResults(html) {
    $('#divDialogContents').html(html);
    $('#step1Content').addClass('d-none');
    $('#step2Content').removeClass('d-none');
    $('#step2Content .ai-result-container').html($container);
    //$('#divDialogContents').mmsDialog({
    //    autoOpen: true,
    //    closeOnEscape: true,
    //    draggable: true,
    //    resizable: true,
    //    modal: true,
    //    closeText: langKeys[currentLanguage]['ttClose'],
    //    title: 'MTR AI Validation Response',
    //    maxwidth: 'auto',
    //    width: '70%',
    //    height: 800,
    //    open: function (event, ui) {
    //        //$('#hdnOrganizationId').val(orgId);
            
    //        $('.ddl-heat-numbers').select2({
    //            placeholder: 'All',
    //            theme: 'bootstrap-5',
    //            width: '70%',
    //            dropdownParent: $('#divDialogContents'),
    //            multiple: true,
    //            //containerCssClass: "w-50 SM",
    //            //dropdownCssClass: "w-50"
    //        })
    //            .on("change", function (e) {
    //                //filterMTRContents();
    //                filterMTRReprot();
    //                /*var currentHeatNos = e.target.value;
    //                var allValues = $(this).val();
    //                console.log("current Value=" + currentHeatNos);
    //                console.log("all Values=" + e.val);
    //                if (currentHeatNos == '-1') {
    //                    console.log('load all heatnumbers');
    //                }
    //                else {
    //                    console.log('following heatnumbers=' + allValues);
    //                }*/
    //            });
    //            //.on("select2-opening", function () { console.log("opening");
    //            //}).on("select2-open", function () { console.log("open");
    //            //}).on("select2-close", function () { console.log("close");
    //            //}).on("select2-highlight", function (e) {console.log("highlighted val=" + e.val + " choice=" + e.choice.text);
    //            //}).on("select2-selecting", function (e) {console.log("selecting val=" + e.val + " choice=" + e.object.text);
    //            //}).on("select2-removed", function (e) {console.log("removed val=" + e.val + " choice=" + e.choice.text);
    //            //}).on("select2-loaded", function (e) {console.log("loaded (data property omitted for brevitiy)");
    //            //}).on("select2-focus", function (e) { console.log("focus"); });
    //    },
    //    close: function () {
    //        $('#divDialogContents').html('');
    //    },
    //    buttons: [
    //        {
    //            text: langKeys[currentLanguage]['msgBtnClose'],
    //            click: function () {
    //                $(this).mmsDialog("close");
    //            }
    //        }]
    //});

    $('#divDialogContents').mmsDialog('open');
}















// Welder Ratings 

function loadWelderRatings(welderId) {
    const css = `
        <style>
            .ratings-container {
                font-size: 15px;
                max-width: 1100px;
                margin: auto;
            }
            .rating-entry {
                border: 1px solid #dee2e6;
                padding: 16px;
                margin-bottom: 15px;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
            .rating-entry div {
                margin-bottom: 10px;
            }
            .rating-entry .label {
                font-weight: bold;
                color: #333;
                display: inline-block;
                font-size: 18px;
                width: 220px; /* Adjust left label width */
            }
            .rating-entry .value {
                display: inline-block;
                color: #555;
                font-size: 18px;
            }
            #viewWelderRatingsPopup .modal-body {
                max-height: 70vh;  /* or any fixed height like 500px */
                overflow-y: auto;
                }
                body.modal-open {
                    overflow: hidden !important;
                }
        </style>
    `;
    $('head').append(css);

    $.ajax({
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=GetWelderRatings',
        type: 'POST',
        data: { welderId: welderId },
        success: function (response) {
            if (response.success) {
                var ratings = response.data;
                var container = $('#viewWelderRatingsPopup .ratings-container');
                container.empty();

                $.each(ratings, function (index, rating) {
                    var html = `
                        <div class="rating-entry">
                            <div><span class="label">Performance Rating :</span> <span class="value">${rating.PerformanceRating ?? '-'}</span></div>
                            <div><span class="label">Performance Comment :</span> <div class="value">${rating.PerformanceComment ?? '-'}</div></div>
                            <div><span class="label">Weld Count :</span> <span class="value">${rating.WeldCount ?? '-'}</span></div>
                            <div><span class="label">Weld Count Comment :</span> <div class="value">${rating.WeldCountComment ?? '-'}</div></div>
                            <div><span class="label">Repairs :</span> <span class="value">${rating.Repairs ?? '-'}</span></div>
                            <div><span class="label">Repairs Comment :</span> <div class="value">${rating.RepairsComment ?? '-'}</div></div>
                            <div><span class="label">Work Ethic Rating :</span> <span class="value">${rating.WorkEthicRating ?? '-'}</span></div>
                            <div><span class="label">Work Ethic Comment :</span> <div class="value">${rating.WorkEthicComment ?? '-'}</div></div>
                        </div>
                    `;
                    container.append(html);
                });

                //$('#viewWelderRatingsPopup').modal('show');
            } else {
                alert("Failed to load welder ratings.");
            }
        },
        error: function () {
            alert("Error occurred while fetching welder ratings.");
        }
    });
}







// Work History

function loadWorkHistory(userId) {
    $.ajax({
        type: 'POST',
        url: 'ManageWelders.aspx/LoadUserControlWorkHistory',

        data: JSON.stringify({ userId: 1000 }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            $('#divDialogContents').html(response.d);
            $('#divDialogContents').mmsDialog({
                autoOpen: true,
                closeOnEscape: true,
                draggable: true,
                resizable: false,
                modal: true,
                closeText: langKeys[currentLanguage]['ttClose'],
                title: 'Work History',
                maxwidth: 'auto',
                width: '70%',
                open: function (event, ui) {
                    getWorkHistory(userId);
                },
                close: function () {
                    $('#divDialogContents').html('');
                },
                buttons: [{
                    text: langKeys[currentLanguage]['msgBtnClose'],
                    click: function () {
                        $(this).mmsDialog("close");
                    }
                }]
            });

            $('#divDialogContents').mmsDialog('open');
        },
        error: function (error) {
            console.log(error);
        }
    });
}


//WPQ

function saveWpq() {
    /*var wpq = {
        FileName: 'WQ24-2250 Tobias Taboada 1in E7018 AWS D1.1.pdf.pdf',
        StartDate: '2023-01-01',
        EndDate: '2023-06-30',
        ExpiryDate: '2023-12-31',
        CertifiedAuthority: 'STI Group',
        CertificateDate: '2023-07-01',
        InspectorName: 'Tyler Carraway',
        LabNo: 'WQ24-2250',
        Notes: 'Static',
        JobId: 1,
        UserId: 2945
    };*/
    var wpq = {
        FileName: $('#').val(),
        StartDate: $('#').val(),
        EndDate: $('#').val(),
        ExpiryDate: $('#').val(),
        CertifiedAuthority: $('#').val(),
        CertificateDate: $('#').val(),
        InspectorName: $('#').val(),
        LabNo: $('#').val(),
        Notes: $('#').val(),
        JobId: $('#').val(),
        UserId: $('#').val()
    };

    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: { wpq: JSON.stringify(wpq), method: 'SaveWpq' },
            success: function (response) {
                //var res = JSON.parse(response);
                switch (response.success) {
                    case true:
                        //updateView(response.data);
                        $.growlUI(null, langKeys[currentLanguage]['sSave']);
                        break;
                    default:
                        $.unblockUI();
                        showErrorDialog(response.message);
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred saveWpq(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        showExceptionDialog(e.message);
    }
}


// Upload WPQ
function uploadWpq() {
    debugger;
    console.log('uploadWpq--');
    var wpq = {
        FileName: $('#wpqFile')[0].files[0]?.name ?? '',
        StartDate: new Date().toISOString().replace("T", " ").substring(0, 23),
        EndDate: $('#txtExpDate').val(),
        ExpiryDate: $('#txtExpDate').val(),
        CertifiedAuthority: "",
        CertificateDate: $('#txtExpDate').val(),
        InspectorName: "Test",
        LabNo: "0",
        Notes: "",
        JobId: parseInt($('#selectJob').val()) || 0,
        TestId: 0, // safe default
        UserId: parseInt($('#userIdHidden').val()) || 0,
        UpdatedBy: parseInt($('#userIdHidden').val()) || 0,
        IsDraft: false,
        fieldData: ""
    };

    console.log('wpq', wpq);

    var formData = new FormData();
    formData.append("wpq", JSON.stringify(wpq));
    const fileInput = $('#wpqFile')[0];
    if (fileInput && fileInput.files.length > 0) {
        formData.append("file", fileInput.files[0]);
    }
    formData.append("method", "UploadWpq");

    try {
        $.ajax({
            type: "POST",
            url: handlersBaseURL + "/IwbHandlers/IwbHandler.ashx",
            data: formData,
            contentType: false,
            processData: false,
            success: function (response) {
                console.log('response-->', response);
                response = JSON.parse(response);
                switch (response.success) {
                    case true:
                        toastr.success('WPQ has been uploaded successfully.');
                        //$('#divDialogContents').mmsDialog('close');
                        $('#uploadWPQModal').modal('hide');
                        getWelders()
                        break;
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                alert("Error occurred uploadWpq(): " + jqXHR.responseText);
            }
        });
    } catch (e) {
        alert("Exception: " + e.message);
    }
}


function getWpqs(userId) {
    //debugger
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetWpqs', userId: userId },
        success: function (response) {
            var wpqs = response.data;
            $.each(wpqs, function (key, item) {
                console.log(item.Id + ':' + item.FileName);
            });

        }
    });
}


//Document
$(document).on('click', '.view-doc', function () {
    const pdfUrl = $(this).data('output-file');
    const signLink = $(this).data('signlink');
    // Open the PDF in a new tab
    window.open(pdfUrl + "#toolbar=0&navpanes=0&scrollbar=0", '_blank');
});

$(document).on('click', '.view-signed-doc', function () {
    const signLink = $(this).data('signlink');
    window.open(signLink + "#toolbar=0&navpanes=0&scrollbar=0", '_blank');
});

$(document).on('click', '.view-input-doc', function (e) {
    var inputFileName = $(this).data('input-file');
    const pdfUrl = clientRootURL + 'SystemUploadedFiles/Iwb/MTR/' + inputFileName;
    // Open the PDF in a new tab
    window.open(pdfUrl + "#toolbar=0&navpanes=0&scrollbar=0", '_blank');
});


function getDocuments() {
    docTable = $('#tblDocuments').dataTable({
        destroy: true,
        processing: false,
        serverSide: true,
        ordering: false,
        searching: false,
        autoWidth: false,
        lengthChange: false,
        paging: true,
        pageLength: pageSize,
        responsive: true,
        //order: [[1, "asc"]],
        //scrollCollapse: true,
        scrollY: '62vh',
        ajax: {
            //url: iwburl + '?OrderBy=Name&OrderDirection=desc',
            url: iwburl,
            type: 'GET',
            datatype: 'json',
            headers: {
                'Content-Type': 'application/json',
            },
            data: function (data, settings) {
                var api = new $.fn.dataTable.Api(settings);
                var pageIndex = api === undefined ? 1 : api.page.info().page + 1;
                var docName = $('#txtFilterDocName').val();
                var docTypeId = $('input[name=rbFilterDocType]:checked').val();
                var docAlias = $('#txtFilterAlias').val();
                console.log('searchbox docName, docTypeId, docAlias', docName, docTypeId, docAlias);
                return { method: 'GetDocuments', pageIndex: pageIndex, pageSize: pageSize, docName: docName, documentTypeId: docTypeId, docAlias: docAlias };
            },
            //error: function (jqXHR, textStatus, errorThrown) { }
            dataSrc: function (json) {
                var totalRows = json.rowCount;
                json.recordsTotal = totalRows;
                json.recordsFiltered = totalRows;
                return json.data;
            }
        },
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-id', data.Id);
            $(row).attr('data-iwbPdfFileName', data.FileName);
            $(row).attr('data-iwbpdffilelink', data.OutputFileName);
            $(row).attr('data-signlink', data.SignLink);
        },
        initComplete: (settings, json) => {
            checkUserPermission();
            initializeDataTableTooltips();
        },
        drawCallback: function (settings) {
            checkUserPermission();
            initializeDataTableTooltips();
        },
        columns: [
            {
                data: '#', sortable: false, width: "5%",
                render: function (data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'Name', width: '15%' },
            { data: 'Type', width: '10%' },
            {
                data: 'Alias', width: '20%', defaultContent: 'N/A',
                render: function (data, type, row) {
                    return data === '' ? row.FileName : data;
                }
            },
            {
                data: 'UploadDate',
                width: '10%',
                defaultContent: 'N/A',
                render: function (data, type, row) {
                    return moment(data).format('DD/MMM/YYYY');
                }
            },
            { data: 'UploadedBy', width: '10%', defaultContent: 'N/A' },
            {
                data: 'Id',
                title: 'Action(s)',
                width: '40%',
                render: function (data, type, row, meta) {
                    let signRequestButton = '<a data-id="' + data + '" href="javascript:void(0);" class="btn btn-sm sign-doc signoff-lnk d-none"><i class="fa fa-pencil me-1"></i>Sign Request</a>';
                    let viewSignOffButton = '<a data-id="' + data + '" data-output-file="' + row.OutputFileName + '" href="javascript:void(0);" class="btn btn-sm view-signed-doc d-none" data-signlink="' + row.SignLink + '"><i class="fa fa-eye me-1"></i>View Signed</a>';
                    let viewOutputButton = '<a data-id="' + data + '" data-output-file="' + row.OutputFileName + '" href="javascript:void(0);" class="btn btn-sm view-doc d-none" data-signlink="' + row.SignLink + '"><i class="fa fa-eye me-1"></i>View</a>';
                    let addPlayListButton = '<a data-id="' + data + '" href="javascript:void(0);" onclick="showPlayListDialog(' + data + ');" class="btn btn-sm"><i class="fa fa-plus me-1"></i>Add to Repository</a>';
                    let viewOriginalButton = '<a data-id="' + data + '" data-input-file="' + row.FileName + '" href="javascript:void(0);" class="btn btn-sm view-input-doc d-none"><i class="fa fa-file-pdf me-1"></i>View Original</a>';
                    let signOffButton = '<a data-id="' + data + '" href="javascript:void(0);" status="' + row.CurrentStatus + '" class="btn btn-sm view-sign-off"><i class="fa fa-eye me-1"></i> View SignOff</a>';
                    let editDocumentButton = '<a data-id="' + data + '" href="javascript:void(0);" class="btn btn-sm edit-doc"><i class="fa fa-pencil me-1"></i>Edit</a>';
                    //let editDocumentButton = '<a data-id="' + data + '" href="javascript:void(0);" class="btn btn-sm edit-doc" onclick="loadDocumentForEdit(' + data + ');"><i class="fa fa-pencil me-1"></i>Edit</a>';
                    return editDocumentButton + signRequestButton + (row.OutputFileName === '' ? "&nbsp;" : viewOutputButton) + viewSignOffButton + addPlayListButton + viewOriginalButton + signOffButton;
                }
            }
        ],
        language: {
            paginate: {
                previous: 'Prev',
                next: 'Next',
            },
            //processing: "Loading Data...",
            zeroRecords: "No matching records found"
        },

    });
    initializeDataTableTooltips();

}

/*$('#tblDocuments').on('page.dt', function () {
    console.log('page change');
    checkUserPermission();
});*/

function loadAddDocument() {
    $.ajax({
        type: 'POST',
        url: "ManageDocuments.aspx/LoadUserControlAddNew",
        data: JSON.stringify({ userId: 1000 }),
        contentType: "application/json; charset=utf-8",
        dataType: 'JSON',
        success: function (response) {
            // Since AddDocument is now a web page, navigate to it
            if (response.d) {
                window.location.href = response.d;
            }
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function saveDocumentEdit() {
    var DocId = $('#hdnDocumentId').val();
    console.log('DocId--', DocId);
    UpdateDocument();
}

function UpdateDocument() {
    var validationResult = validateDocument();
    if (!validationResult.IsValid) {
        $('#edit-validation-summary').html(validation.Messagesmarkup);
        return;
    }

    var formData = new FormData();

    // Get the selected file
    var file = $('#dZUploadDocument').get(0).dropzone.getAcceptedFiles()[0];
    if (file) {
        formData.append("file", file);
    }

    formData.append("Name", $('#edittxtName').val());
    formData.append("Type", $('input[name=rbDoc]:checked').val());
    formData.append("Description", $('#edittxtDescription').val());

    formData.append("DocumentId", $('#hdnDocumentId').val());

    var document = {
        Alias: $('.doc-name').val(),
        Name: $('#edittxtName').val(),
        Type: $('input[name=rbDoc]:checked').val(),
        Description: $('#edittxtDescription').val(),
        Id: $('#hdnDocumentId').val()
    };
    formData.append("doc", JSON.stringify(document));

    $.ajax({
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=SaveDocument',
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        beforeSend: function () {
            $.blockUI({ baseZ: 10000, message: '<h2><i class="fa fa-spinner fa-spin fa-5x fa-fw"></i><span class="sr-only">Processing...</span></h2>' });
        },
        success: function (response) {
            $.unblockUI();
            var resultData = JSON.parse(response);
            if (resultData.success) {
                toastr.success(resultData.message);
                $('#editDocumentModal').modal('hide');
                getDocuments();
            } else {
                toastr.error(resultData.data || resultData.message);
            }
        },
        error: function (xhr, status, error) {
            $.unblockUI();
            toastr.error("Something went wrong. " + error);
        }
    });
}


/*$('#btnSaveDocument').click(function () {
    docDropzone.processQueue();
});*/

function saveDocument() {
    var validationResult = validateDocument();
    if (!validationResult.IsValid) {
        $('#add-validation-summary').html(validation.Messagesmarkup);
        return;
    }

    docDropzone.processQueue();
}

function validateDocument() {
    var isEdit = $('#edittxtName:visible').length > 0;
    var validationTarget = isEdit ? $('#edit-validation-summary') : $('#add-validation-summary');
    validationTarget.html('');

    //$('.validation-summary').html('');

    //var doc = {
    //    Document: $('#dZUploadDocument').get(0).dropzone.getAcceptedFiles(),
    //    Name: $('#txtName').val(),
    //    Type: $('input[name=rbDoc]').is(':checked'),
    //    Description: $('#txtDescription').val(),
    //};

    var name = $('#edittxtName:visible').length ? $('#edittxtName').val() : $('#txtName').val();
    var description = $('#edittxtDescription:visible').length ? $('#edittxtDescription').val() : $('#txtDescription').val();
    var isTypeChecked = $('input[name=rbDoc]:visible').is(':checked');

    var doc = {
        Document: $('#dZUploadDocument').get(0).dropzone.getAcceptedFiles(),
        Name: name,
        Type: isTypeChecked,
        Description: description
    };

    console.log('validateDocument-->', doc);
    try {
        var validationResult = {
            IsValid: true,
            Messagesmarkup: $("<ul></ul>")
        };

        if (doc.Document.length == 0) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please upload valid pdf document.</li>");
        }
        if (doc.Name.trim() == "") {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please enter document name.</li>");
        }
        if (doc.Type == false) {
            validationResult.IsValid = false;
            validationResult.Messagesmarkup.append("<li>Please check document type.</li>");
        }

        if (!validationResult.IsValid) {
            validationTarget.html(validationResult.Messagesmarkup);
        }

        return validationResult;
    }
    catch (e) {
        console.error('Error in validateDocument : ' + e);
    }
}


$(document).on('click', '#btnTryAI', function () {
    debugger;
    var docType = $('input[name=rbDoc]:checked').next('label').text().trim();
    /*$('.validation-summary').html('');
    if (docType != 'MTR') {
        var msg = $("<ul><li>Only MTR document is allowed for OCR</li></ul>");
        $('.validation-summary').append(msg);
        return;
    }*/

    var uploadedFile = $('#uploadDropzone').get(0).dropzone.getAcceptedFiles()[0];

    if (uploadedFile) {
        processDocumentForOCRV1(docType);
    }

    //else {

    //    alert("Please upload a document before running AI validation.");

    //}

});

function formatErrorMessage(jqXHR, exception) {
    if (jqXHR.status === 0) {
        return ('AI Server Unavailable.\n Our AI server is currently down. We are working to resolve the issue as soon as possible. \n Please try again later or contact support if you need assistance.');
    } else if (jqXHR.status == 404) {
        return ('Not found. The requested endpoint was not found. [404]');
    } else if (jqXHR.status == 500) {
        return ('Error in AI Server. Internal Server Error [500].');
    } else if (exception === 'parsererror') {
        return ('Requested JSON parse failed.');
    } else if (exception === 'timeout') {
        return ('Time out error.');
    } else if (exception === 'abort') {
        return ('Ajax request aborted.');
    } else {
        return ('Uncaught Error.\n' + jqXHR.responseText);
    }
}

function processDocumentForOCR(docType) {
    //debugger
    var uploadedFile = $('#dZUploadDocument').get(0).dropzone.getAcceptedFiles()[0];
    var formData = new FormData();
    formData.append('file', uploadedFile);
    formData.append('docType', docType);

    $.blockUI({ baseZ: 10000, message: '<h2><i class="fa fa-spinner fa-spin fa-5x fa-fw"></i><span class="sr-only">' + langKeys[currentLanguage]['Processing'] + '</h2>' });

    $.ajax({
        type: 'POST',
        url: aiApiUrl + '/upload_pdf/',
        data: formData,
        contentType: false,
        processData: false,
        success: function (response) {
            $.unblockUI();
            var html = '';
            switch (docType) {
                case 'MTR':
                    var validationResult = response.ValidationResult;

                    if (validationResult.ResponeInformation === undefined || validationResult.ResponeInformation.Acknowledge === 'Failed') {
                        Swal.fire({
                            title: "Unable to Scan",
                            text: "Unable to scan the required information for MTR Validation, please ensure document is complete and legible.",
                            icon: "error",
                        });
                        return;
                    }
                    if (validationResult.ResponeInformation.Acknowledge === 'Success' && validationResult.ResponeInformation.CompliantStatus == 'Non-Compliant') {
                        Swal.fire({
                            title: "Document has been scanned but Non-Compliant",
                            text: "Do you want to modify the result ?",
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#3085d6",
                            cancelButtonColor: "#d33",
                            confirmButtonText: "Yes",
                            cancelButtonText: "No"
                        }).then((result) => {
                            if (result.isConfirmed) {
                                //console.log('Open input form');
                                createMTRInputForm(response.KeyValues);
                            }
                            else {
                                html = buildResponseMTR(validationResult);
                            }
                        });
                        return;
                    }
                    else {
                        html = buildResponseMTR(validationResult);
                        return;
                    }
                    break;
                case 'WPS':
                    html = buildResponseWPS(response);
                    break;
                case 'PQR':
                    html = buildResponsePQR(response);
                    break;
                case 'WPQ':
                    html = buildResponseWPQ(response);
                    break;
                default:
                    html = '';
                    break;
            }

        },
        error: function (jqXHR, textStatus, errorThrown) {
            //debugger
            var responseTitle = $(jqXHR.responseText).filter('title').get(0);
            //alert($(responseTitle).text() + "\n" + formatErrorMessage(jqXHR, errorThrown)); 

            Swal.fire({
                title: responseTitle,
                text: formatErrorMessage(jqXHR, errorThrown),
                icon: "error",
            });

            $.unblockUI();
            console.log('Error occurred processDocumentForOCR()');
            console.log(jqXHR.responseText);
        }
    });
}








function buildResponseWPS(response) {
    var data2send = JSON.stringify(response, null, 2);
    $.ajax({
        type: 'POST',
        url: 'https://tools.atatus.net/v1/tools/json-to-html',
        data: { jsonData: data2send },
        success: function (response) {
            //console.log(response);
            showAIResults(response);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log('Error occurred buildResponseWPS()');
            console.log(jqXHR.responseText);
        }
    });
}

function buildResponsePQR(response) {
    var data2send = JSON.stringify(response, null, 2);
    $.ajax({
        type: 'POST',
        url: 'https://tools.atatus.net/v1/tools/json-to-html',
        data: { jsonData: data2send },
        success: function (response) {
            //console.log(response);
            showAIResults(response);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log('Error occurred buildResponsePQR()');
            console.log(jqXHR.responseText);
        }
    });
}
function buildResponseWPQ(response) {
    var data2send = JSON.stringify(response, null, 2);
    $.ajax({
        type: 'POST',
        url: 'https://tools.atatus.net/v1/tools/json-to-html',
        data: { jsonData: data2send },
        success: function (response) {
            //console.log(response);
            showAIResults(response);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log('Error occurred buildResponseWPQ()');
            console.log(jqXHR.responseText);
        }
    });
}



function loadSubmitRating(welderUserNum, testId, welderId) {
    debugger;

    $('#submitRatingPopup').mmsDialog({
        autoOpen: true,
        closeOnEscape: true,
        draggable: true,
        resizable: false,
        modal: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: 'Submit Welder Rating',
        maxWidth: 'auto',
        width: '50%',
        open: function () {
            // Optional logic on open
        },
        close: function () {
            // Clear all inputs and validation messages
            $('#submitRatingForm').find('input[type=number], textarea').val('');
            $('.welder-rating-validation-summary').empty();
            $(this).mmsDialog('close');
        },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnSave'],
                click: function () {
                    if (!validateWelderRating()) {
                        return;
                    }
                    var ratingData = {
                        welderId: welderId,
                        performanceRating: $('#performanceRating').val(),
                        performanceComment: $('#performanceComment').val(),
                        weldCount: $('#weldCount').val(),
                        weldCountComment: $('#weldCountComment').val(),
                        repairs: $('#repairs').val(),
                        repairsComment: $('#repairsComment').val(),
                        workEthicRating: $('#workEthicRating').val(),
                        workEthicComment: $('#workEthicComment').val(),
                        createdBy: 1
                    };

                    $.ajax({
                        type: 'POST',
                        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=SaveWelderRatings',
                        data: JSON.stringify(ratingData),
                        contentType: 'application/json; charset=utf-8',
                        dataType: 'json',
                        success: function (response) {
                            if (response.success) {
                                toastr.success('Saved successfully.');
                                $('#submitRatingPopup').mmsDialog('close');

                                $('#submitRatingForm').find('input[type=number], textarea').val('');
                                $('.welder-rating-validation-summary').empty();
                            } else {
                                $.unblockUI();
                                toastr.error(response.message);
                            }
                        },
                        error: function (error) {
                            console.error('Error saving welder rating:', error);
                        }
                    });
                }
            },
            {
                text: langKeys[currentLanguage]['msgBtnClose'],
                click: function () {
                    $('#submitRatingForm').find('input[type=number], textarea').val('');
                    $('.welder-rating-validation-summary').empty();
                    $(this).mmsDialog('close');
                }
            }
        ]
    });

    $('#submitRatingPopup').mmsDialog('open');
}

function validateWelderRating() {
    debugger;
    $('.welder-rating-validation-summary').html('');

    var validationResult = {
        IsValid: true,
        Messagesmarkup: $("<ul></ul>")
    };

    var performanceRating = $('#performanceRating').val();
    var weldCount = $('#weldCount').val();
    var repairs = $('#repairs').val();
    var workEthicRating = $('#workEthicRating').val();

    if (!performanceRating) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please select a performance rating.</li>");
    }

    if (!weldCount) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter the weld count.</li>");
    }

    if (!repairs) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please enter the number of repairs.</li>");
    }

    if (!workEthicRating) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please select a work ethic rating.</li>");
    }

    if (!validationResult.IsValid) {
        $('.welder-rating-validation-summary').html(validationResult.Messagesmarkup);
    }

    return validationResult.IsValid;
}


function fetchWelderRatings() {
    var welderId = $('#submitRatingPopup').data('welderId');
    $.ajax({
        type: 'GET',
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=GetWelderRatings',
        data: {
            welderId: welderId
        },
        success: function (response) {
            var ratingdata = response.data;
            console.log('Get welder rating response->', ratingdata);
            alert('Welder rating fetched successfully!');
        },
        error: function (xhr, status, error) {
            console.error("AJAX Error: ", error);
            alert("Error occurred while fetching welder ratings.");
        }
    });
}





function searchSystemUser(inputVal) {
    $('#tblSystemUsers tbody tr').each(function () {
        let userEmail = $(this).attr("userEmail");
        if (userEmail) {
            var found = false;
            var regExp = new RegExp(inputVal, 'i');
            if (regExp.test(userEmail)) {
                found = true;
            }
        }
        if (found == true)
            $(this).show();
        else
            $(this).hide();
    });
}

function isValidEmailAddress(email) {
    var validationFilter = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/
    if (!(email == "" || email == null) && validationFilter.test(email))
        return true;
    else
        return false;
}

function GenerateParticipantHTML(userEmail) {
    $participantHTML = "<tr userEmail='" + userEmail + "' userNum='0' isSystemUser='false' userName='" + userEmail.split('@')[0] + "' hasMonitorPermission='false'>" +
        "<td><input type='checkbox' /></td>" +
        "<td>" + userEmail + "</td>" +
        "</tr>";
    return $participantHTML;
}


//#region Getting manage document context menu related codes
function showPlayListDialog(selectedDocumentId) {
    debugger;
    $('#divPlayListDialog').mmsDialog({
        autoOpen: true,
        closeOnEscape: true,
        draggable: true,
        resizable: false,
        modal: true,
        closeText: langKeys[currentLanguage]['ttClose'],
        title: 'Select Repository',
        maxwidth: 'auto',
        width: '50%',
        height: 300,
        open: function (event, ui) {
            getAllPlayListData();
        },
        close: function () {

        },
        buttons: [
            {
                text: langKeys[currentLanguage]['msgBtnClose'],
                click: function () {
                    $(this).mmsDialog("close");
                }
            },
            {
                text: langKeys[currentLanguage]['cbxPlayList1'],
                click: function () {
                    //console.log("Selected document  => ", selectedDocumentId);
                    //console.log("Added playlist to  => ", $('#cbxLoadPlayList').val());

                    addDocumentToPlayList(selectedDocumentId, $('#cbxLoadPlayList').val(), this);
                }
            }
        ]
    });
    $('#divPlayListDialog').mmsDialog('open');
}

function getAllPlayListData() {
    //debugger;
    var playlistBaseHandlerURL = '../Handlers/VoiceRecHandlers/PlaylistHandler.ashx?method=FetchAllPlaylists';

    try {
        $.ajax({
            cache: false,
            type: "GET",
            async: true,
            url: playlistBaseHandlerURL,
            data: { isSharedPage: false },
            success: function (response) {
                console.log("Playlistdata => ", response);
                $('#cbxLoadPlayList').empty();
                $('#cbxLoadPlayList').append('<option value="-1">Select a repository</option>');

                var playList = response.data;
                $.each(playList, function (index, item) {
                    $('#cbxLoadPlayList').append('<option value="' + item["Id"] + '">' + item["Name"] + '</option>');
                });
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showExceptionDialog('Error occurred getAllPlayListData(): ' + jqXHR.responseText);
            }
        });
    } catch (e) {
        $.unblockUI();
    }
}

function addDocumentToPlayList(documentId, playListId, modelObject) {
    $('#playlistValidationErrors').empty();
    $('#playlistValidationSummary').hide();

    // Validation logic
    var validationResult = {
        IsValid: true,
        Messagesmarkup: $("<ul></ul>")
    };

    if (playListId === "-1" || !playListId) {
        validationResult.IsValid = false;
        validationResult.Messagesmarkup.append("<li>Please select a repository.</li>");
    }

    if (!validationResult.IsValid) {
        $('#playlistValidationErrors').html(validationResult.Messagesmarkup.html());
        $('#playlistValidationSummary').show();
        return;
    }

    var addPlayListUrl = '../Handlers/VoiceRecHandlers/PlaylistHandler.ashx?method=AddDocumentToPlayList';

    try {
        $.ajax({
            cache: false,
            type: "POST",
            async: true,
            url: addPlayListUrl,
            data: {
                documentId,
                playListId,
                tenantId
            },
            success: function (response) {
                toastr.success('Repository Added successfully!');
                console.log("Playlistdata => ", response);
                $(modelObject).mmsDialog("close");
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showExceptionDialog('Error occurred getAllPlayListData(): ' + jqXHR.responseText);

            }
        });
    } catch (e) {
        $.unblockUI();
    }
}
 //#endregion

//#region mytest page methods
function redirectWpqForm(recordId, isJob, welderId, wpqId) {

    if (wpqId == undefined || wpqId == null) {
        wpqId = 0;
    }

    if (isJob) {
        window.top.location.href = "WPQForm.aspx?jobid=" + recordId + "&welderId=" + welderId + "&wpqId=" + wpqId;
    }
    else {
        window.top.location.href = "WPQForm.aspx?testid=" + recordId + "&welderId=" + welderId + "&wpqId=" + wpqId;
    }
}
//#endregion

//#region manage job methods
function updateApplicantJobStatus(statusId, currentObj, rowData) {
    var applicantData = {
        jobId: rowData.JobId,
        statusId: statusId,
        applicantId: rowData.ApplicantId,
        method: 'UpdateJobApplicantStatus'
    };

    try {
        $.ajax({
            type: 'POST',
            url: iwburl,
            data: applicantData,
            success: function (response) {
                getJobApplicants(rowData.JobId);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $.unblockUI();
                showExceptionDialog('Error occurred updateApplicantJobStatus(): ' + jqXHR.responseText);
            }

        });
    } catch (e) {
        showExceptionDialog(e.message);
    }
}
//#endregion

function ocrResultModalStatus(isShow, resultData) {
    debugger
    console.log('resultData--', resultData)
    $("#ocrResponseModal").modal(isShow ? "show" : "hide");

    if (isShow && resultData != null && resultData != undefined) {
        $("#ocrResponseModal").attr("ocrresult", JSON.stringify(resultData));
        var ocrResultData = null;

        try {
            ocrResultData = JSON.parse(resultData.data.ocrResult);
        } catch (e) {
            ocrResultData = null;
        }

        $("#btnViewOCROutput").hide();
        $("#btnViewOCROutput").attr("outputfile", "");

        console.log('ocrResultData--', ocrResultData);
        if (ocrResultData != null) {
            $("#btnViewOCROutput").show();
            $("#btnViewOCROutput").attr("outputfile", resultData.data.outputFile);

            $("#ocrStatus").html("<p>AI " + ocrResultData.InputReportType + " Verification Complete.</p>");

            if (ocrResultData.CompliantStatus == "Compliant") {
                $("#ocrStatus").append("<p>Status: Complaint</p>");
            }
            else {
                $("#ocrStatus").append("<p>Status: Non-Complaint</p>");
            }

            $("#ocrStatus").append("<p>ProcessingTime: " + ocrResultData.ProcessingTime + "</p>");
        } else {
            $("#ocrStatus").html("<p> Unable to Process the document, </p> ");
            $("#ocrStatus").append("<p>AI server response error</p>");
        }

    }
}

function saveOCResponse() {
    debugger;
    let ocrResposneResult = JSON.parse($("#ocrResponseModal").attr("ocrresult"));
    debugger
    if (ocrResposneResult != null && ocrResposneResult != undefined) {
        var inputData =
        {
            'method': 'SaveOCRResponse',
            docuemntId: ocrResposneResult.data.iwbDocumentid
        }

        $.ajax({
            cache: false,
            type: "POST",
            async: true,
            url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=SaveOCRResponse',
            data: inputData,
            success: function (response) {
                debugger;
                toastr.success("IWB Document saved successfully");
                $('#0divAddDialogContents').mmsDialog("close");
                ocrResultModalStatus(false);
                setTimeout(function () {
                    try {
                        window.top.location.href = 'ManageDocuments.aspx';

                        setTimeout(function () {
                            $('#divAddDialogContents').mmsDialog("close");
                        }, 300);

                    } catch (e) {
                        console.log('Error during redirect or dialog close:', e);
                    }
                }, 800);

                showSelfSignRequestDialog(ocrResposneResult);
                getDocuments();
            },
            error: function (jqXHR, textStatus, errorThrown) {

            }
        });
    }
}

function showOutputPdfFile(currentElement) {

    let outputFile = $(currentElement).attr("outputfile");

    window.open(outputFile + "#toolbar=0&navpanes=0&scrollbar=0", '_blank');
}



function startWPQProcess(currentObj) {

    let attendeeId = $(currentObj).attr("attendeeid");
    let passStatus = $(currentObj).prop('checked') ? 2 : 1;
    let testid = $(currentObj).attr("testid");
    let recordid = $(currentObj).attr("recordid");

    var inputData =
    {
        'method': 'UpdateWelderTestStatus',
        Id: attendeeId,
        PassStatus: passStatus,
        recordid
    };

    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=UpdateWelderTestStatus',
        data: inputData,
        success: function (response) {
            Swal.fire({
                title: "Status",
                text: "Updated successfully. Please fill the WPQ of this welder",
                icon: "success",
            }).then((result) => {
                if (passStatus == 2) {
                    redirectWpqForm(testid, false, attendeeId);
                }
            });
        },
        error: function (jqXHR, textStatus, errorThrown) {

        }
    });
}

function UpdateTestStatus(currentObj, currentStatus) {

    let testId = $(currentObj).data("id");

    var inputData =
    {
        'method': 'UpdateTestStatus',
        Id: testId,
        Status: currentStatus
    };

    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=UpdateTestStatus',
        data: inputData,
        success: function (response) {
            Swal.fire({
                title: "Status",
                text: "Updated successfully.",
                icon: "success",
            }).then((result) => {
                getTests();
            });
        },
        error: function (jqXHR, textStatus, errorThrown) {

        }
    });
}

function showPdfFile(currentElement) {
    console.log('showPdfFile-->', currentElement);
    let outputFile = $(currentElement).attr("output-file");
    window.open(outputFile + "#toolbar=0&navpanes=0&scrollbar=0", '_blank');
}


function updateViewSignOffPopup(isShow, docuemntId) {
    $("#viewSignOffPopup").modal(isShow ? "show" : "hide");

    if (isShow) {
        $("#btnExportSignOff").data("id", docuemntId);
        getSignOffRequestUsers(docuemntId);
    }
}

function getSignOffRequestUsers(documentId) {
    debugger;
    $.ajax({
        type: 'GET',
        url: iwburl,
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: { method: 'GetSignOffRequestUsers', documentId: documentId },
        success: function (response) {
            console.log('response object => ', response);
            var users = response.data.userList;
            let ownerEmail = response.data.ownerEmail;
            console.log('SignOffRequestUsers response:', users);

            $('#tblSignOffRequestUsers').attr("documentid", documentId);
            $('#tblSignOffRequestUsers').attr("ownerEmail", ownerEmail);

            $('#tblSignOffRequestUsers').dataTable({
                data: users,
                destroy: true,
                responsive: true,
                ordering: false,
                searching: false,
                autoWidth: false,
                lengthChange: false,
                paging: false,
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('data-id', data.Id);
                },
                columns: [
                    {
                        data: null,
                        orderable: false,
                        searchable: false,
                        width: "5%",
                        title: `<input type="checkbox" id="selectAllCheckbox" />`,
                        render: function (data, type, row, meta) {
                            return `<input type="checkbox" class="row-checkbox" value="${row.Id || ''}">`;
                        }
                    },
                    { data: 'Name', title: 'Name', width: '25%' },
                    { data: 'Email', title: 'Email', width: '30%' },
                    { data: 'SignStatus', title: 'Sign Status', width: '20%' }
                ],
                language: {
                    paginate: {
                        previous: 'Prev',
                    },
                    processing: "Loading Data...",
                    zeroRecords: "No matching records found"
                },
            });
        }
    });
}

function exportSelectedSignOffUser() {
    var selectedSigners = [];
    $('#tblSignOffRequestUsers tbody input.row-checkbox:checked').each(function () {
        selectedSigners.push(parseInt($(this).val()));
    });

    if (selectedSigners.length === 0) {
        alert("Please select at least one signer to export.");
        return;
    }

    let documentId = $('#tblSignOffRequestUsers').attr("documentid");
    let ownerEmail = $('#tblSignOffRequestUsers').attr("ownerEmail");

    var selectedIdsCSV = selectedSigners.join(',');

    var inputData =
    {
        'method': 'ExportSignedPDF',
        selectedIds: selectedIdsCSV,
        signerEmail: ownerEmail,
        documentId: documentId
    }

    $.blockUI();

    $.ajax({
        cache: false,
        type: "POST",
        async: true,
        url: handlersBaseURL + '/IwbHandlers/IwbHandler.ashx?method=ExportSignedPDF',
        data: inputData,
        success: function (response) {
            console.log("Response => ", response);

            $.unblockUI();
            if (response.success) {
                window.open(response.downloadUrl + "#toolbar=0&navpanes=0&scrollbar=0", '_blank');
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $.unblockUI();
        }
    });
}

