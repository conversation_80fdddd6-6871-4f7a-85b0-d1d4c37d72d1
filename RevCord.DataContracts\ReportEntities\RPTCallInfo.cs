﻿//***********************************************************************
// AssemblyName   : 
// Author               :   <PERSON><PERSON>
// Created ON           :   15-March-2012
//
// Last Modified By     :   <PERSON><PERSON>
// Last Modified On     :   20-April-2012
// Description          :   
//***********************************************************************

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.ReportEntities
{
    public class RPTCallInfo
    {
        #region Properties

        public string Key { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public int Day { get; set; }
        public long Count { get; set; }
        public long Total { get; set; }//Value
        public long Avg { get; set; }
        public int CallHour { get; set; }

        public DateTime CallDate { get; set; }
        public DayOfWeek DayOfWeek { get; set; }
        public string DayOfWeekString { get; set; }

        public int TotalNoOfRings { get; set; }
        public int TotalTransferredCount { get; set; }
        public int TotalAbandonedCount { get; set; }
        public long TotalRingTime { get; set; }
        public long TotalTalkTime { get; set; }
        public long TotalHoldTime { get; set; }


        public int RecorderId { get; set; }
        public string RecoderName { get; set; }

        #endregion

        public RPTCallInfo() { }
        public RPTCallInfo(string key, int year, int month, int day, long count, int recId, string recName)
        {
            this.Key = key;
            this.Year = year;
            this.Month = month;
            this.Day = day;
            this.Count = count;
            if (day != 0)
                this.CallDate = new DateTime(year, month, day);
            else
            {
                int d = day + 1;
                this.CallDate = new DateTime(year, month, d);
            }
            this.RecorderId = recId;
            this.RecoderName = recName;
        }
        public RPTCallInfo(string key, int year, int month, int day, long count, long total, long avg)
        {
            this.Key = key;
            this.Year = year;
            this.Month = month;
            this.Day = day;
            this.Count = count;
            this.Total = total;
            this.Avg = avg;
            if (day != 0)
                this.CallDate = new DateTime(year, month, day);
            else
            {
                int d = day + 1;
                this.CallDate = new DateTime(year, month, d);
            }
        }
        public RPTCallInfo(string key, int year, int month, int day, long count, long total, long avg, int recId, string recName)
        {
            this.Key = key;
            this.Year = year;
            this.Month = month;
            this.Day = day;
            this.Count = count;
            this.Total = total;
            this.Avg = avg;
            if (day != 0)
                this.CallDate = new DateTime(year, month, day);
            else
            {
                int d = day + 1;
                this.CallDate = new DateTime(year, month, d);
            }
            this.RecorderId = recId;
            this.RecoderName = recName;
        }

        public RPTCallInfo(string key, int year, int month, int day, int callHour, long count, long total, long avg)
        {
            this.Key = key;
            this.Year = year;
            this.Month = month;
            this.Day = day;
            this.CallHour = callHour;
            this.Count = count;
            this.Total = total;
            this.Avg = avg;
            if (day != 0)
                this.CallDate = new DateTime(year, month, day);
            else
            {
                int d = day + 1;
                this.CallDate = new DateTime(year, month, d);
            }
        }
        public RPTCallInfo(string key, int year, int month, int day, int callHour, long count, long total, long avg, int recId, string recName)
        {
            this.Key = key;
            this.Year = year;
            this.Month = month;
            this.Day = day;
            this.CallHour = callHour;
            this.Count = count;
            this.Total = total;
            this.Avg = avg;
            if (day != 0)
                this.CallDate = new DateTime(year, month, day);
            else
            {
                int d = day + 1;
                this.CallDate = new DateTime(year, month, d);
            }
            this.RecorderId = recId;
            this.RecoderName = recName;
        }

        public RPTCallInfo(string key, int year, int month, DayOfWeek dayOfWeek, long count, long total, long avg)
        {
            this.Key = key;
            this.Year = year;
            this.Month = month;
            this.DayOfWeek = dayOfWeek;
            this.Count = count;
            this.Total = total;
            this.Avg = avg;

            this.CallDate = new DateTime(year, month, 1);
        }
        public RPTCallInfo(string key, int year, int month, DayOfWeek dayOfWeek, long count, long total, long avg, int recId, string recName)
        {
            this.Key = key;
            this.Year = year;
            this.Month = month;
            this.DayOfWeek = dayOfWeek;
            this.Count = count;
            this.Total = total;
            this.Avg = avg;

            this.CallDate = new DateTime(year, month, 1);
            this.RecorderId = recId;
            this.RecoderName = recName;
        }

        public RPTCallInfo(string key, int year, int month, int day, long count, long total, long avg, int recId, string recName, int noOfRings, int totalTransferredCount, int totalAbandonedCount, long totalRingTime, long totalTalkTime, long totalHoldTime)
        {
            this.Key = key;
            this.Year = year;
            this.Month = month;
            this.Day = day;
            this.Count = count;
            this.Total = total;
            this.Avg = avg;
            if (day != 0)
                this.CallDate = new DateTime(year, month, day);
            else
            {
                int d = day + 1;
                this.CallDate = new DateTime(year, month, d);
            }
            this.RecorderId = recId;
            this.RecoderName = recName;

            this.TotalNoOfRings = noOfRings;
            this.TotalTransferredCount = totalTransferredCount;
            this.TotalAbandonedCount = totalAbandonedCount;
            this.TotalRingTime = totalRingTime;
            this.TotalTalkTime = totalTalkTime;
            this.TotalHoldTime = totalHoldTime;
        }

    }
}
