﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RoleManagement
{
    public class Role
    {
        public int Id { get; set; }
        public int RoleType { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsSystemRole { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }

        public int PermissionsCount { get; set; }
        public int AllocatedCount { get; set; }
    }
}
