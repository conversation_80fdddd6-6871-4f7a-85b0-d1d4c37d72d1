﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.RevcellEntities
{
    [JsonObject("links")]
    public class Link
    {
        [JsonProperty("self")]
        public string Self { get; set; }
        [JsonProperty("first")]
        public string First { get; set; }
        [JsonProperty("next")]
        public string Next { get; set; }
        [JsonProperty("prev")]
        public string Prev { get; set; }
    }
}
